import cx_Oracle
import pandas as pd
import json

# Oracle 資料庫連接資訊
dsn_tns = cx_Oracle.makedsn('172.20.160.32', 1521, service_name='hy')
conn = cx_Oracle.connect(user='system', password='manager', dsn=dsn_tns)

# 執行 SQL 查詢
query = """
SELECT TBITNO, TBNAME, TBBAR, BEDSKC 
FROM IMTB, CMBE 
WHERE BEKEY1 = '57' AND BEKEY2 = TBPACK AND TRIM(TBDATE) IS NULL 
ORDER BY TBITNO
"""

# 使用 pandas 讀取查詢結果
df = pd.read_sql(query, con=conn)
conn.close()

# 定義產品類型對應的組件資訊
# {'name': '寶特瓶', 'category': '塑膠', 'image': 'https://imgur.com/F5dbNU9.png'},
# {'name': '塑膠套', 'category': '塑膠', 'image': 'https://i.imgur.com/JIauTjG.png'}
# {'name': '鐵鋁罐', 'category': '鐵鋁罐', 'image': 'https://i.imgur.com/F3dgmfX.jpeg'}
components_dict = {
    'PET瓶': [
        {'name': '寶特瓶', 'category': '塑膠', 'image': 'https://i.imgur.com/TN4ppng.png'}
    ],
    'PP瓶': [
        {'name': '寶特瓶', 'category': '塑膠', 'image': 'https://i.imgur.com/TN4ppng.png'}
    ],
    '鋁罐': [
        {'name': '鐵鋁罐', 'category': '鐵鋁罐', 'image': 'https://i.imgur.com/kGGIE4A.png'}
    ],
    '鋁箔包': [
        {'name': '鋁箔包', 'category': '紙盒', 'image': 'https://i.imgur.com/xueXrYN.jpeg'}
    ],
    '紙盒': [
        {'name': '紙盒', 'category': '紙盒', 'image': 'https://i.imgur.com/xueXrYN.jpeg'}
    ],
    '鐵罐': [
        {'name': '鐵罐', 'category': '鐵', 'image': 'https://i.imgur.com/kGGIE4A.png'}
    ],
    '玻璃瓶': [
        {'name': '玻璃瓶', 'category': '玻璃', 'image': 'https://i.imgur.com/CWn7J24.png'}
    ],
}

# 將查詢結果轉換為 JSON 格式
result = []
for index, row in df.iterrows():
    product_type = row['BEDSKC']

    if product_type in components_dict:
        product_info = {
            'name': product_type,
            'components': components_dict[product_type]
        }
        result.append({
            'TBITNO': row['TBITNO'],
            'TBNAME': row['TBNAME'],
            'TBBAR': row['TBBAR'],
            'BEDSKC': row['BEDSKC'],
            'product_info': product_info
        })

# 將結果轉換為 JSON 字串
json_result = json.dumps(result, ensure_ascii=False, indent=4)
print(json_result)
