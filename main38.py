import tkinter as tk
from tkinter import messagebox, ttk
import cx_Oracle
import os
import re
from dotenv import load_dotenv

# 載入 .env 檔
if os.path.exists(".env"):
    load_dotenv()

# 預設資料庫連線設定
DEFAULT_DATABASES = {
    "HY1 測試庫": {
        "host": "*************",
        "port": "1521",
        "service_name": "HY1",
        "sys_user": "system",
        "sys_password": "manager"
    },
    "HYIT 測試庫": {
        "host": "*************",
        "port": "1521",
        "service_name": "HYIT",
        "sys_user": "system",
        "sys_password": "Hy20651901"
    },
    "本地開發庫": {
        "host": "localhost",
        "port": "1521",
        "service_name": "XE",
        "sys_user": "system",
        "sys_password": "oracle"
    }
}

# 驗證 Oracle 使用者名稱（僅允許字母、數字、底線，長度2~30）
def is_valid_oracle_identifier(s):
    return re.match(r"^[A-Za-z][A-Za-z0-9_]{1,29}$", s) is not None

# 驗證密碼：不可包含空白或雙引號，且至少6碼
def is_valid_password(pw):
    return len(pw) >= 6 and ' ' not in pw and '"' not in pw

# 權限等級定義
PRIVILEGE_LEVELS = {
    "基本使用者": {
        "roles": ["CONNECT", "RESOURCE"],
        "privileges": ["UNLIMITED TABLESPACE"]
    },
    "開發者": {
        "roles": ["CONNECT", "RESOURCE"],
        "privileges": [
            "UNLIMITED TABLESPACE",
            "CREATE TABLE", "CREATE VIEW", "CREATE SEQUENCE",
            "CREATE PROCEDURE", "CREATE TRIGGER", "CREATE SYNONYM",
            "CREATE MATERIALIZED VIEW"
        ]
    },
    "高級開發者": {
        "roles": ["CONNECT", "RESOURCE"],
        "privileges": [
            "UNLIMITED TABLESPACE",
            "CREATE TABLE", "CREATE VIEW", "CREATE SEQUENCE",
            "CREATE PROCEDURE", "CREATE TRIGGER", "CREATE SYNONYM",
            "CREATE DATABASE LINK", "CREATE MATERIALIZED VIEW",
            "SELECT ANY TABLE", "INSERT ANY TABLE",
            "UPDATE ANY TABLE", "DELETE ANY TABLE",
            "CREATE ANY INDEX", "ALTER ANY INDEX"
        ]
    },
    "系統管理員": {
        "roles": ["DBA"],
        "privileges": [
            "SYSDBA", "SYSOPER",
            "CREATE USER", "ALTER USER", "DROP USER",
            "CREATE ROLE", "ALTER ANY ROLE", "DROP ANY ROLE",
            "GRANT ANY PRIVILEGE", "GRANT ANY ROLE",
            "CREATE ANY TABLE", "ALTER ANY TABLE", "DROP ANY TABLE",
            "BACKUP ANY TABLE", "BECOME USER",
            "FORCE TRANSACTION", "FORCE ANY TRANSACTION",
            "EXECUTE ANY PROCEDURE", "CREATE ANY PROCEDURE",
            "ALTER ANY PROCEDURE", "DROP ANY PROCEDURE"
        ]
    },
    "超級管理員": {
        "roles": ["DBA", "SYSDBA"],
        "privileges": [
            "ALL PRIVILEGES",
            "CREATE USER", "ALTER USER", "DROP USER",
            "CREATE ROLE", "ALTER ANY ROLE", "DROP ANY ROLE",
            "GRANT ANY PRIVILEGE", "GRANT ANY ROLE",
            "ADMINISTER DATABASE TRIGGER",
            "ALTER DATABASE", "ALTER SYSTEM",
            "BACKUP ANY TABLE", "BECOME USER",
            "CREATE ANY CLUSTER", "ALTER ANY CLUSTER", "DROP ANY CLUSTER",
            "CREATE ANY CONTEXT", "DROP ANY CONTEXT",
            "CREATE ANY DIRECTORY", "DROP ANY DIRECTORY",
            "EXECUTE ANY PROCEDURE", "EXECUTE ANY TYPE",
            "FORCE TRANSACTION", "FORCE ANY TRANSACTION",
            "MANAGE TABLESPACE", "UNLIMITED TABLESPACE"
        ]
    }
}

class DatabaseManagerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Oracle 多資料庫使用者管理工具")
        self.root.geometry("700x600")

        # 儲存資料庫連線設定
        self.databases = DEFAULT_DATABASES.copy()

        # 建立主要框架
        self.create_widgets()

    def create_widgets(self):
        # 建立 Notebook (分頁)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 分頁1: 使用者建立
        self.user_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.user_frame, text="建立使用者")
        self.create_user_tab()

        # 分頁2: 資料庫管理
        self.db_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.db_frame, text="資料庫管理")
        self.create_database_tab()

    def create_user_tab(self):
        # 資料庫選擇
        tk.Label(self.user_frame, text="選擇目標資料庫：", font=("Arial", 10, "bold")).pack(pady=(10, 5))
        self.db_var = tk.StringVar(value=list(self.databases.keys())[0])
        self.db_combo = ttk.Combobox(
            self.user_frame,
            textvariable=self.db_var,
            values=list(self.databases.keys()),
            state="readonly",
            width=30
        )
        self.db_combo.pack(pady=5)
        self.db_combo.bind("<<ComboboxSelected>>", self.update_db_info)

        # 資料庫連線資訊顯示
        self.db_info_label = tk.Label(self.user_frame, text="", fg="blue", font=("Arial", 9))
        self.db_info_label.pack(pady=5)

        # 分隔線
        ttk.Separator(self.user_frame, orient='horizontal').pack(fill='x', pady=10)

        # 使用者帳號輸入
        tk.Label(self.user_frame, text="使用者帳號（僅英數與底線）：").pack(pady=5)
        self.username_entry = tk.Entry(self.user_frame, width=30)
        self.username_entry.pack()

        # 密碼輸入
        tk.Label(self.user_frame, text="密碼（不可有空白與雙引號）：").pack(pady=5)
        self.password_entry = tk.Entry(self.user_frame, show="*", width=30)
        self.password_entry.pack()

        # 權限等級選擇
        tk.Label(self.user_frame, text="權限等級：").pack(pady=(15, 5))
        self.privilege_var = tk.StringVar(value="開發者")
        self.privilege_combo = ttk.Combobox(
            self.user_frame,
            textvariable=self.privilege_var,
            values=list(PRIVILEGE_LEVELS.keys()),
            state="readonly",
            width=25
        )
        self.privilege_combo.pack()

        # 權限說明文字框
        tk.Label(self.user_frame, text="權限說明：").pack(pady=(15, 5))
        self.privilege_text = tk.Text(self.user_frame, height=6, width=70, wrap=tk.WORD)
        self.privilege_text.pack(pady=5)

        # 綁定選擇事件
        self.privilege_combo.bind("<<ComboboxSelected>>", self.update_privilege_description)

        # 初始化顯示
        self.update_privilege_description()
        self.update_db_info()

        # 建立按鈕
        self.create_button = tk.Button(self.user_frame, text="建立使用者", command=self.create_user,
                                     bg="#4CAF50", fg="white", font=("Arial", 10, "bold"))
        self.create_button.pack(pady=20)

    def create_database_tab(self):
        # 資料庫列表框架
        list_frame = tk.Frame(self.db_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        tk.Label(list_frame, text="資料庫連線管理", font=("Arial", 12, "bold")).pack(pady=(0, 10))

        # 資料庫列表
        self.db_listbox = tk.Listbox(list_frame, height=8)
        self.db_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.db_listbox.bind("<<ListboxSelect>>", self.on_db_select)

        # 按鈕框架
        button_frame = tk.Frame(list_frame)
        button_frame.pack(fill=tk.X)

        tk.Button(button_frame, text="新增資料庫", command=self.add_database,
                 bg="#2196F3", fg="white").pack(side=tk.LEFT, padx=(0, 5))
        tk.Button(button_frame, text="編輯資料庫", command=self.edit_database,
                 bg="#FF9800", fg="white").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="刪除資料庫", command=self.delete_database,
                 bg="#F44336", fg="white").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="測試連線", command=self.test_connection,
                 bg="#4CAF50", fg="white").pack(side=tk.LEFT, padx=5)

        # 更新資料庫列表
        self.update_db_list()

    def update_privilege_description(self, event=None):
        """更新權限說明"""
        selected_level = self.privilege_var.get()
        if selected_level in PRIVILEGE_LEVELS:
            level_info = PRIVILEGE_LEVELS[selected_level]

            description = f"【{selected_level}】權限包含：\n\n"
            description += "角色 (Roles):\n"
            for role in level_info["roles"]:
                description += f"  • {role}\n"

            description += "\n系統權限 (Privileges):\n"
            for priv in level_info["privileges"]:
                description += f"  • {priv}\n"

            self.privilege_text.delete(1.0, tk.END)
            self.privilege_text.insert(1.0, description)

    def update_db_info(self, event=None):
        """更新資料庫連線資訊顯示"""
        selected_db = self.db_var.get()
        if selected_db in self.databases:
            db_info = self.databases[selected_db]
            info_text = f"連線至: {db_info['host']}:{db_info['port']}/{db_info['service_name']} (使用者: {db_info['sys_user']})"
            self.db_info_label.config(text=info_text)

    def update_db_list(self):
        """更新資料庫列表"""
        self.db_listbox.delete(0, tk.END)
        for db_name, db_info in self.databases.items():
            display_text = f"{db_name} - {db_info['host']}:{db_info['port']}/{db_info['service_name']}"
            self.db_listbox.insert(tk.END, display_text)

        # 更新下拉選單
        if hasattr(self, 'db_combo'):
            self.db_combo['values'] = list(self.databases.keys())

    def on_db_select(self, event=None):
        """資料庫列表選擇事件"""
        selection = self.db_listbox.curselection()
        if selection:
            index = selection[0]
            db_name = list(self.databases.keys())[index]
            # 可以在這裡顯示詳細資訊

    def add_database(self):
        """新增資料庫連線"""
        self.open_database_dialog()

    def edit_database(self):
        """編輯資料庫連線"""
        selection = self.db_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "請先選擇要編輯的資料庫")
            return

        index = selection[0]
        db_name = list(self.databases.keys())[index]
        self.open_database_dialog(db_name)

    def delete_database(self):
        """刪除資料庫連線"""
        selection = self.db_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "請先選擇要刪除的資料庫")
            return

        if len(self.databases) <= 1:
            messagebox.showwarning("警告", "至少需要保留一個資料庫連線")
            return

        index = selection[0]
        db_name = list(self.databases.keys())[index]

        result = messagebox.askyesno("確認刪除", f"確定要刪除資料庫連線 '{db_name}' 嗎？")
        if result:
            del self.databases[db_name]
            self.update_db_list()
            messagebox.showinfo("成功", f"已刪除資料庫連線 '{db_name}'")

    def test_connection(self):
        """測試資料庫連線"""
        selection = self.db_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "請先選擇要測試的資料庫")
            return

        index = selection[0]
        db_name = list(self.databases.keys())[index]
        db_info = self.databases[db_name]

        try:
            dsn = f"{db_info['host']}:{db_info['port']}/{db_info['service_name']}"

            if db_info['sys_user'].lower() == "sys":
                conn = cx_Oracle.connect(
                    user=db_info['sys_user'],
                    password=db_info['sys_password'],
                    dsn=dsn,
                    mode=cx_Oracle.SYSDBA
                )
            else:
                conn = cx_Oracle.connect(
                    user=db_info['sys_user'],
                    password=db_info['sys_password'],
                    dsn=dsn
                )

            conn.close()
            messagebox.showinfo("連線成功", f"成功連線到資料庫 '{db_name}'")

        except cx_Oracle.DatabaseError as e:
            error, = e.args
            messagebox.showerror("連線失敗", f"無法連線到資料庫 '{db_name}'\n錯誤：{error.message}")
        except Exception as e:
            messagebox.showerror("連線失敗", f"無法連線到資料庫 '{db_name}'\n錯誤：{str(e)}")

    def open_database_dialog(self, edit_db_name=None):
        """開啟資料庫設定對話框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("資料庫連線設定" if not edit_db_name else f"編輯資料庫: {edit_db_name}")
        dialog.geometry("400x350")
        dialog.transient(self.root)
        dialog.grab_set()

        # 如果是編輯模式，載入現有資料
        if edit_db_name:
            db_info = self.databases[edit_db_name]
        else:
            db_info = {"host": "", "port": "1521", "service_name": "", "sys_user": "system", "sys_password": ""}

        # 資料庫名稱
        tk.Label(dialog, text="資料庫名稱：").pack(pady=5)
        name_entry = tk.Entry(dialog, width=40)
        name_entry.pack(pady=5)
        if edit_db_name:
            name_entry.insert(0, edit_db_name)

        # 主機位址
        tk.Label(dialog, text="主機位址：").pack(pady=5)
        host_entry = tk.Entry(dialog, width=40)
        host_entry.pack(pady=5)
        host_entry.insert(0, db_info["host"])

        # 連接埠
        tk.Label(dialog, text="連接埠：").pack(pady=5)
        port_entry = tk.Entry(dialog, width=40)
        port_entry.pack(pady=5)
        port_entry.insert(0, db_info["port"])

        # 服務名稱
        tk.Label(dialog, text="服務名稱/SID：").pack(pady=5)
        service_entry = tk.Entry(dialog, width=40)
        service_entry.pack(pady=5)
        service_entry.insert(0, db_info["service_name"])

        # 系統使用者
        tk.Label(dialog, text="系統使用者：").pack(pady=5)
        user_entry = tk.Entry(dialog, width=40)
        user_entry.pack(pady=5)
        user_entry.insert(0, db_info["sys_user"])

        # 系統密碼
        tk.Label(dialog, text="系統密碼：").pack(pady=5)
        password_entry = tk.Entry(dialog, show="*", width=40)
        password_entry.pack(pady=5)
        password_entry.insert(0, db_info["sys_password"])

        # 按鈕框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=20)

        def save_database():
            name = name_entry.get().strip()
            host = host_entry.get().strip()
            port = port_entry.get().strip()
            service = service_entry.get().strip()
            user = user_entry.get().strip()
            password = password_entry.get().strip()

            if not all([name, host, port, service, user, password]):
                messagebox.showwarning("錯誤", "請填寫所有欄位")
                return

            # 檢查名稱是否重複（編輯模式除外）
            if not edit_db_name and name in self.databases:
                messagebox.showwarning("錯誤", "資料庫名稱已存在")
                return

            # 如果是編輯模式且名稱改變，刪除舊的
            if edit_db_name and edit_db_name != name:
                if name in self.databases:
                    messagebox.showwarning("錯誤", "資料庫名稱已存在")
                    return
                del self.databases[edit_db_name]

            # 儲存資料庫設定
            self.databases[name] = {
                "host": host,
                "port": port,
                "service_name": service,
                "sys_user": user,
                "sys_password": password
            }

            self.update_db_list()
            dialog.destroy()
            messagebox.showinfo("成功", f"資料庫 '{name}' 設定已儲存")

        tk.Button(button_frame, text="儲存", command=save_database,
                 bg="#4CAF50", fg="white").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="取消", command=dialog.destroy,
                 bg="#F44336", fg="white").pack(side=tk.LEFT, padx=5)

    def create_user(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        selected_level = self.privilege_var.get()
        selected_db = self.db_var.get()

        # 格式檢查
        if not username or not password:
            messagebox.showwarning("錯誤", "請輸入帳號與密碼")
            return
        if not is_valid_oracle_identifier(username):
            messagebox.showerror("錯誤", "帳號僅可用字母、數字、底線，且第一碼為字母，長度2~30")
            return
        if not is_valid_password(password):
            messagebox.showerror("錯誤", "密碼至少6碼，不能有空白或雙引號")
            return

        # 高權限確認
        if selected_level in ["系統管理員", "超級管理員"]:
            result = messagebox.askyesno(
                "高權限警告",
                f"您即將在資料庫【{selected_db}】建立【{selected_level}】等級的使用者，\n"
                f"此等級擁有極高的系統權限，可能影響整個資料庫安全。\n\n"
                f"確定要繼續嗎？"
            )
            if not result:
                return

        try:
            # 取得選擇的資料庫連線資訊
            if selected_db not in self.databases:
                messagebox.showerror("錯誤", "找不到選擇的資料庫連線設定")
                return

            db_info = self.databases[selected_db]
            dsn = f"{db_info['host']}:{db_info['port']}/{db_info['service_name']}"

            # 若帳號為 sys，使用 SYSDBA 模式
            if db_info['sys_user'].lower() == "sys":
                conn = cx_Oracle.connect(
                    user=db_info['sys_user'],
                    password=db_info['sys_password'],
                    dsn=dsn,
                    mode=cx_Oracle.SYSDBA
                )
            else:
                conn = cx_Oracle.connect(
                    user=db_info['sys_user'],
                    password=db_info['sys_password'],
                    dsn=dsn
                )

            cursor = conn.cursor()

            # 建立使用者（加雙引號處理）
            sql_create = f"""
                CREATE USER "{username}" IDENTIFIED BY "{password}"
                DEFAULT TABLESPACE USERS
                TEMPORARY TABLESPACE TEMP
                QUOTA UNLIMITED ON USERS
            """
            cursor.execute(sql_create)

            # 根據選擇的權限等級授予權限
            level_info = PRIVILEGE_LEVELS[selected_level]

            # 授予角色
            granted_roles = []
            failed_roles = []
            for role in level_info["roles"]:
                try:
                    cursor.execute(f'GRANT {role} TO "{username}"')
                    granted_roles.append(role)
                except cx_Oracle.DatabaseError as e:
                    failed_roles.append(f"{role}: {e}")

            # 授予系統權限
            granted_privileges = []
            failed_privileges = []
            for privilege in level_info["privileges"]:
                try:
                    cursor.execute(f'GRANT {privilege} TO "{username}"')
                    granted_privileges.append(privilege)
                except cx_Oracle.DatabaseError as e:
                    failed_privileges.append(f"{privilege}: {e}")

            # 解鎖帳號
            cursor.execute(f'ALTER USER "{username}" ACCOUNT UNLOCK')
            conn.commit()

            # 建立詳細的成功訊息
            success_msg = f"使用者 {username} 在資料庫【{selected_db}】建立成功！\n\n"
            success_msg += f"權限等級：{selected_level}\n"
            success_msg += f"成功授予 {len(granted_roles)} 個角色和 {len(granted_privileges)} 個系統權限\n"

            if failed_roles or failed_privileges:
                success_msg += f"\n⚠️ 部分權限授予失敗：\n"
                if failed_roles:
                    success_msg += f"失敗角色：{len(failed_roles)} 個\n"
                if failed_privileges:
                    success_msg += f"失敗權限：{len(failed_privileges)} 個\n"

            messagebox.showinfo("成功", success_msg)

            # 清空輸入欄位
            self.username_entry.delete(0, tk.END)
            self.password_entry.delete(0, tk.END)

        except cx_Oracle.DatabaseError as e:
            error, = e.args
            messagebox.showerror("資料庫錯誤", f"連線到資料庫【{selected_db}】時發生錯誤\n錯誤代碼：{error.code}\n{error.message}")
        except Exception as e:
            messagebox.showerror("系統錯誤", f"發生未預期的錯誤：{str(e)}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

if __name__ == "__main__":
    root = tk.Tk()
    app = DatabaseManagerGUI(root)
    root.mainloop()
