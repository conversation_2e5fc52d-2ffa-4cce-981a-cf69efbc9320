import cx_Oracle
import math

# 資料庫連線設定
dsn = cx_Oracle.makedsn("172.20.160.132", 1521, service_name="vmdb")
conn = cx_Oracle.connect(user="system", password="vmmanager", dsn=dsn)

# 查詢使用者建立的 TableSpaces，聯表篩選
query = """
SELECT 
    f.TABLESPACE_NAME, 
    f.FILE_NAME, 
    f.BYTES / 1024 / 1024 AS SIZE_MB, 
    f.AUTOEXTENSIBLE, 
    f.INCREMENT_BY * (SELECT VALUE FROM V$PARAMETER WHERE NAME = 'db_block_size') / 1024 / 1024 AS NEXT_MB
FROM DBA_DATA_FILES f
JOIN DBA_TABLESPACES t ON f.TABLESPACE_NAME = t.TABLESPACE_NAME
WHERE t.CONTENTS = 'PERMANENT' -- 僅選取 PERMANENT 表空間
  AND t.TABLESPACE_NAME NOT IN ('SYSTEM', 'SYSAUX', 'TEMP', 'UNDOTBS1', 'USERS') -- 排除系統表空間
"""
cursor = conn.cursor()
cursor.execute(query)

# 生成 CREATE TABLESPACE 語句
def mb_to_gb_rounded(size_mb):
    """將 MB 轉換為進位到最近整數的 GB"""
    return math.ceil(size_mb / 1024)

tablespace_sqls = []
for tablespace_name, file_name, size_mb, autoextensible, next_mb in cursor:
    # 計算 SIZE (進位到最近整數 GB)
    size_gb = mb_to_gb_rounded(size_mb)
    # 自動擴展設定
    autoextend_clause = f"autoextend on next {int(next_mb)}m maxsize unlimited" if autoextensible == "yes" else "autoextend off"
    # 建立語句
    sql = f"create tablespace {tablespace_name} datafile '{file_name}' size {size_gb}g {autoextend_clause};"
    tablespace_sqls.append(sql)

# 輸出生成的語句
for sql in tablespace_sqls:
    print(sql)

# 關閉連線
cursor.close()
conn.close()