@echo off
echo =====================================
echo STM32 Bootloader 模擬器啟動腳本
echo =====================================
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 錯誤: 未找到 Python
    echo 請安裝 Python 後再試
    pause
    exit /b 1
)

REM 檢查 pyserial 是否安裝
python -c "import serial" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安裝 pyserial...
    pip install pyserial
)

REM 運行模擬器
echo 正在啟動 STM32 模擬器...
python stm32_simulator_cli.py

pause