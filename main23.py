import cx_Oracle
from datetime import datetime, timedelta


class ExcelProcessor:
    def __init__(self, db_connection_str: str):
        self.conn = cx_Oracle.connect(db_connection_str)

    def _format_number(self, value: int) -> str:
        """格式化數字（千分位表示）"""
        formatted = f"{int(value):,}" if value is not None else "0"
        # 使用全形空格（\u3000）進行填充
        return "　" * (12 - len(formatted)) + formatted

    def check_invoices(self, start_date: str, end_date: str) -> str:
        cursor = self.conn.cursor()
        try:
            adjusted_end_date = self._adjust_end_date(end_date)

            sql = """
                SELECT 
                    NVL(SUM(CASE WHEN source = 'INVOICE' THEN cnt ELSE 0 END), 0) as invoice_count,
                    NVL(SUM(CASE WHEN source = 'INVOICE' THEN amt ELSE 0 END), 0) as invoice_amount,
                    NVL(SUM(CASE WHEN source = 'BC' THEN cnt ELSE 0 END), 0) as bc_count,
                    NVL(SUM(CASE WHEN source = 'BC' THEN amt ELSE 0 END), 0) as bc_amount
                FROM (
                    SELECT 
                        'INVOICE' as source,
                        COUNT(*) as cnt,
                        SUM(idamte) as amt
                    FROM inidwk
                    WHERE idindt BETWEEN :start_date AND :end_date
                        AND idstus = '1' 
                        AND idtype = '0'
                    UNION ALL
                    SELECT 
                        'BC' as source,
                        COUNT(*) as cnt,
                        SUM(bctotal) as amt
                    FROM inbc
                    WHERE bcindt BETWEEN :start_date AND :end_date
                        AND bcstus = '1'
                        AND bctype NOT IN ('5', '6')
                )
            """

            cursor.execute(sql, {
                'start_date': start_date,
                'end_date': adjusted_end_date
            })

            result = cursor.fetchone()

            if not result:
                return "```\n無資料\n```"

            # 解析結果
            invoice_count = int(result[0] or 0)
            invoice_amount = int(result[1] or 0)
            bc_count = int(result[2] or 0)
            bc_amount = int(result[3] or 0)
            count_diff = invoice_count - bc_count
            amount_diff = invoice_amount - bc_amount

            # 格式化輸出
            date_info = f"查詢區間: {start_date} ~ {adjusted_end_date} (原始結束日: {end_date})"

            # 使用全形空格對齊標題
            header = f"項目　　　　電子發票　　　營業人　　　　差異"

            # 分隔線
            separator = "─" * len(header)

            # 格式化數據行
            row1 = f"發票數量　　{self._format_number(invoice_count)}　　{self._format_number(bc_count)}　　{self._format_number(count_diff)}"
            row2 = f"發票金額　　{self._format_number(invoice_amount)}　　{self._format_number(bc_amount)}　　{self._format_number(amount_diff)}"

            # 組合最終報告
            report = [
                "```",
                date_info,
                header,
                separator,
                row1,
                row2,
                "```"
            ]

            return "\n".join(report)

        finally:
            cursor.close()

    def _adjust_end_date(self, end_date: str) -> str:
        """調整結束日期（減2天）"""
        year = int(end_date[:3]) + 1911
        month = int(end_date[3:5])
        day = int(end_date[5:])

        date = datetime(year, month, day) - timedelta(days=2)
        return f"{date.year - 1911:03d}{date.strftime('%m%d')}"
# Path: main23.py
processor = ExcelProcessor("system/manager@172.20.160.32:1521/hy")
print(processor.check_invoices("1140201", "1140207"))