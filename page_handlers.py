# page_handlers.py
"""頁面操作處理模組"""
from playwright.sync_api import Page
import time
from datetime import datetime
from typing import Tuple, Optional

from config import Config
from utils import format_filename


class LoginHandler:
    """登入頁面處理類"""

    def __init__(self, page: Page):
        self.page = page

    def fill_input(self, placeholder: str, value: str, timeout: int = 5000) -> bool:
        """填寫輸入欄位"""
        try:
            print(f"正在填寫 {placeholder}: {value}")
            selectors = [
                lambda: self.page.get_by_placeholder(placeholder),
                lambda: self.page.locator(f"input[placeholder='{placeholder}']"),
                lambda: self.page.get_by_role("textbox", name=placeholder),
                lambda: self.page.locator(f"//input[@placeholder='{placeholder}']")
            ]

            for selector in selectors:
                try:
                    input_element = selector()
                    if input_element.is_visible():
                        input_element.wait_for(state="visible", timeout=timeout)
                        input_element.click()
                        input_element.fill("")
                        time.sleep(0.5)
                        input_element.type(value, delay=100)
                        time.sleep(0.5)
                        return input_element.input_value() == value
                except Exception:
                    continue

            return False

        except Exception as e:
            print(f"填寫 {placeholder} 時發生錯誤: {str(e)}")
            return False

    def fill_captcha(self, captcha_text: str) -> bool:
        """填寫驗證碼"""
        try:
            input_selectors = [
                lambda: self.page.get_by_placeholder("圖形驗證碼"),
                lambda: self.page.locator("input[name='captcha']"),
                lambda: self.page.locator("input[name='captchaMTS']"),
                lambda: self.page.get_by_role("textbox", name="圖形驗證碼")
            ]

            for selector in input_selectors:
                try:
                    captcha_input = selector()
                    if captcha_input.is_visible():
                        captcha_input.click()
                        captcha_input.fill("")
                        time.sleep(0.5)
                        captcha_input.type(captcha_text, delay=100)
                        time.sleep(0.5)
                        return True
                except Exception:
                    continue

            return False

        except Exception as e:
            print(f"填寫驗證碼時發生錯誤: {str(e)}")
            return False

    def check_login_status(self) -> Tuple[bool, bool]:
        """
        檢查登入狀態
        Returns:
            Tuple[bool, bool]: (是否登入成功, 是否需要重試)
        """
        try:
            time.sleep(Config.PAGE_LOAD_WAIT)

            if "dashboard" in self.page.url:
                print("成功登入並重定向到儀表板")
                return True, False

            for message, (success, retry) in Config.ERROR_MESSAGES.items():
                try:
                    if self.page.get_by_text(message, exact=True).is_visible(timeout=1000):
                        print(f"發現錯誤訊息: {message}")
                        return success, retry
                except Exception:
                    continue

            try:
                login_button = self.page.get_by_role("button", name="登入")
                if login_button.is_visible(timeout=1000):
                    print("仍在登入頁面，可能需要重試")
                    return False, True
            except Exception:
                pass

            print("沒有發現錯誤訊息且不在登入頁面，判定為登入成功")
            return True, False

        except Exception as e:
            print(f"檢查登入狀態時發生錯誤: {str(e)}")
            return False, True

class ReportHandler:
    """報表頁面處理類"""
    def __init__(self, page: Page, browser_manager):
        self.page = page
        self.browser_manager = browser_manager

    def select_tax_id(self) -> bool:
        """處理統編選擇"""
        try:
            print("\n開始處理統編選擇...")
            select_tax_id_button = self.page.locator('button:has-text("選擇統編")')
            select_tax_id_button.wait_for(state="visible", timeout=5000)
            select_tax_id_button.click()
            time.sleep(3)

            dialog_visible = self.page.evaluate("""() => {
                const dialog = document.querySelector('.modal-dialog');
                return dialog && window.getComputedStyle(dialog).display !== 'none';
            }""")

            if not dialog_visible:
                print("對話框未正確顯示")
                return False

            print("等待統編清單載入...")
            time.sleep(3)

            self.page.evaluate("""() => {
                const checkboxes = document.querySelectorAll('.form-check-input');
                checkboxes.forEach(cb => {
                    if(cb.checked) {
                        cb.checked = false;
                        const event = new Event('change', { bubbles: true });
                        cb.dispatchEvent(event);
                    }
                });
            }""")
            time.sleep(1)

            checkboxes_selected = self.page.evaluate("""() => {
                const checkboxes = Array.from(document.querySelectorAll('.form-check-input'));
                let selectedCount = 0;

                for(let checkbox of checkboxes) {
                    if(checkbox.closest('tr') && !checkbox.checked) {
                        setTimeout(() => {
                            checkbox.checked = true;
                            const event = new Event('change', { bubbles: true });
                            checkbox.dispatchEvent(event);
                        }, selectedCount * 200);
                        selectedCount++;
                    }
                }
                return selectedCount;
            }""")

            print(f"已找到 {checkboxes_selected} 個統編選項")
            time.sleep(3)

            confirm_button = self.page.locator('button.btn.btn-primary:has-text("確認")')
            if confirm_button.is_visible():
                confirm_button.click()
                time.sleep(2)

                input_value = self.page.evaluate("""() => {
                    const input = document.querySelector('#input02');
                    return input ? input.value : '';
                }""")

                if input_value:
                    print(f"統編已選擇成功: {input_value}")
                    return True
                else:
                    print("統編選擇可能未生效")
                    return False
            else:
                print("找不到確認按鈕")
                return False

        except Exception as e:
            print(f"選擇統編時發生錯誤: {str(e)}")
            self.page.screenshot(path=f"tax_id_error_{time.strftime('%Y%m%d_%H%M%S')}.png")
            return False

    def set_date_range(self, start_date: str, end_date: str) -> bool:
        """設置日期範圍"""
        try:
            print(f"\n設置日期範圍: {start_date} ~ {end_date}")

            # 設置為依日期
            self.page.locator("#input01").select_option("D")
            time.sleep(1)

            # 點擊日期輸入框
            date_input = self.page.get_by_label("依日期")
            date_input.click()
            time.sleep(1)

            def parse_tw_date(date_str):
                year = int(date_str.split('年')[0])
                month = int(date_str.split('月')[0])
                day = int(date_str.split('月')[1].split('日')[0])
                return datetime(year, month, day)

            # 解析日期
            start = parse_tw_date(start_date)
            end = parse_tw_date(end_date)

            # 檢查並切換到正確的月份
            current_month_text = self.page.locator(".ant-picker-header-view").inner_text()
            current_month = datetime.strptime(current_month_text, "%Y年%m月")

            # 計算需要切換的月份數
            months_diff = (start.year - current_month.year) * 12 + (start.month - current_month.month)

            # 切換到正確的月份
            if months_diff < 0:
                for _ in range(abs(months_diff)):
                    self.page.get_by_role("button", name="上個月").click()
                    time.sleep(0.5)
            elif months_diff > 0:
                for _ in range(months_diff):
                    self.page.get_by_role("button", name="下個月").click()
                    time.sleep(0.5)

            time.sleep(1)  # 等待日曆更新

            # 使用更精確的定位方式選擇日期
            start_date_locator = f'[title="{start.year}年{start.month}月{start.day}日"]'
            end_date_locator = f'[title="{end.year}年{end.month}月{end.day}日"]'

            # 選擇開始日期
            self.page.locator(start_date_locator).click()
            time.sleep(0.5)

            # 選擇結束日期
            self.page.locator(end_date_locator).click()
            time.sleep(0.5)

            print("日期範圍設置完成")
            return True

        except Exception as e:
            print(f"設置日期範圍時發生錯誤: {str(e)}")
            current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
            try:
                self.page.screenshot(path=f"date_error_{current_time}.png")
            except Exception as screenshot_error:
                print(f"保存錯誤截圖時發生錯誤: {str(screenshot_error)}")
            return False

    def setup_report_options(self) -> bool:
        """設置報表選項"""
        try:
            print("\n設置報表選項...")

            # 設置銷項選擇
            self.page.evaluate("""() => {
                const salesRadio = document.querySelector('#queryInvType_2');
                if (salesRadio) {
                    salesRadio.click();
                    const event = new Event('change', { bubbles: true });
                    salesRadio.dispatchEvent(event);
                }
            }""")
            time.sleep(1)

            # 設置為依日期
            self.page.evaluate("""() => {
                const periodSelect = document.querySelector('#input01');
                if (periodSelect) {
                    periodSelect.value = 'D';
                    const event = new Event('change', { bubbles: true });
                    periodSelect.dispatchEvent(event);
                }
            }""")
            time.sleep(1)

            # 設置檔案類型為EXCEL
            self.page.evaluate("""() => {
                const excelRadio = document.querySelector('#fileType_EXCEL');
                if (excelRadio) {
                    excelRadio.checked = true;
                    const event = new Event('change', { bubbles: true });
                    excelRadio.dispatchEvent(event);
                }
            }""")
            time.sleep(1)

            return True

        except Exception as e:
            print(f"設置報表選項時發生錯誤: {str(e)}")
            return False

    def wait_and_download(self, start_date: str, end_date: str, max_retries: int = 3) -> bool:
        """等待報表生成並下載，支援重試機制"""
        for attempt in range(max_retries):
            try:
                # 設置當前日期範圍
                self.browser_manager.current_date_range = (start_date, end_date)
                print(f"\n🔹 等待報表生成（90秒）: {start_date} ~ {end_date}")
                time.sleep(90)  # 等待報表生成

                print("導航至下載頁面...")
                self.page.goto(Config.REPORT_URL)
                time.sleep(5)  # 等待頁面加載

                print("點擊查詢按鈕...")
                search_button = self.page.locator('button[title="查詢"]')
                if not search_button.is_visible():
                    print("找不到查詢按鈕")
                    continue

                search_button.click()
                time.sleep(5)  # 等待查詢結果

                # 等待表格載入
                try:
                    self.page.wait_for_selector("table", timeout=20000)
                    time.sleep(3)  # 等待表格內容完全載入
                except Exception as e:
                    print(f"等待表格超時：{str(e)}")
                    continue

                # 執行下載
                if self.download_report(start_date, end_date):
                    print("等待檔案完全下載（30秒）...")
                    time.sleep(30)  # 確保檔案完全下載
                    return True

                print(f"第 {attempt + 1} 次嘗試失敗")
                time.sleep(5)

            except Exception as e:
                print(f"❌ 第 {attempt + 1} 次等待並下載報表時發生錯誤: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                continue

        return False

    def trigger_download(self, start_date: str, end_date: str) -> bool:
        """觸發下載按鈕點擊"""
        try:
            def normalize_date(date_str):
                year = date_str.split('年')[0]
                month = date_str.split('年')[1].split('月')[0].zfill(2)
                day = date_str.split('月')[1].replace('日', '').strip().zfill(2)
                return f"{year}{month}{day}"

            target_start = normalize_date(start_date)
            target_end = normalize_date(end_date)

            download_success = self.page.evaluate("""
                ({ target_start, target_end }) => {
                    const rows = Array.from(document.querySelectorAll('tbody tr'));
                    for (const row of rows) {
                        // ... (保持原有的日期比對邏輯)

                        if (rowStartDate === target_start && 
                            rowEndDate === target_end && 
                            statusText === "處理完成") {
                            const downloadBtn = row.querySelector('button');
                            if (downloadBtn) {
                                downloadBtn.click();
                                return true;
                            }
                        }
                    }
                    return false;
                }
            """, {"target_start": target_start, "target_end": target_end})

            return download_success

        except Exception as e:
            print(f"觸發下載失敗: {str(e)}")
            return False

    def download_report(self, start_date: str, end_date: str) -> bool:
        """執行報表下載"""
        try:
            print(f"\n🔹 開始下載報表: {start_date} ~ {end_date}")

            def normalize_date(date_str):
                year = date_str.split('年')[0]
                month = date_str.split('年')[1].split('月')[0].zfill(2)
                day = date_str.split('月')[1].replace('日', '').strip().zfill(2)
                return f"{year}{month}{day}"

            target_start = normalize_date(start_date)
            target_end = normalize_date(end_date)

            print(f"處理後的目標日期: {target_start} ~ {target_end}")

            # 獲取所有符合條件的下載項目
            download_items = self.page.evaluate("""
                ({ target_start, target_end }) => {
                    const rows = Array.from(document.querySelectorAll('tbody tr'));
                    const results = [];
                    const now = new Date();

                    for (let i = 0; i < rows.length; i++) {
                        const row = rows[i];
                        const applyDateCell = row.querySelector('td:nth-child(1)');
                        const applyDateText = applyDateCell?.textContent?.trim() || '';

                        // 解析中文格式的日期時間 (例如：2025年2月7日 13:37:13)
                        const dateTimeMatch = applyDateText.match(/(\d{4})年(\d{1,2})月(\d{1,2})日\s+(\d{2}):(\d{2}):(\d{2})/);
                        if (!dateTimeMatch) continue;

                        const [_, year, month, day, hour, minute, second] = dateTimeMatch;
                        const applyDate = new Date(
                            parseInt(year),
                            parseInt(month) - 1,
                            parseInt(day),
                            parseInt(hour),
                            parseInt(minute),
                            parseInt(second)
                        );

                        // 計算時間差（分鐘）
                        const diffMinutes = (now - applyDate) / (1000 * 60);
                        console.log(`申請時間: ${applyDateText}, 時間差: ${diffMinutes.toFixed(2)} 分鐘`);

                        // 如果時間差超過10分鐘，跳過此項
                        if (diffMinutes > 10) {
                            console.log(`跳過超過10分鐘的項目: ${applyDateText}`);
                            continue;
                        }

                        const conditionCell = row.querySelector('td:nth-child(2)');
                        const statusCell = row.querySelector('td:nth-child(3)');
                        const conditionText = conditionCell?.textContent?.trim() || '';
                        const statusText = statusCell?.textContent?.trim() || '';

                        // 從查詢條件中提取統編
                        const taxIdMatch = conditionText.match(/營業人統編:(\d+)/);
                        if (!taxIdMatch) continue;

                        const taxId = taxIdMatch[1];

                        const datePattern = /發票日期:.+?(\d{4}年\d{1,2}月\d{1,2}日).+?(\d{4}年\d{1,2}月\d{1,2}日)/;
                        const dateMatch = conditionText.match(datePattern);

                        if (!dateMatch) continue;

                        const normalizeDate = (dateStr) => {
                            const [year, month, day] = dateStr.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/).slice(1);
                            return `${year}${month.padStart(2, '0')}${day.padStart(2, '0')}`;
                        };

                        const rowStartDate = normalizeDate(dateMatch[1]);
                        const rowEndDate = normalizeDate(dateMatch[2]);

                        if (rowStartDate === target_start && 
                            rowEndDate === target_end && 
                            statusText === "處理完成") {
                            results.push({
                                taxId,
                                rowIndex: i,
                                applyTime: applyDateText
                            });
                        }
                    }
                    return results;
                }
            """, {"target_start": target_start, "target_end": target_end})

            if not download_items:
                print(f"⚠️ 未找到符合條件的下載項目：{start_date} ~ {end_date}")
                return False

            print(f"找到 {len(download_items)} 個待下載項目")

            success_count = 0
            for idx, item in enumerate(download_items, 1):
                tax_id = item['taxId']
                row_index = item['rowIndex']
                apply_time = item['applyTime']
                print(f"\n處理第 {idx}/{len(download_items)} 個下載項目 (統編: {tax_id}, 申請時間: {apply_time})")

                # 在下載前設置當前統編
                self.browser_manager.set_current_tax_id(tax_id)

                # 設置下載事件監聽器
                with self.page.expect_download(timeout=60000) as download_info:
                    # 點擊下載按鈕
                    self.page.evaluate("""
                        (rowIndex) => {
                            const rows = document.querySelectorAll('tbody tr');
                            const row = rows[rowIndex];
                            const downloadBtn = row.querySelector('button');
                            if (downloadBtn) downloadBtn.click();
                        }
                    """, row_index)

                    try:
                        print("等待下載開始...")
                        download = download_info.value

                        # 使用包含統編的檔名保存
                        filename = format_filename(start_date, end_date, tax_id)
                        print(f"正在保存檔案: {filename}")
                        download.save_as(filename)
                        print(f"✅ 檔案已成功保存: {filename}")
                        success_count += 1

                    except Exception as e:
                        print(f"❌ 保存檔案時發生錯誤: {str(e)}")
                        continue

                # 等待一小段時間再處理下一個下載
                time.sleep(2)

            return success_count > 0

        except Exception as e:
            print(f"❌ 下載報表時發生錯誤: {str(e)}")
            return False