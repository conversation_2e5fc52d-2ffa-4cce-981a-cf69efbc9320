import asyncio
import json
import csv
from datetime import datetime
from playwright.async_api import async_playwright
import re
import time

class CarrefourScraper:
    def __init__(self):
        self.base_url = "https://online.carrefour.com.tw"
        self.target_url = "https://online.carrefour.com.tw/zh/%E5%93%81%E7%89%8C%E6%97%97%E8%89%A6%E9%A4%A8/%E9%BB%91%E6%9D%BE%E5%93%81%E7%89%8C%E9%A4%A8"
        self.products = []
        
    async def scrape_products(self, headless=True, max_pages=5):
        """爬取產品資訊"""
        async with async_playwright() as p:
            # 啟動瀏覽器
            browser = await p.chromium.launch(headless=headless)
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            page = await context.new_page()
            
            try:
                print(f"正在訪問: {self.target_url}")
                await page.goto(self.target_url, wait_until="networkidle")
                await page.wait_for_timeout(3000)  # 等待頁面完全載入
                
                # 處理可能的彈窗或 cookie 同意
                await self.handle_popups(page)
                
                page_count = 0
                while page_count < max_pages:
                    print(f"正在爬取第 {page_count + 1} 頁...")
                    
                    # 爬取當前頁面的產品
                    products_on_page = await self.extract_products_from_page(page)
                    self.products.extend(products_on_page)
                    
                    print(f"第 {page_count + 1} 頁找到 {len(products_on_page)} 個產品")
                    
                    # 嘗試點擊下一頁
                    if not await self.go_to_next_page(page):
                        print("沒有更多頁面或無法找到下一頁按鈕")
                        break
                        
                    page_count += 1
                    await page.wait_for_timeout(2000)  # 等待頁面載入
                
            except Exception as e:
                print(f"爬取過程中發生錯誤: {e}")
            finally:
                await browser.close()
                
        return self.products
    
    async def handle_popups(self, page):
        """處理彈窗和 cookie 同意"""
        try:
            # 等待並關閉可能的 cookie 同意彈窗
            cookie_selectors = [
                'button[data-testid="accept-all"]',
                'button:has-text("同意")',
                'button:has-text("接受")',
                'button:has-text("確定")',
                '.cookie-accept',
                '#cookie-accept'
            ]
            
            for selector in cookie_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=2000)
                    await page.click(selector)
                    print("已處理 cookie 同意彈窗")
                    break
                except:
                    continue
                    
            # 關閉其他可能的彈窗
            close_selectors = [
                'button:has-text("關閉")',
                'button:has-text("×")',
                '.close-button',
                '.modal-close'
            ]
            
            for selector in close_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=1000)
                    await page.click(selector)
                    break
                except:
                    continue
                    
        except Exception as e:
            print(f"處理彈窗時發生錯誤: {e}")
    
    async def extract_products_from_page(self, page):
        """從當前頁面提取產品資訊"""
        products = []
        
        try:
            # 等待產品容器載入
            await page.wait_for_selector('[data-testid="product-card"], .product-item, .product-card', timeout=10000)
            
            # 多種可能的產品選擇器
            product_selectors = [
                '[data-testid="product-card"]',
                '.product-item',
                '.product-card',
                '[class*="product"]',
                '[data-qa="product-tile"]'
            ]
            
            product_elements = None
            for selector in product_selectors:
                try:
                    product_elements = await page.query_selector_all(selector)
                    if product_elements:
                        print(f"使用選擇器 '{selector}' 找到 {len(product_elements)} 個產品")
                        break
                except:
                    continue
            
            if not product_elements:
                print("未找到產品元素，嘗試通用選擇器...")
                # 使用更通用的方法
                product_elements = await page.query_selector_all('div:has(img):has-text("NT$"), div:has(img):has-text("$")')
            
            for element in product_elements:
                try:
                    product_data = await self.extract_product_data(element, page)
                    if product_data and product_data.get('name') and product_data.get('price'):
                        products.append(product_data)
                except Exception as e:
                    print(f"提取單個產品資料時發生錯誤: {e}")
                    continue
                    
        except Exception as e:
            print(f"提取產品資訊時發生錯誤: {e}")
            
        return products
    
    async def extract_product_data(self, element, page):
        """提取單個產品的詳細資訊"""
        try:
            # 產品名稱
            name_selectors = [
                '[data-testid="product-name"]',
                '.product-name',
                '.product-title',
                'h3', 'h4', 'h5',
                '[class*="name"]',
                '[class*="title"]'
            ]
            
            name = await self.get_text_by_selectors(element, name_selectors)
            
            # 產品價格
            price_selectors = [
                '[data-testid="product-price"]',
                '.product-price',
                '.price',
                '[class*="price"]',
                'span:has-text("NT$")',
                'span:has-text("$")'
            ]
            
            price_text = await self.get_text_by_selectors(element, price_selectors)
            price = self.parse_price(price_text)
            
            # 產品圖片
            img_element = await element.query_selector('img')
            image_url = ""
            if img_element:
                image_url = await img_element.get_attribute('src') or await img_element.get_attribute('data-src')
                if image_url and not image_url.startswith('http'):
                    image_url = self.base_url + image_url
            
            # 產品連結
            link_element = await element.query_selector('a')
            product_url = ""
            if link_element:
                href = await link_element.get_attribute('href')
                if href:
                    product_url = href if href.startswith('http') else self.base_url + href
            
            # 其他可能的資訊
            description = await self.get_text_by_selectors(element, ['.product-description', '.description', '[class*="desc"]'])
            brand = await self.get_text_by_selectors(element, ['.brand', '[class*="brand"]'])
            
            return {
                'name': name.strip() if name else '',
                'price': price,
                'price_text': price_text.strip() if price_text else '',
                'image_url': image_url,
                'product_url': product_url,
                'description': description.strip() if description else '',
                'brand': brand.strip() if brand else '',
                'scraped_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"提取產品資料時發生錯誤: {e}")
            return None
    
    async def get_text_by_selectors(self, element, selectors):
        """使用多個選擇器嘗試獲取文字"""
        for selector in selectors:
            try:
                target_element = await element.query_selector(selector)
                if target_element:
                    text = await target_element.inner_text()
                    if text and text.strip():
                        return text.strip()
            except:
                continue
        return ""
    
    def parse_price(self, price_text):
        """解析價格文字，提取數字"""
        if not price_text:
            return 0
        
        # 移除貨幣符號和其他非數字字符，保留小數點
        price_numbers = re.findall(r'[\d,]+\.?\d*', price_text.replace(',', ''))
        
        if price_numbers:
            try:
                return float(price_numbers[0])
            except ValueError:
                return 0
        return 0
    
    async def go_to_next_page(self, page):
        """嘗試前往下一頁"""
        next_selectors = [
            'button:has-text("下一頁")',
            'a:has-text("下一頁")',
            'button[aria-label*="next"]',
            'a[aria-label*="next"]',
            '.pagination-next',
            '[data-testid="next-page"]',
            'button:has-text(">")',
            'a:has-text(">")'
        ]
        
        for selector in next_selectors:
            try:
                next_button = await page.query_selector(selector)
                if next_button:
                    # 檢查按鈕是否可點擊
                    is_disabled = await next_button.get_attribute('disabled')
                    if not is_disabled:
                        await next_button.click()
                        await page.wait_for_timeout(2000)
                        return True
            except Exception as e:
                print(f"嘗試點擊下一頁時發生錯誤: {e}")
                continue
        
        return False
    
    def save_to_csv(self, filename=None):
        """將產品資料儲存為 CSV 檔案"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"carrefour_products_{timestamp}.csv"
        
        if not self.products:
            print("沒有產品資料可儲存")
            return
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['name', 'price', 'price_text', 'image_url', 'product_url', 'description', 'brand', 'scraped_at']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for product in self.products:
                writer.writerow(product)
        
        print(f"已儲存 {len(self.products)} 個產品到 {filename}")
    
    def save_to_json(self, filename=None):
        """將產品資料儲存為 JSON 檔案"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"carrefour_products_{timestamp}.json"
        
        if not self.products:
            print("沒有產品資料可儲存")
            return
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.products, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"已儲存 {len(self.products)} 個產品到 {filename}")
    
    def print_summary(self):
        """列印爬取結果摘要"""
        if not self.products:
            print("沒有找到任何產品")
            return
        
        print(f"\n=== 爬取結果摘要 ===")
        print(f"總共找到 {len(self.products)} 個產品")
        
        if self.products:
            prices = [p['price'] for p in self.products if p['price'] > 0]
            if prices:
                print(f"價格範圍: NT$ {min(prices):.0f} - NT$ {max(prices):.0f}")
                print(f"平均價格: NT$ {sum(prices)/len(prices):.0f}")
        
        print("\n前 5 個產品:")
        for i, product in enumerate(self.products[:5], 1):
            print(f"{i}. {product['name']} - NT$ {product['price']:.0f}")

async def main():
    """主函數"""
    scraper = CarrefourScraper()
    
    print("開始爬取家樂福黑松品牌館...")
    print("這可能需要幾分鐘時間，請耐心等待...")
    
    # 開始爬取 (headless=False 可以看到瀏覽器操作過程)
    products = await scraper.scrape_products(headless=False, max_pages=2)
    
    # 顯示結果摘要
    scraper.print_summary()
    
    # 儲存結果
    if products:
        scraper.save_to_csv()
        scraper.save_to_json()
    
    print("\n爬取完成！")

if __name__ == "__main__":
    asyncio.run(main())
