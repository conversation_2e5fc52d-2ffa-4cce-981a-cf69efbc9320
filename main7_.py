import win32com.client as win32
import re

def is_black_or_automatic(font):
    """
    檢查字體顏色是否為黑色或自動（通常表示為黑色）。
    """
    return font.Color == win32.constants.wdColorAutomatic or font.Color == 0 or font.Color == -16777216

def get_cell_text_and_color(table, row, col):
    """
    嘗試從指定的行和列獲取單元格文本，並檢查字體顏色是否為黑色。
    如果單元格合併並且無法訪問，將捕獲異常並返回None及顏色檢查結果。
    """
    try:
        cell = table.Cell(Row=row, Column=col)
        text = cell.Range.Text.strip(chr(7)).strip()  # 清除單元格結束字符
        is_black = is_black_or_automatic(cell.Range.Font)
        return text, is_black
    except Exception:
        return None, False

def extract_data_from_table(table):
    """
    從給定的表格中提取所有行的產品代碼和價格，僅考慮黑色字體的價格。
    """
    data = []
    previous_product_code = None
    previous_price = None

    for row_index in range(1, table.Rows.Count + 1):
        product_code = get_cell_text_and_color(table, row_index, 1)[0] or previous_product_code
        cell_text, is_black = get_cell_text_and_color(table, row_index, 3)

        price = None
        if cell_text:
            price_match = re.search(r'(\d+)\s*元/箱', cell_text)
            if price_match:
                price = price_match.group(0)

        price = price or previous_price

        if product_code and price:
            data.append([product_code, price])
            previous_product_code, previous_price = product_code, price

    return data

def extract_table_data(doc):
    """
    處理文檔中的每個表格，從中提取產品代碼和價格。
    """
    data = []
    for table in doc.Tables:
        data.extend(extract_data_from_table(table))
    return data

def main():
    word = win32.gencache.EnsureDispatch('Word.Application')
    word.Visible = False

    try:
        doc_path = r'C:\Users\<USER>\Downloads\113年大潤發4072+4081中元檔.docx'
        doc = word.Documents.Open(doc_path)

        table_data = extract_table_data(doc)
        for entry in table_data:
            print('貨號:', entry[0], '| 價格:', entry[1])

        doc.Close(False)
    finally:
        word.Quit()

if __name__ == "__main__":
    main()
