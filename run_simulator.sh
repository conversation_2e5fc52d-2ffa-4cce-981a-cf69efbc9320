#!/bin/bash
# STM32 模擬器運行腳本

echo "====================================="
echo "STM32 Bootloader 模擬器啟動腳本"
echo "====================================="
echo ""

# 檢查 Python 是否安裝
if ! command -v python3 &> /dev/null; then
    echo "錯誤: 未找到 Python3"
    echo "請安裝 Python3 後再試"
    exit 1
fi

# 檢查 pyserial 是否安裝
python3 -c "import serial" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安裝 pyserial..."
    pip3 install pyserial || pip install pyserial
fi

# 運行模擬器
echo "正在啟動 STM32 模擬器..."
python3 stm32_simulator_cli.py