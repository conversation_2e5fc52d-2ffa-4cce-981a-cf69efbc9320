import ldap3
import sys

def test_simple_ldap_auth():
    """測試簡單 LDAP 認證（不需要服務帳號）"""
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389, get_info=ldap3.ALL)
    
    print("=== 簡單 LDAP 認證測試 ===")
    print("這種方式不需要服務帳號，直接用使用者帳號認證")
    
    username = input("輸入測試帳號 (或按 Enter 使用 16613): ").strip() or "16613"
    password = input("輸入密碼: ").strip()
    
    if not password:
        print("需要密碼才能測試")
        return
    
    # 測試不同的使用者 DN 格式
    user_dn_formats = [
        f"{username}@heysong.com.tw",  # UPN 格式
        f"HEYSONG\\{username}",        # NetBIOS 格式
        f"CN={username},OU=資訊人員,OU=台北總公司,OU=使用者帳號,DC=heysong,DC=com,DC=tw",  # 完整 DN
    ]
    
    for user_dn in user_dn_formats:
        print(f"\n測試格式: {user_dn}")
        try:
            conn = ldap3.Connection(server, user=user_dn, password=password)
            if conn.bind():
                print("✓ 認證成功！")
                
                # 嘗試讀取自己的資訊
                try:
                    # 搜尋自己的資訊
                    search_base = "DC=heysong,DC=com,DC=tw"
                    search_filter = f"(sAMAccountName={username})"
                    
                    result = conn.search(search_base, search_filter, ldap3.SUBTREE, 
                                       attributes=['distinguishedName', 'sAMAccountName', 
                                                 'userPrincipalName', 'mail', 'givenName', 'sn'])
                    
                    if result and conn.entries:
                        print("✓ 可以讀取使用者資訊:")
                        entry = conn.entries[0]
                        print(f"  DN: {entry.distinguishedName}")
                        print(f"  帳號: {entry.sAMAccountName}")
                        print(f"  Email: {entry.userPrincipalName}")
                        print(f"  名字: {getattr(entry, 'givenName', '未設定')}")
                        print(f"  姓氏: {getattr(entry, 'sn', '未設定')}")
                    else:
                        print("✗ 無法讀取使用者資訊")
                        
                except Exception as e:
                    print(f"✗ 讀取使用者資訊時發生錯誤: {e}")
                
                conn.unbind()
                
                # 如果這個格式成功，就是可用的格式
                print(f"\n🎉 建議的 Gitea 設定:")
                print(f"認證類型: LDAP (simple auth)")
                print(f"使用者 DN 模板: {user_dn}")
                return user_dn
                
            else:
                print("✗ 認證失敗")
                
        except Exception as e:
            print(f"✗ 連接錯誤: {e}")
    
    print("\n❌ 所有格式都無法認證")
    return None

def generate_simple_ldap_config(user_dn_template):
    """產生簡單 LDAP 設定指南"""
    print("\n" + "="*60)
    print("GITEA 簡單 LDAP 設定指南")
    print("="*60)
    
    print("\n1. 進入 Gitea 管理介面")
    print("   - 網站管理 → 認證來源")
    
    print("\n2. 修改現有認證來源或新增")
    print("   - 認證類型: LDAP (simple auth)")
    
    print("\n3. 基本設定")
    print("   認證名稱: 黑松 LDAP Simple")
    print("   主機: HSWDC00.heysong.com.tw")
    print("   連接埠: 389")
    print("   安全協定: 未加密")
    
    print("\n4. 使用者設定")
    if user_dn_template:
        if "@" in user_dn_template:
            print(f"   使用者 DN: %<EMAIL>")
        elif "\\" in user_dn_template:
            print(f"   使用者 DN: HEYSONG\\%s")
        else:
            print(f"   使用者 DN: {user_dn_template.replace(username, '%s')}")
    
    print("\n5. 優缺點")
    print("   ✅ 優點:")
    print("     - 不需要服務帳號")
    print("     - 設定簡單")
    print("     - 立即可用")
    
    print("   ❌ 缺點:")
    print("     - 無法預先搜尋使用者")
    print("     - 無法同步使用者屬性")
    print("     - 功能較少")

if __name__ == "__main__":
    user_dn = test_simple_ldap_auth()
    if user_dn:
        generate_simple_ldap_config(user_dn)
    else:
        print("\n建議聯繫 IT 申請服務帳號，使用 LDAP (via BindDN) 方式")
