# 🔧 Gitea LDAP 最終修正指南

## 🔍 問題確認

**Python 測試結果：** ✅ 完全正常
- LDAP 連接成功
- 認證格式正確 (`<EMAIL>`)
- 使用者篩選器搜尋成功
- 所有認證格式都能成功

**Gitea 登入結果：** ❌ 失敗
- 顯示「帳號或密碼不正確」
- 只有一個認證來源 (ID: 2)

## 🎯 **根本原因**

在 LDAP (simple auth) 模式下，使用者篩選器可能會干擾認證流程。

## 🛠️ **修正步驟**

### 步驟 1：清空使用者篩選器
1. 登入 Gitea 管理介面
   - http://*************:3100
   - admin2 / @Heysong20651901

2. 進入認證來源設定
   - 網站管理 → 認證來源 → 黑松 LDAP Simple

3. **重要修正**：
   - 找到「使用者篩選器」欄位
   - **完全清空該欄位**
   - 點擊「更新認證來源」

### 步驟 2：確認其他設定
確保以下設定正確：
```
認證類型: LDAP (simple auth)
用戶 DN: %<EMAIL>
使用者篩選器: (留空) ← 關鍵！
電子郵件屬性: userPrincipalName
該認證來源已啟用: ✓
```

### 步驟 3：測試登入
- 帳號：16613
- 密碼：Tt9566659

## 🔄 **備用方案**

如果清空使用者篩選器後還是無法登入：

### 方案 A：改用 LDAP (via BindDN)
1. 申請服務帳號（聯繫 IT）
2. 改為 LDAP (via BindDN) 模式
3. 填入服務帳號資訊

### 方案 B：檢查 Gitea 日誌
1. 查看 Gitea 日誌檔案
2. 尋找 LDAP 認證相關錯誤訊息
3. 根據錯誤訊息調整設定

## 📊 **預期結果**

修正後應該能夠：
- ✅ 使用公司帳號 (16613) 登入
- ✅ 自動建立 Gitea 使用者帳號
- ✅ 同步基本使用者資訊

## 🎯 **成功指標**

1. 登入成功，不再顯示錯誤訊息
2. 自動建立使用者帳號
3. 顯示正確的使用者名稱和電子郵件

## 📞 **如果還是無法解決**

建議：
1. 檢查 Gitea 版本是否支援 simple auth
2. 考慮升級 Gitea 版本
3. 改用更穩定的 LDAP (via BindDN) 方式

---

**下一步：立即執行步驟 1，清空使用者篩選器！**
