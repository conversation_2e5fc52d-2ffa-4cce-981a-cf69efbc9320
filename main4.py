import random
import datetime

def calculate_checksum(data):
    # 從 S2(Sent Length) 到 S11(Index)
    checksum_data = data[1:]
    # 計算 ASCII 碼值的總和
    checksum = sum(ord(c) for c in checksum_data)
    # 轉換為 16 進制字符串
    hex_checksum = format(checksum, '04X')
    # 轉換為 10 進制
    decimal_checksum = int(hex_checksum, 16)
    return f"{decimal_checksum:04d}"

def generate_client_string():
    # 生成各個字段
    sent_head = "V"
    sent_length = "63"  # 假設長度固定為 63
    vmc_no = f"{random.randint(0, 999999):06d}"
    time = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    channel_no = f"{random.randint(1, 999):03d}"
    coin_all = f"{random.randint(0, 999):03d}"
    coin_chg = f"{random.randint(0, 999):03d}"
    barcode = ''.join([str(random.randint(0, 9)) for _ in range(16)])
    trade_type = str(random.randint(0, 9))
    nc = f"{random.randint(0, 99999):05d}"
    index = f"{random.randint(0, 9999):04d}"
    sent_eot = "E"

    # 組合字串（不包括 checksum）
    data_without_checksum = (sent_head + sent_length + vmc_no + time + channel_no +
                             coin_all + coin_chg + barcode + trade_type + nc + index)

    # 計算 checksum
    checksum = calculate_checksum(data_without_checksum)

    # 組合完整的字串
    return data_without_checksum + checksum + sent_eot

def validate_checksum(data, expected_checksum):
    calculated_checksum = calculate_checksum(data[:58])
    print(f"Calculated Checksum: {calculated_checksum}")
    print(f"Expected Checksum: {expected_checksum}")
    return calculated_checksum == expected_checksum

def parse_data(data):
    parsed_data = {}
    parsed_data['Sent Head'] = data[0:1]
    parsed_data['Sent Length'] = data[1:3]
    parsed_data['VMC NO'] = data[3:9]
    parsed_data['Time'] = data[9:23]
    parsed_data['Channel NO'] = data[23:26]
    parsed_data['CoinALL'] = data[26:29]
    parsed_data['CoinCHG'] = data[29:32]
    parsed_data['Barcode'] = data[32:48]
    parsed_data['Trade_Type'] = data[48:49]
    parsed_data['NC'] = data[49:54]
    parsed_data['Index'] = data[54:58]
    parsed_data['Chksum'] = data[58:62]
    parsed_data['Sent EOT'] = data[62:63]
    return parsed_data

# 生成測試用的 client 字串
for i in range(5):  # 生成 5 個測試字串
    test_data = generate_client_string()
    print(f"\nTest Data {i+1}: {test_data}")

    # 驗證 Chksum
    expected_checksum = test_data[58:62]
    if validate_checksum(test_data, expected_checksum):
        print("Checksum 驗證通過")
        # 解析數據
        parsed_data = parse_data(test_data)
        print("Parsed Data:", parsed_data)
    else:
        print("Checksum 驗證失敗")

    # 額外的檢查
    print(f"Calculated checksum in hex: 0x{int(calculate_checksum(test_data[:58])):04X}")
    print(f"Expected checksum: {expected_checksum}")