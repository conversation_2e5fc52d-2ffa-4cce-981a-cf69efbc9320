from playwright.sync_api import Playwright, sync_playwright, expect, TimeoutError
import time
import cv2
import numpy as np
import ddddocr
import sys

# 定義密碼清單
PASSWORDS = [
    "Aa12345678",  # 當前密碼
    "Aa123456789",  # 備用密碼1
    # 可以繼續添加更多備用密碼
]


def recognize_captcha(image_path):
    # 使用 ddddocr 進行驗證碼識別
    ocr = ddddocr.DdddOcr(show_ad=False)

    # 讀取圖片
    with open(image_path, 'rb') as f:
        image_bytes = f.read()

    # 識別驗證碼
    result = ocr.classification(image_bytes)
    print(f"識別出的驗證碼: {result}")
    return result


def fill_input(page, placeholder: str, value: str, timeout: int = 5000) -> bool:
    try:
        print(f"正在填寫 {placeholder}: {value}")
        # 嘗試多種定位方式
        input_element = None
        selectors = [
            lambda: page.get_by_placeholder(placeholder),
            lambda: page.locator(f"input[placeholder='{placeholder}']"),
            lambda: page.get_by_role("textbox", name=placeholder),
            lambda: page.locator(f"//input[@placeholder='{placeholder}']")
        ]

        for selector in selectors:
            try:
                input_element = selector()
                if input_element.is_visible():
                    break
            except Exception:
                continue

        if not input_element:
            print(f"無法找到輸入欄位：{placeholder}")
            return False

        # 等待元素可見
        input_element.wait_for(state="visible", timeout=timeout)

        # 點擊並填入值
        input_element.click()
        input_element.fill("")  # 清空現有內容
        time.sleep(0.5)
        input_element.type(value, delay=100)
        time.sleep(0.5)

        # 檢查輸入值
        actual_value = input_element.input_value()
        print(f"{placeholder} 輸入值檢查 - 預期: {value}, 實際: {actual_value}")

        return actual_value == value
    except Exception as e:
        print(f"填寫 {placeholder} 時發生錯誤: {str(e)}")
        return False


def fill_captcha(page, captcha_text: str) -> bool:
    try:
        # 嘗試多種方式定位驗證碼輸入框
        input_selectors = [
            lambda: page.get_by_placeholder("圖形驗證碼"),
            lambda: page.locator("input[name='captcha']"),
            lambda: page.locator("input[name='captchaMTS']"),
            lambda: page.get_by_role("textbox", name="圖形驗證碼")
        ]

        for selector in input_selectors:
            try:
                captcha_input = selector()
                if captcha_input.is_visible():
                    captcha_input.click()
                    captcha_input.fill("")
                    time.sleep(0.5)
                    captcha_input.type(captcha_text, delay=100)
                    time.sleep(0.5)

                    actual_value = captcha_input.input_value()
                    print(f"驗證碼輸入值檢查 - 預期: {captcha_text}, 實際: {actual_value}")
                    return True
            except Exception as e:
                print(f"嘗試填入驗證碼失敗: {str(e)}")
                continue

        return False
    except Exception as e:
        print(f"填寫驗證碼時發生錯誤: {str(e)}")
        return False


def check_login_status(page) -> tuple[bool, bool]:
    """
    檢查登入狀態
    返回: (是否登入成功, 是否需要重試)
    """
    try:
        time.sleep(3)

        # 檢查是否成功登入到儀表板
        if "dashboard" in page.url:
            print("成功登入並重定向到儀表板")
            return True, False

        # 檢查常見錯誤訊息
        error_messages = {
            "登入失敗超過次數上限": (False, False),
            "帳號已鎖定": (False, False),
            "系統暫時無法服務": (False, True),
            "驗證碼錯誤": (False, True),
            "密碼錯誤": (False, False),
            "帳號密碼錯誤": (False, False)
        }

        for message, (success, retry) in error_messages.items():
            try:
                if page.get_by_text(message, exact=True).is_visible(timeout=1000):
                    print(f"發現錯誤訊息: {message}")
                    return success, retry
            except Exception:
                continue

        # 檢查是否還在登入頁面
        try:
            login_button = page.get_by_role("button", name="登入")
            if login_button.is_visible(timeout=1000):
                print("仍在登入頁面，可能需要重試")
                return False, True
        except Exception:
            pass

        # 如果沒有明確的錯誤訊息且不在登入頁面，可能已經成功登入
        print("沒有發現錯誤訊息且不在登入頁面，判定為登入成功")
        return True, False

    except Exception as e:
        print(f"檢查登入狀態時發生錯誤: {str(e)}")
        return False, True


def try_login(page, password: str, max_attempts: int = 2) -> bool:
    """
    嘗試使用指定密碼登入
    """
    print(f"\n嘗試使用密碼登入...")

    for attempt in range(max_attempts):
        try:
            if attempt > 0:
                print(f"登入重試 {attempt + 1}/{max_attempts}")
                # 重新載入頁面
                page.reload()
                time.sleep(2)

            # 填寫登入表單
            if not all([
                fill_input(page, "統一編號", "80706866"),
                fill_input(page, "帳號", "A959A728C41"),
                fill_input(page, "密碼", password)
            ]):
                print("填寫表單失敗")
                continue

            # 處理驗證碼
            captcha_img = page.get_by_role("img", name="圖形驗證碼", exact=True)
            captcha_img.wait_for()
            captcha_img.screenshot(path="captcha.png")

            captcha_text = recognize_captcha("captcha.png")
            if not fill_captcha(page, captcha_text):
                print("填寫驗證碼失敗")
                continue

            # 點擊登入按鈕
            submit_button = page.get_by_role("button", name="登入")
            submit_button.click()
            print("已點擊登入按鈕")

            # 檢查登入結果
            login_success, should_retry = check_login_status(page)
            if login_success:
                print("登入成功！")
                return True
            if not should_retry:
                print(f"登入失敗且無需重試")
                return False

        except Exception as e:
            print(f"登入嘗試失敗: {str(e)}")
            if attempt < max_attempts - 1:
                continue

    print(f"已達最大嘗試次數 {max_attempts}")
    return False


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    login_success = False

    try:
        # 設置較長的超時時間
        page.set_default_timeout(30000)

        # 訪問網站
        print("正在訪問網站...")
        page.goto("https://www.einvoice.nat.gov.tw/")
        time.sleep(2)

        # 點擊登入按鈕
        print("點擊登入按鈕...")
        page.get_by_role("link", name="登入").click()
        time.sleep(2)

        # 點擊營業人登入選項
        print("點擊營業人登入選項...")
        page.get_by_role("link", name="營業人/ 扣繳單位").click()
        time.sleep(3)

        # 嘗試每個密碼
        for i, password in enumerate(PASSWORDS, 1):
            print(f"\n嘗試第 {i}/{len(PASSWORDS)} 個密碼")
            if try_login(page, password):
                login_success = True
                print("登入成功完成！")
                break
            print(f"當前密碼登入失敗")

        if not login_success:
            print("所有密碼都嘗試失敗")

    except Exception as e:
        print(f"發生錯誤：{str(e)}")
        # 儲存錯誤截圖
        page.screenshot(path=f"error_{time.strftime('%Y%m%d_%H%M%S')}.png")

    finally:
        if not login_success:
            time.sleep(3)
            context.close()
            browser.close()
            sys.exit(1)


if __name__ == "__main__":
    with sync_playwright() as playwright:
        run(playwright)