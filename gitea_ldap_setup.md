# Gitea LDAP 簡單設定指南

## 目標
使用公司網域帳號密碼直接登入 Gitea，無需申請額外的服務帳號。

## 前置準備

### 測試連接
使用 `python test_simple_ldap.py` 測試 LDAP 連接和認證。

## Gitea 設定步驟

### 步驟 1: 進入管理介面
1. 以管理員身份登入 Gitea (http://172.20.160.11:3100)
2. 帳號：admin2，密碼：@Heysong20651901
3. 點擊右上角頭像 → `網站管理`
4. 在左側選單選擇 `認證來源`

### 步驟 2: 修改現有認證來源
1. 點擊現有的 `黑松 Active Directory` 認證來源
2. 或者新增認證來源，選擇認證類型: `LDAP (simple auth)`

### 步驟 3: 基本設定
```
認證名稱: 黑松 LDAP Simple
認證類型: LDAP (simple auth)
主機: HSWDC00.heysong.com.tw
連接埠: 389
安全協定: 未加密
```

### 步驟 4: 使用者設定（重要）
```
使用者 DN: %<EMAIL>
```
**注意：** 這是關鍵設定，%s 會被使用者輸入的帳號替換

### 步驟 5: 屬性設定（可選）
```
使用者名稱屬性: sAMAccountName
名字屬性: givenName
姓氏屬性: sn
電子郵件屬性: userPrincipalName
```

### 步驟 6: 啟用設定
```
☑ 該認證來源已啟用
```

### 步驟 7: 儲存設定
點擊 `更新認證來源` 完成設定

## 使用說明

### 使用者登入方式
使用者可以直接使用公司網域帳號登入 Gitea：
- **使用者名稱**: 公司帳號 (例如: 16613)
- **密碼**: 公司 AD 密碼

### 首次登入
1. 使用者首次透過 LDAP 登入時，Gitea 會自動建立帳號
2. 帳號名稱會自動設定
3. 電子郵件地址會自動設定

### 登入範例
- 帳號：16613
- 密碼：您的公司密碼

## 故障排除

### 常見問題
1. **連接失敗**: 檢查主機名稱和連接埠
2. **認證失敗**: 檢查使用者 DN 格式是否正確
3. **登入失敗**: 確認使用公司帳號和密碼

### 測試工具
使用 `python test_simple_ldap.py` 進行連接和認證測試。

### 如果還是無法登入
1. 檢查使用者 DN 設定是否為：`%<EMAIL>`
2. 確認認證類型是：`LDAP (simple auth)`
3. 確認認證來源已啟用

## 優點與限制

### ✅ 優點
- **無需服務帳號** - 立即可用
- **設定簡單** - 只需要基本資訊
- **直接認證** - 使用公司帳密即可

### ❌ 限制
- 無法預先搜尋使用者資訊
- 無法自動同步使用者屬性
- 功能相對簡單

## 安全建議

1. **網路安全**
   - 考慮使用 LDAPS (636 port) 加密連接
   - 限制網路存取範圍

2. **監控**
   - 定期檢查認證日誌
   - 監控異常登入行為
