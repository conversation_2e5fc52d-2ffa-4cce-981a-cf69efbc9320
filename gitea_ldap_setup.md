# Gitea LDAP 設定完整指南

## 前置準備

### 1. 申請服務帳號
聯繫 IT 部門申請專用的 LDAP 服務帳號：

**申請郵件範本：**
```
主旨: 申請 Gitea LDAP 服務帳號

您好，

我們需要為 Gitea 系統設定 LDAP 認證，請協助建立一個專用的服務帳號，需求如下：

1. 帳號用途: Gitea LDAP 認證服務
2. 所需權限: 讀取 AD 使用者資訊
3. 搜尋範圍: DC=heysong,DC=com,DC=tw
4. 建議 OU: OU=服務帳號,DC=heysong,DC=com,DC=tw
5. 建議帳號名稱: gitea-service

請提供:
- 完整的服務帳號 DN
- 服務帳號密碼

謝謝！
```

### 2. 測試連接
使用 `python main42.py` 測試 LDAP 連接和認證。

## Gitea 設定步驟

### 步驟 1: 進入管理介面
1. 以管理員身份登入 Gitea
2. 點擊右上角頭像 → `網站管理`
3. 在左側選單選擇 `認證來源`

### 步驟 2: 新增認證來源
1. 點擊 `新增認證來源` 按鈕
2. 選擇認證類型: `LDAP (via BindDN)`

### 步驟 3: 基本設定
```
認證名稱: 黑松 LDAP
認證類型: LDAP (via BindDN)
主機: HSWDC00.heysong.com.tw
連接埠: 389
安全協定: 未加密
```

### 步驟 4: 綁定設定
```
Bind DN: [IT提供的服務帳號DN]
Bind 密碼: [IT提供的服務帳號密碼]
```

### 步驟 5: 使用者設定
```
使用者搜尋基礎: DC=heysong,DC=com,DC=tw
使用者篩選器: (&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s))
使用者名稱屬性: sAMAccountName
名字屬性: givenName
姓氏屬性: sn
電子郵件屬性: userPrincipalName
```

### 步驟 6: 進階設定
```
☑ 啟用使用者同步
☑ 同步使用者屬性
☑ 啟用
```

### 步驟 7: 測試設定
1. 點擊 `測試` 按鈕
2. 輸入測試使用者帳號和密碼
3. 確認測試成功

### 步驟 8: 儲存設定
點擊 `新增認證來源` 完成設定

## 使用說明

### 使用者登入方式
使用者可以使用以下方式登入 Gitea：
- 使用者名稱: `sAMAccountName` (例如: 16613)
- 密碼: AD 密碼

### 首次登入
1. 使用者首次透過 LDAP 登入時，Gitea 會自動建立帳號
2. 使用者資訊會從 AD 同步
3. 電子郵件地址會自動設定為 `userPrincipalName`

## 故障排除

### 常見問題
1. **連接失敗**: 檢查主機名稱和連接埠
2. **認證失敗**: 檢查 Bind DN 和密碼
3. **找不到使用者**: 檢查使用者篩選器和搜尋基礎
4. **權限不足**: 確認服務帳號有讀取權限

### 測試工具
使用 `python main42.py` 進行完整的連接和認證測試。

## 安全建議

1. **服務帳號安全**
   - 使用專用的服務帳號
   - 定期更換密碼
   - 限制最小必要權限

2. **網路安全**
   - 考慮使用 LDAPS (636 port) 加密連接
   - 限制網路存取範圍

3. **監控**
   - 定期檢查認證日誌
   - 監控異常登入行為
