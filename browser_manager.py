# browser_manager.py
from playwright.sync_api import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, BrowserContext, Page
import time
import re
from typing import Optional, Tuple, Callable
from config import Config
from utils import recognize_captcha, format_filename
from page_handlers import LoginHandler


class BrowserManager:
    """瀏覽器管理類"""

    def __init__(self, playwright: Playwright):
        self.playwright = playwright
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.download_finished = False
        self.current_date_range: Optional[Tuple[str, str]] = None
        self.pending_downloads = {}
        self.current_tax_id: Optional[str] = None
        self.downloaded_files = []

    def initialize(self) -> bool:
        """初始化瀏覽器"""
        try:
            print("\n=== 啟動瀏覽器 ===")
            # 只傳入瀏覽器相關設定
            self.browser = self.playwright.chromium.launch(**Config.BROWSER_CONFIG)

            # 設定 context 參數
            context_params = {
                "viewport": Config.VIEWPORT_CONFIG,
                "accept_downloads": True,
                "device_scale_factor": 0.9  # 設定縮放比例為 90%
            }

            self.context = self.browser.new_context(**context_params)
            self.context.set_default_timeout(Config.DEFAULT_TIMEOUT)
            self.page = self.context.new_page()

            # 設定縮放
            self.page.evaluate("document.body.style.zoom = '90%'")

            # 設置下載事件監聽
            self.page.on("download", self._handle_download)

            return True

        except Exception as e:
            print(f"瀏覽器初始化失敗: {str(e)}")
            return False

    def _validate_captcha(self, max_retries: int = Config.MAX_RETRIES) -> Optional[str]:
        """
        驗證碼處理與驗證

        Args:
            max_retries: 最大重試次數

        Returns:
            str: 有效的驗證碼文字，如果都失敗則返回 None
        """
        for attempt in range(max_retries):
            try:
                # 獲取驗證碼圖片
                captcha_img = self.page.get_by_role("img", name="圖形驗證碼", exact=True)
                captcha_img.wait_for()
                captcha_img.screenshot(path="captcha.png")

                # 識別驗證碼
                captcha_text = recognize_captcha("captcha.png")
                print(f"第 {attempt + 1} 次識別驗證碼結果: {captcha_text}")

                # 使用正則表達式檢查是否為純數字
                if re.match(r'^\d+$', captcha_text):
                    print(f"獲得有效的數字驗證碼: {captcha_text}")
                    return captcha_text

                print(f"驗證碼 {captcha_text} 不是純數字，嘗試更新...")
                # 點擊更新按鈕
                update_button = self.page.get_by_role("button", name="更新圖形驗證碼")
                update_button.click()
                time.sleep(1)  # 等待新的驗證碼加載

            except Exception as e:
                print(f"驗證碼處理過程中發生錯誤: {str(e)}")
                if attempt < max_retries - 1:
                    print("嘗試更新驗證碼...")
                    try:
                        update_button = self.page.get_by_role("button", name="更新圖形驗證碼")
                        update_button.click()
                        time.sleep(1)
                    except Exception as e:
                        print(f"更新驗證碼失敗: {str(e)}")

        print(f"已達到最大重試次數 {max_retries}，驗證碼處理失敗")
        return None

    def _handle_download(self, download) -> None:
        """處理下載事件"""
        try:
            print("正在處理下載事件...")
            print(f"當前日期範圍: {self.current_date_range}")
            if self.current_date_range:
                start_date, end_date = self.current_date_range
                # 使用當前的統編生成檔名
                filename = format_filename(start_date, end_date, self.current_tax_id)
                print(f"正在保存檔案: {filename}")
                download.save_as(filename)
                # 使用帶有統編的檔名加入到追蹤列表
                self.downloaded_files.append(filename)
                print(f"✅ 檔案已成功保存: {filename}")
                self.download_finished = True
            else:
                print("警告：沒有當前日期範圍信息")
                temp_filename = f"report_{time.strftime('%Y%m%d_%H%M%S')}.xlsx"
                download.save_as(temp_filename)
                self.downloaded_files.append(temp_filename)
                print(f"檔案已保存為: {temp_filename}")
                self.download_finished = True

        except Exception as e:
            print(f"下載檔案時發生錯誤: {str(e)}")
            self.download_finished = False

    def set_current_tax_id(self, tax_id: str) -> None:
        """設置當前處理的統編"""
        self.current_tax_id = tax_id

    def get_downloaded_files(self) -> list:
        """獲取所有下載的檔案清單"""
        return self.downloaded_files

    def prepare_download(self, start_date: str, end_date: str) -> None:
        """準備下載任務"""
        # 這個方法應該在觸發下載前調用
        current_time = time.strftime('%Y%m%d_%H%M%S')
        key = f"report_{current_time}.xlsx"  # 使用時間戳建立唯一的鍵
        self.pending_downloads[key] = (start_date, end_date)

    def login(self) -> bool:
        """執行登入流程"""
        if not self.page:
            return False

        try:
            print("\n=== 開始登入流程 ===")
            print("正在訪問網站...")
            self.page.goto(Config.BASE_URL)
            time.sleep(2)
            self.page.get_by_role("link", name="登入").click()
            time.sleep(2)
            self.page.get_by_role("link", name="營業人/ 扣繳單位").click()
            time.sleep(3)

            login_handler = LoginHandler(self.page)

            # 嘗試所有密碼
            for i, password in enumerate(Config.PASSWORDS, 1):
                print(f"\n嘗試第 {i}/{len(Config.PASSWORDS)} 個密碼")

                # 填寫登入表單
                if not all([
                    login_handler.fill_input("統一編號", Config.DEFAULT_TAX_ID),
                    login_handler.fill_input("帳號", Config.DEFAULT_ACCOUNT),
                    login_handler.fill_input("密碼", password)
                ]):
                    print("填寫表單失敗")
                    continue

                # 處理驗證碼
                captcha_text = self._validate_captcha()
                if not captcha_text:
                    print("無法獲得有效的驗證碼，嘗試下一次登入")
                    continue

                if not login_handler.fill_captcha(captcha_text):
                    print("填寫驗證碼失敗")
                    continue

                # 點擊登入按鈕
                submit_button = self.page.get_by_role("button", name="登入")
                submit_button.click()
                print("已點擊登入按鈕")

                # 檢查登入結果
                login_success, should_retry = login_handler.check_login_status()
                if login_success:
                    print("登入成功！")
                    return True
                if not should_retry:
                    print(f"登入失敗且無需重試")
                    break

            return False

        except Exception as e:
            print(f"登入過程發生錯誤: {str(e)}")
            return False

    def cleanup(self):
        """清理資源"""
        print("\n=== 清理資源 ===")
        print("\n下載的檔案清單：")
        for file in self.downloaded_files:
            print(f"- {file}")

        if self.context:
            try:
                self.context.close()
            except Exception as e:
                print(f"關閉context時發生錯誤: {str(e)}")

        if self.browser:
            try:
                self.browser.close()
            except Exception as e:
                print(f"關閉瀏覽器時發生錯誤: {str(e)}")