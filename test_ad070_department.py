import ldap3
import sys

def test_ad070_department():
    """測試 AD070 部門的使用者結構"""
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389, get_info=ldap3.ALL)
    
    print("=== 測試 AD070 部門使用者結構 ===")
    
    username = input("輸入測試帳號 (或按 Enter 使用 16613): ").strip() or "16613"
    password = input("輸入密碼: ").strip()
    
    if not password:
        print("需要密碼才能測試")
        return
    
    # 1. 先用管理員權限連接
    user_dn = f"{username}@heysong.com.tw"
    print(f"\n1. 連接 LDAP: {user_dn}")
    
    try:
        conn = ldap3.Connection(server, user=user_dn, password=password)
        if conn.bind():
            print("   ✅ LDAP 連接成功")
            
            # 2. 搜尋 AD070 相關的使用者
            print("\n2. 搜尋 AD070 部門使用者...")
            
            # 搜尋包含 AD070 的使用者
            search_filters = [
                "(department=AD070)",
                "(departmentNumber=AD070)", 
                "(ou=AD070)",
                "(description=*AD070*)",
                "(&(objectCategory=Person)(objectClass=User)(department=AD070))",
                "(&(objectCategory=Person)(objectClass=User)(departmentNumber=AD070))"
            ]
            
            for i, search_filter in enumerate(search_filters, 1):
                print(f"\n   測試篩選器 {i}: {search_filter}")
                try:
                    result = conn.search('DC=heysong,DC=com,DC=tw', search_filter, ldap3.SUBTREE,
                                       attributes=['distinguishedName', 'sAMAccountName', 'department', 
                                                 'departmentNumber', 'ou', 'description', 'title', 'company'])
                    
                    if result and conn.entries:
                        print(f"   ✅ 找到 {len(conn.entries)} 個使用者")
                        for entry in conn.entries[:3]:  # 只顯示前3個
                            print(f"      - {entry.sAMAccountName}: {entry.distinguishedName}")
                            if hasattr(entry, 'department'):
                                print(f"        部門: {entry.department}")
                            if hasattr(entry, 'departmentNumber'):
                                print(f"        部門編號: {entry.departmentNumber}")
                            if hasattr(entry, 'description'):
                                print(f"        描述: {entry.description}")
                    else:
                        print("   ❌ 未找到使用者")
                        
                except Exception as e:
                    print(f"   ❌ 搜尋錯誤: {e}")
            
            # 3. 檢查目前登入使用者的部門資訊
            print(f"\n3. 檢查使用者 {username} 的部門資訊...")
            try:
                result = conn.search('DC=heysong,DC=com,DC=tw', 
                                   f"(sAMAccountName={username})", ldap3.SUBTREE,
                                   attributes=['distinguishedName', 'sAMAccountName', 'department', 
                                             'departmentNumber', 'ou', 'description', 'title', 'company'])
                
                if result and conn.entries:
                    entry = conn.entries[0]
                    print(f"   使用者: {entry.sAMAccountName}")
                    print(f"   DN: {entry.distinguishedName}")
                    print(f"   部門: {getattr(entry, 'department', '未設定')}")
                    print(f"   部門編號: {getattr(entry, 'departmentNumber', '未設定')}")
                    print(f"   描述: {getattr(entry, 'description', '未設定')}")
                    print(f"   職稱: {getattr(entry, 'title', '未設定')}")
                    print(f"   公司: {getattr(entry, 'company', '未設定')}")
                    
                    # 檢查是否屬於 AD070
                    is_ad070 = False
                    if hasattr(entry, 'department') and 'AD070' in str(entry.department):
                        is_ad070 = True
                        print("   ✅ 此使用者屬於 AD070 部門")
                    elif hasattr(entry, 'departmentNumber') and 'AD070' in str(entry.departmentNumber):
                        is_ad070 = True
                        print("   ✅ 此使用者屬於 AD070 部門")
                    elif hasattr(entry, 'description') and 'AD070' in str(entry.description):
                        is_ad070 = True
                        print("   ✅ 此使用者描述包含 AD070")
                    else:
                        print("   ❌ 此使用者不屬於 AD070 部門")
                        
                else:
                    print("   ❌ 未找到使用者資訊")
                    
            except Exception as e:
                print(f"   ❌ 搜尋錯誤: {e}")
            
            conn.unbind()
            
        else:
            print("   ❌ LDAP 連接失敗")
            
    except Exception as e:
        print(f"   ❌ 連接錯誤: {e}")

def suggest_gitea_filter():
    """建議 Gitea 的篩選器設定"""
    print(f"\n💡 建議的 Gitea 篩選器設定:")
    print("根據測試結果，以下是可能的篩選器選項：")
    print()
    print("選項 1 - 使用 department 屬性:")
    print("(&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s)(department=AD070))")
    print()
    print("選項 2 - 使用 departmentNumber 屬性:")
    print("(&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s)(departmentNumber=AD070))")
    print()
    print("選項 3 - 使用 description 包含:")
    print("(&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s)(description=*AD070*))")
    print()
    print("選項 4 - 使用 OU 結構 (如果 AD070 是組織單位):")
    print("(&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s))")
    print("並將用戶搜尋基準改為: OU=AD070,DC=heysong,DC=com,DC=tw")

if __name__ == "__main__":
    test_ad070_department()
    suggest_gitea_filter()
