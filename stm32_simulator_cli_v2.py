#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STM32 Bootloader 模擬器 - 改進版
修復了數據接收分片的問題
"""

import serial
import serial.tools.list_ports
import threading
import time
import struct
import sys
from datetime import datetime

class STM32BootloaderSimulatorV2:
    def __init__(self):
        self.serial_port = None
        self.is_connected = False
        self.is_bootloader_mode = False
        self.receive_buffer = bytearray()
        
        # Bootloader 狀態
        self.state = "NORMAL"  # NORMAL, BOOTLOADER, WAIT_SYNC
        self.last_command = None
        self.write_address = 0
        self.expected_data_length = 0
        
        # 寫入狀態
        self.write_state = None  # None, WAIT_ADDR, WAIT_DATA
        self.write_data_needed = 0  # 等待的數據字節數
        
        # 自定義協議常數
        self.STX = 0x01
        self.CMD_ENTER_BOOTLOADER = 0xB2
        self.CMD_RESET_MCU = 0xA1
        
        # STM32 Bootloader 命令
        self.STM32_SYNC = 0x7F
        self.STM32_ACK = 0x79
        self.STM32_NACK = 0x1F
        self.STM32_CMD_GET = 0x00
        self.STM32_CMD_ERASE = 0x43
        self.STM32_CMD_WRITE = 0x31
        self.STM32_CMD_GO = 0x21
        
        # 模擬的 Flash 記憶體
        self.flash_memory = {}  # 地址 -> 數據
        self.flash_start = 0x08000000
        
        # 統計信息
        self.write_count = 0
        self.total_bytes_written = 0
        
    def list_ports(self):
        """列出可用串口"""
        ports = serial.tools.list_ports.comports()
        if not ports:
            print("未找到串口設備")
            return None
            
        print("\n可用串口：")
        for i, port in enumerate(ports):
            print(f"{i+1}. {port.device} - {port.description}")
        return ports
    
    def connect(self, port_name, baud_rate=115200):
        """連接串口"""
        try:
            self.serial_port = serial.Serial(
                port=port_name,
                baudrate=baud_rate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            
            self.is_connected = True
            self.log_message(f"✅ STM32 Bootloader 模擬器 V2 已啟動")
            self.log_message(f"   串口: {port_name}, 波特率: {baud_rate}")
            
            # 啟動接收線程
            self.receive_thread = threading.Thread(target=self.receive_loop, daemon=True)
            self.receive_thread.start()
            
            return True
            
        except Exception as e:
            self.log_message(f"❌ 連接錯誤: {str(e)}")
            return False
    
    def disconnect(self):
        """斷開連接"""
        self.is_connected = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
        self.log_message("❌ 模擬器已停止")
    
    def receive_loop(self):
        """接收循環"""
        while self.is_connected and self.serial_port:
            try:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    self.process_received_data(data)
                time.sleep(0.01)
            except Exception as e:
                self.log_message(f"❌ 接收錯誤: {str(e)}")
                break
    
    def process_received_data(self, data):
        """處理接收到的數據"""
        hex_display = ' '.join([f"{b:02X}" for b in data])
        self.log_message(f"📥 接收: {hex_display}")
        
        self.receive_buffer.extend(data)
        
        # 如果正在等待寫入數據，優先處理
        if self.write_state == "WAIT_DATA":
            self.process_write_data()
            return
        elif self.write_state == "WAIT_ADDR":
            self.process_write_address()
            return
        elif self.write_state == "WAIT_GO_ADDRESS":
            self.process_go_address()
            return
        
        # 根據當前狀態處理數據
        if self.state == "NORMAL":
            self.process_normal_mode()
        elif self.state == "WAIT_SYNC":
            self.process_wait_sync()
        elif self.state == "BOOTLOADER":
            self.process_bootloader_mode()
    
    def process_normal_mode(self):
        """處理正常模式下的命令 - 匹配 C# 專案格式"""
        while len(self.receive_buffer) > 0:
            # 查找 STX
            if self.receive_buffer[0] != self.STX:
                self.receive_buffer.pop(0)
                continue
            
            # 檢查是否有足夠的數據
            if len(self.receive_buffer) < 3:  # STX + LEN + CMD
                break
            
            length = self.receive_buffer[1]
            
            # C# 專案特殊處理：進入 Bootloader 命令
            if len(self.receive_buffer) >= 3 and self.receive_buffer[2] == self.CMD_ENTER_BOOTLOADER:
                # C# 使用固定長度 12，實際包長度是 14
                if length == 12:
                    total_length = 14  # STX(1) + LEN(1) + CMD(1) + DATA(10) + CHECKSUM(1)
                else:
                    # 標準計算
                    total_length = 2 + length  # STX + LEN + (CMD + DATA + CHECKSUM)
            else:
                # 標準計算：STX + LEN + length指定的字節數
                total_length = 2 + length
            
            if len(self.receive_buffer) < total_length:
                break
            
            # 提取包
            packet = self.receive_buffer[:total_length]
            self.receive_buffer = self.receive_buffer[total_length:]
            
            # 計算校驗和 - 根據 C# 專案：LEN ^ CMD ^ DATA
            length_byte = packet[1]
            cmd = packet[2]
            data = packet[3:-1]
            
            # 校驗和計算：length ^ cmd ^ all data bytes
            checksum = length_byte ^ cmd
            for b in data:
                checksum ^= b
            
            if checksum != packet[-1]:
                self.log_message(f"❌ 校驗和錯誤: 計算={checksum:02X}, 接收={packet[-1]:02X}")
                continue
            
            self.log_message(f"📦 解析命令: CMD=0x{cmd:02X}, LEN={length}, DATA={' '.join([f'{b:02X}' for b in data])}")
            
            if cmd == self.CMD_ENTER_BOOTLOADER:
                # 進入 Bootloader 模式
                expected_data = b'123456789:'
                if data == expected_data:
                    self.log_message("✅ 收到正確的進入 Bootloader 命令")
                    self.log_message("   數據: " + data.decode('ascii'))
                    self.log_message("🔄 模擬設備重啟，進入 Bootloader 模式...")
                    
                    # 模擬重啟延遲
                    time.sleep(0.5)
                    
                    # 切換到等待同步狀態
                    self.state = "WAIT_SYNC"
                    self.log_message("⏳ 等待 Android 發送 0x7F 同步字節...")
                    # 注意：不要在這裡發送任何響應，等待 Android 發送 0x7F
                else:
                    self.log_message(f"❌ Bootloader 數據不匹配: {data}")
            
            elif cmd == self.CMD_RESET_MCU:
                self.log_message("🔄 收到重置命令，模擬設備重啟...")
                self.reset_state()
                self.send_response(b"RESET_OK")
    
    def process_wait_sync(self):
        """等待 STM32 同步字節"""
        while len(self.receive_buffer) > 0:
            byte = self.receive_buffer.pop(0)
            if byte == self.STM32_SYNC:
                self.log_message("✅ 收到 STM32 同步字節 (0x7F)")
                self.state = "BOOTLOADER"
                
                # 發送 ACK
                self.send_byte(self.STM32_ACK)
                self.log_message(f"📤 發送 ACK (0x{self.STM32_ACK:02X})")
                self.log_message("🚀 進入 STM32 Bootloader 模式")
            else:
                self.log_message(f"❓ 非預期字節: 0x{byte:02X}")
    
    def process_bootloader_mode(self):
        """處理 Bootloader 模式命令"""
        while len(self.receive_buffer) >= 2:
            cmd = self.receive_buffer[0]
            cmd_complement = self.receive_buffer[1]
            
            # 檢查命令和補碼
            if (cmd ^ cmd_complement) != 0xFF:
                self.log_message(f"❌ 命令補碼錯誤: CMD=0x{cmd:02X}, ~CMD=0x{cmd_complement:02X}")
                self.receive_buffer.pop(0)
                continue
            
            self.receive_buffer = self.receive_buffer[2:]
            self.last_command = cmd
            
            # 處理 STM32 Bootloader 命令
            if cmd == self.STM32_CMD_GET:
                self.handle_get_command()
            elif cmd == self.STM32_CMD_ERASE:
                self.handle_erase_command()
            elif cmd == self.STM32_CMD_WRITE:
                self.handle_write_command()
            elif cmd == self.STM32_CMD_GO:
                self.handle_go_command()
            else:
                self.log_message(f"❓ 未知命令: 0x{cmd:02X}")
                self.send_byte(self.STM32_NACK)
    
    def handle_get_command(self):
        """處理 GET 命令"""
        self.log_message("📋 GET 命令 - 返回支持的命令列表")
        self.send_byte(self.STM32_ACK)
        
        # 發送支持的命令
        commands = [self.STM32_CMD_GET, self.STM32_CMD_ERASE, 
                   self.STM32_CMD_WRITE, self.STM32_CMD_GO]
        self.send_byte(len(commands))
        for cmd in commands:
            self.send_byte(cmd)
        self.send_byte(self.STM32_ACK)
    
    def handle_erase_command(self):
        """處理 ERASE 命令"""
        self.log_message("🗑️ ERASE 命令 - 等待擦除參數...")
        self.send_byte(self.STM32_ACK)
        
        # 等待擦除參數
        start_time = time.time()
        while time.time() - start_time < 2:  # 2秒超時
            if len(self.receive_buffer) >= 2:
                erase_type = self.receive_buffer.pop(0)
                checksum = self.receive_buffer.pop(0)
                
                if erase_type == 0xFF:  # 全局擦除
                    self.log_message("🗑️ 執行全局擦除...")
                    self.flash_memory.clear()
                    self.write_count = 0
                    self.total_bytes_written = 0
                    time.sleep(1)  # 模擬擦除延遲
                    self.send_byte(self.STM32_ACK)
                    self.log_message("✅ 擦除完成")
                else:
                    self.log_message(f"❓ 不支持的擦除類型: 0x{erase_type:02X}")
                    self.send_byte(self.STM32_NACK)
                break
            time.sleep(0.01)
    
    def handle_write_command(self):
        """處理 WRITE 命令"""
        self.log_message("✏️ WRITE 命令 - 等待地址...")
        self.send_byte(self.STM32_ACK)
        
        # 設置為等待地址狀態
        self.write_state = "WAIT_ADDR"
        self.process_write_address()
    
    def process_write_address(self):
        """處理寫入地址"""
        if len(self.receive_buffer) >= 5:
            addr_bytes = self.receive_buffer[:4]
            addr_checksum = self.receive_buffer[4]
            self.receive_buffer = self.receive_buffer[5:]
            
            # 驗證地址校驗和
            calc_checksum = self.calculate_xor_checksum(addr_bytes)
            if calc_checksum == addr_checksum:
                self.write_address = struct.unpack(">I", bytes(addr_bytes))[0]
                self.log_message(f"📍 寫入地址: 0x{self.write_address:08X}")
                self.send_byte(self.STM32_ACK)
                
                # 設置為等待數據狀態
                self.write_state = "WAIT_DATA"
                self.write_data_needed = 0
                self.process_write_data()
            else:
                self.log_message(f"❌ 地址校驗和錯誤")
                self.send_byte(self.STM32_NACK)
                self.write_state = None
    
    def process_write_data(self):
        """處理寫入數據"""
        # 如果還不知道需要多少數據，先讀取長度字節
        if self.write_data_needed == 0 and len(self.receive_buffer) >= 1:
            length_byte = self.receive_buffer[0]
            self.write_data_needed = length_byte + 1 + 1 + 1  # N-1 + data + checksum
            self.log_message(f"📏 期望數據長度: {length_byte + 1} 字節 (N-1={length_byte:02X})")
        
        # 檢查是否有足夠的數據
        if self.write_data_needed > 0 and len(self.receive_buffer) >= self.write_data_needed:
            # 提取數據
            length_byte = self.receive_buffer[0]
            data_length = length_byte + 1
            data = self.receive_buffer[1:1+data_length]
            data_checksum = self.receive_buffer[1+data_length]
            self.receive_buffer = self.receive_buffer[self.write_data_needed:]
            
            # 驗證數據校驗和
            check_data = bytearray([length_byte])
            check_data.extend(data)
            calc_checksum = self.calculate_xor_checksum(check_data)
            
            if calc_checksum == data_checksum:
                # 寫入 Flash
                for i, byte in enumerate(data):
                    self.flash_memory[self.write_address + i] = byte
                
                self.write_count += 1
                self.total_bytes_written += len(data)
                
                self.log_message(f"✅ 寫入 {len(data)} 字節到 0x{self.write_address:08X} (第 {self.write_count} 塊)")
                self.send_byte(self.STM32_ACK)
                
                # 顯示寫入的數據
                hex_display = ' '.join([f"{b:02X}" for b in data[:16]])
                if len(data) > 16:
                    hex_display += " ..."
                self.log_message(f"   數據: {hex_display}")
                
                # 重置寫入狀態
                self.write_state = None
                self.write_data_needed = 0
            else:
                self.log_message(f"❌ 數據校驗和錯誤: 計算={calc_checksum:02X}, 接收={data_checksum:02X}")
                self.send_byte(self.STM32_NACK)
                self.write_state = None
                self.write_data_needed = 0
        else:
            # 數據不足，顯示緩衝區狀態
            if self.write_data_needed > 0:
                self.log_message(f"⏳ 等待數據... (需要: {self.write_data_needed}, 緩衝區: {len(self.receive_buffer)})")
    
    def handle_go_command(self):
        """處理 GO 命令"""
        self.log_message("🚀 GO 命令 - 等待跳轉地址...")
        self.send_byte(self.STM32_ACK)
        
        # 設置狀態等待地址
        self.write_state = "WAIT_GO_ADDRESS"
        self.process_go_address()
    
    def process_go_address(self):
        """處理 GO 命令的地址"""
        if len(self.receive_buffer) >= 5:
            # 提取地址和校驗和
            addr_data = self.receive_buffer[:5]
            self.receive_buffer = self.receive_buffer[5:]
            
            # 解析地址 (大端序)
            address = (addr_data[0] << 24) | (addr_data[1] << 16) | (addr_data[2] << 8) | addr_data[3]
            checksum = addr_data[4]
            
            # 計算 XOR 校驗和
            calc_checksum = addr_data[0] ^ addr_data[1] ^ addr_data[2] ^ addr_data[3]
            
            hex_display = ' '.join([f"{b:02X}" for b in addr_data])
            self.log_message(f"📥 收到跳轉地址: {hex_display}")
            self.log_message(f"   地址: 0x{address:08X}")
            self.log_message(f"   校驗和: 收到=0x{checksum:02X}, 計算=0x{calc_checksum:02X}")
            
            if checksum == calc_checksum:
                # 發送 ACK
                self.send_byte(self.STM32_ACK)
                self.log_message("📤 發送: 79 (ACK)")
                
                # 模擬跳轉
                self.log_message(f"🚀 模擬跳轉到地址 0x{address:08X}")
                self.log_message("=" * 50)
                self.log_message("✅ GO 命令執行成功！")
                self.log_message("🔄 STM32 已跳轉到應用程式")
                self.log_message("⚠️  Bootloader 模式結束")
                self.log_message("=" * 50)
                
                # 顯示寫入統計
                self.show_statistics()
                
                # 顯示 Flash 內容摘要
                if self.flash_memory:
                    min_addr = min(self.flash_memory.keys())
                    max_addr = max(self.flash_memory.keys())
                    self.log_message(f"   地址範圍: 0x{min_addr:08X} - 0x{max_addr:08X}")
                    
                    # 顯示前 16 字節
                    self.log_message(f"\n📝 Flash 內容預覽 (0x{self.flash_start:08X}):")
                    preview = []
                    for i in range(16):
                        addr = self.flash_start + i
                        if addr in self.flash_memory:
                            preview.append(f"{self.flash_memory[addr]:02X}")
                        else:
                            preview.append("--")
                    self.log_message("   " + " ".join(preview))
                
                # 重置狀態
                time.sleep(0.5)
                self.log_message("\n✅ 退出 Bootloader，返回正常模式")
                self.reset_state()
            else:
                # 發送 NACK
                self.send_byte(self.STM32_NACK)
                self.log_message("📤 發送: 1F (NACK) - 地址校驗和錯誤")
            
            self.write_state = None
    
    def send_byte(self, byte):
        """發送單個字節"""
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.write(bytes([byte]))
    
    def send_response(self, data):
        """發送響應數據"""
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.write(data)
            hex_display = ' '.join([f"{b:02X}" for b in data])
            self.log_message(f"📤 發送響應: {hex_display}")
    
    def calculate_xor_checksum(self, data):
        """計算 XOR 校驗和"""
        checksum = 0
        for byte in data:
            checksum ^= byte
        return checksum
    
    def reset_state(self):
        """重置狀態"""
        self.state = "NORMAL"
        self.is_bootloader_mode = False
        self.last_command = None
        self.write_state = None
        self.write_data_needed = 0
        self.receive_buffer.clear()
        self.log_message("🔄 狀態已重置")
    
    def show_statistics(self):
        """顯示統計信息"""
        self.log_message(f"\n📊 寫入統計:")
        self.log_message(f"   寫入次數: {self.write_count}")
        self.log_message(f"   總數據量: {self.total_bytes_written} 字節")
        
        if self.flash_memory:
            min_addr = min(self.flash_memory.keys())
            max_addr = max(self.flash_memory.keys())
            self.log_message(f"   地址範圍: 0x{min_addr:08X} - 0x{max_addr:08X}")
    
    def log_message(self, message):
        """記錄消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {message}")
    
    def run(self):
        """運行模擬器"""
        print("\n===== STM32 Bootloader 模擬器 V2 =====")
        print("修復了數據接收分片的問題")
        print("==========================================")
        
        # 列出串口
        ports = self.list_ports()
        if not ports:
            return
        
        # 選擇串口
        try:
            choice = int(input("\n請選擇串口編號: ")) - 1
            if 0 <= choice < len(ports):
                port_name = ports[choice].device
            else:
                print("無效的選擇")
                return
        except ValueError:
            print("無效的輸入")
            return
        
        # 選擇波特率
        baud_rate = input("請輸入波特率 [115200]: ").strip()
        if not baud_rate:
            baud_rate = 115200
        else:
            try:
                baud_rate = int(baud_rate)
            except ValueError:
                print("無效的波特率")
                return
        
        # 連接串口
        if not self.connect(port_name, baud_rate):
            return
        
        print("\n模擬器已啟動！按 Ctrl+C 退出")
        print("等待接收命令...\n")
        
        try:
            while self.is_connected:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n正在關閉模擬器...")
            self.show_statistics()
            self.disconnect()

if __name__ == "__main__":
    simulator = STM32BootloaderSimulatorV2()
    simulator.run()