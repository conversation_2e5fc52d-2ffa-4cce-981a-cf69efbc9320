import pandas as pd

# Load the Excel file
file_path = "114年丁種統一發票字軌組.xlsx"
excel_data = pd.ExcelFile(file_path)

# Parse the specified sheet and treat all columns as strings
sheet = excel_data.parse('工作表1', dtype=str).fillna('NA')  # Ensure NaN values are treated as empty strings

# Prepare data for SQL insertion
data_rows = []
year = '114'  # Fixed year as a string

# Iterate through each row in the sheet
for _, row in sheet.iterrows():
    start_month = row['起']  # Keep as string
    end_month = row['迄']  # Keep as string

    # Loop through columns after "起" and "迄" for track data
    for col in sheet.columns[2:]:
        track = row[col]
        if track:  # Ensure no empty tracks are processed
            data_rows.append((year, start_month, end_month, track))

# Convert to a DataFrame for easier SQL processing
sql_data = pd.DataFrame(data_rows, columns=['AEYEAR', 'AESMON', 'AEEMON', 'AECHAR'])

# Generate SQL INSERT statements
insert_statements = []
table_name = "INAE"
for _, row in sql_data.iterrows():
    insert_statements.append(
        f"INSERT INTO {table_name} (AEYEAR, AESMON, AEEMON, AECHAR) VALUES ('{row['AEYEAR']}', '{row['AESMON']}', '{row['AEEMON']}', '{row['AECHAR']}');"
    )

# Print a few SQL statements for verification
for statement in insert_statements[:5]:
    print(statement)

# Save the SQL statements to a file
sql_file_path = "invoice_tracks_insert.sql"
with open(sql_file_path, "w", encoding="utf-8") as file:
    file.write("\n".join(insert_statements))

print(f"SQL insert statements saved to: {sql_file_path}")
