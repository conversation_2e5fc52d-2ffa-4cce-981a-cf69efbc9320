"""
家樂福爬蟲展示版本 - 顯示瀏覽器操作過程
這個版本會打開瀏覽器視窗，讓您看到整個爬取過程
"""

import asyncio
import json
import csv
from datetime import datetime
from playwright.async_api import async_playwright
import re
import time

class CarrefourScraperDemo:
    def __init__(self):
        self.base_url = "https://online.carrefour.com.tw"
        self.target_url = "https://online.carrefour.com.tw/zh/%E5%93%81%E7%89%8C%E6%97%97%E8%89%A6%E9%A4%A8/%E9%BB%91%E6%9D%BE%E5%93%81%E7%89%8C%E9%A4%A8"
        self.products = []
        
    async def scrape_with_demo(self, max_pages=2):
        """展示版爬取 - 顯示瀏覽器操作過程"""
        print("🚀 啟動展示模式爬蟲...")
        print("📱 即將打開瀏覽器視窗，您可以看到整個操作過程")
        print("⏱️  請耐心等待，整個過程大約需要 2-3 分鐘")
        print("-" * 50)
        
        async with async_playwright() as p:
            # 啟動瀏覽器 - 顯示視窗
            print("🌐 正在啟動 Chrome 瀏覽器...")
            browser = await p.chromium.launch(
                headless=False,  # 顯示瀏覽器視窗
                slow_mo=1000,    # 每個操作間隔 1 秒，方便觀看
                args=[
                    '--start-maximized',  # 最大化視窗
                    '--disable-blink-features=AutomationControlled'  # 避免被偵測為自動化
                ]
            )
            
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            page = await context.new_page()
            
            try:
                print(f"🔗 正在訪問目標網站...")
                print(f"   網址: {self.target_url}")
                
                # 訪問網站
                await page.goto(self.target_url, wait_until="networkidle", timeout=60000)
                print("✅ 網站載入完成")

                # 等待頁面完全載入
                print("⏳ 等待頁面完全載入...")
                await page.wait_for_timeout(8000)

                # 檢查頁面標題
                title = await page.title()
                print(f"📄 頁面標題: {title}")

                # 等待用戶觀看
                await page.wait_for_timeout(3000)
                
                # 處理彈窗
                print("🔍 檢查並處理可能的彈窗...")
                await self.handle_popups_demo(page)
                
                page_count = 0
                while page_count < max_pages:
                    current_page = page_count + 1
                    print(f"\n📄 正在處理第 {current_page} 頁...")
                    
                    # 滾動頁面確保所有內容載入
                    print("   📜 滾動頁面載入所有產品...")
                    await self.scroll_page_demo(page)
                    
                    # 爬取當前頁面的產品
                    print("   🔍 開始提取產品資訊...")
                    products_on_page = await self.extract_products_demo(page)
                    self.products.extend(products_on_page)
                    
                    print(f"   ✅ 第 {current_page} 頁完成，找到 {len(products_on_page)} 個產品")
                    
                    # 如果不是最後一頁，嘗試點擊下一頁
                    if current_page < max_pages:
                        print(f"   ➡️  準備前往第 {current_page + 1} 頁...")
                        if not await self.go_to_next_page_demo(page):
                            print("   ⚠️  沒有找到下一頁按鈕，結束爬取")
                            break
                        
                        # 等待新頁面載入
                        await page.wait_for_timeout(3000)
                        
                    page_count += 1
                
                print(f"\n🎉 爬取完成！總共處理了 {page_count} 頁")
                print("   瀏覽器將在 5 秒後關閉...")
                await page.wait_for_timeout(5000)
                
            except Exception as e:
                print(f"❌ 爬取過程中發生錯誤: {e}")
                print("   瀏覽器將在 10 秒後關閉...")
                await page.wait_for_timeout(10000)
            finally:
                await browser.close()
                
        return self.products
    
    async def handle_popups_demo(self, page):
        """展示版彈窗處理"""
        try:
            print("   🔍 尋找 Cookie 同意按鈕...")
            
            cookie_selectors = [
                'button[data-testid="accept-all"]',
                'button:has-text("同意")',
                'button:has-text("接受")',
                'button:has-text("確定")',
                '.cookie-accept',
                '#cookie-accept'
            ]
            
            found_popup = False
            for selector in cookie_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=2000)
                    if element:
                        print(f"   ✅ 找到並點擊 Cookie 同意按鈕")
                        await element.click()
                        found_popup = True
                        break
                except:
                    continue
            
            if not found_popup:
                print("   ℹ️  未發現需要處理的彈窗")
                
            await page.wait_for_timeout(2000)
                    
        except Exception as e:
            print(f"   ⚠️  處理彈窗時發生錯誤: {e}")
    
    async def scroll_page_demo(self, page):
        """展示版頁面滾動"""
        try:
            # 滾動到頁面底部以載入所有產品
            print("   📜 開始滾動頁面...")
            
            # 獲取初始頁面高度
            last_height = await page.evaluate("document.body.scrollHeight")
            
            scroll_count = 0
            while scroll_count < 3:  # 最多滾動 3 次
                # 滾動到底部
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                print(f"   📜 滾動第 {scroll_count + 1} 次...")
                
                # 等待新內容載入
                await page.wait_for_timeout(2000)
                
                # 檢查是否有新內容載入
                new_height = await page.evaluate("document.body.scrollHeight")
                if new_height == last_height:
                    print("   ✅ 頁面內容已完全載入")
                    break
                    
                last_height = new_height
                scroll_count += 1
            
            # 滾動回頂部
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(1000)
            
        except Exception as e:
            print(f"   ⚠️  滾動頁面時發生錯誤: {e}")
    
    async def extract_products_demo(self, page):
        """展示版產品提取"""
        products = []
        
        try:
            print("   🔍 尋找產品容器...")

            # 等待頁面基本元素載入
            await page.wait_for_timeout(5000)

            # 檢查頁面內容
            page_content = await page.content()
            print(f"   📄 頁面內容長度: {len(page_content)} 字符")

            # 基於測試結果優化的產品選擇器
            product_selectors = [
                'div[class*="item"]',  # 測試發現有 858 個這樣的元素
                '[data-testid="product-card"]',
                '.product-item',
                '.product-card',
                '[class*="product"]',
                'div:has(img):has-text("NT$")',
                'div:has(img):has-text("$")',
                'a:has(img)',
                'div:has(img)'
            ]
            
            product_elements = None
            used_selector = ""
            
            for selector in product_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements and len(elements) > 0:
                        product_elements = elements
                        used_selector = selector
                        print(f"   ✅ 使用選擇器 '{selector}' 找到 {len(elements)} 個產品元素")
                        break
                except:
                    continue
            
            if not product_elements:
                print("   ⚠️  未找到產品元素，嘗試通用方法...")
                # 使用更通用的方法
                product_elements = await page.query_selector_all('div:has(img)')
                print(f"   ℹ️  使用通用選擇器找到 {len(product_elements)} 個可能的產品元素")
            
            # 提取產品資訊
            extracted_count = 0
            for i, element in enumerate(product_elements[:20]):  # 限制最多處理 20 個元素
                try:
                    if i % 5 == 0:  # 每 5 個產品顯示一次進度
                        print(f"   📦 正在處理第 {i+1}-{min(i+5, len(product_elements))} 個產品...")
                    
                    product_data = await self.extract_product_data_demo(element)
                    if product_data and product_data.get('name') and product_data.get('price'):
                        products.append(product_data)
                        extracted_count += 1
                        
                except Exception as e:
                    continue
            
            print(f"   ✅ 成功提取 {extracted_count} 個有效產品")
                    
        except Exception as e:
            print(f"   ❌ 提取產品資訊時發生錯誤: {e}")
            
        return products
    
    async def extract_product_data_demo(self, element):
        """展示版產品資料提取"""
        try:
            # 產品名稱
            name_selectors = [
                '[data-testid="product-name"]',
                '.product-name',
                '.product-title',
                'h3', 'h4', 'h5',
                '[class*="name"]',
                '[class*="title"]'
            ]
            
            name = await self.get_text_by_selectors(element, name_selectors)
            
            # 產品價格
            price_selectors = [
                '[data-testid="product-price"]',
                '.product-price',
                '.price',
                '[class*="price"]',
                'span:has-text("NT$")',
                'span:has-text("$")'
            ]
            
            price_text = await self.get_text_by_selectors(element, price_selectors)
            price = self.parse_price(price_text)
            
            # 產品圖片
            img_element = await element.query_selector('img')
            image_url = ""
            if img_element:
                image_url = await img_element.get_attribute('src') or await img_element.get_attribute('data-src')
                if image_url and not image_url.startswith('http'):
                    image_url = self.base_url + image_url
            
            # 產品連結
            link_element = await element.query_selector('a')
            product_url = ""
            if link_element:
                href = await link_element.get_attribute('href')
                if href:
                    product_url = href if href.startswith('http') else self.base_url + href
            
            return {
                'name': name.strip() if name else '',
                'price': price,
                'price_text': price_text.strip() if price_text else '',
                'image_url': image_url,
                'product_url': product_url,
                'scraped_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            return None
    
    async def get_text_by_selectors(self, element, selectors):
        """使用多個選擇器嘗試獲取文字"""
        for selector in selectors:
            try:
                target_element = await element.query_selector(selector)
                if target_element:
                    text = await target_element.inner_text()
                    if text and text.strip():
                        return text.strip()
            except:
                continue
        return ""
    
    def parse_price(self, price_text):
        """解析價格文字，提取數字"""
        if not price_text:
            return 0
        
        # 移除貨幣符號和其他非數字字符，保留小數點
        price_numbers = re.findall(r'[\d,]+\.?\d*', price_text.replace(',', ''))
        
        if price_numbers:
            try:
                return float(price_numbers[0])
            except ValueError:
                return 0
        return 0
    
    async def go_to_next_page_demo(self, page):
        """展示版下一頁操作"""
        print("   🔍 尋找下一頁按鈕...")
        
        next_selectors = [
            'button:has-text("下一頁")',
            'a:has-text("下一頁")',
            'button[aria-label*="next"]',
            'a[aria-label*="next"]',
            '.pagination-next',
            '[data-testid="next-page"]',
            'button:has-text(">")',
            'a:has-text(">")'
        ]
        
        for selector in next_selectors:
            try:
                next_button = await page.query_selector(selector)
                if next_button:
                    # 檢查按鈕是否可點擊
                    is_disabled = await next_button.get_attribute('disabled')
                    if not is_disabled:
                        print(f"   ✅ 找到下一頁按鈕，準備點擊...")
                        
                        # 滾動到按鈕位置
                        await next_button.scroll_into_view_if_needed()
                        await page.wait_for_timeout(1000)
                        
                        # 點擊按鈕
                        await next_button.click()
                        print(f"   ✅ 已點擊下一頁按鈕")
                        
                        # 等待頁面載入
                        await page.wait_for_timeout(3000)
                        return True
            except Exception as e:
                print(f"   ⚠️  嘗試點擊下一頁時發生錯誤: {e}")
                continue
        
        print("   ⚠️  未找到可用的下一頁按鈕")
        return False
    
    def save_demo_results(self):
        """儲存展示結果"""
        if not self.products:
            print("❌ 沒有產品資料可儲存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"demo_carrefour_products_{timestamp}.csv"
        json_filename = f"demo_carrefour_products_{timestamp}.json"
        
        # 儲存 CSV
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['name', 'price', 'price_text', 'image_url', 'product_url', 'scraped_at']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for product in self.products:
                writer.writerow(product)
        
        # 儲存 JSON
        with open(json_filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.products, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"💾 已儲存 {len(self.products)} 個產品到:")
        print(f"   📄 CSV: {csv_filename}")
        print(f"   📄 JSON: {json_filename}")
    
    def print_demo_summary(self):
        """列印展示結果摘要"""
        if not self.products:
            print("❌ 沒有找到任何產品")
            return
        
        print(f"\n🎉 === 爬取結果摘要 ===")
        print(f"📊 總共找到 {len(self.products)} 個產品")
        
        if self.products:
            prices = [p['price'] for p in self.products if p['price'] > 0]
            if prices:
                print(f"💰 價格範圍: NT$ {min(prices):.0f} - NT$ {max(prices):.0f}")
                print(f"💰 平均價格: NT$ {sum(prices)/len(prices):.0f}")
        
        print(f"\n📦 產品列表 (前 5 個):")
        for i, product in enumerate(self.products[:5], 1):
            print(f"   {i}. {product['name'][:50]}{'...' if len(product['name']) > 50 else ''}")
            print(f"      💰 NT$ {product['price']:.0f}")

async def main():
    """主展示函數"""
    print("🎬 === 家樂福爬蟲展示模式 ===")
    print("📱 此模式會打開瀏覽器視窗，讓您觀看整個爬取過程")
    print("⏱️  整個過程大約需要 2-3 分鐘，請耐心等待")
    print("🔍 目標: 家樂福黑松品牌館")
    print("=" * 60)
    
    scraper = CarrefourScraperDemo()
    
    # 開始展示爬取
    products = await scraper.scrape_with_demo(max_pages=2)
    
    # 顯示結果摘要
    scraper.print_demo_summary()
    
    # 儲存結果
    if products:
        scraper.save_demo_results()
    
    print("\n🎉 展示完成！感謝您的觀看！")

if __name__ == "__main__":
    asyncio.run(main())
