"""
快速測試家樂福爬蟲
這個腳本會進行簡單的測試，確保爬蟲可以正常運作
"""

import asyncio
import sys
import os

# 檢查是否已安裝 playwright
try:
    from playwright.async_api import async_playwright
    print("✓ Playwright 已安裝")
except ImportError:
    print("✗ Playwright 未安裝，請先執行: python setup_scraper.py")
    sys.exit(1)

async def test_website_access():
    """測試是否能夠訪問目標網站"""
    print("\n=== 測試網站訪問 ===")
    
    async with async_playwright() as p:
        try:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            target_url = "https://online.carrefour.com.tw/zh/%E5%93%81%E7%89%8C%E6%97%97%E8%89%A6%E9%A4%A8/%E9%BB%91%E6%9D%BE%E5%93%81%E7%89%8C%E9%A4%A8"
            
            print(f"正在訪問: {target_url}")
            response = await page.goto(target_url, timeout=30000)
            
            if response.status == 200:
                print("✓ 網站訪問成功")
                
                # 檢查頁面標題
                title = await page.title()
                print(f"頁面標題: {title}")
                
                # 檢查是否有產品元素
                await page.wait_for_timeout(3000)
                
                # 嘗試找到產品元素
                selectors_to_test = [
                    '[data-testid="product-card"]',
                    '.product-item',
                    '.product-card',
                    'div:has(img)',
                    'img'
                ]
                
                found_elements = False
                for selector in selectors_to_test:
                    try:
                        elements = await page.query_selector_all(selector)
                        if elements:
                            print(f"✓ 找到 {len(elements)} 個元素 (選擇器: {selector})")
                            found_elements = True
                            break
                    except:
                        continue
                
                if not found_elements:
                    print("⚠️ 未找到產品元素，可能需要調整選擇器")
                
            else:
                print(f"✗ 網站訪問失敗，狀態碼: {response.status}")
                
            await browser.close()
            return response.status == 200
            
        except Exception as e:
            print(f"✗ 訪問網站時發生錯誤: {e}")
            return False

async def quick_scrape_test():
    """快速爬取測試 - 只爬取第一頁的少量產品"""
    print("\n=== 快速爬取測試 ===")
    
    try:
        from carrefour_scraper import CarrefourScraper
        
        scraper = CarrefourScraper()
        
        # 只爬取 1 頁進行測試
        print("開始測試爬取 (僅第 1 頁)...")
        products = await scraper.scrape_products(headless=True, max_pages=1)
        
        if products:
            print(f"✓ 測試成功！找到 {len(products)} 個產品")
            
            # 顯示前 3 個產品
            print("\n前 3 個產品:")
            for i, product in enumerate(products[:3], 1):
                print(f"{i}. {product['name'][:50]}...")
                print(f"   價格: NT$ {product['price']:.0f}")
                print()
            
            # 儲存測試結果
            scraper.save_to_csv("test_results.csv")
            print("✓ 測試結果已儲存到 test_results.csv")
            
            return True
        else:
            print("✗ 測試失敗：未找到任何產品")
            return False
            
    except Exception as e:
        print(f"✗ 爬取測試失敗: {e}")
        return False

def check_dependencies():
    """檢查依賴套件"""
    print("=== 檢查依賴套件 ===")
    
    required_packages = {
        'playwright': 'playwright',
        'asyncio': 'asyncio',
        'csv': 'csv',
        'json': 'json',
        'datetime': 'datetime',
        're': 're'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✓ {package_name}")
        except ImportError:
            print(f"✗ {package_name} (缺少)")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n缺少的套件: {', '.join(missing_packages)}")
        print("請執行: python setup_scraper.py")
        return False
    else:
        print("✓ 所有依賴套件都已安裝")
        return True

def check_files():
    """檢查必要檔案是否存在"""
    print("\n=== 檢查檔案 ===")
    
    required_files = [
        'carrefour_scraper.py',
        'setup_scraper.py'
    ]
    
    missing_files = []
    
    for filename in required_files:
        if os.path.exists(filename):
            print(f"✓ {filename}")
        else:
            print(f"✗ {filename} (缺少)")
            missing_files.append(filename)
    
    if missing_files:
        print(f"\n缺少的檔案: {', '.join(missing_files)}")
        return False
    else:
        print("✓ 所有必要檔案都存在")
        return True

async def main():
    """主測試流程"""
    print("=== 家樂福爬蟲快速測試 ===\n")
    
    # 1. 檢查依賴
    if not check_dependencies():
        print("\n請先安裝依賴套件後再進行測試")
        return
    
    # 2. 檢查檔案
    if not check_files():
        print("\n請確保所有必要檔案都存在")
        return
    
    # 3. 測試網站訪問
    if not await test_website_access():
        print("\n網站訪問測試失敗，請檢查網路連線")
        return
    
    # 4. 快速爬取測試
    if await quick_scrape_test():
        print("\n🎉 所有測試通過！爬蟲可以正常使用")
        print("\n您現在可以執行完整的爬蟲:")
        print("python carrefour_scraper.py")
    else:
        print("\n❌ 爬取測試失敗，請檢查網站結構是否有變化")

def run_installation_check():
    """檢查是否需要安裝"""
    try:
        from playwright.async_api import async_playwright
        return True
    except ImportError:
        print("Playwright 未安裝，正在自動安裝...")
        
        try:
            import subprocess
            import sys
            
            # 安裝 playwright
            subprocess.check_call([sys.executable, "-m", "pip", "install", "playwright"])
            
            # 安裝瀏覽器
            subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
            
            print("✓ Playwright 安裝完成")
            return True
            
        except Exception as e:
            print(f"✗ 自動安裝失敗: {e}")
            print("請手動執行: python setup_scraper.py")
            return False

if __name__ == "__main__":
    # 檢查並自動安裝依賴
    if run_installation_check():
        # 執行測試
        asyncio.run(main())
    else:
        print("請先安裝依賴後再執行測試")
