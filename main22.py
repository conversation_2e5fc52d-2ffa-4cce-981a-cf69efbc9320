from datetime import datetime, timedelta
from typing import List, <PERSON><PERSON>
from typing import Optional


def get_date_periods(target_date: Optional[datetime] = None) -> List[Tuple[str, str]]:
    """
    根據指定日期範圍（當月1號 ~ target_date）切割成適當的區間。

    Args:
        target_date: 目標日期 (例如 2025-01-16)

    Returns:
        List[Tuple[str, str]]: 日期區間列表，每個元素為 (開始日期, 結束日期)
    """
    year = target_date.year
    month = target_date.month

    # 取得該月第一天
    first_day = datetime(year, month, 1)
    last_day = target_date  # 只計算到指定的 target_date，而不是整個月份

    # 計算總天數
    days_in_period = (last_day - first_day).days + 1

    # 🔹 設定切割邏輯
    if days_in_period > 27:  # 當 `target_date` 是整個月份，切 4 段
        num_splits = 4
    elif days_in_period > 14:  # 超過 14 天但不足 1 個月，切 2 段
        num_splits = 2
    else:  # 小於 14 天，直接 1 段
        num_splits = 1

    interval = days_in_period // num_splits  # 平均分割
    periods = []
    current = first_day

    for i in range(num_splits):
        # 計算結束日期
        if i == num_splits - 1:  # 最後一段
            end = last_day
        else:
            end = current + timedelta(days=interval - 1)

        if current > end:
            break

        start_str = current.strftime('%Y年%m月%d日')
        end_str = end.strftime('%Y年%m月%d日')

        periods.append((start_str, end_str))

        if end >= last_day:
            break

        current = end + timedelta(days=1)

    return periods



# 計算上個月最後一天
data = datetime.now().replace(day=1) - timedelta(days=1)
data = datetime.strptime(data.strftime('%Y-%m-%d'), '%Y-%m-%d')
print(data)

print(get_date_periods(data))


