2025-04-17 14:45:37,521 - __main__ - INFO - ===== 全聯門店 AARACO 欄位更新工具 =====
2025-04-17 14:45:37,521 - __main__ - INFO - 正在載入資料...
2025-04-17 14:45:37,537 - __main__ - INFO - 
1. 正在分析現有 AARACO 值...
2025-04-17 14:45:37,537 - __main__ - INFO - 分析現有 AARACO 值...
2025-04-17 14:45:37,537 - __main__ - INFO - AARACO 值分析結果 (總筆數: 589):
2025-04-17 14:45:37,537 - __main__ - INFO -   valid: 522 筆 (88.6%)
2025-04-17 14:45:37,537 - __main__ - INFO -   invalid: 24 筆 (4.1%)
2025-04-17 14:45:37,537 - __main__ - INFO -   empty: 6 筆 (1.0%)
2025-04-17 14:45:37,538 - __main__ - INFO -   partial: 37 筆 (6.3%)
2025-04-17 14:45:37,555 - __main__ - INFO - 已產生 AARACO 分析報告，保存到 output\aaraco_analysis_report_20250417_144537.txt 和 output\aaraco_analysis_detail_20250417_144537.csv 檔案
2025-04-17 14:45:37,555 - __main__ - INFO - 
2. 正在讀取地區代碼對應表...
2025-04-17 14:45:37,556 - __main__ - INFO - 從 ASUSER_CMBE.json 成功讀取了 367 筆地區代碼
2025-04-17 14:45:37,556 - __main__ - INFO -    讀取了 367 筆地區代碼
2025-04-17 14:45:37,556 - __main__ - INFO - 
3. 正在進行資料匹配...
2025-04-17 14:45:37,556 - __main__ - INFO - 預處理資料中...
2025-04-17 14:45:37,559 - __main__ - INFO - 預處理完成: 1226 個店號, 1226 個標準化店名, 1226 個標準化地址
2025-04-17 14:45:37,559 - __main__ - INFO - 開始處理 589 筆 ASAA 記錄...
2025-04-17 14:45:39,511 - __main__ - INFO - 匹配方法統計:
2025-04-17 14:45:39,511 - __main__ - INFO -   店號完全匹配: 2 筆 (0.3%)
2025-04-17 14:45:39,511 - __main__ - INFO -   店名模糊匹配: 176 筆 (29.9%)
2025-04-17 14:45:39,511 - __main__ - INFO -   地址模糊匹配: 227 筆 (38.5%)
2025-04-17 14:45:39,511 - __main__ - INFO -   從ASAA地址猜測: 73 筆 (12.4%)
2025-04-17 14:45:39,511 - __main__ - INFO - 匹配完成: 成功匹配 478 筆, 無法匹配 111 筆
2025-04-17 14:45:39,511 - __main__ - INFO -    成功匹配: 478 筆
2025-04-17 14:45:39,511 - __main__ - INFO -    無法匹配: 111 筆
2025-04-17 14:45:39,511 - __main__ - INFO - 
4. 正在產生更新 SQL 語句...
2025-04-17 14:45:39,512 - __main__ - INFO - 產生了 137 筆更新語句
2025-04-17 14:45:39,512 - __main__ - INFO -    產生了 137 筆更新語句
2025-04-17 14:45:39,512 - __main__ - INFO - 
5. 正在產生驗證報告...
2025-04-17 14:45:39,547 - __main__ - INFO - 
所有處理完成。以下是產生的檔案:
2025-04-17 14:45:39,547 - __main__ - INFO - 1. output\aaraco_analysis_report_20250417_144537.txt - 現有 AARACO 值分析報告
2025-04-17 14:45:39,547 - __main__ - INFO - 2. output\aaraco_analysis_detail_20250417_144537.csv - 現有 AARACO 值詳細清單
2025-04-17 14:45:39,547 - __main__ - INFO - 3. output\aaraco_validation_report_20250417_144537.csv - 匹配結果與建議更新報告
2025-04-17 14:45:39,547 - __main__ - INFO - 4. output\aaraco_no_match_report_20250417_144537.csv - 無法匹配的記錄報告
2025-04-17 14:45:39,547 - __main__ - INFO - 5. output\aaraco_update_statistics_20250417_144537.txt - 更新統計資訊
2025-04-17 14:45:39,547 - __main__ - INFO - 
可執行的 SQL 檔案:
2025-04-17 14:45:39,547 - __main__ - INFO - 1. output\update_aaraco_高信心度_20250417_144537.sql - 高信心度(90-100%) 的更新語句
2025-04-17 14:45:39,547 - __main__ - INFO - 2. output\update_aaraco_中信心度_20250417_144537.sql - 中信心度(70-89%) 的更新語句
2025-04-17 14:45:39,547 - __main__ - INFO - 3. output\update_aaraco_低信心度_20250417_144537.sql - 低信心度(50-69%) 的更新語句
2025-04-17 14:45:39,547 - __main__ - INFO - 
建議執行順序:
2025-04-17 14:45:39,547 - __main__ - INFO - 1. 先檢查分析報告和驗證報告，了解整體資料情況
2025-04-17 14:45:39,547 - __main__ - INFO - 2. 先執行高信心度的更新語句進行驗證
2025-04-17 14:45:39,547 - __main__ - INFO - 3. 如果高信心度的更新正確，再考慮中信心度的更新
2025-04-17 14:45:39,547 - __main__ - INFO - 4. 低信心度和極低信心度的更新建議人工檢查後再執行
2025-04-17 15:06:48,636 - __main__ - INFO - ===== 全聯門店 AARACO 欄位更新工具 =====
2025-04-17 15:06:48,636 - __main__ - INFO - 正在載入資料...
2025-04-17 15:06:48,665 - __main__ - INFO - 
1. 正在分析現有 AARACO 值...
2025-04-17 15:06:48,665 - __main__ - INFO - 分析現有 AARACO值...
2025-04-17 15:06:48,666 - __main__ - INFO - AARACO 值分析結果 (總筆數: 2595):
2025-04-17 15:06:48,666 - __main__ - INFO -   valid: 1250 筆 (48.2%)
2025-04-17 15:06:48,666 - __main__ - INFO -   invalid: 26 筆 (1.0%)
2025-04-17 15:06:48,666 - __main__ - INFO -   empty: 1247 筆 (48.1%)
2025-04-17 15:06:48,666 - __main__ - INFO -   partial: 72 筆 (2.8%)
2025-04-17 15:06:48,692 - __main__ - INFO - 已產生 AARACO 分析報告，保存到 output\aaraco_analysis_report_20250417_150648.txt 和 output\aaraco_analysis_detail_20250417_150648.csv 檔案
2025-04-17 15:06:48,692 - __main__ - INFO - 
2. 正在讀取地區代碼對應表...
2025-04-17 15:06:48,696 - __main__ - INFO - 從 ASUSER_CMBE.json 成功讀取了 367 筆地區代碼
2025-04-17 15:06:48,696 - __main__ - INFO -    讀取了 367 筆地區代碼
2025-04-17 15:06:48,696 - __main__ - INFO - 
3. 正在載入手動修正資料...
2025-04-17 15:06:48,697 - __main__ - WARNING - 找不到現有的分析報告，無法載入手動修正
2025-04-17 15:06:48,697 - __main__ - INFO - 
4. 正在進行資料匹配...
2025-04-17 15:06:48,697 - __main__ - INFO - 預處理資料中...
2025-04-17 15:06:48,700 - __main__ - INFO - 預處理完成: 1226 個店號, 1226 個標準化店名, 1226 個標準化地址
2025-04-17 15:06:48,701 - __main__ - INFO - 載入了 0 個手動修正的區域代碼
2025-04-17 15:06:48,701 - __main__ - INFO - 開始處理 2595 筆 ASAA 記錄...
2025-04-17 15:06:53,109 - __main__ - INFO - 已處理 1000/2595 筆記錄 (38.5%)
2025-04-17 15:06:54,534 - __main__ - INFO - 已處理 2000/2595 筆記錄 (77.1%)
2025-04-17 15:06:54,534 - __main__ - INFO - 匹配方法統計:
2025-04-17 15:06:54,534 - __main__ - INFO -   店號完全匹配: 11 筆 (0.4%)
2025-04-17 15:06:54,534 - __main__ - INFO -   店名模糊匹配: 160 筆 (6.2%)
2025-04-17 15:06:54,534 - __main__ - INFO -   地址模糊匹配: 692 筆 (26.7%)
2025-04-17 15:06:54,534 - __main__ - INFO -   從ASAA地址猜測: 290 筆 (11.2%)
2025-04-17 15:06:54,534 - __main__ - INFO - 匹配完成: 成功匹配 1153 筆, 無法匹配 1442 筆
2025-04-17 15:06:54,534 - __main__ - INFO -    成功匹配: 1153 筆
2025-04-17 15:06:54,534 - __main__ - INFO -    無法匹配: 1442 筆
2025-04-17 15:06:54,535 - __main__ - INFO - 
5. 正在產生驗證報告和測試表SQL...
2025-04-17 15:06:54,586 - __main__ - INFO - 
所有處理完成。以下是產生的檔案:
2025-04-17 15:06:54,587 - __main__ - INFO - 1. output\aaraco_analysis_report_20250417_150648.txt - 現有 AARACO 值分析報告
2025-04-17 15:06:54,587 - __main__ - INFO - 2. output\aaraco_analysis_detail_20250417_150648.csv - 現有 AARACO 值詳細清單
2025-04-17 15:06:54,587 - __main__ - INFO - 3. output\aaraco_validation_report_20250417_150648.csv - 匹配結果與建議更新報告
2025-04-17 15:06:54,587 - __main__ - INFO - 4. output\aaraco_no_match_report_20250417_150648.csv - 無法匹配的記錄報告
2025-04-17 15:06:54,587 - __main__ - INFO - 5. output\aaraco_update_statistics_20250417_150648.txt - 更新統計資訊
2025-04-17 15:06:54,587 - __main__ - INFO - 6. output\create_test_table_20250417_150648.sql - 創建測試表SQL
2025-04-17 15:06:54,587 - __main__ - INFO - 7. output\insert_test_data_20250417_150648.sql - 插入測試資料SQL
2025-04-17 15:06:54,587 - __main__ - INFO - 8. output\aaraco_manual_correction_template_20250417_150648.csv - 手動修正模板
2025-04-17 15:06:54,587 - __main__ - INFO - 
使用說明:
2025-04-17 15:06:54,587 - __main__ - INFO - 1. 先執行「創建測試表SQL」創建測試表
2025-04-17 15:06:54,587 - __main__ - INFO - 2. 再執行「插入測試資料SQL」將匹配結果導入測試表
2025-04-17 15:06:54,587 - __main__ - INFO - 3. 檢查測試表中的資料，確認AARACO欄位是否正確
2025-04-17 15:06:54,587 - __main__ - INFO - 4. 如有需要修正的記錄，可修改「手動修正模板」，然後重新執行程式
2025-04-17 15:06:54,587 - __main__ - INFO - 5. 確認無誤後，可以產生正式更新語句應用到正式表中
2025-04-17 15:25:56,533 - __main__ - INFO - ===== 全聯門店 AARACO 欄位更新工具 =====
2025-04-17 15:25:56,533 - __main__ - INFO - 正在載入資料...
2025-04-17 15:25:56,567 - __main__ - INFO - 
1. 正在分析現有 AARACO 值...
2025-04-17 15:25:56,567 - __main__ - INFO - 分析現有 AARACO 值...
2025-04-17 15:25:56,568 - __main__ - INFO - AARACO 值分析結果 (總筆數: 2595):
2025-04-17 15:25:56,568 - __main__ - INFO -   valid: 1250 筆 (48.2%)
2025-04-17 15:25:56,568 - __main__ - INFO -   invalid: 26 筆 (1.0%)
2025-04-17 15:25:56,568 - __main__ - INFO -   empty: 1247 筆 (48.1%)
2025-04-17 15:25:56,568 - __main__ - INFO -   partial: 72 筆 (2.8%)
2025-04-17 15:25:56,590 - __main__ - INFO - 已產生 AARACO 分析報告，保存到 output\aaraco_analysis_report_20250417_152556.txt 和 output\aaraco_analysis_detail_20250417_152556.csv 檔案
2025-04-17 15:25:56,590 - __main__ - INFO - 
2. 正在讀取地區代碼對應表...
2025-04-17 15:25:56,595 - __main__ - INFO - 從 ASUSER_CMBE.json 成功讀取了 367 筆地區代碼
2025-04-17 15:25:56,595 - __main__ - INFO -    讀取了 367 筆地區代碼
2025-04-17 15:25:56,595 - __main__ - INFO - 
3. 正在載入手動修正資料...
2025-04-17 15:25:56,596 - __main__ - WARNING - 找不到現有的分析報告，無法載入手動修正
2025-04-17 15:25:56,596 - __main__ - INFO - 
4. 正在進行資料匹配...
2025-04-17 15:25:56,596 - __main__ - INFO - 預處理資料中...
2025-04-17 15:25:56,600 - __main__ - INFO - 預處理完成: 1226 個店號, 1226 個標準化店名, 1226 個標準化地址
2025-04-17 15:25:56,600 - __main__ - INFO - 載入了 0 個手動修正的區域代碼
2025-04-17 15:25:56,600 - __main__ - INFO - 開始處理 2595 筆 ASAA 記錄...
2025-04-17 15:26:00,811 - __main__ - INFO - 已處理 1000/2595 筆記錄 (38.5%)
2025-04-17 15:26:02,201 - __main__ - INFO - 已處理 2000/2595 筆記錄 (77.1%)
2025-04-17 15:26:02,201 - __main__ - INFO - 匹配方法統計:
2025-04-17 15:26:02,201 - __main__ - INFO -   店號完全匹配: 11 筆 (0.4%)
2025-04-17 15:26:02,201 - __main__ - INFO -   店名模糊匹配: 160 筆 (6.2%)
2025-04-17 15:26:02,201 - __main__ - INFO -   地址模糊匹配: 692 筆 (26.7%)
2025-04-17 15:26:02,201 - __main__ - INFO -   從ASAA地址猜測: 290 筆 (11.2%)
2025-04-17 15:26:02,201 - __main__ - INFO - 匹配完成: 成功匹配 1153 筆, 無法匹配 1442 筆
2025-04-17 15:26:02,201 - __main__ - INFO -    成功匹配: 1153 筆
2025-04-17 15:26:02,201 - __main__ - INFO -    無法匹配: 1442 筆
2025-04-17 15:26:02,201 - __main__ - INFO - 
5. 正在產生驗證報告和測試表SQL...
2025-04-17 15:26:02,261 - __main__ - INFO - 
所有處理完成。以下是產生的檔案:
2025-04-17 15:26:02,261 - __main__ - INFO - 1. output\aaraco_analysis_report_20250417_152556.txt - 現有 AARACO 值分析報告
2025-04-17 15:26:02,261 - __main__ - INFO - 2. output\aaraco_analysis_detail_20250417_152556.csv - 現有 AARACO 值詳細清單
2025-04-17 15:26:02,261 - __main__ - INFO - 3. output\aaraco_validation_report_20250417_152556.csv - 匹配結果與建議更新報告
2025-04-17 15:26:02,261 - __main__ - INFO - 4. output\aaraco_no_match_report_20250417_152556.csv - 無法匹配的記錄報告
2025-04-17 15:26:02,261 - __main__ - INFO - 5. output\aaraco_update_statistics_20250417_152556.txt - 更新統計資訊
2025-04-17 15:26:02,261 - __main__ - INFO - 6. output\create_test_table_20250417_152556.sql - 創建測試表SQL
2025-04-17 15:26:02,261 - __main__ - INFO - 7. output\insert_test_data_20250417_152556.sql - 插入測試資料SQL
2025-04-17 15:26:02,261 - __main__ - INFO - 8. output\aaraco_manual_correction_template_20250417_152556.csv - 手動修正模板
2025-04-17 15:26:02,261 - __main__ - INFO - 
使用說明:
2025-04-17 15:26:02,261 - __main__ - INFO - 1. 先執行「創建測試表SQL」創建測試表
2025-04-17 15:26:02,261 - __main__ - INFO - 2. 再執行「插入測試資料SQL」將匹配結果導入測試表
2025-04-17 15:26:02,261 - __main__ - INFO - 3. 檢查測試表中的資料，確認AARACO欄位是否正確
2025-04-17 15:26:02,261 - __main__ - INFO - 4. 如有需要修正的記錄，可修改「手動修正模板」，然後重新執行程式
2025-04-17 15:26:02,261 - __main__ - INFO - 5. 確認無誤後，可以產生正式更新語句應用到正式表中
2025-04-17 15:40:22,192 - __main__ - INFO - ===== 全聯門店 AARACO 欄位更新工具 =====
2025-04-17 15:40:22,192 - __main__ - INFO - 正在載入資料...
2025-04-17 15:40:22,240 - __main__ - INFO - 
1. 正在分析現有 AARACO 值...
2025-04-17 15:40:22,240 - __main__ - INFO - 分析現有 AARACO 值...
2025-04-17 15:40:22,241 - __main__ - INFO - AARACO 值分析結果 (總筆數: 2595):
2025-04-17 15:40:22,241 - __main__ - INFO -   valid: 1250 筆 (48.2%)
2025-04-17 15:40:22,241 - __main__ - INFO -   invalid: 26 筆 (1.0%)
2025-04-17 15:40:22,241 - __main__ - INFO -   empty: 1247 筆 (48.1%)
2025-04-17 15:40:22,241 - __main__ - INFO -   partial: 72 筆 (2.8%)
2025-04-17 15:40:22,266 - __main__ - INFO - 已產生 AARACO 分析報告，保存到 output\aaraco_analysis_report_20250417_154022.txt 和 output\aaraco_analysis_detail_20250417_154022.csv 檔案
2025-04-17 15:40:22,266 - __main__ - INFO - 
2. 正在讀取地區代碼對應表...
2025-04-17 15:40:22,270 - __main__ - INFO - 從 ASUSER_CMBE.json 成功讀取了 367 筆地區代碼
2025-04-17 15:40:22,270 - __main__ - INFO -    讀取了 367 筆地區代碼
2025-04-17 15:40:22,270 - __main__ - INFO - 
3. 正在載入手動修正資料...
2025-04-17 15:40:22,270 - __main__ - WARNING - 找不到現有的分析報告，無法載入手動修正
2025-04-17 15:40:22,271 - __main__ - INFO - 
4. 正在進行資料匹配...
2025-04-17 15:40:22,271 - __main__ - INFO - 預處理資料中...
2025-04-17 15:40:22,275 - __main__ - INFO - 預處理完成: 1226 個店號, 1226 個標準化店名, 1226 個標準化地址
2025-04-17 15:40:22,275 - __main__ - INFO - 載入了 0 個手動修正的區域代碼
2025-04-17 15:40:22,276 - __main__ - INFO - 開始處理 2595 筆 ASAA 記錄...
2025-04-17 15:40:28,546 - __main__ - INFO - 已處理 1000/2595 筆記錄 (38.5%)
2025-04-17 15:40:29,953 - __main__ - INFO - 已處理 2000/2595 筆記錄 (77.1%)
2025-04-17 15:40:29,954 - __main__ - INFO - 匹配方法統計:
2025-04-17 15:40:29,954 - __main__ - INFO -   店號完全匹配: 11 筆 (0.4%)
2025-04-17 15:40:29,954 - __main__ - INFO -   店名模糊匹配: 160 筆 (6.2%)
2025-04-17 15:40:29,955 - __main__ - INFO -   地址模糊匹配: 692 筆 (26.7%)
2025-04-17 15:40:29,955 - __main__ - INFO -   從ASAA地址猜測: 290 筆 (11.2%)
2025-04-17 15:40:29,955 - __main__ - INFO - 匹配完成: 成功匹配 1153 筆, 無法匹配 1442 筆
2025-04-17 15:40:29,955 - __main__ - INFO -    成功匹配: 1153 筆
2025-04-17 15:40:29,955 - __main__ - INFO -    無法匹配: 1442 筆
2025-04-17 15:40:29,955 - __main__ - INFO - 
5. 正在產生驗證報告和測試表SQL...
2025-04-17 15:40:30,019 - __main__ - INFO - 
所有處理完成。以下是產生的檔案:
2025-04-17 15:40:30,019 - __main__ - INFO - 1. output\aaraco_analysis_report_20250417_154022.txt - 現有 AARACO 值分析報告
2025-04-17 15:40:30,019 - __main__ - INFO - 2. output\aaraco_analysis_detail_20250417_154022.csv - 現有 AARACO 值詳細清單
2025-04-17 15:40:30,019 - __main__ - INFO - 3. output\aaraco_validation_report_20250417_154022.csv - 匹配結果與建議更新報告
2025-04-17 15:40:30,019 - __main__ - INFO - 4. output\aaraco_no_match_report_20250417_154022.csv - 無法匹配的記錄報告
2025-04-17 15:40:30,019 - __main__ - INFO - 5. output\aaraco_update_statistics_20250417_154022.txt - 更新統計資訊
2025-04-17 15:40:30,019 - __main__ - INFO - 6. output\create_test_table_20250417_154022.sql - 創建測試表SQL
2025-04-17 15:40:30,019 - __main__ - INFO - 7. output\insert_test_data_20250417_154022.sql - 插入測試資料SQL
2025-04-17 15:40:30,019 - __main__ - INFO - 8. output\aaraco_manual_correction_template_20250417_154022.csv - 手動修正模板
2025-04-17 15:40:30,019 - __main__ - INFO - 
使用說明:
2025-04-17 15:40:30,019 - __main__ - INFO - 1. 先執行「創建測試表SQL」創建測試表
2025-04-17 15:40:30,019 - __main__ - INFO - 2. 再執行「插入測試資料SQL」將匹配結果導入測試表
2025-04-17 15:40:30,019 - __main__ - INFO - 3. 檢查測試表中的資料，確認AARACO欄位是否正確
2025-04-17 15:40:30,019 - __main__ - INFO - 4. 如有需要修正的記錄，可修改「手動修正模板」，然後重新執行程式
2025-04-17 15:40:30,019 - __main__ - INFO - 5. 確認無誤後，可以產生正式更新語句應用到正式表中
2025-04-17 15:54:07,240 - __main__ - INFO - ===== 全聯門店 AARACO 欄位更新工具 =====
2025-04-17 15:54:07,240 - __main__ - INFO - 正在載入資料...
2025-04-17 15:54:07,268 - __main__ - INFO - 
1. 正在分析現有 AARACO 值...
2025-04-17 15:54:07,268 - __main__ - INFO - 分析現有 AARACO 值...
2025-04-17 15:54:07,269 - __main__ - INFO - AARACO 值分析結果 (總筆數: 2595):
2025-04-17 15:54:07,269 - __main__ - INFO -   valid: 1250 筆 (48.2%)
2025-04-17 15:54:07,269 - __main__ - INFO -   invalid: 26 筆 (1.0%)
2025-04-17 15:54:07,269 - __main__ - INFO -   empty: 1247 筆 (48.1%)
2025-04-17 15:54:07,269 - __main__ - INFO -   partial: 72 筆 (2.8%)
2025-04-17 15:54:07,294 - __main__ - INFO - 已產生 AARACO 分析報告，保存到 output\aaraco_analysis_report_20250417_155407.txt 和 output\aaraco_analysis_detail_20250417_155407.csv 檔案
2025-04-17 15:54:07,295 - __main__ - INFO - 
2. 正在讀取地區代碼對應表...
2025-04-17 15:54:07,299 - __main__ - INFO - 從 ASUSER_CMBE.json 成功讀取了 367 筆地區代碼
2025-04-17 15:54:07,299 - __main__ - INFO -    讀取了 367 筆地區代碼
2025-04-17 15:54:07,299 - __main__ - INFO - 
3. 正在載入手動修正資料...
2025-04-17 15:54:07,300 - __main__ - WARNING - 找不到現有的分析報告，無法載入手動修正
2025-04-17 15:54:07,300 - __main__ - INFO - 
4. 正在進行資料匹配...
2025-04-17 15:54:07,300 - __main__ - INFO - 預處理資料中...
2025-04-17 15:54:07,304 - __main__ - INFO - 預處理完成: 1226 個店號, 1226 個標準化店名, 1226 個標準化地址
2025-04-17 15:54:07,304 - __main__ - INFO - 載入了 0 個手動修正的區域代碼
2025-04-17 15:54:07,305 - __main__ - INFO - 開始處理 2595 筆 ASAA 記錄...
2025-04-17 15:54:11,558 - __main__ - INFO - 已處理 1000/2595 筆記錄 (38.5%)
2025-04-17 15:54:12,865 - __main__ - INFO - 已處理 2000/2595 筆記錄 (77.1%)
2025-04-17 15:54:12,865 - __main__ - INFO - 匹配方法統計:
2025-04-17 15:54:12,865 - __main__ - INFO -   店號完全匹配: 11 筆 (0.4%)
2025-04-17 15:54:12,865 - __main__ - INFO -   店名模糊匹配: 160 筆 (6.2%)
2025-04-17 15:54:12,865 - __main__ - INFO -   地址模糊匹配: 692 筆 (26.7%)
2025-04-17 15:54:12,865 - __main__ - INFO -   從ASAA地址猜測: 290 筆 (11.2%)
2025-04-17 15:54:12,866 - __main__ - INFO - 匹配完成: 成功匹配 1153 筆, 無法匹配 1442 筆
2025-04-17 15:54:12,866 - __main__ - INFO -    成功匹配: 1153 筆
2025-04-17 15:54:12,866 - __main__ - INFO -    無法匹配: 1442 筆
2025-04-17 15:54:12,866 - __main__ - INFO - 
5. 正在產生驗證報告和測試表SQL...
2025-04-17 15:54:12,934 - __main__ - INFO - 
所有處理完成。以下是產生的檔案:
2025-04-17 15:54:12,934 - __main__ - INFO - 1. output\aaraco_analysis_report_20250417_155407.txt - 現有 AARACO 值分析報告
2025-04-17 15:54:12,934 - __main__ - INFO - 2. output\aaraco_analysis_detail_20250417_155407.csv - 現有 AARACO 值詳細清單
2025-04-17 15:54:12,934 - __main__ - INFO - 3. output\aaraco_validation_report_20250417_155407.csv - 匹配結果與建議更新報告
2025-04-17 15:54:12,934 - __main__ - INFO - 4. output\aaraco_no_match_report_20250417_155407.csv - 無法匹配的記錄報告
2025-04-17 15:54:12,934 - __main__ - INFO - 5. output\aaraco_update_statistics_20250417_155407.txt - 更新統計資訊
2025-04-17 15:54:12,934 - __main__ - INFO - 6. output\create_test_table_20250417_155407.sql - 創建測試表SQL
2025-04-17 15:54:12,934 - __main__ - INFO - 7. output\insert_test_data_20250417_155407.sql - 插入測試資料SQL
2025-04-17 15:54:12,934 - __main__ - INFO - 8. output\aaraco_manual_correction_template_20250417_155407.csv - 手動修正模板
2025-04-17 15:54:12,934 - __main__ - INFO - 
使用說明:
2025-04-17 15:54:12,934 - __main__ - INFO - 1. 先執行「創建測試表SQL」創建測試表
2025-04-17 15:54:12,934 - __main__ - INFO - 2. 再執行「插入測試資料SQL」將匹配結果導入測試表
2025-04-17 15:54:12,934 - __main__ - INFO - 3. 檢查測試表中的資料，確認AARACO欄位是否正確
2025-04-17 15:54:12,934 - __main__ - INFO - 4. 如有需要修正的記錄，可修改「手動修正模板」，然後重新執行程式
2025-04-17 15:54:12,934 - __main__ - INFO - 5. 確認無誤後，可以產生正式更新語句應用到正式表中
2025-04-17 15:59:40,966 - __main__ - INFO - ===== 全聯門店 AARACO 欄位更新工具 =====
2025-04-17 15:59:40,967 - __main__ - INFO - 正在載入資料...
2025-04-17 15:59:40,998 - __main__ - INFO - 
1. 正在分析現有 AARACO 值...
2025-04-17 15:59:40,998 - __main__ - INFO - 分析現有 AARACO 值...
2025-04-17 15:59:40,998 - __main__ - INFO - AARACO 值分析結果 (總筆數: 2595):
2025-04-17 15:59:40,998 - __main__ - INFO -   valid: 1250 筆 (48.2%)
2025-04-17 15:59:40,998 - __main__ - INFO -   invalid: 26 筆 (1.0%)
2025-04-17 15:59:40,998 - __main__ - INFO -   empty: 1247 筆 (48.1%)
2025-04-17 15:59:40,999 - __main__ - INFO -   partial: 72 筆 (2.8%)
2025-04-17 15:59:41,024 - __main__ - INFO - 已產生 AARACO 分析報告，保存到 output\aaraco_analysis_report_20250417_155940.txt 和 output\aaraco_analysis_detail_20250417_155940.csv 檔案
2025-04-17 15:59:41,024 - __main__ - INFO - 
2. 正在讀取地區代碼對應表...
2025-04-17 15:59:41,029 - __main__ - INFO - 從 ASUSER_CMBE.json 成功讀取了 367 筆地區代碼
2025-04-17 15:59:41,029 - __main__ - INFO -    讀取了 367 筆地區代碼
2025-04-17 15:59:41,029 - __main__ - INFO - 
3. 正在載入手動修正資料...
2025-04-17 15:59:41,031 - __main__ - WARNING - 找不到現有的分析報告，無法載入手動修正
2025-04-17 15:59:41,031 - __main__ - INFO - 
4. 正在進行資料匹配...
2025-04-17 15:59:41,031 - __main__ - INFO - 預處理資料中...
2025-04-17 15:59:41,034 - __main__ - INFO - 預處理完成: 1226 個店號, 1226 個標準化店名, 1226 個標準化地址
2025-04-17 15:59:41,034 - __main__ - INFO - 載入了 0 個手動修正的區域代碼
2025-04-17 15:59:41,034 - __main__ - INFO - 開始處理 2595 筆 ASAA 記錄...
2025-04-17 15:59:45,936 - __main__ - INFO - 已處理 1000/2595 筆記錄 (38.5%)
2025-04-17 15:59:48,872 - __main__ - INFO - 已處理 2000/2595 筆記錄 (77.1%)
2025-04-17 15:59:48,873 - __main__ - INFO - 匹配方法統計:
2025-04-17 15:59:48,873 - __main__ - INFO -   店號完全匹配: 11 筆 (0.4%)
2025-04-17 15:59:48,873 - __main__ - INFO -   店名模糊匹配: 160 筆 (6.2%)
2025-04-17 15:59:48,873 - __main__ - INFO -   地址模糊匹配: 692 筆 (26.7%)
2025-04-17 15:59:48,873 - __main__ - INFO -   從ASAA地址猜測: 290 筆 (11.2%)
2025-04-17 15:59:48,873 - __main__ - INFO - 匹配完成: 成功匹配 1153 筆, 無法匹配 1442 筆
2025-04-17 15:59:48,873 - __main__ - INFO -    成功匹配: 1153 筆
2025-04-17 15:59:48,873 - __main__ - INFO -    無法匹配: 1442 筆
2025-04-17 15:59:48,873 - __main__ - INFO - 
5. 正在產生驗證報告和測試表SQL...
2025-04-17 15:59:48,968 - __main__ - INFO - 
所有處理完成。以下是產生的檔案:
2025-04-17 15:59:48,969 - __main__ - INFO - 1. output\aaraco_analysis_report_20250417_155940.txt - 現有 AARACO 值分析報告
2025-04-17 15:59:48,969 - __main__ - INFO - 2. output\aaraco_analysis_detail_20250417_155940.csv - 現有 AARACO 值詳細清單
2025-04-17 15:59:48,969 - __main__ - INFO - 3. output\aaraco_validation_report_20250417_155940.csv - 匹配結果與建議更新報告
2025-04-17 15:59:48,969 - __main__ - INFO - 4. output\aaraco_no_match_report_20250417_155940.csv - 無法匹配的記錄報告
2025-04-17 15:59:48,969 - __main__ - INFO - 5. output\aaraco_update_statistics_20250417_155940.txt - 更新統計資訊
2025-04-17 15:59:48,969 - __main__ - INFO - 6. output\create_test_table_20250417_155940.sql - 創建測試表SQL
2025-04-17 15:59:48,969 - __main__ - INFO - 7. output\insert_test_data_20250417_155940.sql - 插入測試資料SQL
2025-04-17 15:59:48,969 - __main__ - INFO - 8. output\aaraco_manual_correction_template_20250417_155940.csv - 手動修正模板
2025-04-17 15:59:48,969 - __main__ - INFO - 
使用說明:
2025-04-17 15:59:48,969 - __main__ - INFO - 1. 先執行「創建測試表SQL」創建測試表
2025-04-17 15:59:48,969 - __main__ - INFO - 2. 再執行「插入測試資料SQL」將匹配結果導入測試表
2025-04-17 15:59:48,969 - __main__ - INFO - 3. 檢查測試表中的資料，確認AARACO欄位是否正確
2025-04-17 15:59:48,969 - __main__ - INFO - 4. 如有需要修正的記錄，可修改「手動修正模板」，然後重新執行程式
2025-04-17 15:59:48,969 - __main__ - INFO - 5. 確認無誤後，可以產生正式更新語句應用到正式表中
