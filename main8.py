import pandas as pd
from fuzzywuzzy import process

# 讀取Excel文件
file_path = r'C:\Users\<USER>\Documents\data.xlsx'
df = pd.read_excel(file_path, header=None, names=['品號', '名稱', '查找名稱'])

# 過濾掉非字符串的值
df['名稱'] = df['名稱'].astype(str)
df['查找名稱'] = df['查找名稱'].astype(str)

# 定義一個模糊匹配的函數
def fuzzy_match(lookup_value, choices, threshold=80):
    result = process.extractOne(lookup_value, choices)
    return result if result and result[1] >= threshold else None

# 對每個查找名稱進行模糊匹配，並返回對應的代號
matched_ids = []
for item in df['查找名稱']:
    match = fuzzy_match(item, df['名稱'].tolist())
    matched_id = df[df['名稱'] == match[0]]['品號'].values[0] if match else None
    matched_ids.append(matched_id)

# 將匹配結果放在D列
df['匹配代號'] = matched_ids

# 將結果寫回Excel文件
output_file_path = r'C:\Users\<USER>\Documents\output_data.xlsx'
df.to_excel(output_file_path, index=False)

print(f'匹配結果已寫入 {output_file_path}')
