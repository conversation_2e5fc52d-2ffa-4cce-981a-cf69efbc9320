# C# STM32 Bootloader 模擬測試功能說明

## 概述
我已經在 C# 專案中添加了完整的模擬測試功能，您可以在不連接實際硬體的情況下測試整個燒錄流程。

## 新增功能

### 1. UI 改動
- 在主界面添加了綠色的「模擬測試」按鈕
- 位置：載入 HEX 檔案按鈕下方
- 功能：執行完整的燒錄流程模擬

### 2. 核心類別

#### MockSerialPort.cs
模擬串口通訊，包含：
- 完整的 STM32 Bootloader 協議響應
- 256KB 模擬 Flash 記憶體
- 所有命令的正確 ACK/NACK 響應
- 詳細的數據記錄

#### SimulatedBootloaderTest.cs
執行測試流程，包含：
- 載入並解析實際的 HEX 檔案
- 執行完整的燒錄步驟
- 生成詳細的測試報告
- 實時進度更新

## 使用方法

### 步驟 1：載入 HEX 檔案
```
1. 點擊「載入燒錄 Hex檔案」
2. 選擇要測試的 .hex 檔案
```

### 步驟 2：執行模擬測試
```
1. 點擊綠色的「模擬測試」按鈕
2. 確認對話框中點擊「是」
3. 等待測試完成
```

### 步驟 3：查看結果
```
1. 測試完成後會彈出結果視窗
2. 可以查看詳細的測試日誌
3. 測試報告自動保存在 HEX 檔案目錄
```

## 測試流程詳情

### 1. 進入 Bootloader
```
→ 發送: 01 0C B2 31 32 33 34 35 36 37 38 39 3A B5  (自定義命令)
← 接收: (等待 MCU 重啟)
→ 發送: 7F  (同步字節)
← 接收: 79  (ACK)
```

### 2. 擦除 Flash
```
→ 發送: 43 BC  (擦除命令)
← 接收: 79  (ACK)
→ 發送: FF 00  (全局擦除)
← 接收: 79  (ACK)
```

### 3. 寫入數據
```
→ 發送: 31 CE  (寫入命令)
← 接收: 79  (ACK)
→ 發送: 08 00 00 00 08  (地址 + 校驗和)
← 接收: 79  (ACK)
→ 發送: FF [256字節數據] [校驗和]
← 接收: 79  (ACK)
```

## 測試報告內容

生成的測試報告包含：
```
================================================================================
                    STM32 Bootloader 測試報告
================================================================================
測試時間: 2024-01-XX XX:XX:XX
HEX 檔案: firmware.hex
測試結果: 通過

測試摘要:
--------
- 串口連接測試: 通過
- 進入 Bootloader: 通過
- 獲取設備信息: 通過
- 擦除 Flash: 通過
- 寫入程序: 通過 (10 個數據塊)
- 驗證程序: 通過
- 執行程序: 通過

詳細日誌:
--------
[完整的測試日誌...]

協議交互記錄:
------------
[所有發送和接收的數據...]
```

## 與 Android 比對

使用這個模擬測試，您可以：

1. **比對命令格式**
   - C# 使用的確切命令序列
   - 每個命令的參數和校驗和

2. **比對時序**
   - 各個步驟之間的延遲
   - 等待響應的超時時間

3. **比對數據處理**
   - HEX 檔案的解析方式
   - 數據分塊的大小（256 字節）
   - 地址計算方式

4. **找出差異**
   - Android 是否使用相同的命令格式？
   - 數據包的結構是否一致？
   - 校驗和計算是否相同？

## 建議的除錯步驟

1. **運行 C# 模擬測試**，保存測試報告
2. **運行 Android 應用**，記錄實際發送的數據
3. **比對兩者的差異**：
   - 進入 Bootloader 命令是否相同？
   - 寫入數據的格式是否一致？
   - 響應處理是否正確？

4. **重點檢查**：
   - C# 的進入 Bootloader 命令確實是 `01 0C B2 31...`
   - Android 是否也發送相同的命令？
   - 如果不同，需要修改 Android 的實現

這個模擬測試工具能幫助您快速找出 Android 實現的問題所在！