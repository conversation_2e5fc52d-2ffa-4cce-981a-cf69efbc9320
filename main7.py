import win32com.client as win32
import re
import cx_Oracle
import openpyxl
from openpyxl import Workbook


def is_black_or_automatic(font):
    """
    檢查字體顏色是否為黑色或自動（通常表示為黑色）。
    """
    return font.Color == win32.constants.wdColorAutomatic or font.Color == 0 or font.Color == -16777216


def get_cell_text_and_color(table, row, col):
    """
    嘗試從指定的行和列獲取單元格文本，並檢查字體顏色是否為黑色。
    如果單元格合併並且無法訪問，將捕獲異常並返回None及顏色檢查結果。
    """
    try:
        cell = table.Cell(Row=row, Column=col)
        text = cell.Range.Text.strip(chr(7)).strip()  # 清除單元格結束字符
        is_black = is_black_or_automatic(cell.Range.Font)
        return text, is_black
    except Exception:
        return None, False


def query_product_code(cursor, rout, product_number):
    """
    根據貨號查詢品號。
    """
    query = "SELECT APITNO FROM PROD_CODE_MAP WHERE APROUT = :qrout AND APOHIT = :product_number"
    cursor.execute(query, qrout=rout, product_number=product_number)
    result = cursor.fetchone()
    return result[0] if result else None


def extract_data_from_table(table, rout, cursor):
    """
    從給定的表格中提取所有行的產品代碼和價格，僅考慮黑色字體的價格。
    增加檢查以確保貨號只包含數字，並將貨號轉換為實際品號。
    """
    data = []
    previous_product_code = None
    previous_price = None

    for row_index in range(1, table.Rows.Count + 1):
        product_code_text, is_black = get_cell_text_and_color(table, row_index, 1)

        # 檢查貨號是否只包含數字
        if product_code_text and re.match(r'^\d+$', product_code_text):
            product_number = product_code_text
            product_code = query_product_code(cursor, rout, product_number)
        else:
            continue

        price_text, is_black = get_cell_text_and_color(table, row_index, 3)
        price = None
        unit = None
        if price_text:
            price_match = re.search(r'(\d+)\s*(元/箱)', price_text)
            if price_match:
                price = price_match.group(1)
                unit = price_match.group(2)

        price = price or previous_price
        unit = unit or '元/箱'

        if product_code and price:
            data.append([product_code, price, unit])
            previous_product_code, previous_price = product_code, price

    return data


def extract_table_data(doc, rout, cursor):
    """
    處理文檔中的每個表格，從中提取產品代碼和價格。
    """
    data = []
    for table in doc.Tables:
        data.extend(extract_data_from_table(table, rout, cursor))
    return data


def save_to_excel(data, file_path):
    """
    將提取的數據保存到Excel文件中。
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "產品價格"

    # 添加標題
    ws.append(["品號", "價格", "單位"])

    # 添加數據
    for entry in data:
        ws.append(entry)

    wb.save(file_path)


def main():
    dsn_tns = cx_Oracle.makedsn('172.20.160.34', '1521', service_name='WEB')
    connection = cx_Oracle.connect(user='B2B', password='B2B', dsn=dsn_tns)

    word = win32.gencache.EnsureDispatch('Word.Application')
    word.Visible = False

    try:
        cursor = connection.cursor()
        doc_path = r'C:\Users\<USER>\Downloads\113年大潤發4072+4081中元檔.docx'
        rout = '10106'
        doc = word.Documents.Open(doc_path)

        table_data = extract_table_data(doc, rout, cursor)

        # 儲存到Excel
        excel_path = r'C:\Users\<USER>\Downloads\產品價格表.xlsx'
        save_to_excel(table_data, excel_path)

        doc.Close(False)
    finally:
        word.Quit()
        cursor.close()
        connection.close()


if __name__ == "__main__":
    main()
