# Gitea LDAP 問題修正指南

## 🔍 問題分析

經過檢查 `/admin/auths/2`，發現設定基本正確，但測試登入時出現「帳號或密碼不正確」錯誤。

## 🛠️ 修正方案

### 方案 1：清空使用者篩選器（推薦）

在 simple auth 模式下，使用者篩選器可能會干擾認證流程。

**修正步驟：**
1. 進入 http://172.20.160.11:3100/admin/auths/2
2. 找到「使用者篩選器」欄位
3. **清空該欄位**（完全留空）
4. 點擊「更新認證來源」

### 方案 2：改回 LDAP (via BindDN) 模式

如果方案 1 無效，建議改回使用服務帳號的方式。

## 🧪 測試步驟

### 1. Python 測試
```bash
python test_simple_ldap.py
```

### 2. Gitea 測試
- 帳號：16613
- 密碼：公司密碼

## 📋 完整設定檢查清單

### 認證來源 #2 應該設定為：
```
認證類型: LDAP (simple auth)
認證名稱: 黑松 LDAP Simple
主機地址: HSWDC00.heysong.com.tw
連接埠: 389
安全協定: Unencrypted
用戶 DN: %<EMAIL>
使用者篩選器: (留空) ← 重要！
電子郵件屬性: userPrincipalName
名字屬性: givenName
姓氏屬性: sn
該認證來源已啟用: ✓
```

## 🔄 替代方案

如果 simple auth 持續有問題，建議：

1. **申請服務帳號**
   - 聯繫 IT 部門
   - 使用之前提供的申請範本

2. **改用 LDAP (via BindDN)**
   - 更穩定可靠
   - 功能更完整

## 📞 下一步

1. 先嘗試清空使用者篩選器
2. 如果還是無法登入，建議申請服務帳號
3. 可以同時保留兩個認證來源進行測試

## 🎯 預期結果

修正後，員工應該能夠：
- 使用公司帳號（如：16613）
- 使用公司密碼
- 成功登入 Gitea
- 自動建立使用者帳號
