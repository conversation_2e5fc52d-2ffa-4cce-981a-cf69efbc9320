# STM32 Bootloader 模擬器使用說明

## 概述
此模擬器用於驗證 Android 應用的韌體上傳功能，模擬 STM32 板子的 Bootloader 行為。

## 文件說明
- `stm32_bootloader_simulator.py` - GUI 版本（需要 tkinter）
- `stm32_simulator_cli.py` - 命令行版本（推薦使用）
- `test_protocol_parsing.py` - 協議解析測試工具
- `run_simulator.bat` - Windows 啟動腳本
- `run_simulator.sh` - Linux/macOS 啟動腳本

## 安裝依賴
```bash
pip install pyserial
```

## 使用方法

### 1. 啟動模擬器

**Windows:**
```cmd
run_simulator.bat
```

**Linux/macOS:**
```bash
./run_simulator.sh
```

**或直接運行:**
```bash
python stm32_simulator_cli.py
```

### 2. 選擇串口
模擬器會列出所有可用的串口，選擇與 Android 設備連接的串口。

### 3. Android 端測試

1. 在 Android 應用中連接到相同的串口
2. 選擇「韌體更新」→「測試協議包生成」
3. 觀察模擬器的接收日誌

## 協議說明

### 自定義協議格式
```
[STX][LEN][CMD][DATA][CHECKSUM]
```
- STX: 0x01 (起始標記)
- LEN: 從 LEN 自己到 CHECKSUM 的字節數
- CMD: 命令字節
- DATA: 數據內容（可選）
- CHECKSUM: XOR 校驗和（不包含 STX）

### 進入 Bootloader 命令
```
01 0D B2 31 32 33 34 35 36 37 38 39 3A B4
```
- STX: 0x01
- LEN: 0x0D (13字節)
- CMD: 0xB2
- DATA: "123456789:" (ASCII)
- CHECKSUM: 0xB4

### STM32 標準 Bootloader 協議
進入 Bootloader 後，使用標準 STM32 協議：
- 同步字節: 0x7F
- ACK: 0x79
- NACK: 0x1F
- 命令格式: [CMD][~CMD]

## 問題修復記錄

### 1. 包長度計算錯誤
**問題**: Python 模擬器期望 `total_length = 3 + length`
**修復**: 改為 `total_length = 1 + length`

修復位置: `stm32_bootloader_simulator.py` 第 218 行
```python
# 錯誤的計算
total_length = 3 + length  # STX + LEN + CMD + DATA + CHECKSUM

# 正確的計算
total_length = 1 + length  # STX + (LEN包含的字節數)
```

### 2. Android 發送 HEX 文本問題
**問題**: Android 發送 HEX 文件內容作為文本而非二進制
**修復**: 創建 `IntelHexParser.java` 解析 HEX 文件

## 測試流程

1. 啟動 Python 模擬器
2. 在 Android 應用中執行「測試協議包生成」
3. 檢查模擬器輸出：
   ```
   ✅ 收到正確的進入 Bootloader 命令
   🔄 模擬設備重啟，進入 Bootloader 模式...
   ⏳ 等待 STM32 同步字節 (0x7F)...
   ```
4. Android 發送同步字節 0x7F
5. 模擬器回應 ACK (0x79)
6. 進入 STM32 Bootloader 模式

## 常見問題

### Q: 模擬器顯示「校驗和錯誤」
A: 檢查 Android 端的校驗和計算是否正確，應該是 XOR 所有字節（不包含 STX）

### Q: 模擬器不接收數據
A: 
1. 確認串口連接正確
2. 檢查波特率設置（默認 115200）
3. 確認 Android 端已連接並發送數據

### Q: GUI 版本無法運行
A: 使用命令行版本 `stm32_simulator_cli.py`，不需要 tkinter

## 調試技巧

1. 使用 `test_protocol_parsing.py` 測試協議解析
2. 在 Android 端使用「測試協議包生成」功能查看詳細輸出
3. 檢查模擬器日誌中的十六進制數據