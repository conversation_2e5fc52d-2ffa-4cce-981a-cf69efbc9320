from bs4 import BeautifulSoup
from docx import Document
from docx.shared import Pt, Cm
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
import os
import subprocess


def preprocess_html_for_code_blocks(html_file_path, output_file_path=None):
    """預處理 HTML 檔案，增強程式碼區塊轉換效果"""
    if output_file_path is None:
        output_file_path = html_file_path.replace('.html', '_processed.html')

    # 讀取 HTML 檔案
    with open(html_file_path, "r", encoding="utf-8") as f:
        html_content = f.read()

    # 使用 BeautifulSoup 解析 HTML
    soup = BeautifulSoup(html_content, "html.parser")

    # 處理程式碼區塊
    for code_element in soup.find_all(['pre', 'code']):
        # 為程式碼區塊添加樣式
        if not code_element.has_attr("style"):
            code_element["style"] = ""

        code_element[
            "style"] += "background-color: #f0f0f0; font-family: monospace; padding: 10px; border: 1px solid #ccc; white-space: pre-wrap; color: #333;"

        # 確保程式碼保留空格和換行
        if code_element.name == 'pre' and not code_element.find('code'):
            code_text = code_element.get_text()
            code_element.clear()
            code_element.string = code_text

    # 處理表格
    for table in soup.find_all('table'):
        if not table.has_attr("border") or table["border"] == "0":
            table["border"] = "1"

        for cell in table.find_all(["td", "th"]):
            if not cell.has_attr("style"):
                cell["style"] = ""
            cell["style"] += "border: 1px solid black; padding: 5px;"

    # 保存預處理後的 HTML
    with open(output_file_path, "w", encoding="utf-8") as f:
        f.write(str(soup))

    return output_file_path


def convert_with_htmldocx(html_file_path, docx_file_path, process_code_blocks=True):
    """使用 htmldocx 將 HTML 轉換為 DOCX，增強程式碼區塊處理"""
    try:
        from htmldocx import HtmlToDocx
    except ImportError:
        print("錯誤：未安裝 htmldocx 套件，請執行 pip install htmldocx")
        return False

    # 檢查檔案是否存在
    if not os.path.exists(html_file_path):
        print(f"錯誤：找不到 HTML 檔案 {html_file_path}")
        return False

    # 預處理 HTML 以增強程式碼區塊
    if process_code_blocks:
        processed_html_path = preprocess_html_for_code_blocks(html_file_path)
        html_path_to_use = processed_html_path
    else:
        html_path_to_use = html_file_path

    # 讀取 HTML 檔案
    with open(html_path_to_use, "r", encoding="utf-8") as f:
        html_content = f.read()

    # 建立 Word 文件
    doc = Document()

    # 設置頁面邊距
    section = doc.sections[0]
    section.left_margin = Cm(2.54)
    section.right_margin = Cm(2.54)
    section.top_margin = Cm(2.54)
    section.bottom_margin = Cm(2.54)

    # 將 HTML 轉換為 DOCX
    html_to_docx = HtmlToDocx()
    html_to_docx.add_html_to_document(html_content, doc)

    # 後處理程式碼區塊樣式
    for paragraph in doc.paragraphs:
        # 檢查段落是否可能是程式碼區塊（基於內容特徵）
        if (paragraph.text.strip().startswith("def ") or
                paragraph.text.strip().startswith("import ") or
                paragraph.text.strip().startswith("from ") or
                paragraph.text.strip().startswith("#") or
                "```" in paragraph.text or
                "class " in paragraph.text):

            # 設置程式碼區塊段落格式
            for run in paragraph.runs:
                run.font.name = "Courier New"
                run.font.size = Pt(9)

    # 儲存 DOCX 檔案
    try:
        doc.save(docx_file_path)
        print(f"成功將 HTML 檔案轉換為 DOCX：{docx_file_path}")

        # 清理臨時檔案
        if process_code_blocks and os.path.exists(processed_html_path):
            try:
                os.remove(processed_html_path)
            except:
                pass

        return True
    except Exception as e:
        print(f"儲存 DOCX 檔案時發生錯誤：{str(e)}")
        return False


def convert_with_pandoc(html_file_path, docx_file_path, process_code_blocks=True):
    """使用 pandoc 將 HTML 轉換為 DOCX，增強程式碼區塊處理"""
    # 檢查檔案是否存在
    if not os.path.exists(html_file_path):
        print(f"錯誤：找不到 HTML 檔案 {html_file_path}")
        return False

    # 預處理 HTML 以增強程式碼區塊
    if process_code_blocks:
        processed_html_path = preprocess_html_for_code_blocks(html_file_path)
        html_path_to_use = processed_html_path
    else:
        html_path_to_use = html_file_path

    try:
        # 執行 pandoc 命令，加入程式碼高亮選項
        result = subprocess.run([
            "pandoc",
            html_path_to_use,
            "-f", "html",
            "-t", "docx",
            "-o", docx_file_path,
            "--highlight-style=tango",  # 使用 tango 高亮樣式
            "--wrap=none"  # 不自動折行
        ], capture_output=True, text=True)

        # 清理臨時檔案
        if process_code_blocks and os.path.exists(processed_html_path):
            try:
                os.remove(processed_html_path)
            except:
                pass

        # 檢查結果
        if result.returncode == 0:
            print(f"成功將 HTML 檔案轉換為 DOCX：{docx_file_path}")
            return True
        else:
            print(f"pandoc 轉換失敗：{result.stderr}")
            return False
    except Exception as e:
        print(f"執行 pandoc 時發生錯誤：{str(e)}")
        return False


def convert_with_python_docx(html_file_path, docx_file_path):
    """使用 python-docx 和 BeautifulSoup 將 HTML 轉換為 DOCX，專門處理表格和程式碼區塊"""
    # 檢查檔案是否存在
    if not os.path.exists(html_file_path):
        print(f"錯誤：找不到 HTML 檔案 {html_file_path}")
        return False

    # 讀取 HTML 檔案
    with open(html_file_path, "r", encoding="utf-8") as f:
        html_content = f.read()

    # 使用 BeautifulSoup 解析 HTML
    soup = BeautifulSoup(html_content, "html.parser")

    # 建立 Word 文件
    doc = Document()

    # 設置頁面邊距
    section = doc.sections[0]
    section.left_margin = Cm(2.54)
    section.right_margin = Cm(2.54)
    section.top_margin = Cm(2.54)
    section.bottom_margin = Cm(2.54)

    # 處理 HTML 內容
    for element in soup.find_all(["h1", "h2", "h3", "p", "table", "pre", "code"]):
        if element.name in ["h1", "h2", "h3"]:
            level = int(element.name[1])
            heading = doc.add_heading(element.get_text().strip(), level=level)

        elif element.name == "p":
            # 檢查是否含有程式碼
            if element.find(["code", "pre"]):
                code_text = element.get_text().strip()
                para = doc.add_paragraph()
                run = para.add_run(code_text)
                run.font.name = "Courier New"
                run.font.size = Pt(9)
            else:
                doc.add_paragraph(element.get_text().strip())

        elif element.name in ["pre", "code"]:
            # 處理獨立的程式碼區塊
            if element.parent.name != "pre" and element.parent.name != "code":
                code_text = element.get_text().strip()
                para = doc.add_paragraph()
                run = para.add_run(code_text)
                run.font.name = "Courier New"
                run.font.size = Pt(9)

                # 添加程式碼區塊背景色
                shading_elm = parse_xml(f'<w:shd {nsdecls("w")} w:fill="F0F0F0"/>')
                para._element.get_or_add_pPr().append(shading_elm)

        elif element.name == "table":
            # 處理表格
            rows = element.find_all("tr")
            if not rows:
                continue

            # 計算最大列數
            max_cols = max([len(row.find_all(["td", "th"])) for row in rows])

            # 創建表格
            table = doc.add_table(rows=len(rows), cols=max_cols)
            table.style = 'Table Grid'

            # 填充表格內容
            for i, row in enumerate(rows):
                cells = row.find_all(["td", "th"])
                for j, cell in enumerate(cells):
                    if j < max_cols:  # 確保不超出範圍
                        text = cell.get_text().strip()
                        table.cell(i, j).text = text

                        # 是否為表頭
                        is_header = cell.name == "th" or i == 0
                        if is_header:
                            for paragraph in table.cell(i, j).paragraphs:
                                for run in paragraph.runs:
                                    run.bold = True

                            # 設置表頭背景色
                            shading_elm = parse_xml(f'<w:shd {nsdecls("w")} w:fill="D9D9D9"/>')
                            table.cell(i, j)._element.tcPr.append(shading_elm)

            # 表格後添加空行
            doc.add_paragraph()

    # 儲存 DOCX 檔案
    try:
        doc.save(docx_file_path)
        print(f"成功將 HTML 檔案轉換為 DOCX：{docx_file_path}")
        return True
    except Exception as e:
        print(f"儲存 DOCX 檔案時發生錯誤：{str(e)}")
        return False


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="HTML 轉 DOCX 工具（支援表格和程式碼區塊）")
    parser.add_argument("html_path", help="HTML 檔案路徑")
    parser.add_argument("docx_path", help="輸出 DOCX 檔案路徑")
    parser.add_argument("--method", choices=["htmldocx", "pandoc", "python-docx"], default="htmldocx", help="轉換方法")

    args = parser.parse_args()

    if args.method == "htmldocx":
        convert_with_htmldocx(args.html_path, args.docx_path)
    elif args.method == "pandoc":
        convert_with_pandoc(args.html_path, args.docx_path)
    elif args.method == "python-docx":
        convert_with_python_docx(args.html_path, args.docx_path)