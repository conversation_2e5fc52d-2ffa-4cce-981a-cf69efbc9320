import re
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import chardet


class LogComparisonApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Oracle日誌比對工具")
        self.root.geometry("900x650")
        self.root.configure(padx=10, pady=10)

        # 變數
        self.export_log_path = tk.StringVar()
        self.insert_log_path = tk.StringVar()

        # 主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 檔案選擇區域
        file_frame = ttk.LabelFrame(main_frame, text="檔案選擇")
        file_frame.pack(fill=tk.X, padx=5, pady=5)

        # Export日誌選擇
        ttk.Label(file_frame, text="Export日誌:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(file_frame, textvariable=self.export_log_path, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="瀏覽...", command=self.browse_export_log).grid(row=0, column=2, padx=5, pady=5)

        # Insert日誌選擇
        ttk.Label(file_frame, text="Insert日誌:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(file_frame, textvariable=self.insert_log_path, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="瀏覽...", command=self.browse_insert_log).grid(row=1, column=2, padx=5, pady=5)

        # 按鈕區域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(button_frame, text="比對日誌", command=self.compare_logs).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(button_frame, text="重設", command=self.reset_fields).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(button_frame, text="退出", command=root.quit).pack(side=tk.RIGHT, padx=5, pady=5)

        # 結果顯示區域
        result_frame = ttk.LabelFrame(main_frame, text="比對結果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 建立Treeview用於顯示結果
        self.result_tree = ttk.Treeview(result_frame)
        self.result_tree["columns"] = ("表名", "Export行數", "Insert行數", "是否一致", "差異")

        # 設定欄位標題
        self.result_tree.column("#0", width=0, stretch=tk.NO)
        self.result_tree.column("表名", anchor=tk.W, width=200)
        self.result_tree.column("Export行數", anchor=tk.CENTER, width=150)
        self.result_tree.column("Insert行數", anchor=tk.CENTER, width=150)
        self.result_tree.column("是否一致", anchor=tk.CENTER, width=100)
        self.result_tree.column("差異", anchor=tk.CENTER, width=100)

        self.result_tree.heading("#0", text="", anchor=tk.W)
        self.result_tree.heading("表名", text="表名", anchor=tk.W)
        self.result_tree.heading("Export行數", text="Export行數", anchor=tk.CENTER)
        self.result_tree.heading("Insert行數", text="Insert行數", anchor=tk.CENTER)
        self.result_tree.heading("是否一致", text="是否一致", anchor=tk.CENTER)
        self.result_tree.heading("差異", text="差異", anchor=tk.CENTER)

        # 添加垂直捲軸
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=scrollbar.set)

        # 放置Treeview和捲軸
        self.result_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 按表名排序的按鈕
        sort_button_frame = ttk.Frame(main_frame)
        sort_button_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(sort_button_frame, text="按表名排序", command=lambda: self.sort_results("表名")).pack(side=tk.LEFT,
                                                                                                         padx=5)
        ttk.Button(sort_button_frame, text="按差異排序", command=lambda: self.sort_results("差異", reverse=True)).pack(
            side=tk.LEFT, padx=5)
        ttk.Button(sort_button_frame, text="只顯示不一致表格", command=self.show_only_mismatches).pack(side=tk.LEFT,
                                                                                                       padx=5)
        ttk.Button(sort_button_frame, text="顯示所有表格", command=self.show_all_tables).pack(side=tk.LEFT, padx=5)

        # 統計資訊區域
        stats_frame = ttk.LabelFrame(main_frame, text="統計資訊")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)

        self.stats_text = tk.Text(stats_frame, height=5, width=80)
        self.stats_text.pack(fill=tk.X, expand=True, padx=5, pady=5)
        self.stats_text.config(state=tk.DISABLED)

        # 狀態欄
        self.status_var = tk.StringVar()
        self.status_var.set("就緒")
        self.status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 存儲所有結果的列表
        self.all_results = []

    def browse_export_log(self):
        filepath = filedialog.askopenfilename(
            title="選擇Export日誌檔",
            filetypes=[("日誌檔", "*.log"), ("文本檔", "*.txt"), ("所有檔案", "*.*")]
        )
        if filepath:
            self.export_log_path.set(filepath)

    def browse_insert_log(self):
        filepath = filedialog.askopenfilename(
            title="選擇Insert日誌檔",
            filetypes=[("日誌檔", "*.log"), ("文本檔", "*.txt"), ("所有檔案", "*.*")]
        )
        if filepath:
            self.insert_log_path.set(filepath)

    def detect_encoding(self, file_path):
        """檢測檔案編碼"""
        with open(file_path, 'rb') as f:
            result = chardet.detect(f.read())
        encoding = result['encoding']
        confidence = result['confidence']
        self.status_var.set(f"檢測到 {file_path} 的編碼為 {encoding}，置信度: {confidence:.2f}")
        return encoding

    def read_file_with_encoding(self, file_path):
        """嘗試使用檢測到的編碼讀取檔案，如果失敗則嘗試其他編碼"""
        encodings_to_try = ['utf-8', 'big5', 'cp950', 'gb18030', 'latin1']

        # 先檢測編碼
        detected_encoding = self.detect_encoding(file_path)
        if detected_encoding:
            encodings_to_try.insert(0, detected_encoding)

        # 嘗試不同的編碼
        content = None
        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                    content = f.read()
                self.status_var.set(f"成功使用 {encoding} 編碼讀取檔案")
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            raise Exception(f"無法以任何已知編碼讀取檔案 {file_path}")

        return content

    def parse_export_log(self, log_file):
        """解析Export日誌檔"""
        try:
            content = self.read_file_with_encoding(log_file)
            tables_data = {}

            # 模式1：嘗試匹配您提供的日誌格式
            pattern1 = r'\. \. exporting table\s+(\w+)\s+(\d+) rows exported'
            matches1 = re.findall(pattern1, content)

            # 如果沒有找到匹配，嘗試其他格式
            if not matches1:
                pattern2 = r'exporting table\s+(\w+)\s+(\d+) rows exported'
                matches1 = re.findall(pattern2, content)

            for match in matches1:
                table_name = match[0].strip()
                rows = int(match[1])
                tables_data[table_name] = rows

            self.status_var.set(f"從Export日誌中識別到 {len(tables_data)} 個表")
            return tables_data
        except Exception as e:
            self.status_var.set(f"解析Export日誌時出錯: {str(e)}")
            raise

    def parse_insert_log(self, log_file):
        """解析Insert日誌檔"""
        try:
            content = self.read_file_with_encoding(log_file)
            tables_data = {}

            # 模式：尋找INSERT語句和Affected rows
            pattern = r'-- (\w+) 的INSERT語句.*?> Affected rows: (\d+)'
            matches = re.findall(pattern, content, re.DOTALL)

            for match in matches:
                table_name = match[0].strip()
                rows = int(match[1])
                tables_data[table_name] = rows

            self.status_var.set(f"從Insert日誌中識別到 {len(tables_data)} 個表")
            return tables_data
        except Exception as e:
            self.status_var.set(f"解析Insert日誌時出錯: {str(e)}")
            raise

    def compare_logs(self):
        """比對日誌檔並顯示結果"""
        export_log_path = self.export_log_path.get()
        insert_log_path = self.insert_log_path.get()

        if not export_log_path or not insert_log_path:
            messagebox.showerror("錯誤", "請選擇Export日誌檔和Insert日誌檔！")
            return

        try:
            # 清空先前的結果
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)

            self.status_var.set("正在解析Export日誌...")
            export_data = self.parse_export_log(export_log_path)

            self.status_var.set("正在解析Insert日誌...")
            insert_data = self.parse_insert_log(insert_log_path)

            self.status_var.set("正在比對結果...")

            # 比對結果
            all_tables = sorted(list(set(export_data.keys()) | set(insert_data.keys())))
            self.all_results = []

            for table in all_tables:
                export_rows = export_data.get(table, 0)
                insert_rows = insert_data.get(table, 0)
                match = export_rows == insert_rows
                difference = insert_rows - export_rows

                # 添加到結果列表
                self.all_results.append({
                    '表名': table,
                    'Export行數': export_rows,
                    'Insert行數': insert_rows,
                    '是否一致': match,
                    '差異': difference
                })

            # 顯示所有結果
            self.show_all_tables()

            # 計算統計資訊
            total_tables = len(self.all_results)
            match_tables = sum(1 for r in self.all_results if r['是否一致'])
            mismatch_tables = total_tables - match_tables
            match_percentage = (match_tables / total_tables * 100) if total_tables > 0 else 0

            # 分析差異類型
            insert_only = sum(1 for r in self.all_results if r['Export行數'] == 0 and r['Insert行數'] > 0)
            export_only = sum(1 for r in self.all_results if r['Export行數'] > 0 and r['Insert行數'] == 0)
            different_rows = sum(
                1 for r in self.all_results if r['Export行數'] > 0 and r['Insert行數'] > 0 and not r['是否一致'])

            # 顯示統計資訊
            stats_info = (
                f"總表格數: {total_tables}       一致表格數: {match_tables}       不一致表格數: {mismatch_tables}       一致率: {match_percentage:.2f}%\n"
                f"僅在Insert中存在的表格: {insert_only}       僅在Export中存在的表格: {export_only}       行數不一致的表格: {different_rows}\n\n"
                f"最大差異的表格: {max(self.all_results, key=lambda x: abs(x['差異']))['表名']} (差異: {max(self.all_results, key=lambda x: abs(x['差異']))['差異']})"
            )

            self.stats_text.config(state=tk.NORMAL)
            self.stats_text.delete('1.0', tk.END)
            self.stats_text.insert(tk.END, stats_info)
            self.stats_text.config(state=tk.DISABLED)

            self.status_var.set(
                f"比對完成，識別到 {total_tables} 個表格，其中 {match_tables} 個一致，{mismatch_tables} 個不一致")

        except Exception as e:
            messagebox.showerror("錯誤", f"比對過程中發生錯誤：{str(e)}")
            self.status_var.set("比對過程中發生錯誤")

    def show_all_tables(self):
        """顯示所有表格結果"""
        # 清空先前的結果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 添加所有結果到Treeview
        for result in self.all_results:
            match_symbol = "✓" if result['是否一致'] else "✗"
            self.result_tree.insert("", tk.END, values=(
                result['表名'],
                result['Export行數'],
                result['Insert行數'],
                match_symbol,
                result['差異']
            ))

        self.status_var.set(f"顯示所有 {len(self.all_results)} 個表格")

    def show_only_mismatches(self):
        """只顯示不一致的表格"""
        # 清空先前的結果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 過濾出不一致的表格
        mismatches = [r for r in self.all_results if not r['是否一致']]

        # 添加不一致的結果到Treeview
        for result in mismatches:
            self.result_tree.insert("", tk.END, values=(
                result['表名'],
                result['Export行數'],
                result['Insert行數'],
                "✗",
                result['差異']
            ))

        self.status_var.set(f"顯示 {len(mismatches)} 個不一致的表格")

    def sort_results(self, column, reverse=False):
        """根據指定列排序結果"""
        # 清空先前的結果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 排序結果
        if column == "表名":
            sorted_results = sorted(self.all_results, key=lambda x: x['表名'], reverse=reverse)
        elif column == "差異":
            sorted_results = sorted(self.all_results, key=lambda x: abs(x['差異']), reverse=reverse)

        # 添加排序後的結果到Treeview
        for result in sorted_results:
            match_symbol = "✓" if result['是否一致'] else "✗"
            self.result_tree.insert("", tk.END, values=(
                result['表名'],
                result['Export行數'],
                result['Insert行數'],
                match_symbol,
                result['差異']
            ))

        self.status_var.set(f"已按 {column} {'降序' if reverse else '升序'} 排序")

    def reset_fields(self):
        """重設所有欄位"""
        self.export_log_path.set("")
        self.insert_log_path.set("")

        # 清空結果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        self.all_results = []

        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete('1.0', tk.END)
        self.stats_text.config(state=tk.DISABLED)

        self.status_var.set("已重設")


if __name__ == "__main__":
    root = tk.Tk()
    app = LogComparisonApp(root)
    root.mainloop()