import re
import os
import pandas as pd
import sqlite3
from fuzzywuzzy import fuzz
from datetime import datetime
import logging

# 設置日誌 (INFO 等級以上會在終端顯示，DEBUG 可以在檔案中記錄)
logging.basicConfig(
    level=logging.DEBUG,  # 若只想在檔案裡看 DEBUG，可在此設 logging.DEBUG
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("product_matching.log", mode='a', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


# 資料庫連接函數
def get_db_connection(db_path="product_matching.db"):
    """建立資料庫連接"""
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn


# 初始化資料庫
def initialize_database(db_path="product_matching.db"):
    """初始化資料庫結構"""
    conn = get_db_connection(db_path)
    cursor = conn.cursor()

    # 客戶資料表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_code TEXT UNIQUE NOT NULL,
        customer_name TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # 客戶特定的商品匹配規則
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS matching_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        rule_type TEXT NOT NULL,
        pattern TEXT NOT NULL,
        replacement TEXT,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
    )
    ''')

    # 已知的商品匹配結果
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS known_matches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        customer_product_name TEXT NOT NULL,
        company_product_code TEXT NOT NULL,
        confidence INTEGER DEFAULT 100,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        UNIQUE(customer_id, customer_product_name)
    )
    ''')

    # 匹配歷史記錄
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS matching_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        match_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        file_name TEXT,
        total_products INTEGER,
        matched_products INTEGER,
        match_rate REAL,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
    )
    ''')

    conn.commit()
    conn.close()
    logger.info("資料庫初始化完成")


def get_or_create_customer(customer_code, customer_name):
    """檢查客戶是否存在，不存在則創建"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # 查詢客戶
    cursor.execute("SELECT id FROM customers WHERE customer_code = ?", (customer_code,))
    customer = cursor.fetchone()

    if customer:
        customer_id = customer['id']
        logger.debug(f"客戶已存在: {customer_code} (ID: {customer_id})")
    else:
        # 建立新客戶
        cursor.execute(
            "INSERT INTO customers (customer_code, customer_name) VALUES (?, ?)",
            (customer_code, customer_name)
        )
        conn.commit()
        customer_id = cursor.lastrowid
        logger.info(f"已建立新客戶: {customer_name} (ID: {customer_id})")

    conn.close()
    return customer_id


def load_customer_rules(customer_id):
    """載入客戶特定的匹配規則 (replace/prefix/product_specific)"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # 取得所有規則
    cursor.execute(
        "SELECT rule_type, pattern, replacement FROM matching_rules WHERE customer_id = ?",
        (customer_id,)
    )
    rows = cursor.fetchall()
    conn.close()

    replace_rules = []
    prefix_rules = {}
    product_specific_rules = {}

    for row in rows:
        rule_type = row['rule_type']
        pattern = row['pattern']
        replacement = row['replacement'] or ''

        if rule_type == 'replace':
            replace_rules.append({
                'pattern': pattern,
                'replacement': replacement
            })
        elif rule_type == 'prefix':
            # prefix:keyword1,keyword2...
            try:
                prefix, keywords = pattern.split(':')
                keywords = keywords.split(',')
                if prefix not in prefix_rules:
                    prefix_rules[prefix] = []
                prefix_rules[prefix].extend(keywords)
            except Exception as e:
                logger.warning(f"前綴規則資料格式錯誤: {pattern} -> {e}")

        elif rule_type == 'product_specific':
            # 客戶商品名稱->公司代號
            try:
                customer_name_rule, company_code = pattern.split('->')
                product_specific_rules[customer_name_rule.strip()] = company_code.strip()
            except Exception as e:
                logger.warning(f"特定規則資料格式錯誤: {pattern} -> {e}")

    logger.debug(f"[load_customer_rules] replace_rules={replace_rules}")
    logger.debug(f"[load_customer_rules] prefix_rules={prefix_rules}")
    logger.debug(f"[load_customer_rules] product_specific_rules={product_specific_rules}")

    return {
        'replace_rules': replace_rules,
        'prefix_rules': prefix_rules,
        'product_specific_rules': product_specific_rules
    }


def save_customer_rule(customer_id, rule_type, pattern, replacement=None):
    """儲存客戶特定的匹配規則"""
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute(
            "SELECT id FROM matching_rules WHERE customer_id = ? AND rule_type = ? AND pattern = ?",
            (customer_id, rule_type, pattern)
        )
        existing = cursor.fetchone()
        if existing:
            # 更新
            rule_id = existing['id']
            cursor.execute(
                "UPDATE matching_rules SET replacement = ? WHERE id = ?",
                (replacement, rule_id)
            )
            logger.debug(f"更新現有規則: {rule_type} | {pattern} -> {replacement}")
        else:
            # 新增
            cursor.execute(
                "INSERT INTO matching_rules (customer_id, rule_type, pattern, replacement) VALUES (?, ?, ?, ?)",
                (customer_id, rule_type, pattern, replacement)
            )
            logger.debug(f"新增規則: {rule_type} | {pattern} -> {replacement}")
        conn.commit()
        return True
    except Exception as e:
        conn.rollback()
        logger.error(f"儲存規則時發生錯誤: {e}")
        return False
    finally:
        conn.close()


def load_known_matches(customer_id):
    """載入客戶已知的商品匹配"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute(
        "SELECT customer_product_name, company_product_code FROM known_matches WHERE customer_id = ?",
        (customer_id,)
    )
    rows = cursor.fetchall()
    conn.close()

    known = {}
    for r in rows:
        known[r['customer_product_name']] = r['company_product_code']
    logger.debug(f"[load_known_matches] total={len(known)}")
    return known


def save_known_match(customer_id, customer_product_name, company_product_code, confidence=100):
    """儲存已確認的商品匹配"""
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute(
            """INSERT OR REPLACE INTO known_matches 
            (customer_id, customer_product_name, company_product_code, confidence) 
            VALUES (?, ?, ?, ?)""",
            (customer_id, customer_product_name, company_product_code, confidence)
        )
        conn.commit()
        logger.debug(f"已儲存已知匹配: {customer_product_name} -> {company_product_code} (conf={confidence})")
        return True
    except Exception as e:
        conn.rollback()
        logger.error(f"儲存匹配時發生錯誤: {e}")
        return False
    finally:
        conn.close()


def record_matching_history(customer_id, file_name, total_products, matched_products):
    """記錄匹配歷史"""
    conn = get_db_connection()
    cursor = conn.cursor()

    match_rate = (matched_products / total_products) * 100 if total_products else 0
    cursor.execute(
        """INSERT INTO matching_history 
        (customer_id, file_name, total_products, matched_products, match_rate) 
        VALUES (?, ?, ?, ?, ?)""",
        (customer_id, file_name, total_products, matched_products, match_rate)
    )
    conn.commit()
    conn.close()

    logger.info(f"已記錄匹配歷史: total={total_products}, matched={matched_products}, rate={match_rate:.2f}%")


# ---------------------
#   清理 & 匹配邏輯
# ---------------------

def normalize_capacity(name):
    """標準化容量：cc、公升 等轉成 ml"""
    # 轉換公升到毫升
    name = re.sub(r'(\d+(?:\.\d+)?)\s*(?:公升|[lL])\b', lambda m: f"{float(m.group(1)) * 1000:.0f}ml", name)
    # 標準化 cc / ML / ml
    name = re.sub(r'(\d+)\s*(?:cc|CC|ML)\b', r'\1ml', name)
    # 毫升 -> ml
    name = re.sub(r'(\d+)毫升', r'\1ml', name)
    return name


def extract_capacity(name):
    """從字串提取第一個容量 (xxx ml) 的數字"""
    m = re.search(r'(\d+)ml', name)
    return m.group(1) if m else ""


def clean_product_name(name, customer_id=None, is_reference=False):
    """
    清理商品名稱，並額外回傳容量數字 (不含 ml)
    """
    if not isinstance(name, str):
        name = str(name)

    original_name = name  # for debug
    name = name.lower()
    name = normalize_capacity(name)

    # 1) 各種「果汁飲料」 => "果汁"
    name = re.sub(r'(綜合)?果汁(?:飲料)?', '果汁', name)

    # 2) 蜜桃C/蘋果C/百香果C -> 蜜桃c/蘋果c
    name = re.sub(r'(蜜桃|蘋果|百香果|葡萄)\s*[cC]\b', r'\1c', name)

    # 3) 其他替換 (品牌字等)
    name = re.sub(r'黑松', '', name)
    name = re.sub(r'韋恩flash brew', 'flashbrew', name)
    name = re.sub(r'沙士', 'sarsi', name)
    name = re.sub(r'汽水', 'soda', name)
    name = re.sub(r'冬瓜茶', 'wintermelon', name)

    # 4) 包裝: 寶特瓶/pet瓶 -> pet, 罐類 -> can
    name = re.sub(r'(寶特瓶|pet瓶)', 'pet', name)
    name = re.sub(r'(易開罐|鋁罐|罐裝)', 'can', name)

    # 5) 移除可能的商品編號
    name = re.sub(r'\d+dc\d+', '', name)
    name = re.sub(r'#\d+', '', name)

    # 6) 擷取容量
    capacity = extract_capacity(name)

    # 移除容量字樣 (避免重複)
    name = re.sub(r'\d+\s*ml', '', name)

    # 7) 去除多餘符號
    name = re.sub(r'[\W_]+', ' ', name)
    name = name.strip()

    # 若有客戶 "replace" 規則
    if customer_id and not is_reference:
        rules = load_customer_rules(customer_id)
        for r in rules.get('replace_rules', []):
            pat = r['pattern']
            repl = r['replacement']
            # 偵測是否正則
            if pat.startswith('^') or pat.endswith('$') or '|' in pat:
                name = re.sub(pat, repl, name)
            else:
                name = name.replace(pat, repl)

    logger.debug(f"[clean_product_name] 原始={original_name} -> 清理後={name}, 容量={capacity}")
    return name, capacity


def apply_prefix_rules(clean_name, original_name, capacity, customer_id=None):
    """
    決定前綴 (pkl/pet/can/...)，並把容量數字合併進前綴，如 pkl320XX
    """
    logger.debug(f"[apply_prefix_rules] clean_name={clean_name}, original_name={original_name}, capacity={capacity}")

    # 預設前綴規則
    default_prefix_rules = {
        "pkl": [
            "茶", "wintermelon", "青", "紅", "奶", "微糖", "果汁", "蜜桃", "蘋果", "百香果",
            "葡萄", "蜂蜜", "c果汁", "wintermelon"
        ],
        "pet": [
            "純水", "礦泉水", "天霖", "水", "飲水", "flashbrew"
        ],
        "can": [
            "易開罐", "鋁罐", "罐裝", "can", "soda", "沙士", "sarsi"
        ]
    }

    original_lower = original_name.lower()

    # 1) 先看是否能從原始名稱直接判斷
    prefix = None
    if "罐" in original_lower or "can" in original_lower:
        prefix = "can"
    elif "瓶" in original_lower or "pet" in original_lower:
        prefix = "pet"
    elif "盒" in original_lower or "box" in original_lower or "紙" in original_lower:
        prefix = "box"

    # 2) 若還沒決定，就使用 關鍵字規則
    if not prefix:
        prefix_rules = default_prefix_rules
        if customer_id:
            # 載入客戶自定前綴規則
            crules = load_customer_rules(customer_id)
            if 'prefix_rules' in crules and crules['prefix_rules']:
                prefix_rules = crules['prefix_rules']
        # 依關鍵字判斷
        decided_prefix = None
        for pfx, keywords in prefix_rules.items():
            # 若 clean_name 或 original_lower 有出現該 prefix 的關鍵字
            if any(k in clean_name for k in keywords) or any(k in original_lower for k in keywords):
                decided_prefix = pfx
                break

        # 預設 fallback = "pkl"
        prefix = decided_prefix if decided_prefix else "pkl"

    # 3) 最後合併容量
    #   若 capacity=320 => pkl320
    #   再把清理後名稱接上 -> pkl320蜂蜜wintermelon
    final_name = prefix
    if capacity:
        final_name += capacity
    final_name += clean_name

    logger.debug(f"[apply_prefix_rules] => {final_name}")
    return final_name


def enhanced_fuzzy_match(query, choices, reference_data, threshold=55):
    """
    進階模糊比對 + 前綴容量加分
    query: str (e.g. pkl320蜂蜜wintermelon)
    choices: list of cleaned ref name
    reference_data: dict, key=clean_name, val={"產品代號":..., "原名稱":...}
    threshold: 分數門檻
    """
    logger.debug(f"[enhanced_fuzzy_match] query={query}, threshold={threshold}")

    if not choices:
        return None, 0, None

    best_match = None
    highest_score = 0
    best_product_code = None

    # 解析 query prefix & capacity
    query_prefix = None
    query_capacity = None
    for pfx in ["pkl", "pet", "can", "box"]:
        if query.startswith(pfx):
            query_prefix = pfx
            # 取後續可能的數字
            remainder = query[len(pfx):]
            cap_match = re.match(r'(\d+)', remainder)
            if cap_match:
                query_capacity = cap_match.group(1)
            break

    # 若完全一樣
    if query in reference_data:
        logger.debug(f"[enhanced_fuzzy_match] 完全匹配: {query}")
        return query, 100, reference_data[query]["產品代號"]

    # 依序遍歷
    for choice in choices:
        choice_info = reference_data[choice]

        # 解析 choice prefix & capacity
        choice_prefix = None
        choice_capacity = None
        for pfx in ["pkl", "pet", "can", "box"]:
            if choice.startswith(pfx):
                choice_prefix = pfx
                remainder = choice[len(pfx):]
                cap_match = re.match(r'(\d+)', remainder)
                if cap_match:
                    choice_capacity = cap_match.group(1)
                break

        # 基本 fuzzy
        basic_score = fuzz.ratio(query, choice)
        token_sort_score = fuzz.token_sort_ratio(query, choice)
        partial_score = fuzz.partial_ratio(query, choice)

        # 前綴/容量加分
        prefix_bonus = 0
        if query_prefix and choice_prefix and (query_prefix == choice_prefix):
            prefix_bonus = 10
        capacity_bonus = 0
        if query_capacity and choice_capacity and (query_capacity == choice_capacity):
            capacity_bonus = 15

        # 關鍵字加分
        keywords = ["茶", "冬瓜", "蜂蜜", "果汁", "蜜桃", "蘋果", "百香果", "葡萄",
                    "flashbrew", "sarsi", "wintermelon", "微糖", "無糖"]
        keyword_score = 0
        for kw in keywords:
            if kw in query and kw in choice:
                keyword_score += 5

        final_score = (basic_score * 0.25) + (token_sort_score * 0.2) \
                      + (partial_score * 0.15) + prefix_bonus + capacity_bonus + keyword_score

        if final_score > highest_score:
            highest_score = final_score
            best_match = choice
            best_product_code = choice_info["產品代號"]

    logger.debug(f"[enhanced_fuzzy_match] best_match={best_match}, highest_score={highest_score}")

    if highest_score >= threshold:
        return best_match, highest_score, best_product_code
    else:
        return None, 0, None


# ---------------------
#   主流程函式
# ---------------------

def match_products(
    reference_file,
    customer_file,
    customer_code,
    customer_name=None,
    sheet_name=None,
    product_code_col="產品代號",
    product_name_col="商品名稱",
    output_file=None,
    threshold=55
):
    """
    1) 讀取參考商品 & 客戶商品
    2) 清理並套用前綴 & 合併容量
    3) 特定規則 -> 已知匹配 -> 精確 -> 模糊
    4) 更新資料庫 & 輸出
    """
    # 若尚未建立資料庫
    if not os.path.exists("product_matching.db"):
        initialize_database()

    customer_id = get_or_create_customer(customer_code, customer_name or customer_code)
    logger.info(f"客戶ID={customer_id}, 代碼={customer_code}")

    # 載入已知匹配
    known_matches = load_known_matches(customer_id)
    logger.info(f"已載入 {len(known_matches)} 筆已知匹配")

    # 讀取 參考商品
    try:
        xls_ref = pd.ExcelFile(reference_file)
        logger.info(f"參考商品檔案工作表: {xls_ref.sheet_names}")
        df_ref = pd.read_excel(xls_ref, sheet_name="參考商品名稱")
        logger.info(f"參考商品筆數: {len(df_ref)}")
    except Exception as e:
        logger.error(f"讀取參考商品檔案時發生錯誤: {e}")
        return None

    # 讀取 客戶商品
    try:
        xls_cust = pd.ExcelFile(customer_file)
        logger.info(f"客戶商品檔案工作表: {xls_cust.sheet_names}")
        target_sheet = sheet_name or "需要匹配的商品名稱"
        if target_sheet not in xls_cust.sheet_names:
            logger.warning(f"找不到指定工作表 {target_sheet}，改用第一個工作表")
            target_sheet = xls_cust.sheet_names[0]
        df_cust = pd.read_excel(xls_cust, sheet_name=target_sheet)
        logger.info(f"客戶商品筆數: {len(df_cust)}")
    except Exception as e:
        logger.error(f"讀取客戶商品檔案時發生錯誤: {e}")
        return None

    # 必要欄位檢查
    if product_code_col not in df_ref.columns:
        logger.error(f"參考商品中找不到 '{product_code_col}' 欄位")
        return None
    if product_name_col not in df_ref.columns or product_name_col not in df_cust.columns:
        logger.error(f"無法在檔案中找到 '{product_name_col}' 欄位")
        return None

    # 去除缺值
    df_ref = df_ref.dropna(subset=[product_code_col, product_name_col])
    df_cust = df_cust.dropna(subset=[product_name_col])

    # 清理 參考商品 (若參考商品已經是最終格式，可視情況省略)
    clean_ref = []
    for idx, row in df_ref.iterrows():
        orig_name = row[product_name_col]
        c_name, c_capacity = clean_product_name(orig_name, is_reference=True)
        # 這裡示範也套用前綴，以保證與客戶端一致
        c_final = apply_prefix_rules(c_name, orig_name, c_capacity, None)  # 不一定要customer_id
        clean_ref.append(c_final)

    df_ref["清理後商品名稱"] = clean_ref

    # 清理 客戶商品
    df_cust["清理後商品名稱"], df_cust["容量"] = zip(
        *df_cust[product_name_col].apply(
            lambda x: clean_product_name(x, customer_id=customer_id, is_reference=False)
        )
    )

    df_cust["修正後商品名稱"] = df_cust.apply(
        lambda row: apply_prefix_rules(
            row["清理後商品名稱"], row[product_name_col], row["容量"], customer_id
        ),
        axis=1
    )

    # 建立 參考商品字典
    reference_dict = {}
    for _, row in df_ref.iterrows():
        ref_key = row["清理後商品名稱"]
        reference_dict[ref_key] = {
            "原名稱": row[product_name_col],
            "產品代號": row[product_code_col]
        }

    # 載入客戶特定規則 (product_specific)
    rules = load_customer_rules(customer_id)
    product_specific_rules = rules.get('product_specific_rules', {})

    # 初始化結果欄位
    df_cust["最佳匹配"] = None
    df_cust["匹配分數"] = 0
    df_cust["產品代號"] = None
    df_cust["匹配來源"] = None

    # 開始匹配
    for i, row in df_cust.iterrows():
        cust_prod_name = row[product_name_col]
        final_name = row["修正後商品名稱"]

        # 1) 特定規則
        if cust_prod_name in product_specific_rules:
            code = product_specific_rules[cust_prod_name]
            df_cust.at[i, "產品代號"] = code
            df_cust.at[i, "匹配來源"] = "特定規則"
            df_cust.at[i, "匹配分數"] = 100
            logger.debug(f"[匹配] 特定規則: {cust_prod_name} -> {code}")
            continue

        # 2) 已知匹配
        if cust_prod_name in known_matches:
            code = known_matches[cust_prod_name]
            df_cust.at[i, "產品代號"] = code
            df_cust.at[i, "匹配來源"] = "已知匹配"
            df_cust.at[i, "匹配分數"] = 100
            logger.debug(f"[匹配] 已知匹配: {cust_prod_name} -> {code}")
            continue

        # 3) 精確匹配
        if final_name in reference_dict:
            df_cust.at[i, "最佳匹配"] = final_name
            df_cust.at[i, "產品代號"] = reference_dict[final_name]["產品代號"]
            df_cust.at[i, "匹配來源"] = "精確匹配"
            df_cust.at[i, "匹配分數"] = 100
            logger.debug(f"[匹配] 精確匹配: {cust_prod_name} -> {final_name}")
            continue

        # 4) 模糊匹配
        best_match, score, product_code = enhanced_fuzzy_match(
            final_name,
            list(reference_dict.keys()),
            reference_dict,
            threshold=threshold
        )

        if best_match:
            df_cust.at[i, "最佳匹配"] = best_match
            df_cust.at[i, "產品代號"] = product_code
            df_cust.at[i, "匹配來源"] = "模糊匹配"
            df_cust.at[i, "匹配分數"] = score
            logger.debug(f"[匹配] 模糊匹配: {cust_prod_name} -> {best_match} (score={score})")
        else:
            logger.debug(f"[匹配] 未匹配: {cust_prod_name}")

    # 統計
    total = len(df_cust)
    matched = df_cust["產品代號"].notna().sum()
    match_rate = (matched / total) * 100 if total else 0
    logger.info(f"匹配完成: 總數={total}, 匹配成功={matched}, 匹配率={match_rate:.2f}%")

    # 記錄匹配歷史
    record_matching_history(
        customer_id,
        os.path.basename(customer_file),
        total,
        matched
    )

    # 自動學習高分匹配 (>=90)
    high_conf_df = df_cust[
        (df_cust["匹配分數"] >= 90)
        & (df_cust["匹配來源"] != "已知匹配")
        & (df_cust["匹配來源"] != "特定規則")
    ]
    logger.info(f"自動學習高分匹配: {len(high_conf_df)} 筆")
    for _, r in high_conf_df.iterrows():
        save_known_match(
            customer_id,
            r[product_name_col],
            r["產品代號"],
            confidence=r["匹配分數"]
        )

    # 輸出
    if output_file:
        df_cust.to_excel(output_file, index=False)
        logger.info(f"比對結果已儲存至 {output_file}")

    return df_cust


def learn_from_confirmed_matches(confirmed_file, customer_code):
    """從確認的匹配檔案學習 (自動建立 product_specific 規則 & known_matches)"""
    if not os.path.exists("product_matching.db"):
        initialize_database()

    # 取得客戶ID
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT id FROM customers WHERE customer_code = ?", (customer_code,))
    c = cursor.fetchone()
    conn.close()

    if not c:
        logger.error(f"找不到客戶 {customer_code}")
        return False

    customer_id = c['id']
    logger.info(f"學習匹配: 客戶 {customer_code} (ID={customer_id})")

    try:
        df_confirmed = pd.read_excel(confirmed_file)
    except Exception as e:
        logger.error(f"讀取確認匹配檔案時發生錯誤: {e}")
        return False

    required_cols = ["商品名稱", "產品代號"]
    if not all(x in df_confirmed.columns for x in required_cols):
        logger.error(f"檔案中缺少必要欄位: {required_cols}")
        return False

    # 載入現有規則
    rules = load_customer_rules(customer_id)

    for _, row in df_confirmed.iterrows():
        cust_name = row["商品名稱"]
        code = row["產品代號"]

        # 儲存到 known_matches
        save_known_match(customer_id, cust_name, code)

        # 建 product_specific (若重複則不會重複新增)
        pattern = f"{cust_name}->{code}"
        save_customer_rule(customer_id, 'product_specific', pattern, None)

    logger.info(f"已從 {len(df_confirmed)} 筆確認匹配更新 known_matches & product_specific 規則")
    return True


def generate_matching_report(customer_code, output_file=None):
    """產生匹配報告 (含歷史記錄)"""
    if not os.path.exists("product_matching.db"):
        logger.error("資料庫不存在，無法產生報告")
        return None

    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute("SELECT id, customer_name FROM customers WHERE customer_code = ?", (customer_code,))
    c = cursor.fetchone()
    if not c:
        logger.error(f"無此客戶: {customer_code}")
        conn.close()
        return None

    customer_id = c['id']
    cus_name = c['customer_name']

    cursor.execute("""
        SELECT match_date, file_name, total_products, matched_products, match_rate
        FROM matching_history
        WHERE customer_id = ?
        ORDER BY match_date DESC
    """, (customer_id,))
    history = cursor.fetchall()

    cursor.execute("SELECT COUNT(*) as cnt FROM known_matches WHERE customer_id = ?", (customer_id,))
    known_cnt = cursor.fetchone()['cnt']

    cursor.execute("""
        SELECT rule_type, COUNT(*) as cnt 
        FROM matching_rules 
        WHERE customer_id=? 
        GROUP BY rule_type
    """, (customer_id,))
    rule_rows = cursor.fetchall()
    conn.close()

    rule_summary = {}
    for r in rule_rows:
        rule_summary[r['rule_type']] = r['cnt']

    # 開始寫報告
    report = []
    report.append(f"# {cus_name} ({customer_code}) 匹配報告\n")
    report.append("## 基本資訊\n")
    report.append(f"- 客戶名稱: {cus_name}\n")
    report.append(f"- 客戶代碼: {customer_code}\n")
    report.append(f"- 已知匹配數量: {known_cnt}\n")
    report.append("- 規則數量:\n")
    for rt, cnt in rule_summary.items():
        report.append(f"  - {rt}: {cnt}\n")

    report.append("\n## 匹配歷史\n")
    if not history:
        report.append("無匹配歷史\n")
    else:
        report.append("| 日期 | 檔案名稱 | 總商品數量 | 匹配成功數量 | 匹配率 |\n")
        report.append("|------|----------|------------|--------------|--------|\n")
        for h in history:
            report.append(f"| {h['match_date']} | {h['file_name']} | {h['total_products']} "
                          f"| {h['matched_products']} | {h['match_rate']:.2f}% |\n")

    final_report = "".join(report)

    # 若需輸出到檔
    if output_file:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(final_report)
        logger.info(f"匹配報告已輸出至 {output_file}")

    return final_report


# ---- 測試示例 (可自行移除) ----
if __name__ == "__main__":
    # 若第一次使用，沒有DB檔，可初始化
    if not os.path.exists("product_matching.db"):
        initialize_database()

    # 假設要匹配
    df_result = match_products(
        reference_file="商品比對.xlsx",
        customer_file="商品比對.xlsx",
        customer_code="CPC",
        customer_name="中國石油公司",
        output_file="比對結果_中油_改進版.xlsx",
        threshold=35  # 可再調整
    )

    # 產生報告
    generate_matching_report("CPC", "匹配報告_CPC.md")
