# 家樂福產品爬蟲 (Carrefour Product Scraper)

使用 Python Playwright 爬取家樂福線上購物網站的產品與價格資訊。

## 功能特色

- 🛒 爬取家樂福黑松品牌館產品資訊
- 💰 提取產品名稱、價格、圖片、連結等詳細資訊
- 📄 支援 CSV 和 JSON 格式輸出
- 🔄 支援多頁面爬取
- 🎯 智能選擇器，適應網頁結構變化
- ⚡ 異步處理，提高爬取效率
- 🛡️ 內建錯誤處理和重試機制

## 檔案說明

- `carrefour_scraper.py` - 主要爬蟲程式
- `setup_scraper.py` - 自動安裝依賴和設定
- `quick_test.py` - 快速測試腳本
- `scraper_example.py` - 使用範例 (執行 setup_scraper.py 後自動生成)

## 快速開始

### 1. 安裝依賴

```bash
# 方法一：自動安裝 (推薦)
python setup_scraper.py

# 方法二：手動安裝
pip install playwright
python -m playwright install chromium
```

### 2. 快速測試

```bash
python quick_test.py
```

### 3. 開始爬取

```bash
# 基本使用
python carrefour_scraper.py

# 或使用範例腳本
python scraper_example.py
```

## 使用方法

### 基本使用

```python
import asyncio
from carrefour_scraper import CarrefourScraper

async def main():
    scraper = CarrefourScraper()
    
    # 爬取產品 (最多 3 頁)
    products = await scraper.scrape_products(
        headless=True,    # True=隱藏瀏覽器, False=顯示瀏覽器
        max_pages=3       # 最多爬取頁數
    )
    
    # 顯示結果摘要
    scraper.print_summary()
    
    # 儲存結果
    scraper.save_to_csv()
    scraper.save_to_json()

asyncio.run(main())
```

### 自訂目標網址

```python
scraper = CarrefourScraper()
scraper.target_url = "https://online.carrefour.com.tw/zh/其他品牌館網址"
products = await scraper.scrape_products()
```

### 篩選產品

```python
# 篩選特定價格範圍的產品
filtered_products = [p for p in products if 100 <= p['price'] <= 500]

# 篩選包含特定關鍵字的產品
keyword_products = [p for p in products if '關鍵字' in p['name']]
```

## 輸出格式

### 產品資料結構

```json
{
  "name": "產品名稱",
  "price": 299.0,
  "price_text": "NT$ 299",
  "image_url": "https://example.com/image.jpg",
  "product_url": "https://example.com/product",
  "description": "產品描述",
  "brand": "品牌名稱",
  "scraped_at": "2024-01-01T12:00:00"
}
```

### 輸出檔案

- **CSV 格式**: `carrefour_products_YYYYMMDD_HHMMSS.csv`
- **JSON 格式**: `carrefour_products_YYYYMMDD_HHMMSS.json`

## 參數說明

### scrape_products() 參數

- `headless` (bool): 
  - `True` - 隱藏瀏覽器視窗 (預設)
  - `False` - 顯示瀏覽器操作過程
  
- `max_pages` (int): 
  - 最多爬取的頁面數量 (預設: 5)

## 注意事項

### 使用規範

1. **遵守網站條款**: 請遵守家樂福網站的使用條款和 robots.txt
2. **合理使用**: 避免過於頻繁的請求，以免對網站造成負擔
3. **資料用途**: 爬取的資料僅供個人學習和研究使用

### 技術限制

1. **網頁結構變化**: 網站更新可能導致選擇器失效
2. **反爬蟲機制**: 網站可能有反爬蟲保護
3. **網路連線**: 需要穩定的網路連線

### 常見問題

**Q: 爬取失敗怎麼辦？**
A: 
1. 檢查網路連線
2. 執行 `python quick_test.py` 進行診斷
3. 嘗試減少 `max_pages` 參數

**Q: 找不到產品怎麼辦？**
A: 網站結構可能已變化，需要更新選擇器

**Q: 瀏覽器啟動失敗？**
A: 重新執行 `python -m playwright install chromium`

## 進階功能

### 自訂選擇器

```python
# 修改產品選擇器
async def custom_extract_products(self, page):
    # 自訂產品提取邏輯
    pass
```

### 加入延遲

```python
# 在爬取間隔加入延遲
await page.wait_for_timeout(5000)  # 等待 5 秒
```

### 處理動態內容

```python
# 等待動態內容載入
await page.wait_for_selector('.product-card', timeout=10000)
```

## 系統需求

- Python 3.7+
- Windows/macOS/Linux
- 至少 2GB 可用記憶體
- 穩定的網路連線

## 依賴套件

- `playwright` - 瀏覽器自動化
- `asyncio` - 異步處理
- `csv` - CSV 檔案處理
- `json` - JSON 檔案處理
- `re` - 正則表達式
- `datetime` - 時間處理

## 授權

此專案僅供學習和研究使用。請遵守相關網站的使用條款。

## 更新日誌

### v1.0.0
- 初始版本
- 支援家樂福黑松品牌館爬取
- CSV/JSON 輸出功能
- 多頁面爬取支援

## 聯絡資訊

如有問題或建議，請透過以下方式聯絡：
- 建立 Issue
- 提交 Pull Request

---

**免責聲明**: 此工具僅供學習和研究目的。使用者需自行承擔使用風險，並遵守相關法律法規和網站使用條款。
