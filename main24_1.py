import re
import numpy as np
import pandas as pd
from fuzzywuzzy import process

# 讀取 Excel 檔案
file_path = "商品比對.xlsx"
xls = pd.ExcelFile(file_path)

# **讀取參考商品名稱與需要匹配的商品名稱**
df_sheet2 = pd.read_excel(xls, sheet_name="參考商品名稱")
df_sheet3 = pd.read_excel(xls, sheet_name="需要匹配的商品名稱")

# **確保沒有 NaN 值**
df_sheet2 = df_sheet2.dropna(subset=["商品名稱", "產品代號"])
df_sheet3 = df_sheet3.dropna(subset=["商品名稱"])

# **清理商品名稱函數**
def clean_product_name(name):
    name = name.lower()  # 統一轉為小寫
    name = re.sub(r'黑松', '', name)  # 移除品牌 "黑松"
    name = re.sub(r'綜合果汁飲料', '', name)  # 移除 "綜合果汁飲料"
    name = re.sub(r'(\d+ml|\d+cc)', '', name)  # 移除 300ml, 500ml
    name = re.sub(r'500dc\d+', '', name)  # 移除 "500DC12299886" 這類數字+DC編碼
    name = re.sub(r'[\W_]+', ' ', name)  # 移除特殊符號
    return name.strip()

# **清理商品名稱**
df_sheet2["清理後商品名稱"] = df_sheet2["商品名稱"].apply(clean_product_name)
df_sheet3["清理後商品名稱"] = df_sheet3["商品名稱"].apply(clean_product_name)

# **修正 df_sheet3 的商品名稱，補回 `pkl`、`pet` 前綴**
df_sheet3["修正後商品名稱"] = df_sheet3["清理後商品名稱"].apply(
    lambda x: f"pkl{x}" if "c" in x else f"pet{x}" if "純水" in x or "天霖" in x else x
)

# **建立商品名稱對應產品代號的字典**
product_map = {row["清理後商品名稱"]: row["產品代號"] for _, row in df_sheet2.iterrows()}

# **LOG 1：檢查 product_map**
print(f"🔍 product_map 內容（前10筆）：")
for i, (key, value) in enumerate(product_map.items()):
    if i < 10:
        print(f"  - {key} ➝ {value}")
print(f"🔍 總共建立 {len(product_map)} 筆商品名稱對應產品代號")

# **LOG 2：檢查 df_sheet3 修正後商品名稱**
print("\n🔍 df_sheet3 修正後商品名稱（前10筆）：")
for i, name in enumerate(df_sheet3["修正後商品名稱"][:10]):
    print(f"  - {df_sheet3.iloc[i]['商品名稱']} ➝ {name}")

# **使用 fuzzywuzzy 進行模糊比對**
def fuzzy_match(name, choices, threshold=80):
    best_match, score = process.extractOne(name, choices)
    return best_match if score >= threshold else None

df_sheet3["最佳匹配"] = df_sheet3["修正後商品名稱"].apply(
    lambda x: fuzzy_match(x, list(product_map.keys()), threshold=70)
)

# **根據匹配結果查找對應的產品代號**
df_sheet3["產品代號"] = df_sheet3["最佳匹配"].apply(lambda x: product_map.get(x, None) if x else None)

# **LOG 3：檢查最佳匹配**
print("\n🔍 Best Matches 內容（前10筆）：")
for i in range(10):
    print(f"  - {df_sheet3.iloc[i]['商品名稱']} ➝ 最佳匹配: {df_sheet3['最佳匹配'].iloc[i]} ➝ 產品代號: {df_sheet3['產品代號'].iloc[i]}")

# **儲存比對結果**
output_file = "比對結果.xlsx"
df_sheet3.to_excel(output_file, index=False)

print(f"\n✅ 比對完成，結果已儲存至 {output_file}")
