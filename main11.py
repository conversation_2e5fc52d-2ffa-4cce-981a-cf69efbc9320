import requests
import xml.etree.ElementTree as ET


# 取得縣市清單的函數
def get_counties():
    url = 'https://api.nlsc.gov.tw/other/ListCounty'
    response = requests.get(url)

    if response.status_code == 200:
        # 解析 XML
        root = ET.fromstring(response.content)
        counties = []
        for county in root.findall('countyItem'):
            county_code = county.find('countycode').text
            county_name = county.find('countyname').text
            counties.append((county_code, county_name))
        return counties
    else:
        return None


# 根據縣市代碼取得鄉鎮市區的函數
def get_towns(county_code):
    url = f'https://api.nlsc.gov.tw/other/ListTown1/{county_code}'
    response = requests.get(url)

    if response.status_code == 200:
        # 解析 XML
        root = ET.fromstring(response.content)
        towns = []
        for town in root.findall('townItem'):
            town_code = town.find('towncode').text
            town_name = town.find('townname').text
            towns.append((town_code, town_name))
        return towns
    else:
        return None


# 主程序：依序取得縣市及其鄉鎮市區
counties = get_counties()
if counties:
    for county_code, county_name in counties:
        print(f'縣市: {county_name} ({county_code})')
        towns = get_towns(county_code)
        if towns:
            for town_code, town_name in towns:
                print(f'  鄉鎮區: {town_name} ({town_code})')
        else:
            print(f'  無法取得 {county_name} 的鄉鎮市區資料。')
else:
    print("無法取得縣市清單。")
