import configparser
import hashlib
import xml.etree.ElementTree as ET

# 讀取並解析 Caesar FTP 的 settings.ini 文件，使用 big5 編碼
config = configparser.ConfigParser()
config.read('settings.ini', encoding='big5')

# 創建 FileZilla Server 的 XML 根元素
filezilla_root = ET.Element('FileZillaServer')
settings_elem = ET.SubElement(filezilla_root, 'Settings')
groups_elem = ET.SubElement(filezilla_root, 'Groups')
users_elem = ET.SubElement(filezilla_root, 'Users')

# 添加基本設置
ET.SubElement(settings_elem, 'Item', name="Admin port", type="numeric").text = '14147'


# 定義 MD5 加密函數
def encrypt_password(password):
    hash_obj = hashlib.md5(password.encode('utf-8'))
    return hash_obj.hexdigest()


# 解析 Caesar FTP 用戶信息
for section in config.sections():
    if section.startswith('USER='):
        login = config[section].get('Login', config[section]['Name']).strip('" ')
        password = config[section].get('Password', None)
        if password:
            password = password.strip('" ')
        else:
            # 如果缺少 Password 欄位，設置一個默認密碼
            password = 'default_password'
        encrypted_password = encrypt_password(password)

        # 創建 FileZilla Server 的用戶元素
        user_elem = ET.SubElement(users_elem, 'User', Name=login)
        ET.SubElement(user_elem, 'Option', Name="Pass").text = encrypted_password
        ET.SubElement(user_elem, 'Option', Name="Group").text = config[section].get('GroupName', '').strip('" ')
        ET.SubElement(user_elem, 'Option', Name="Bypass server userlimit").text = '0'
        ET.SubElement(user_elem, 'Option', Name="User Limit").text = '0'
        ET.SubElement(user_elem, 'Option', Name="IP Limit").text = '0'
        ET.SubElement(user_elem, 'Option', Name="Enabled").text = config[section].get('Activated', '1')
        ET.SubElement(user_elem, 'Option', Name="Comments").text = ''
        ET.SubElement(user_elem, 'Option', Name="ForceSsl").text = '0'
        ET.SubElement(user_elem, 'Option', Name="8plus3").text = '0'

        ipfilter_elem = ET.SubElement(user_elem, 'IpFilter')
        ET.SubElement(ipfilter_elem, 'Disallowed')
        ET.SubElement(ipfilter_elem, 'Allowed')

        permissions_elem = ET.SubElement(user_elem, 'Permissions')

        # 配置用戶目錄及其權限
        for i in range(1, 10):  # 假設最多 10 個路徑
            path_key = f'FSysModification_Path{i}'
            real_path_key = f'FSysModification_RealPath{i}'
            if path_key in config[section] and real_path_key in config[section]:
                real_path = config[section][real_path_key].strip('" ')
                permission_elem = ET.SubElement(permissions_elem, 'Permission')
                permission_elem.set('Dir', real_path)
                ET.SubElement(permission_elem, 'Option', Name="FileRead").text = '1'
                ET.SubElement(permission_elem, 'Option', Name="FileWrite").text = '1'
                ET.SubElement(permission_elem, 'Option', Name="FileDelete").text = '1'
                ET.SubElement(permission_elem, 'Option', Name="FileAppend").text = '1'
                ET.SubElement(permission_elem, 'Option', Name="DirCreate").text = '1'
                ET.SubElement(permission_elem, 'Option', Name="DirDelete").text = '1'
                ET.SubElement(permission_elem, 'Option', Name="DirList").text = '1'
                ET.SubElement(permission_elem, 'Option', Name="DirSubdirs").text = '1'
                ET.SubElement(permission_elem, 'Option', Name="IsHome").text = '1'
                ET.SubElement(permission_elem, 'Option', Name="AutoCreate").text = '0'

        speedlimits_elem = ET.SubElement(user_elem, 'SpeedLimits', DlType="0", DlLimit="10", ServerDlLimitBypass="0",
                                         UlType="0", UlLimit="10", ServerUlLimitBypass="0")
        ET.SubElement(speedlimits_elem, 'Download')
        ET.SubElement(speedlimits_elem, 'Upload')

# 生成 FileZilla Server 的 XML 樹
filezilla_tree = ET.ElementTree(filezilla_root)

# 保存到新的 XML 文件
filezilla_tree.write('filezilla_ftp.xml', encoding='utf-8', xml_declaration=True)

print("用戶數據已成功從 Caesar FTP 轉換到 FileZilla Server。")
