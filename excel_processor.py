# excel_processor.py
import polars as pl
from datetime import datetime
import cx_Oracle
import os
import re
from typing import Optional, Tuple


class ExcelProcessor:
    _deletion_performed = False  # 類別變數

    def __init__(self, db_connection_str: str):
        """初始化處理器"""
        self.conn = cx_Oracle.connect(db_connection_str)

    @classmethod
    def reset_deletion_status(cls):
        """重設刪除狀態（類別方法）"""
        cls._deletion_performed = False
        print("已重設刪除狀態標記")

    def process_excel_files(self, file_paths: list) -> bool:
        """處理一批 Excel 檔案"""
        try:
            # 在開始新的一批檔案處理前，重設狀態
            ExcelProcessor._deletion_performed = False

            success = True
            for file_path in file_paths:
                if not self.process_excel_file(file_path, id_type=0):
                    success = False

            return success
        finally:
            # 在這批檔案處理完後，無論成功與否都重設狀態
            ExcelProcessor._deletion_performed = False
            print("✅ 已重設刪除狀態，準備下一批處理")

    def process_excel_file(self, file_path: str, id_type: int) -> bool:
        """處理單個 Excel 檔案"""
        cursor = None
        try:
            print(f"\n=== 開始處理Excel檔案: {file_path} ===")

            if not os.path.exists(file_path):
                print(f"找不到檔案: {file_path}")
                return False

            start_date, end_date = self._extract_dates_from_filename(file_path)
            print(f"檔案日期範圍: {start_date} ~ {end_date}")

            # 讀取 Excel 檔案
            try:
                df = pl.read_excel(
                    file_path,
                    sheet_name="Invoice"
                )
                # 跳過第一行
                df = df.slice(0)
            except Exception as e:
                print(f"讀取Excel時發生錯誤: {str(e)}")
                return False

            if len(df) == 0:
                print("Excel檔案沒有資料")
                return False

            # 開始交易
            cursor = self.conn.cursor()

            try:
                # 只在第一次執行刪除操作
                if not ExcelProcessor._deletion_performed:
                    deleted_count = self._delete_old_data(cursor, id_type, start_date, end_date)
                    print(f"✅ 已刪除 {deleted_count} 筆舊資料")
                    ExcelProcessor._deletion_performed = True
                else:
                    print("跳過刪除舊資料（已在之前執行）")

                # 處理並插入新資料
                processed_count = self._process_data(cursor, df, id_type)

                if processed_count > 0:
                    # 提交交易
                    self.conn.commit()
                    print(f"✅ 交易成功完成，共處理 {processed_count} 筆資料")
                    return True
                else:
                    # 回滾交易
                    self.conn.rollback()
                    print("❌ 沒有資料需要處理，已回滾交易")
                    return False

            except Exception as e:
                # 發生錯誤時回滾交易
                self.conn.rollback()
                print(f"❌ 處理失敗，已回滾交易: {str(e)}")
                raise

        except Exception as e:
            print(f"❌ 處理失敗: {str(e)}")
            if "df" in locals():
                print("\n資料框架資訊:")
                print(f"欄位: {df.columns}")
                print(f"資料量: {len(df)}")
            return False

        finally:
            if cursor:
                cursor.close()
            try:
                os.remove(file_path)
                print(f"✅ 已刪除處理完成的檔案: {file_path}")
            except Exception as e:
                print(f"⚠️ 刪除檔案失敗: {str(e)}")


    def _delete_old_data(self, cursor: cx_Oracle.Cursor, id_type: int, start_date: str, end_date: str) -> int:
        """刪除指定日期範圍的舊資料"""
        # 計算去年的日期 (根據起始日期)
        previous_year = str(int(start_date[:3]) - 1).zfill(3) + start_date[3:]

        print(f"準備刪除區間 {start_date} ~ {end_date} 的資料")
        cursor.execute("""
            DELETE FROM inidwk 
            WHERE idtype = :qtype 
            AND ((idindt >= :qdate1 AND idindt <= :qdate2) 
                 OR (idindt < :qdate3))
        """, {
            'qtype': str(id_type),
            'qdate1': start_date,
            'qdate2': end_date,
            'qdate3': previous_year
        })

        return cursor.rowcount

    def _process_data(self, cursor: cx_Oracle.Cursor, df: pl.DataFrame, id_type: int) -> int:
        """處理Excel資料並寫入資料庫"""
        total_rows = len(df)
        processed_rows = 0
        error_rows = 0
        batch_size = 1000
        current_batch = []

        # 準備 SQL 語句
        sql = """
            INSERT INTO inidwk 
            (idtype, idivno, idindt, idstus, idbsno, idamte)
            VALUES 
            (:qtype, :qivno, :qindt, :qstus, :qbsno, :qamte)
        """

        print("\n開始處理資料...")

        for row in df.iter_rows(named=True):
            try:
                # 檢查發票號碼是否為空
                if not row['發票號碼']:
                    continue

                # 取得狀態
                status = self._get_status(str(row['發票狀態']))

                # 處理日期
                date = self._convert_date(str(row['發票日期']))

                # 根據不同類型處理資料
                if id_type == 0:  # 銷項
                    business_id = str(row['買方統一編號']).strip()
                    amount = int(float(str(row['總計'])))
                else:  # 進項
                    business_id = str(row['賣方統一編號']).strip()
                    amount = int(float(str(row['應稅銷售額'])))

                # 準備資料
                params = {
                    'qtype': str(id_type),
                    'qivno': str(row['發票號碼']).strip(),
                    'qindt': date,
                    'qstus': status,
                    'qbsno': business_id,
                    'qamte': amount
                }

                current_batch.append(params)
                processed_rows += 1

                # 當達到批次大小時進行插入
                if len(current_batch) >= batch_size:
                    cursor.executemany(sql, current_batch)
                    current_batch = []
                    # print(f"已處理: {processed_rows}/{total_rows} 筆")

            except Exception as e:
                error_rows += 1
                print(f"處理資料時發生錯誤: {str(e)}")
                print(f"問題資料: {row}")
                continue

        # 處理最後一批資料
        if current_batch:
            cursor.executemany(sql, current_batch)

        print(f"\n處理完成:")
        print(f"總資料量: {total_rows} 筆")
        print(f"成功處理: {processed_rows} 筆")
        print(f"處理失敗: {error_rows} 筆")

        return processed_rows

    def _convert_date(self, date_str: str) -> str:
        """轉換日期格式為民國年7碼格式 (例如: 1140324)"""
        try:
            # 移除可能的時間部分
            date_str = date_str.split(' ')[0]

            # 解析日期
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')

            # 轉換為民國年
            roc_year = date_obj.year - 1911

            # 格式化為7碼字串 (3碼年 + 2碼月 + 2碼日)
            return f"{roc_year:03d}{date_obj.strftime('%m%d')}"
        except Exception as e:
            print(f"日期轉換錯誤 ({date_str}): {str(e)}")
            raise

    def _get_status(self, status_str: str) -> str:
        """轉換狀態代碼"""
        status_map = {
            '開立已確認': '1',
            '作廢已確認': '3',
            '退回已確認': '3',
            '已註銷': '0'
        }
        return status_map.get(status_str.strip(), '5')

    def _extract_dates_from_filename(self, filename: str) -> Tuple[str, str]:
        """
        從檔名解析起迄日期
        支援兩種格式：
        1. report_YYYYMMDD_YYYYMMDD.xlsx
        2. report_TAXID_YYYYMMDD_YYYYMMDD.xlsx
        """
        basename = os.path.basename(filename)

        # 嘗試匹配兩種格式
        patterns = [
            r'report_(\d{8})_(\d{8})\.xlsx',  # 無統編格式
            r'report_\d{8}_(\d{8})_(\d{8})\.xlsx'  # 有統編格式
        ]

        for pattern in patterns:
            match = re.match(pattern, basename)
            if match:
                def to_roc_date(date_str: str) -> str:
                    year = int(date_str[:4])
                    roc_year = year - 1911
                    return f"{roc_year:03d}{date_str[4:8]}"

                return to_roc_date(match.group(1)), to_roc_date(match.group(2))

        raise ValueError(f"無法從檔名解析日期: {filename}")

    def __del__(self):
        """析構函式，確保資料庫連線被關閉"""
        try:
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()
        except Exception as e:
            print(f"關閉資料庫連線時發生錯誤: {str(e)}")