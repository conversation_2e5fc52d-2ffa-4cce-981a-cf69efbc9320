import polars as pl
from sqlalchemy import create_engine, text

# 建立資料庫連線
engine = create_engine("oracle+cx_oracle://system:vmmanager@172.20.160.132/VMDB")

# 查詢資料
query = text("""
SELECT BHMTNO, BHDATE, BHTIME, BHSLNO, BHLINE, BHSAMT, BHCHAN, BHBDTM
FROM VSBH
WHERE /*BHMTNO = 'V030063' AND*/ BHBDTM IS NOT NULL
ORDER BY BHMTNO, BHDATE, BHLINE, BHSAMT, BHCHAN, BHSLNO
""")

with engine.connect() as connection:
    result = connection.execute(query)
    data = result.fetchall()
    columns = [col.lower() for col in result.keys()]

# 將資料轉為 Polars DataFrame
df = pl.DataFrame(data, schema=columns)
print("\n【初始數據】:")
print(df)

# 確保 `bhbdtm` 是 4 位數格式（補齊前導 0）
df = df.with_columns(
    pl.col("bhbdtm").str.replace(r"[^0-9]", "").str.zfill(4).alias("bhbdtm")
)
print("\n【補齊前導 0 的 bhbdtm】:")
print(df)

# 提取 `bhslno` 中的數字部分
df = df.with_columns(
    pl.col("bhslno").str.extract(r"S(\d+)").cast(pl.Int64).alias("bhslno_numeric")
)
print("\n【提取數字的 bhslno_numeric】:")
print(df)

# 計算 `bhbdtm` 的總秒數（MMSS 格式轉換）
df = df.with_columns(
    (
        (pl.col("bhbdtm").str.slice(0, 2).cast(pl.Int64) * 60) +  # 分鐘轉換為秒
        (pl.col("bhbdtm").str.slice(2, 2).cast(pl.Int64))         # 加上秒數
    ).alias("bhbdtm_seconds")
)
print("\n【bhbdtm_seconds 計算結果】:")
print(df)

# 判斷 BHSLNO 是否連續，包括跨日情況
df = df.with_columns(
    pl.when(
        # 同日情況下 BHSLNO 是否連續
        (pl.col("bhdate") == pl.col("bhdate").shift(1)) &
        (
            (pl.col("bhslno_numeric") - pl.col("bhslno_numeric").shift(1) == 1) |  # 正常連續
            ((pl.col("bhslno_numeric").shift(1) == 450) & (pl.col("bhslno_numeric") == 1))  # 特殊情況
        )
    )
    .then(1)
    .when(
        # 跨日情況下 BHSLNO 是否連續
        (pl.col("bhdate") != pl.col("bhdate").shift(1)) &  # 跨日
        (
            (pl.col("bhslno_numeric") - pl.col("bhslno_numeric").shift(1) == 1) |  # 正常連續
            ((pl.col("bhslno_numeric").shift(1) == 450) & (pl.col("bhslno_numeric") == 1))  # 特殊情況
        )
    )
    .then(1)
    .otherwise(0)
    .alias("is_sequential")
)
print("\n【is_sequential 判斷結果】:")
print(df)

# 計算時間差，基於多欄位分組
df = df.with_columns(
    pl.col("bhbdtm_seconds")
    .diff()
    .abs()
    .over(["bhmtno", "bhline", "bhsamt", "bhchan", "bhdate"])  # 修改分組依據
    .alias("nc_diff")
)
print("\n【時間差 (nc_diff) 計算結果】:")
print(df)

# 判定是否為重複資料
duplicate_data = df.filter(
    (pl.col("nc_diff") <= 4) &          # 時間差小於等於 4 秒
    (pl.col("is_sequential") == 1)     # BHSLNO 必須連續
)
print("\n【篩選重複資料結果】:")
print(duplicate_data)
