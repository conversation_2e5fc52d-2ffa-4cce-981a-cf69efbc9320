# Android STM32 Bootloader 問題修正總結

## 發現的問題

### 1. **自定義協議包長度錯誤**
- 收到: `01 0D B2 31 32 33 34 35 36 37 38 39 3A B4`
- 預期: `01 0F B2 31 32 33 34 35 36 37 38 39 3A B3`
- 原因: `BOOTLOADER_ENTRY_DATA` 定義錯誤，應該是 "123456789:" 但實際只有 "123456789"

### 2. **發送 HEX 檔案內容而非二進制數據**
收到的數據實際上是 Intel HEX 檔案的 ASCII 內容：
```
FF 3A 30 32 30 30 30 30 30 34 30 38 30 30 46 32 0D 0A
```
解碼後是：
```
:020000040800F2
```
這是 HEX 檔案的一行文本！

## 修正方案

### 1. 修正自定義協議數據
在 `CustomBootloaderProtocol.java` 中：
```java
// 錯誤的定義（10 字節）
private static final byte[] BOOTLOADER_ENTRY_DATA = {
    0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A
};

// 應該改為（注意 ASCII 值）
private static final byte[] BOOTLOADER_ENTRY_DATA = 
    "123456789:".getBytes(); // 這樣更清楚
```

### 2. 添加 Intel HEX 解析器
已創建 `IntelHexParser.java` 來正確解析 .hex 檔案。

### 3. 修改韌體載入邏輯
已更新 `FirmwareUpdateHelper.java`：
- 自動檢測檔案類型（.hex 或 .bin）
- 使用 HEX 解析器將 .hex 轉換為二進制
- 不再直接發送檔案內容

## 測試驗證

### 使用 Python 模擬器驗證
1. 運行 `stm32_bootloader_simulator.py`
2. 從 Android 發送韌體更新
3. 檢查接收到的數據

### 正確的流程應該是：
1. 進入 Bootloader: `01 0F B2 31 32 33 34 35 36 37 38 39 3A B3`
2. 同步字節: `7F`
3. 擦除命令: `43 BC` 然後 `FF 00`
4. 寫入命令: `31 CE`
5. 地址: `08 00 00 00 08`（0x08000000 + 校驗和）
6. 數據: `FF ...`（實際的二進制韌體數據，不是 HEX 文本）

## 建議的代碼修改

### 1. 在 `CustomBootloaderProtocol.java`：
```java
// 使用更清晰的方式定義
private static final byte[] BOOTLOADER_ENTRY_DATA = 
    "123456789:".getBytes(StandardCharsets.US_ASCII);
```

### 2. 在 `SimpleTerminalActivityV2.java`：
確保使用更新後的 `FirmwareUpdateHelper`（已包含 HEX 解析）。

### 3. 測試步驟：
1. 選擇 .hex 檔案
2. 查看日誌確認「HEX 檔案解析成功」
3. 檢查「二進制大小」是否合理（不應該是 HEX 文本大小）
4. 使用 Python 模擬器確認收到的是二進制數據

這樣應該就能正確上傳韌體了！