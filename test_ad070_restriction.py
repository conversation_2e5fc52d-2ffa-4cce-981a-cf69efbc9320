import ldap3

def test_ad070_restriction():
    """測試 AD070 群組限制是否正確工作"""
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389)
    
    print("=== 測試 AD070 群組限制 ===")
    
    # 使用 16613 的認證來搜尋其他使用者
    conn = ldap3.Connection(server, user='<EMAIL>', password='Tt9566659')
    
    if conn.bind():
        print("✅ 連接成功")
        
        # 1. 確認 16613 是 AD070 群組成員
        print("\n1. 確認 16613 是 AD070 群組成員:")
        result = conn.search('DC=heysong,DC=com,DC=tw', '(sAMAccountName=16613)', ldap3.SUBTREE,
                           attributes=['sAMAccountName', 'memberOf'])
        
        if result and conn.entries:
            entry = conn.entries[0]
            is_ad070_member = False
            if hasattr(entry, 'memberOf'):
                for group in entry.memberOf:
                    if 'CN=AD070,' in str(group):
                        is_ad070_member = True
                        print(f"   ✅ 16613 是 AD070 群組成員: {group}")
                        break
            
            if not is_ad070_member:
                print("   ❌ 16613 不是 AD070 群組成員")
        
        # 2. 搜尋非 AD070 群組的使用者
        print("\n2. 搜尋非 AD070 群組的使用者:")
        
        # 搜尋一些使用者來測試
        test_filters = [
            '(&(objectCategory=Person)(objectClass=User)(!(memberOf=CN=AD070,OU=部門群組,OU=台北總公司,OU=群組,DC=heysong,DC=com,DC=tw)))',
            '(&(objectCategory=Person)(objectClass=User)(memberOf=CN=AD073,OU=部門群組,OU=台北總公司,OU=群組,DC=heysong,DC=com,DC=tw))'
        ]
        
        for i, search_filter in enumerate(test_filters, 1):
            print(f"\n   測試篩選器 {i}: 搜尋非 AD070 使用者")
            try:
                result = conn.search('DC=heysong,DC=com,DC=tw', search_filter, ldap3.SUBTREE,
                                   attributes=['sAMAccountName', 'cn', 'memberOf'])
                
                if result and conn.entries:
                    print(f"   找到 {len(conn.entries)} 個非 AD070 使用者:")
                    for entry in conn.entries[:3]:  # 只顯示前3個
                        print(f"     - {entry.sAMAccountName}: {getattr(entry, 'cn', '未知')}")
                        
                        # 檢查這些使用者是否真的不是 AD070 成員
                        is_ad070 = False
                        if hasattr(entry, 'memberOf'):
                            for group in entry.memberOf:
                                if 'CN=AD070,' in str(group):
                                    is_ad070 = True
                                    break
                        
                        if is_ad070:
                            print(f"       ⚠️  警告: {entry.sAMAccountName} 實際上是 AD070 成員")
                        else:
                            print(f"       ✅ {entry.sAMAccountName} 確實不是 AD070 成員")
                else:
                    print("   未找到非 AD070 使用者")
                    
            except Exception as e:
                print(f"   ❌ 搜尋錯誤: {e}")
        
        # 3. 測試新的 Gitea 篩選器
        print("\n3. 測試 Gitea 的新篩選器:")
        gitea_filter = "(&(objectCategory=Person)(objectClass=User)(sAMAccountName=16613)(memberOf=CN=AD070,OU=部門群組,OU=台北總公司,OU=群組,DC=heysong,DC=com,DC=tw))"
        print(f"   篩選器: {gitea_filter}")
        
        try:
            result = conn.search('DC=heysong,DC=com,DC=tw', gitea_filter, ldap3.SUBTREE,
                               attributes=['sAMAccountName', 'cn'])
            
            if result and conn.entries:
                print(f"   ✅ 篩選器測試成功: 找到 {len(conn.entries)} 個符合條件的使用者")
                for entry in conn.entries:
                    print(f"     - {entry.sAMAccountName}: {getattr(entry, 'cn', '未知')}")
            else:
                print("   ❌ 篩選器測試失敗: 未找到符合條件的使用者")
                
        except Exception as e:
            print(f"   ❌ 篩選器測試錯誤: {e}")
        
        conn.unbind()
        
    else:
        print("❌ LDAP 連接失敗")
    
    # 4. 總結
    print(f"\n💡 AD070 限制設定總結:")
    print("✅ 已成功設定 LDAP 篩選器限制只有 AD070 群組成員可以登入")
    print("✅ 16613 (AD070 成員) 可以正常登入")
    print("❌ 非 AD070 群組成員將無法登入 Gitea")
    print()
    print("🔧 目前的 Gitea 設定:")
    print("認證類型: LDAP (simple auth)")
    print("用戶 DN: %<EMAIL>")
    print("用戶搜尋基準: DC=heysong,DC=com,DC=tw")
    print("使用者篩選器: (&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s)(memberOf=CN=AD070,OU=部門群組,OU=台北總公司,OU=群組,DC=heysong,DC=com,DC=tw))")
    print()
    print("🎯 效果:")
    print("- 只有 AD070 部門的員工可以登入 Gitea")
    print("- 其他部門的員工將被拒絕登入")
    print("- 管理員帳號 (admin2) 不受此限制影響")

if __name__ == "__main__":
    test_ad070_restriction()
