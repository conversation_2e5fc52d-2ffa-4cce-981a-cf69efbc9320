# Gitea LDAP 快速設定指南

## 🎯 目標
讓員工直接用公司帳號密碼登入 Gitea，無需申請額外服務帳號。

## 📋 設定步驟

### 1. 登入 Gitea 管理介面
- 網址：http://172.20.160.11:3100
- 帳號：admin2
- 密碼：@Heysong20651901

### 2. 進入認證設定
1. 點擊右上角頭像 → `網站管理`
2. 左側選單 → `認證來源`
3. 點擊現有的 `黑松 Active Directory`

### 3. 修改設定
**重要：將以下設定改為：**

```
認證類型: LDAP (simple auth)
認證名稱: 黑松 LDAP Simple
主機: HSWDC00.heysong.com.tw
連接埠: 389
安全協定: 未加密
使用者 DN: %<EMAIL>
```

**清空以下欄位：**
- Bind DN：留空
- Bind 密碼：留空

**確認勾選：**
- ☑ 該認證來源已啟用

### 4. 儲存設定
點擊 `更新認證來源`

## 🧪 測試登入

### 測試帳號
- 使用者名稱：16613
- 密碼：公司 AD 密碼

### 登入步驟
1. 前往 http://172.20.160.11:3100
2. 點擊 `登入`
3. 輸入公司帳號（如：16613）
4. 輸入公司密碼
5. 點擊登入

## ✅ 成功指標
- 能夠成功登入
- 自動建立使用者帳號
- 顯示正確的使用者名稱

## 🔧 故障排除

### 如果無法登入
1. **檢查設定**
   - 確認認證類型是 `LDAP (simple auth)`
   - 確認使用者 DN 是 `%<EMAIL>`
   - 確認 Bind DN 和 Bind 密碼都是空白

2. **檢查帳號密碼**
   - 使用正確的公司帳號（不含 @heysong.com.tw）
   - 使用正確的公司密碼

3. **檢查網路**
   - 確認能連接到 HSWDC00.heysong.com.tw:389

### 測試工具
如果需要詳細測試，可以運行：
```bash
python test_simple_ldap.py
```

## 📝 使用說明

### 員工登入方式
- **帳號**：公司帳號（如：16613）
- **密碼**：公司 AD 密碼

### 首次登入
- Gitea 會自動建立帳號
- 使用公司帳號作為使用者名稱
- 自動設定基本資訊

## 🎉 完成！
設定完成後，所有員工都可以直接使用公司帳號密碼登入 Gitea。
