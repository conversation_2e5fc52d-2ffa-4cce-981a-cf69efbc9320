def calculate_actual_raise(current_salary, food_allowance, specialist_allowance, duty_allowance, stock_trust_fund, promotion_position):
    # Define position allowances
    position_allowances = {
        "副課長": 8500,
        "課長": 10000,
        "副理": 14500,
        "經理": 16000,
        # You can add more positions and their allowances here
    }

    # Get the position allowance for the promotion position
    if promotion_position not in position_allowances:
        return "Invalid promotion position"

    position_allowance = position_allowances[promotion_position]

    # Calculate the original total salary
    original_total_salary = current_salary + food_allowance + specialist_allowance + duty_allowance + stock_trust_fund

    # Calculate the new total salary after promotion
    new_total_salary = current_salary + food_allowance + position_allowance + stock_trust_fund

    # Calculate the actual raise
    actual_raise = new_total_salary - original_total_salary

    return actual_raise

# Input values
current_salary = float(input("Enter your current salary (薪工資): "))
food_allowance = float(input("Enter your food allowance (伙食費): "))
specialist_allowance = float(input("Enter your specialist allowance (專員津貼): "))
duty_allowance = float(input("Enter your duty allowance (職務津貼): "))
stock_trust_fund = float(input("Enter your stock trust fund (員工持股信託公提金): "))
promotion_position = input("Enter the promotion position (晉升職位): ")

# Calculate the actual raise
actual_raise = calculate_actual_raise(current_salary, food_allowance, specialist_allowance, duty_allowance, stock_trust_fund, promotion_position)

# Output the result
print(f"The actual raise after promotion to {promotion_position} is: {actual_raise} 元")
