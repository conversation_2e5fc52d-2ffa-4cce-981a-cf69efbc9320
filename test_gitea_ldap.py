import ldap3
import sys

def test_gitea_ldap_config():
    """測試 Gitea LDAP 設定的具體配置"""
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389, get_info=ldap3.ALL)
    
    print("=== Gitea LDAP 設定診斷 ===")
    
    username = input("輸入測試帳號 (或按 Enter 使用 16613): ").strip() or "16613"
    password = input("輸入密碼: ").strip()
    
    if not password:
        print("需要密碼才能測試")
        return
    
    # 測試 Gitea 目前的設定
    print(f"\n🔍 測試 Gitea 目前的設定...")
    
    # 1. 測試用戶 DN 格式
    user_dn = f"{username}@heysong.com.tw"
    print(f"1. 測試用戶 DN: {user_dn}")
    
    try:
        conn = ldap3.Connection(server, user=user_dn, password=password)
        if conn.bind():
            print("   ✅ 用戶 DN 認證成功")
            
            # 2. 測試使用者篩選器
            print("2. 測試使用者篩選器...")
            
            # 目前 Gitea 的篩選器
            search_filter = f"(&(objectCategory=Person)(objectClass=User)(sAMAccountName={username}))"
            print(f"   篩選器: {search_filter}")
            
            try:
                result = conn.search('DC=heysong,DC=com,DC=tw', search_filter, ldap3.SUBTREE,
                                   attributes=['distinguishedName', 'sAMAccountName', 'userPrincipalName', 'givenName', 'sn'])
                
                if result and conn.entries:
                    print("   ✅ 篩選器搜尋成功")
                    entry = conn.entries[0]
                    print(f"   找到使用者: {entry.distinguishedName}")
                    print(f"   sAMAccountName: {entry.sAMAccountName}")
                    print(f"   userPrincipalName: {entry.userPrincipalName}")
                    print(f"   givenName: {getattr(entry, 'givenName', '未設定')}")
                    print(f"   sn: {getattr(entry, 'sn', '未設定')}")
                else:
                    print("   ❌ 篩選器搜尋失敗 - 這可能是 Gitea 登入失敗的原因！")
                    
            except Exception as e:
                print(f"   ❌ 篩選器搜尋錯誤: {e}")
            
            # 3. 測試不使用篩選器的情況
            print("3. 測試不使用篩選器...")
            print("   在 simple auth 模式下，不應該需要篩選器")
            print("   ✅ 直接認證已經成功，建議清空 Gitea 的使用者篩選器")
            
            conn.unbind()
            
        else:
            print("   ❌ 用戶 DN 認證失敗")
            
    except Exception as e:
        print(f"   ❌ 連接錯誤: {e}")
    
    # 4. 提供修正建議
    print(f"\n💡 修正建議:")
    print("1. 確認 Gitea 設定:")
    print("   - 認證類型: LDAP (simple auth)")
    print(f"   - 用戶 DN: %<EMAIL>")
    print("   - 使用者篩選器: (建議清空)")
    print("   - 電子郵件屬性: userPrincipalName")
    
    print("\n2. 如果還是無法登入:")
    print("   - 檢查是否有其他認證來源干擾")
    print("   - 確認認證來源的優先順序")
    print("   - 檢查 Gitea 日誌檔案")

def test_multiple_auth_formats():
    """測試多種認證格式"""
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389)
    
    print("\n=== 測試多種認證格式 ===")
    
    username = "16613"  # 固定使用測試帳號
    password = input("輸入 16613 的密碼: ").strip()
    
    if not password:
        return
    
    # 測試不同的認證格式
    auth_formats = [
        f"{username}@heysong.com.tw",
        f"HEYSONG\\{username}",
        f"CN=吳榮哲,OU=資訊人員,OU=台北總公司,OU=使用者帳號,DC=heysong,DC=com,DC=tw"
    ]
    
    for i, auth_format in enumerate(auth_formats, 1):
        print(f"\n{i}. 測試格式: {auth_format}")
        try:
            conn = ldap3.Connection(server, user=auth_format, password=password)
            if conn.bind():
                print("   ✅ 認證成功")
                conn.unbind()
            else:
                print("   ❌ 認證失敗")
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")

if __name__ == "__main__":
    test_gitea_ldap_config()
    test_multiple_auth_formats()
