#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STM32 GO 命令處理模組
用於添加到 stm32_simulator_cli_v2.py
"""

def handle_go_command(self):
    """
    處理 GO 命令 (0x21 0xDE)
    GO 命令格式：
    1. 接收 0x21 0xDE
    2. 發送 ACK (0x79)
    3. 接收地址 (4 bytes) + XOR checksum (1 byte)
    4. 驗證並發送 ACK
    """
    
    # 在 process_bootloader_mode 方法中添加：
    
    # GO 命令
    if len(self.receive_buffer) >= 2 and self.receive_buffer[0] == 0x21 and self.receive_buffer[1] == 0xDE:
        self.receive_buffer = self.receive_buffer[2:]
        self.log_message("📥 收到 GO 命令 (0x21 0xDE)")
        
        # 發送 ACK
        self.send_byte(self.STM32_ACK)
        self.log_message("📤 發送: 79 (ACK)")
        
        # 設置狀態等待地址
        self.write_state = "WAIT_GO_ADDRESS"
        self.log_message("⏳ 等待跳轉地址...")
        return

def handle_go_address(self):
    """
    處理 GO 命令的地址部分
    """
    
    # 在 process_write_address 方法後添加類似的處理：
    
    if self.write_state == "WAIT_GO_ADDRESS":
        if len(self.receive_buffer) >= 5:
            # 提取地址和校驗和
            addr_data = self.receive_buffer[:5]
            self.receive_buffer = self.receive_buffer[5:]
            
            # 解析地址 (大端序)
            address = (addr_data[0] << 24) | (addr_data[1] << 16) | (addr_data[2] << 8) | addr_data[3]
            checksum = addr_data[4]
            
            # 計算 XOR 校驗和
            calc_checksum = addr_data[0] ^ addr_data[1] ^ addr_data[2] ^ addr_data[3]
            
            hex_display = ' '.join([f"{b:02X}" for b in addr_data])
            self.log_message(f"📥 收到跳轉地址: {hex_display}")
            self.log_message(f"   地址: 0x{address:08X}")
            self.log_message(f"   校驗和: 收到=0x{checksum:02X}, 計算=0x{calc_checksum:02X}")
            
            if checksum == calc_checksum:
                # 發送 ACK
                self.send_byte(self.STM32_ACK)
                self.log_message("📤 發送: 79 (ACK)")
                
                # 模擬跳轉
                self.log_message(f"🚀 模擬跳轉到地址 0x{address:08X}")
                self.log_message("=" * 50)
                self.log_message("✅ GO 命令執行成功！")
                self.log_message("🔄 STM32 已跳轉到應用程式")
                self.log_message("⚠️  Bootloader 模式結束")
                self.log_message("=" * 50)
                
                # 顯示寫入統計
                self.log_message(f"\n📊 燒錄統計:")
                self.log_message(f"   寫入次數: {self.write_count}")
                self.log_message(f"   總字節數: {self.total_bytes_written}")
                self.log_message(f"   Flash 使用: {len(self.flash_memory)} 個地址")
                
                # 重置狀態（模擬重啟）
                self.state = "NORMAL"
                self.is_bootloader_mode = False
                self.write_state = None
                
                # 可選：顯示 Flash 內容摘要
                if self.flash_memory:
                    min_addr = min(self.flash_memory.keys())
                    max_addr = max(self.flash_memory.keys())
                    self.log_message(f"   地址範圍: 0x{min_addr:08X} - 0x{max_addr:08X}")
                    
                    # 顯示前 16 字節
                    self.log_message(f"\n📝 Flash 內容預覽 (0x{self.flash_start:08X}):")
                    preview = []
                    for i in range(16):
                        addr = self.flash_start + i
                        if addr in self.flash_memory:
                            preview.append(f"{self.flash_memory[addr]:02X}")
                        else:
                            preview.append("--")
                    self.log_message("   " + " ".join(preview))
                
            else:
                # 發送 NACK
                self.send_byte(self.STM32_NACK)
                self.log_message("📤 發送: 1F (NACK) - 地址校驗和錯誤")
            
            self.write_state = None

# 在主程式的命令列表中添加 GO 命令的說明
"""
支援的 STM32 Bootloader 命令：
- 0x00: Get
- 0x01: Get Version
- 0x02: Get ID
- 0x21: Go (跳轉執行) ← 新增
- 0x31: Write Memory
- 0x43: Extended Erase
"""

# 測試 GO 命令的範例
"""
測試步驟：
1. 進入 Bootloader 模式
2. 執行同步 (0x7F)
3. 擦除和寫入韌體
4. 發送 GO 命令：
   - TX: 21 DE
   - RX: 79 (ACK)
   - TX: 08 00 00 00 08  (地址 0x08000000，校驗和 0x08)
   - RX: 79 (ACK)
5. 模擬器顯示跳轉成功
"""