import pandas as pd

# 讀取 Excel 文件
excel_file = "00000松深客戶基本資料.xlsx"  # 替換為你的 Excel 文件路徑
sheet_name = "客戶"  # 替換為你的工作表名稱

# 讀取 A 欄位數據
data = pd.read_excel(excel_file, sheet_name=sheet_name, usecols="A", dtype=str)
values = data.iloc[:, 0].dropna().tolist()  # 移除空值，轉為列表

# 生成 SQL 語法
table_name = "TEMP_ADCSTN2"  # 臨時表的名稱

sql_statements = [f"CREATE GLOBAL TEMPORARY TABLE {table_name} (ADCSTN VARCHAR2(50));"]

for value in values:
    sql_statements.append(f"INSERT INTO {table_name} (ADCSTN) VALUES ('{value}');")

# 將 SQL 語法寫入文件
with open("insert_adcstn_correct.sql", "w", encoding="utf-8") as f:
    f.write("\n".join(sql_statements))

print("SQL 文件已生成，請檢查 'insert_adcstn_correct.sql'。")
