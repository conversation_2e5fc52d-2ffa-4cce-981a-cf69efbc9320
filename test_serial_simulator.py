#!/usr/bin/env python3
"""
串口模擬器 - 測試 Android APP 接收包含換行符的訊息
模擬 STM32 板子發送包含 0D0A (\\r\\n) 的資料
"""

import serial
import time
import sys

def send_hex_data(port, baudrate=115200):
    """
    發送包含 16 進位換行符的測試資料
    """
    try:
        # 開啟串口
        ser = serial.Serial(port, baudrate, timeout=1)
        print(f"串口已開啟: {port} @ {baudrate} bps")
        
        # 等待連接穩定
        time.sleep(2)
        
        # 測試案例 1: 發送簡單訊息 + CRLF
        print("\n測試 1: 發送 'Hello' + 0D0A")
        # 方法 1: 使用字串的跳脫字符
        message1 = "Hello\r\n"
        ser.write(message1.encode('utf-8'))
        print(f"已發送: {message1.encode('utf-8').hex()}")
        time.sleep(1)
        
        # 測試案例 2: 直接發送 16 進位資料
        print("\n測試 2: 發送 'World' + 0D0A (使用 bytes)")
        # 方法 2: 直接構造 bytes
        message2 = b'World' + b'\x0D\x0A'  # 0x0D = \r, 0x0A = \n
        ser.write(message2)
        print(f"已發送: {message2.hex()}")
        time.sleep(1)
        
        # 測試案例 3: 多行訊息
        print("\n測試 3: 發送多行訊息")
        multiline = "Line 1\r\nLine 2\r\nLine 3\r\n"
        ser.write(multiline.encode('utf-8'))
        print(f"已發送: {multiline.encode('utf-8').hex()}")
        time.sleep(1)
        
        # 測試案例 4: 使用 bytearray 構造複雜訊息
        print("\n測試 4: 使用 bytearray 發送")
        data = bytearray()
        data.extend(b'Test message')
        data.append(0x0D)  # CR
        data.append(0x0A)  # LF
        ser.write(data)
        print(f"已發送: {data.hex()}")
        time.sleep(1)
        
        # 測試案例 5: 連續發送不帶換行的訊息
        print("\n測試 5: 發送不帶換行的訊息")
        ser.write(b'No newline here')
        print("已發送: 'No newline here' (無換行)")
        time.sleep(1)
        
        # 測試案例 6: 只發送換行符
        print("\n測試 6: 只發送換行符 0D0A")
        ser.write(b'\x0D\x0A')
        print("已發送: 0d0a")
        time.sleep(1)
        
        # 互動模式
        print("\n進入互動模式 (輸入 'quit' 退出)")
        print("輸入格式:")
        print("  - 普通文字: 直接輸入")
        print("  - 16進位: 以 'hex:' 開頭，如 hex:48656C6C6F0D0A")
        print("  - 添加換行: 在訊息後加 '\\n'")
        
        while True:
            user_input = input("\n> ")
            
            if user_input.lower() == 'quit':
                break
                
            if user_input.startswith('hex:'):
                # 發送 16 進位資料
                hex_string = user_input[4:].strip()
                try:
                    hex_data = bytes.fromhex(hex_string)
                    ser.write(hex_data)
                    print(f"已發送 16 進位: {hex_data.hex()}")
                except ValueError:
                    print("錯誤: 無效的 16 進位字串")
            else:
                # 發送文字資料
                if user_input.endswith('\\n'):
                    # 替換 \\n 為實際的 CRLF
                    user_input = user_input[:-2] + '\r\n'
                    
                encoded = user_input.encode('utf-8')
                ser.write(encoded)
                print(f"已發送: '{user_input}' (hex: {encoded.hex()})")
        
        ser.close()
        print("\n串口已關閉")
        
    except serial.SerialException as e:
        print(f"串口錯誤: {e}")
    except KeyboardInterrupt:
        print("\n程式被中斷")
        if 'ser' in locals() and ser.is_open:
            ser.close()

def list_ports():
    """列出可用的串口"""
    import serial.tools.list_ports
    
    print("可用的串口:")
    ports = serial.tools.list_ports.comports()
    for port in ports:
        print(f"  {port.device} - {port.description}")
    
    if not ports:
        print("  未找到串口")

def main():
    """主程式"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print(f"  {sys.argv[0]} <串口名稱> [波特率]")
        print(f"  {sys.argv[0]} list  # 列出可用串口")
        print("\n範例:")
        print(f"  {sys.argv[0]} COM3")
        print(f"  {sys.argv[0]} /dev/ttyUSB0 115200")
        sys.exit(1)
    
    if sys.argv[1] == 'list':
        list_ports()
        sys.exit(0)
    
    port = sys.argv[1]
    baudrate = int(sys.argv[2]) if len(sys.argv) > 2 else 115200
    
    send_hex_data(port, baudrate)

if __name__ == "__main__":
    main()