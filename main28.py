import wmi
import sys
import time
import socket
import platform
import win32com.client
from win32com.client import pywintypes
import traceback
import os


def test_network_connectivity(server, port=135):
    """測試伺服器網路連線能力"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((server, port))
        sock.close()

        if result == 0:
            print(f"網路連線成功: 可以連接到 {server}:{port}")
            return True
        else:
            print(f"網路連線失敗: 無法連接到 {server}:{port}，錯誤碼: {result}")
            return False
    except socket.gaierror:
        print(f"網路連線錯誤: 無法解析主機名稱 {server}")
        return False
    except socket.error as e:
        print(f"網路連線錯誤: {e}")
        return False


def check_wmi_service_status(server, username=None, password=None):
    """檢查WMI服務狀態"""
    try:
        # 嘗試直接使用WMI來檢查服務
        conn = connect_to_server_wmi(server, username, password, check_service=False)
        if conn:
            services = conn.Win32_Service(Name="Winmgmt")
            if services:
                service = services[0]
                print(f"WMI服務狀態: {service.State}")
                return service.State == "Running"
            else:
                print("無法找到WMI服務")
                return False
    except Exception as e:
        print(f"檢查WMI服務時發生錯誤: {e}")
        print("將嘗試使用SC命令檢查...")

        # 如果WMI連接失敗，嘗試使用SC命令
        try:
            import subprocess
            result = subprocess.run(["sc", "\\\\" + server, "query", "Winmgmt"],
                                    capture_output=True, text=True, timeout=10)
            if "RUNNING" in result.stdout:
                print("WMI服務正在運行 (通過SC命令確認)")
                return True
            else:
                print(f"WMI服務未運行 (通過SC命令確認): {result.stdout}")
                return False
        except Exception as sc_err:
            print(f"使用SC命令檢查服務時出錯: {sc_err}")
            return False


def diagnose_wmi_error(error):
    """診斷WMI錯誤並提供詳細信息"""
    error_str = str(error)
    error_code = None

    # 嘗試解析錯誤碼
    try:
        if hasattr(error, 'com_error'):
            error_code = error.com_error.hresult
        elif ',' in error_str and '(' in error_str:
            parts = error_str.split('(')
            if len(parts) > 1:
                code_part = parts[1].split(',')[0]
                try:
                    error_code = int(code_part)
                except:
                    pass
    except:
        pass

    print(f"完整錯誤信息: {error_str}")
    print(f"錯誤代碼: {error_code if error_code else '無法確定'}")

    # 常見錯誤診斷
    if "呼叫被訊息篩選器取消" in error_str or "Call was canceled by the message filter" in error_str or error_code == -2147418110:
        print("\n可能原因: COM安全設定問題 (訊息篩選器取消)")
        print("建議解決方案:")
        print("1. 檢查DCOM配置和COM安全設定")
        print("2. 執行dcomcnfg.exe並檢查My Computer > Properties > Default Properties")
        print("   確保'Enable Distributed COM on this computer'已啟用")
        print("3. 檢查COM Security標籤中的權限設定")
        print("4. 您可能需要增加用戶在DCOM上的權限")
        print("5. 嘗試在管理員模式下運行程式")

    elif "Access is denied" in error_str or "拒絕存取" in error_str or error_code == -2147023510:
        print("\n可能原因: 權限不足")
        print("建議解決方案:")
        print("1. 確保使用的帳戶擁有管理員權限")
        print("2. 檢查伺服器上的Windows防火牆設定")
        print("3. 確認WMI服務已啟用且正在運行")

    elif "The RPC server is unavailable" in error_str or "RPC 伺服器無法使用" in error_str or error_code == -2147023174:
        print("\n可能原因: RPC服務問題")
        print("建議解決方案:")
        print("1. 確保RPC服務正在目標伺服器上運行")
        print("2. 檢查防火牆是否阻止了RPC流量 (端口135)")
        print("3. 確認網路連接是否正常")

    elif "User credentials cannot be used for local connections" in error_str or "使用者憑證無法用於本地連線" in error_str:
        print("\n可能原因: 為本地連接提供了認證")
        print("建議解決方案:")
        print("1. 連接本地電腦時不要提供用戶名和密碼")
        print("2. 如果是遠端連接，確保伺服器名稱正確")

    elif "Invalid class" in error_str or "無效的類別" in error_str:
        print("\n可能原因: WMI類別不存在或命名空間錯誤")
        print("建議解決方案:")
        print("1. 確認使用的WMI類別名稱和命名空間正確")
        print("2. 可能需要嘗試不同的命名空間")

    else:
        print("\n這是一個不常見的錯誤。建議:")
        print("1. 檢查網路連接")
        print("2. 確認伺服器WMI服務運行狀態")
        print("3. 檢查用戶權限")
        print("4. 確認防火牆設定")
        print("5. 嘗試使用不同的認證方式")


def try_alternative_connection_methods(server, username=None, password=None):
    """嘗試替代的連接方法"""
    methods_tried = 0
    methods_succeeded = 0

    print("\n嘗試替代連接方法...")

    # 方法1: 直接使用COM對象
    try:
        methods_tried += 1
        print("\n方法 1: 使用COM對象直接連接")
        locator = win32com.client.Dispatch("WbemScripting.SWbemLocator")

        if username and password:
            service = locator.ConnectServer(server, "root\\cimv2", username, password)
        else:
            service = locator.ConnectServer(server, "root\\cimv2")

        # 測試連接
        wql = "SELECT Caption FROM Win32_OperatingSystem"
        results = service.ExecQuery(wql)

        for os in results:
            print(f"操作系統: {os.Caption}")

        print("方法 1 成功!")
        methods_succeeded += 1
    except Exception as e:
        print(f"方法 1 失敗: {e}")

    # 方法2: 嘗試不同的命名空間
    namespaces = ["root\\cimv2", "root\\default", "root\\StandardCimv2", "root\\subscription"]
    for ns in namespaces:
        try:
            methods_tried += 1
            print(f"\n方法 2: 使用命名空間 {ns}")
            if username and password:
                conn = wmi.WMI(computer=server, user=username, password=password, namespace=ns)
            else:
                conn = wmi.WMI(computer=server, namespace=ns)

            # 嘗試基本查詢
            if ns == "root\\cimv2":
                result = conn.Win32_ComputerSystem()
            else:
                # 使用通用WQL查詢對於任何命名空間都應該有效
                wql = "SELECT * FROM __Namespace"
                result = conn.query(wql)

            if result:
                print(f"成功連接到命名空間 {ns}")
                methods_succeeded += 1

        except Exception as e:
            print(f"使用命名空間 {ns} 連接失敗: {e}")

    # 方法3: 嘗試WMI Moniker字符串
    try:
        methods_tried += 1
        print("\n方法 3: 使用WMI Moniker字符串")

        moniker = f"winmgmts:{'' if not username else '//' + username + ':' + password + '@'}{server}/root/cimv2"
        print(f"Moniker: {moniker.replace(password if password else '', '********')}")

        conn = win32com.client.GetObject(moniker)
        query = conn.ExecQuery("SELECT Name FROM Win32_Process WHERE Name='svchost.exe'")

        count = 0
        for item in query:
            count += 1
            if count == 1:
                print(f"找到進程: {item.Name}")
                break

        print("方法 3 成功!")
        methods_succeeded += 1
    except Exception as e:
        print(f"方法 3 失敗: {e}")

    print(f"\n替代方法摘要: 成功 {methods_succeeded}/{methods_tried}")
    return methods_succeeded > 0


def connect_to_server_wmi(server, username=None, password=None, namespace="root\\cimv2", check_service=True):
    """
    建立與遠端伺服器的WMI連接，並提供詳細的錯誤處理

    參數:
        server: 伺服器名稱或IP地址
        username: 用戶名稱 (如果需要驗證)
        password: 密碼 (如果需要驗證)
        namespace: WMI命名空間
        check_service: 是否檢查WMI服務狀態

    返回:
        成功時返回WMI連接物件，失敗時返回None
    """
    # 首先檢查目標伺服器是否可連接
    if not test_network_connectivity(server):
        print("無法建立到目標伺服器的網路連接。請檢查網路設定和防火牆規則。")
        return None

    # 如果目標是本地電腦但提供了認證，提供警告
    local_names = ['localhost', '127.0.0.1', '.', socket.gethostname()]
    if server.lower() in [name.lower() for name in local_names] and (username or password):
        print("警告: 連接本地電腦時不應提供用戶認證，將忽略提供的用戶名和密碼")
        username = None
        password = None

    print(f"正在嘗試連接到伺服器 {server} 的WMI服務 (命名空間: {namespace})...")

    try:
        # 記錄起始時間以測量連接時間
        start_time = time.time()

        # 根據是否提供認證建立連接
        if username and password:
            connection = wmi.WMI(
                computer=server,
                user=username,
                password=password,
                namespace=namespace,
                # privileges=["Security"]  # 添加安全權限
            )
        else:
            connection = wmi.WMI(
                computer=server,
                namespace=namespace,
                # privileges=["Security"]
            )

        # 計算連接耗時
        elapsed_time = time.time() - start_time
        print(f"成功連接到伺服器 {server} (耗時: {elapsed_time:.2f}秒)")

        return connection

    except wmi.x_wmi as e:
        print(f"WMI連接錯誤: {e}")

        # 詳細診斷錯誤
        diagnose_wmi_error(e)

        # 檢查WMI服務狀態
        if check_service:
            print("\n檢查WMI服務狀態...")
            if not check_wmi_service_status(server, username, password):
                print("WMI服務可能未正確運行。請檢查伺服器上的WMI服務狀態。")

        # 嘗試替代連接方法
        if try_alternative_connection_methods(server, username, password):
            print("\n替代連接方法成功。您可能需要修改程式碼以使用成功的方法。")

        return None

    except pywintypes.com_error as e:
        print(f"COM錯誤: {e}")
        diagnose_wmi_error(e)
        return None

    except Exception as e:
        print(f"發生未預期的錯誤: {e}")
        print("詳細錯誤信息:")
        traceback.print_exc()
        return None


def test_wmi_connection(connection):
    """測試WMI連接是否正常工作，並提供詳細信息"""
    if not connection:
        print("無法測試WMI連接: 連接對象為空")
        return False

    try:
        print("\n正在測試WMI連接...")

        # 測試1: 基本操作系統信息
        try:
            print("\n測試1: 獲取操作系統信息")
            os_info = connection.Win32_OperatingSystem()[0]
            print(f"操作系統: {os_info.Caption}")
            print(f"版本: {os_info.Version}")
            print(f"製造商: {os_info.Manufacturer}")
            print(f"系統目錄: {os_info.SystemDirectory}")
            print(f"最後啟動時間: {os_info.LastBootUpTime}")
            print("測試1成功!")
        except Exception as e:
            print(f"測試1失敗: {e}")

        # 測試2: 處理器信息
        try:
            print("\n測試2: 獲取處理器信息")
            for idx, processor in enumerate(connection.Win32_Processor(), 1):
                print(f"處理器 {idx}: {processor.Name}")
                print(f"  負載百分比: {processor.LoadPercentage}%")
                print(f"  核心數: {processor.NumberOfCores}")
                print(f"  線程數: {processor.NumberOfLogicalProcessors}")
            print("測試2成功!")
        except Exception as e:
            print(f"測試2失敗: {e}")

        # 測試3: 記憶體信息
        try:
            print("\n測試3: 獲取記憶體信息")
            computer = connection.Win32_ComputerSystem()[0]
            total_ram = float(computer.TotalPhysicalMemory) / (1024 ** 3)
            print(f"總物理記憶體: {total_ram:.2f} GB")

            memory_info = connection.Win32_PhysicalMemory()
            for idx, mem in enumerate(memory_info, 1):
                try:
                    capacity = float(mem.Capacity) / (1024 ** 3)
                    print(f"記憶體模組 {idx}: {capacity:.2f} GB")
                    if hasattr(mem, 'Speed') and mem.Speed:
                        print(f"  速度: {mem.Speed} MHz")
                except:
                    print(f"  記憶體模組 {idx}: 無法讀取信息")
            print("測試3成功!")
        except Exception as e:
            print(f"測試3失敗: {e}")

        # 測試4: 磁碟信息
        try:
            print("\n測試4: 獲取磁碟信息")
            disk_drives = connection.Win32_LogicalDisk(DriveType=3)  # 3 = 本地磁碟
            for disk in disk_drives:
                free_space = float(disk.FreeSpace) / (1024 ** 3)
                total_space = float(disk.Size) / (1024 ** 3)
                print(f"磁碟 {disk.DeviceID}")
                print(f"  總空間: {total_space:.2f} GB")
                print(f"  可用空間: {free_space:.2f} GB")
                print(f"  使用率: {((total_space - free_space) / total_space * 100):.2f}%")
            print("測試4成功!")
        except Exception as e:
            print(f"測試4失敗: {e}")

        # 測試5: 網路配置
        try:
            print("\n測試5: 獲取網路配置")
            nic_configs = connection.Win32_NetworkAdapterConfiguration(IPEnabled=True)
            for nic in nic_configs:
                print(f"網卡: {nic.Description}")
                if nic.IPAddress:
                    print(f"  IP地址: {', '.join(nic.IPAddress)}")
                if nic.DefaultIPGateway:
                    print(f"  預設閘道: {', '.join(nic.DefaultIPGateway)}")
                if nic.DNSServerSearchOrder:
                    print(f"  DNS伺服器: {', '.join(nic.DNSServerSearchOrder)}")
                print(f"  MAC地址: {nic.MACAddress}")
            print("測試5成功!")
        except Exception as e:
            print(f"測試5失敗: {e}")

        # 測試6: 服務狀態
        try:
            print("\n測試6: 獲取關鍵服務狀態")
            important_services = ["Winmgmt", "RpcSs", "LanmanServer", "W32Time"]
            for service_name in important_services:
                services = connection.Win32_Service(Name=service_name)
                if services:
                    service = services[0]
                    print(f"服務 {service.DisplayName} ({service.Name}):")
                    print(f"  狀態: {service.State}")
                    print(f"  啟動類型: {service.StartMode}")
                else:
                    print(f"找不到服務: {service_name}")
            print("測試6成功!")
        except Exception as e:
            print(f"測試6失敗: {e}")

        print("\n--------WMI連接測試摘要--------")
        print("請檢查上述測試結果。如果大多數測試成功，WMI連接基本正常。")
        print("注意: 即使某些測試失敗，也可能只是權限問題或特定類別不可用，而非連接完全失敗。")
        return True
    except Exception as e:
        print(f"測試WMI連接時發生錯誤: {e}")
        print("詳細錯誤信息:")
        traceback.print_exc()
        return False


def main():
    print("=" * 80)
    print(f"WMI 連接測試工具 - 運行於 {platform.node()}")
    print("=" * 80)

    # 檢查是否以管理員身份運行
    try:
        is_admin = os.path.isfile(os.path.join(os.environ.get("SystemRoot", "C:\\Windows"), "temp", "UAC.txt"))
        if not is_admin:
            print("警告: 此程式可能需要管理員權限才能正常工作。請考慮使用管理員身份重新運行。")
    except:
        print("無法確定程式是否以管理員身份運行。")

    # 使用固定值替代交互式輸入
    server = "HSWNS02"  # 您指定的伺服器
    username = "PowerUser"  # 您指定的用戶名
    password = "Power123"  # 您指定的密碼

    print(f"\n使用以下設定:")
    print(f"伺服器: {server}")
    print(f"用戶名: {username}")
    print(f"密碼: {'*' * len(password)}")

    # 開始連接測試
    print("\n開始WMI連接測試...")
    conn = connect_to_server_wmi(server, username, password)

    if conn:
        test_wmi_connection(conn)
    else:
        print("\n無法建立WMI連接，測試終止。")

        # 嘗試特殊處理對於 "呼叫被訊息篩選器取消" 錯誤
        print("\n由於您可能遇到了「呼叫被訊息篩選器取消」的錯誤，以下是特別的解決方案:")
        print("1. 嘗試使用完整的網域資訊:")
        print("   - 如果伺服器在域中，嘗試使用完整格式: DOMAIN\\PowerUser")

        print("\n2. 嘗試使用DCOM連接:")
        try:
            print("   正在嘗試使用DCOM直接連接...")
            locator = win32com.client.Dispatch("WbemScripting.SWbemLocator")
            locator.Security_.ImpersonationLevel = 3  # impersonate
            service = locator.ConnectServer(server, "root\\cimv2", username, password)
            print("   DCOM連接成功!")

            # 測試查詢
            query = service.ExecQuery("SELECT Caption FROM Win32_OperatingSystem")
            for os in query:
                print(f"   操作系統: {os.Caption}")

        except Exception as e:
            print(f"   DCOM連接失敗: {e}")

        print("\n3. 檢查DCOM設定:")
        print("   - 在伺服器上執行 dcomcnfg.exe")
        print("   - 檢查 Component Services > Computers > My Computer > DCOM Config")
        print("   - 尋找 'Windows Management Instrumentation' 並檢查其安全性設定")

        print("\n4. 嘗試修改防火牆:")
        print("   - 確保伺服器防火牆允許WMI流量 (TCP 135)")
        print("   - 在伺服器上，執行 'netsh firewall set service RemoteAdmin enable'")

        print("\n5. 系統管理工具解決方案:")
        print("   - 在伺服器上執行 'wmimgmt.msc'")
        print("   - 右鍵點擊 'WMI Control'，選擇 'Properties'")
        print("   - 前往 'Security' 標籤，確保您的用戶有適當的權限")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程式已被用戶中斷")
    except Exception as e:
        print(f"\n程式執行過程中發生未處理的錯誤: {e}")
        print("詳細錯誤信息:")
        traceback.print_exc()
    finally:
        print("\n程式已結束")