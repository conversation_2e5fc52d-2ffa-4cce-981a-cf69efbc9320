import os

from config import Config
from excel_processor import ExcelProcessor

def process_downloaded_files(file_paths: list) -> bool:
    """處理一批下載的檔案"""
    try:
        processor = ExcelProcessor(Config.DB_CONNECTION_STRING)
        return processor.process_excel_files(file_paths)
    except Exception as e:
        print(f"處理檔案時發生錯誤: {str(e)}")
        return False

# 使用範例
file_path = r'C:\Users\<USER>\PycharmProjects\TEST\report_23577737_20250101_20250108.xlsx'
file_path2 = r'C:\Users\<USER>\PycharmProjects\TEST\report_72801698_20250101_20250108.xlsx'

downloaded_files = [file_path, file_path2]
if process_downloaded_files(downloaded_files):
    print("所有檔案處理完成")
else:
    print("檔案處理過程中發生錯誤")