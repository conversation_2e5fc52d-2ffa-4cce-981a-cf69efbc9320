import winrm
import wmi
import pywintypes # 需要 pywin32
import sys
import traceback
import socket # 用於 WMI 錯誤診斷中的本地主機名比較

# --- 設定您的目標伺服器和憑證 ---
server = "172.20.160.34"
username = "HSWORA03\PowerUser"  # 可能需要嘗試 DOMAIN\PowerUser 或 HSWNS02\PowerUser
password = "Power123"
wmi_namespace = "root\\cimv2"
# ---------------------------------

print("="*30 + " WinRM 測試 " + "="*30)
print(f"正在嘗試使用 WinRM 連接至 {server}...")
print(f"使用者名稱: {username}")
print(f"密碼: {'*' * len(password)}")

winrm_auth_success = False
session = None
try:
    session = winrm.Session(
        server,
        auth=(username, password),
        transport='ntlm'
    )
    print(f"\nWinRM 基礎連線驗證成功！")
    print("這表示伺服器初步接受了您提供的帳號和密碼。")
    winrm_auth_success = True

    try:
        print("\n嘗試透過 WinRM 執行 'hostname' 指令...")
        result = session.run_cmd('hostname')
        if result.status_code == 0:
            print(f"WinRM 指令執行成功，伺服器主機名稱: {result.std_out.decode().strip()}")
        else:
            # 即使基礎驗證成功，執行也可能因權限不足失敗
            print(f"WinRM 指令執行失敗，狀態碼: {result.status_code}")
            print(f"錯誤輸出: {result.std_err.decode().strip()}")
            print("-> 這通常表示使用者權限不足以在伺服器上執行遠端指令。")
    except Exception as cmd_e:
        print(f"\n執行 WinRM 遠端指令時發生錯誤: {cmd_e}")
        print("-> 這通常表示使用者權限不足以在伺服器上建立執行會話。")

except winrm.exceptions.AuthenticationError as auth_err:
    print(f"\nWinRM 連線失敗：身份驗證錯誤！")
    print(f"錯誤訊息: {auth_err}")
    print("-> 請檢查使用者名稱、密碼、帳號狀態(鎖定/停用)。")

except winrm.exceptions.WinRMTransportError as trans_err:
    print(f"\nWinRM 連線失敗：傳輸錯誤！")
    print(f"錯誤訊息: {trans_err}")
    print("-> 請檢查網路連線、伺服器 WinRM 服務狀態、防火牆。")

except Exception as e:
    print(f"\nWinRM 測試中發生未預期的錯誤：")
    print(f"錯誤類型: {type(e).__name__}")
    print(f"錯誤訊息: {e}")

print("\n" + "="*30 + " WMI 測試 " + "="*30)

if not winrm_auth_success:
    print("由於 WinRM 基礎驗證失敗，可能無法進行 WMI 連線。仍將嘗試...")

# --- WMI 連線嘗試 ---
print(f"正在嘗試使用 WMI 連接至 {server} (命名空間: {wmi_namespace})...")
print(f"使用者名稱: {username}")
print(f"密碼: {'*' * len(password)}")

wmi_connection = None
try:
    # 建立 WMI 連線 - 注意：提供 user/password 時不應加 privileges
    wmi_connection = wmi.WMI(
        computer=server,
        user=username,
        password=password,
        namespace=wmi_namespace
    )
    print("\nWMI 連線成功！")

    # 嘗試執行一個簡單的 WMI 查詢
    try:
        print("\n嘗試執行 WMI 查詢 (取得作業系統名稱)...")
        os_info = wmi_connection.Win32_OperatingSystem()[0]
        print(f"WMI 查詢成功，作業系統: {os_info.Caption}")
    except Exception as query_e:
        print(f"\nWMI 查詢失敗: {query_e}")
        print("-> 雖然連線成功，但可能缺乏讀取特定 WMI 類別的權限。")

except wmi.x_wmi as wmi_err:
    print(f"\nWMI 連線失敗: {wmi_err}")
    error_str = str(wmi_err)
    print("\n可能原因分析:")
    if " Zugriff verweigert" in error_str or "Access is denied" in error_str or "存取被拒" in error_str:
        print("-> 權限不足 (Access Denied)。使用者需要 DCOM 遠端存取/啟用權限，以及目標 WMI 命名空間的 '遠端啟用' 權限。")
    elif "Der RPC-Server ist nicht verfügbar" in error_str or "The RPC server is unavailable" in error_str or "RPC 伺服器無法使用" in error_str:
        print("-> RPC 伺服器無法使用。請檢查伺服器上的 RPC 服務狀態及防火牆 (TCP 135)。")
    elif "Call was canceled by the message filter" in error_str or "呼叫被訊息篩選器取消" in error_str:
         print("-> 呼叫被訊息篩選器取消。通常與 DCOM 安全性設定或逾時有關，常伴隨權限問題。")
    else:
        print("-> 其他 WMI 錯誤，請參考詳細錯誤訊息。")

except pywintypes.com_error as com_err:
    print(f"\nWMI 連線失敗 (COM Error): {com_err}")
    # 解析常見 COM 錯誤碼
    hr = com_err.hresult
    print(f"HRESULT: {hr} ({hex(hr)})")
    if hr == -2147024891: # 0x80070005
        print("-> 權限不足 (Access Denied)。使用者需要 DCOM 遠端存取/啟用權限，以及目標 WMI 命名空間的 '遠端啟用' 權限。")
    elif hr == -2147023174: # 0x800706BA
        print("-> RPC 伺服器無法使用。請檢查伺服器上的 RPC 服務狀態及防火牆 (TCP 135)。")
    elif hr == -2147418110: # 0x80010102
        print("-> 呼叫被訊息篩選器取消。通常與 DCOM 安全性設定或逾時有關，常伴隨權限問題。")
    else:
        print("-> 其他 COM 錯誤，請參考詳細錯誤訊息。")

except Exception as e:
    print(f"\nWMI 測試中發生未預期的錯誤：")
    print(f"錯誤類型: {type(e).__name__}")
    print(f"錯誤訊息: {e}")
    print("詳細追蹤信息:")
    # traceback.print_exc() # 如果需要非常詳細的追蹤訊息，可以取消註解此行

finally:
    print("\n測試結束。")