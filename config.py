# config.py
"""設定檔模組"""
from typing import Dict, List, Tuple, Any


class Config:
    """系統設定類"""
    # 登入憑證
    PASSWORDS: List[str] = [
        "Aa123456789",  # 當前密碼 Aa123456789 song30809@
    ]
    DEFAULT_TAX_ID = "********" # ******** ********
    DEFAULT_ACCOUNT = "A959A728C41" # A959A728C41 A9245E88C41

    # 網站 URL
    BASE_URL = "https://www.einvoice.nat.gov.tw"
    DASHBOARD_URL = f"{BASE_URL}/dashboard"
    REPORT_URL = f"{DASHBOARD_URL}/btb/btb411w/offline"
    ADD_REPORT_URL = f"{REPORT_URL}/add"

    DB_CONNECTION_STRING = "system/manager@172.20.160.32:1521/hy"

    # 瀏覽器基本設定
    BROWSER_CONFIG = {
        "headless": False,
        "slow_mo": 100
    }

    # 視窗和顯示設定
    VIEWPORT_CONFIG = {
        "width": 1440,
        "height": 900,
    }

    WINDOW_SIZE = {
        "width": 1440,
        "height": 900,
    }

    # 超時設定
    DEFAULT_TIMEOUT = 30000  # 30 秒
    DOWNLOAD_TIMEOUT = 40  # 60 秒
    PAGE_LOAD_WAIT = 3  # 3 秒
    MAX_RETRIES = 5

    # 錯誤訊息對照表
    ERROR_MESSAGES: Dict[str, Tuple[bool, bool]] = {
        "登入失敗超過次數上限": (False, False),
        "帳號已鎖定": (False, False),
        "系統暫時無法服務": (False, True),
        "驗證碼錯誤": (False, True),
        "密碼錯誤": (False, False),
        "帳號密碼錯誤": (False, False)
    }