import sys
import polars as pl
import pandas as pd
from PyQt6.QtWidgets import (
    QApplication, QWidget, QPushButton, QVBoxLayout,
    QFileDialog, QLabel, QComboBox, QTextEdit, QMessageBox
)
from PyQt6.QtCore import Qt


class SalesAnalysisApp(QWidget):
    def __init__(self):
        super().__init__()

        self.initUI()

    def initUI(self):
        self.setWindowTitle("數據分析工具")
        self.setGeometry(200, 200, 500, 400)

        layout = QVBoxLayout()

        # 選擇文件按鈕
        self.btn_select_file = QPushButton("選擇 Excel/CSV 檔案")
        self.btn_select_file.clicked.connect(self.load_file)
        layout.addWidget(self.btn_select_file)

        # 顯示選擇的文件名
        self.label_file = QLabel("未選擇檔案")
        layout.addWidget(self.label_file)

        # 選擇分析欄位
        self.combo_column = QComboBox()
        layout.addWidget(QLabel("選擇分析的產品代號欄位:"))
        layout.addWidget(self.combo_column)

        # 分析按鈕
        self.btn_analyze = QPushButton("開始分析")
        self.btn_analyze.clicked.connect(self.analyze_data)
        layout.addWidget(self.btn_analyze)

        # 錯誤檢查結果
        self.text_errors = QTextEdit()
        self.text_errors.setReadOnly(True)
        layout.addWidget(QLabel("錯誤檢查結果:"))
        layout.addWidget(self.text_errors)

        # 下載 Excel 按鈕
        self.btn_save_excel = QPushButton("存成 Excel")
        self.btn_save_excel.clicked.connect(self.save_excel)
        self.btn_save_excel.setEnabled(False)
        layout.addWidget(self.btn_save_excel)

        self.setLayout(layout)
        self.df = None
        self.analysis_result = None

    def load_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "選擇檔案", "", "Excel Files (*.xlsx);;CSV Files (*.csv)")
        if not file_path:
            return

        self.label_file.setText(f"選擇的檔案: {file_path}")

        # 嘗試讀取檔案
        try:
            if file_path.endswith(".csv"):
                self.df = pl.read_csv(file_path)
            else:
                self.df = pl.read_excel(file_path)

            # 取得欄位名稱並顯示在下拉選單
            self.combo_column.clear()
            self.combo_column.addItems(self.df.columns)
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"無法讀取檔案: {str(e)}")
            self.df = None

    def analyze_data(self):
        if self.df is None:
            QMessageBox.warning(self, "警告", "請先選擇檔案！")
            return

        selected_column = self.combo_column.currentText()
        if not selected_column:
            QMessageBox.warning(self, "警告", "請選擇分析欄位！")
            return

        # 檢查數據格式
        errors = []
        if selected_column not in self.df.columns:
            errors.append(f"錯誤: 欄位 '{selected_column}' 不存在")

        required_columns = ["數量", "商品總價", "訂單狀態"]
        for col in required_columns:
            if col not in self.df.columns:
                errors.append(f"缺少必要欄位: {col}")

        # 顯示錯誤
        if errors:
            self.text_errors.setText("\n".join(errors))
            return

        # 執行分析
        try:
            self.analysis_result = self.df.groupby(selected_column).agg([
                pl.col("數量").sum().alias("總銷售數量"),
                pl.col("商品總價").sum().alias("總銷售金額"),
                pl.col("訂單狀態").count().alias("訂單數量")
            ])

            # 顯示分析結果
            self.text_errors.setText(f"分析完成！\n共 {len(self.analysis_result)} 個產品代號")
            self.btn_save_excel.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"分析過程中發生錯誤: {str(e)}")

    def save_excel(self):
        if self.analysis_result is None:
            QMessageBox.warning(self, "警告", "尚未進行分析")
            return

        file_path, _ = QFileDialog.getSaveFileName(self, "儲存 Excel", "", "Excel Files (*.xlsx)")
        if not file_path:
            return

        try:
            self.analysis_result.to_pandas().to_excel(file_path, index=False)
            QMessageBox.information(self, "成功", f"已存為 Excel: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"儲存 Excel 失敗: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SalesAnalysisApp()
    window.show()
    sys.exit(app.exec())
