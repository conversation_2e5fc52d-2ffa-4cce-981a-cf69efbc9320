#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試 STM32 Bootloader 寫入問題
分析為什麼在第 11 個數據塊時出錯
"""

def analyze_log():
    """分析日誌中的問題"""
    print("===== STM32 Bootloader 寫入問題分析 =====\n")
    
    # 成功的寫入操作
    successful_writes = [
        (0x08000000, 256),
        (0x08000100, 256),
        (0x08000200, 256),
        (0x08000300, 256),
        (0x08000400, 256),
        (0x08000500, 256),
        (0x08000600, 256),
        (0x08000700, 256),
        (0x08000800, 256),
        (0x08000900, 256),
    ]
    
    print("成功寫入的數據塊：")
    for i, (addr, size) in enumerate(successful_writes):
        print(f"  {i+1}. 地址: 0x{addr:08X}, 大小: {size} 字節")
    
    print(f"\n共成功寫入: {len(successful_writes)} 個數據塊")
    print(f"總數據量: {sum(size for _, size in successful_writes)} 字節")
    
    # 失敗的寫入
    print("\n失敗的寫入操作：")
    print("  11. 地址: 0x08000A00 - 成功接收地址")
    print("      但在接收數據時出錯")
    
    # 分析失敗的數據
    print("\n問題分析：")
    print("1. 前 10 個數據塊都成功寫入，每個 256 字節")
    print("2. 第 11 個數據塊在發送數據後出現問題")
    print("3. 接收到的數據：")
    print("   FF 00 20 2B E0 4F F0 01 0C 0C FA 00 F3 B1 F8 00 C0 0C EA 03 06 9E 42 20")
    print("   ^^ 這是數據長度字節 (0xFF = 255)")
    print("   之後應該有 256 字節數據 + 1 字節校驗和")
    
    print("\n可能的原因：")
    print("1. 數據分片問題：256 字節數據可能被分成多個包發送")
    print("2. 緩衝區問題：數據還沒完全到達就開始處理")
    print("3. 狀態機問題：在等待數據時，狀態被錯誤地改變")
    
    print("\n建議的修復：")
    print("1. 確保等待所有數據到達後再處理")
    print("2. 在寫入數據狀態時，不要將接收的數據當作命令處理")
    print("3. 增加更詳細的調試輸出，顯示緩衝區狀態")

def simulate_write_data_packet():
    """模擬寫入數據包的格式"""
    print("\n\n===== STM32 寫入數據包格式 =====")
    
    print("\n1. 寫入命令流程：")
    print("   → 發送: 31 CE (WRITE_MEMORY 命令)")
    print("   ← 接收: 79 (ACK)")
    print("   → 發送: 08 00 0A 00 02 (地址 0x08000A00 + 校驗和)")
    print("   ← 接收: 79 (ACK)")
    print("   → 發送: FF [256字節數據] [校驗和]")
    print("   ← 接收: 79 (ACK)")
    
    print("\n2. 數據包格式：")
    print("   [N-1] [DATA...] [CHECKSUM]")
    print("   - N-1 = 0xFF (表示 256 字節)")
    print("   - DATA = 256 字節的實際數據")
    print("   - CHECKSUM = N-1 XOR 所有數據字節")
    
    print("\n3. 問題點：")
    print("   模擬器在接收到 0xFF 後，應該等待 256 + 1 = 257 字節")
    print("   但看起來數據還沒完全到達就開始處理了")

if __name__ == "__main__":
    analyze_log()
    simulate_write_data_packet()