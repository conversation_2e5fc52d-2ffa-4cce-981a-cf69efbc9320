"""
家樂福爬蟲安裝和設定腳本
此腳本會自動安裝所需的依賴並提供使用說明
"""

import subprocess
import sys
import os

def install_requirements():
    """安裝所需的 Python 套件"""
    requirements = [
        "playwright",
        "asyncio",
        "beautifulsoup4",
        "requests"
    ]
    
    print("正在安裝所需的 Python 套件...")
    
    for package in requirements:
        try:
            print(f"安裝 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安裝成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {package} 安裝失敗: {e}")
            return False
    
    return True

def install_playwright_browsers():
    """安裝 Playwright 瀏覽器"""
    print("\n正在安裝 Playwright 瀏覽器...")
    try:
        subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
        print("✓ Chromium 瀏覽器安裝成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Playwright 瀏覽器安裝失敗: {e}")
        return False

def create_requirements_txt():
    """創建 requirements.txt 檔案"""
    requirements_content = """playwright>=1.40.0
asyncio
beautifulsoup4>=4.12.0
requests>=2.31.0
"""
    
    with open("requirements.txt", "w", encoding="utf-8") as f:
        f.write(requirements_content)
    
    print("✓ 已創建 requirements.txt 檔案")

def create_usage_example():
    """創建使用範例腳本"""
    example_content = '''"""
家樂福爬蟲使用範例
"""

import asyncio
from carrefour_scraper import CarrefourScraper

async def example_usage():
    """使用範例"""
    
    # 創建爬蟲實例
    scraper = CarrefourScraper()
    
    print("=== 家樂福產品爬蟲範例 ===")
    print("目標網站: 家樂福黑松品牌館")
    print("開始爬取...")
    
    # 爬取產品 (最多 2 頁，可以看到瀏覽器操作)
    products = await scraper.scrape_products(
        headless=False,  # 設為 True 可隱藏瀏覽器視窗
        max_pages=2      # 爬取頁數
    )
    
    # 顯示結果
    scraper.print_summary()
    
    # 儲存結果
    if products:
        scraper.save_to_csv("example_products.csv")
        scraper.save_to_json("example_products.json")
        
        print(f"\\n找到的前 3 個產品:")
        for i, product in enumerate(products[:3], 1):
            print(f"{i}. 產品名稱: {product['name']}")
            print(f"   價格: NT$ {product['price']:.0f}")
            print(f"   連結: {product['product_url']}")
            print()

def custom_scraper_example():
    """自訂爬蟲範例"""
    
    async def custom_scrape():
        scraper = CarrefourScraper()
        
        # 可以修改目標 URL
        scraper.target_url = "https://online.carrefour.com.tw/zh/其他品牌館網址"
        
        # 爬取產品
        products = await scraper.scrape_products(headless=True, max_pages=1)
        
        # 篩選特定價格範圍的產品
        filtered_products = [p for p in products if 100 <= p['price'] <= 500]
        
        print(f"價格在 NT$ 100-500 之間的產品: {len(filtered_products)} 個")
        
        return filtered_products
    
    return asyncio.run(custom_scrape())

if __name__ == "__main__":
    # 執行基本範例
    asyncio.run(example_usage())
'''
    
    with open("scraper_example.py", "w", encoding="utf-8") as f:
        f.write(example_content)
    
    print("✓ 已創建 scraper_example.py 使用範例")

def print_usage_instructions():
    """列印使用說明"""
    instructions = """
=== 家樂福爬蟲使用說明 ===

1. 基本使用:
   python carrefour_scraper.py

2. 使用範例:
   python scraper_example.py

3. 自訂使用:
   from carrefour_scraper import CarrefourScraper
   
   scraper = CarrefourScraper()
   products = await scraper.scrape_products(headless=True, max_pages=5)

4. 參數說明:
   - headless: True=隱藏瀏覽器, False=顯示瀏覽器操作
   - max_pages: 最多爬取幾頁

5. 輸出檔案:
   - CSV 格式: carrefour_products_YYYYMMDD_HHMMSS.csv
   - JSON 格式: carrefour_products_YYYYMMDD_HHMMSS.json

6. 注意事項:
   - 請遵守網站的 robots.txt 和使用條款
   - 建議在爬取間隔加入適當的延遲
   - 大量爬取時請考慮對網站伺服器的影響

7. 常見問題:
   - 如果爬取失敗，可能是網站結構改變，需要更新選擇器
   - 網路連線問題可能導致超時，可以增加等待時間
   - 某些產品可能因為動態載入而無法抓取

8. 進階功能:
   - 可以修改 target_url 來爬取其他品牌館
   - 可以調整選擇器來適應不同的網頁結構
   - 可以加入更多的產品資訊欄位
"""
    
    print(instructions)

def main():
    """主安裝流程"""
    print("=== 家樂福爬蟲安裝程式 ===\n")
    
    # 1. 安裝 Python 套件
    if not install_requirements():
        print("套件安裝失敗，請檢查網路連線和 Python 環境")
        return
    
    # 2. 安裝 Playwright 瀏覽器
    if not install_playwright_browsers():
        print("瀏覽器安裝失敗，請手動執行: python -m playwright install chromium")
        return
    
    # 3. 創建相關檔案
    create_requirements_txt()
    create_usage_example()
    
    print("\n=== 安裝完成 ===")
    print("✓ 所有依賴已安裝完成")
    print("✓ 範例檔案已創建")
    
    # 4. 顯示使用說明
    print_usage_instructions()
    
    print("\n現在您可以執行以下命令來測試爬蟲:")
    print("python carrefour_scraper.py")
    print("或")
    print("python scraper_example.py")

if __name__ == "__main__":
    main()
