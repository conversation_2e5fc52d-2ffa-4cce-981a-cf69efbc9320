def calculate_checksum(data):
    # 從 S2(Sent Length) 到 S11(Index)
    checksum_data = data[1:58]
    # 計算 ASCII 碼值的總和
    checksum = sum(ord(c) for c in checksum_data)
    # 轉換為 16 進制字符串
    hex_checksum = format(checksum, '04X')
    # 轉換為 10 進制
    decimal_checksum = int(hex_checksum, 16)
    return f"{decimal_checksum:04d}"

def validate_checksum(data, expected_checksum):
    calculated_checksum = calculate_checksum(data)
    print(f"Calculated Checksum: {calculated_checksum}")
    print(f"Expected Checksum: {expected_checksum}")
    return calculated_checksum == expected_checksum

def parse_data(data):
    parsed_data = {}
    parsed_data['Sent Head'] = data[0:1]
    parsed_data['Sent Length'] = data[1:3]
    parsed_data['VMC NO'] = data[3:9]
    parsed_data['Time'] = data[9:23]
    parsed_data['Channel NO'] = data[23:26]
    parsed_data['CoinALL'] = data[26:29]
    parsed_data['CoinCHG'] = data[29:32]
    parsed_data['Barcode'] = data[32:48]
    parsed_data['Trade_Type'] = data[48:49]
    parsed_data['NC'] = data[49:54]
    parsed_data['Index'] = data[54:58]
    parsed_data['Chksum'] = data[58:62]
    parsed_data['Sent EOT'] = data[62:63]
    return parsed_data

# 正確的數據
received_data = "V6300000220240711150136001016001012345678912345605432112342880E"

# 取出傳入的 Chksum 值
expected_checksum = received_data[58:62]

# 驗證 Chksum
if validate_checksum(received_data[:58] + received_data[62:], expected_checksum):
    print("Checksum 驗證通過")
    # 解析數據
    parsed_data = parse_data(received_data)
    print("Parsed Data:", parsed_data)
else:
    print("Checksum 驗證失敗")

# 額外的檢查
print(f"Calculated checksum in hex: 0x{int(calculate_checksum(received_data[:58] + received_data[62:])):04X}")
print(f"Expected checksum: {expected_checksum}")