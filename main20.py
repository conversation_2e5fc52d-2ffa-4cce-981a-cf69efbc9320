# main.py
"""主程式模組"""
import os

from playwright.sync_api import sync_playwright
from datetime import datetime
import sys
import time
import argparse
from typing import Optional

from config import Config
from excel_processor import ExcelProcessor
from utils import get_date_periods, format_filename
from page_handlers import ReportHandler
from browser_manager import <PERSON>rowserManager

def process_report(report_handler: ReportHandler, start_date: str, end_date: str, retry_count: int = 3) -> bool:
    """
    處理單個報表的下載

    Args:
        report_handler: 報表處理器
        start_date: 開始日期
        end_date: 結束日期
        retry_count: 重試次數

    Returns:
        bool: 是否成功
    """
    for retry in range(retry_count):
        print(f"\n執行第 {retry + 1}/{retry_count} 次報表查詢...")

        # 導航到報表頁面
        report_handler.page.goto(Config.ADD_REPORT_URL)
        time.sleep(5)

        # 選擇統編
        if not report_handler.select_tax_id():
            print("統編選擇失敗")
            continue

        # 設置日期範圍
        if not report_handler.set_date_range(start_date, end_date):
            print("日期設置失敗")
            continue

        # 設置報表選項
        if not report_handler.setup_report_options():
            print("報表選項設置失敗")
            continue

        # 點擊儲存並等待下載
        print("\n點擊儲存按鈕...")
        save_button = report_handler.page.locator('button:has-text("儲存")')
        if save_button.is_visible():

            save_button.click()
            print("已點擊儲存按鈕")
            time.sleep(2)

            # 等待下載完成
            if report_handler.wait_and_download(start_date, end_date):
                return True
        else:
            print("找不到儲存按鈕")

        print(f"第 {retry + 1} 次嘗試失敗")
        time.sleep(5)

    return False


def process_downloaded_files(file_paths: list) -> bool:
    """處理一批下載的檔案"""
    try:
        processor = ExcelProcessor(Config.DB_CONNECTION_STRING)

        for file_path in file_paths:
            if os.path.exists(file_path):
                print(f"\n處理檔案: {file_path}")
                if processor.process_excel_file(file_path, id_type=0):
                    print(f"✅ 檔案 {file_path} 處理完成")
                else:
                    print(f"❌ 檔案 {file_path} 處理失敗")
                    return False
            else:
                print(f"❌ 找不到下載的檔案: {file_path}")
                return False

        return True

    except Exception as e:
        print(f"處理檔案時發生錯誤: {str(e)}")
        return False

def run(target_date: Optional[datetime] = None) -> None:
    """
    主運行函數

    Args:
        target_date: 目標日期，如果未指定則使用當前日期
    """
    if target_date is None:
        target_date = datetime.now()

    with sync_playwright() as playwright:
        browser_manager = BrowserManager(playwright)
        success = False

        try:
            # 初始化瀏覽器
            if not browser_manager.initialize():
                print("瀏覽器初始化失敗")
                sys.exit(1)

            # 執行登入
            if not browser_manager.login():
                print("\n=== 登入失敗 ===")
                print("所有密碼都嘗試失敗")
                sys.exit(1)

            # 取得目標月份的時期
            periods = get_date_periods(target_date)
            if not periods:
                print("指定的月份沒有可處理的時期")
                return

            report_handler = ReportHandler(
                page=browser_manager.page,
                browser_manager=browser_manager
            )
            report_handler.page.browser_manager = browser_manager

            # 處理每個時期
            for period_idx, (start_date, end_date) in enumerate(periods, 1):
                print(f"\n=== 處理第 {period_idx}/{len(periods)} 個時期 ({start_date} ~ {end_date}) ===")

                # 下載報表
                file_path = format_filename(start_date, end_date)
                success = process_report(report_handler, start_date, end_date)

                if not success:
                    print(f"\n第 {period_idx} 期下載失敗")
                    continue

                # 等待確保所有檔案下載完成
                print("\n等待確保所有檔案下載完成...")
                time.sleep(10)

                # 處理所有下載的檔案
                downloaded_files = browser_manager.get_downloaded_files()
                if not downloaded_files:
                    print("沒有發現任何下載的檔案")
                    continue

                print(f"\n開始處理下載的檔案，共 {len(downloaded_files)} 個：")
                if process_downloaded_files(downloaded_files):
                    print("所有檔案處理完成")
                else:
                    print("檔案處理過程中發生錯誤")

                # 清空已處理的檔案列表
                browser_manager.downloaded_files.clear()

        except Exception as e:
            print(f"\n=== 發生錯誤 ===")
            print(f"錯誤訊息：{str(e)}")
            if browser_manager.page:
                error_screenshot = f"error_{time.strftime('%Y%m%d_%H%M%S')}.png"
                browser_manager.page.screenshot(path=error_screenshot)
                print(f"已保存錯誤截圖：{error_screenshot}")
            sys.exit(1)

        finally:
            if success:
                print("\n=== 等待下載完成後關閉瀏覽器 ===")
                if not browser_manager.download_finished:
                    print("等待最後下載完成...")
                    time.sleep(10)

                # 獲取並處理下載的檔案
                downloaded_files = browser_manager.get_downloaded_files()
                if process_downloaded_files(downloaded_files):
                    print("所有檔案處理完成")
                else:
                    print("檔案處理過程中發生錯誤")

            browser_manager.cleanup()
            print("程式執行完成！")

            if not success:
                sys.exit(1)


def parse_args():
    """解析命令列參數"""
    parser = argparse.ArgumentParser(description='發票管理系統報表下載工具')
    parser.add_argument('--date',
                        help='指定目標日期 (格式: YYYY-MM-DD)',
                        default=None)

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    DEFAULT_DATE = ""  # 預設值可以留空

    try:
        target_date = (
            datetime.strptime(args.date, '%Y-%m-%d') if args.date
            else datetime.strptime(DEFAULT_DATE, '%Y-%m-%d') if DEFAULT_DATE
            else None
        )
    except ValueError:
        print('日期格式錯誤，請使用 YYYY-MM-DD 格式')
        sys.exit(1)

    run(target_date)
