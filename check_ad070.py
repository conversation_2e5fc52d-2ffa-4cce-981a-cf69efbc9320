import ldap3

def check_ad070_structure():
    """檢查 AD070 部門結構"""
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389)
    conn = ldap3.Connection(server, user='<EMAIL>', password='Tt9566659')
    
    if conn.bind():
        print('✅ 連接成功，開始搜尋 AD070 部門...')
        
        # 1. 搜尋使用者 16613 的詳細資訊
        print('\n1. 檢查使用者 16613 的部門資訊:')
        result = conn.search('DC=heysong,DC=com,DC=tw', '(sAMAccountName=16613)', ldap3.SUBTREE,
                           attributes=['distinguishedName', 'sAMAccountName', 'department', 'departmentNumber', 
                                     'ou', 'description', 'title', 'company', 'memberOf'])
        
        if result and conn.entries:
            entry = conn.entries[0]
            print(f'   使用者: {entry.sAMAccountName}')
            print(f'   DN: {entry.distinguishedName}')
            print(f'   部門: {getattr(entry, "department", "未設定")}')
            print(f'   部門編號: {getattr(entry, "departmentNumber", "未設定")}')
            print(f'   描述: {getattr(entry, "description", "未設定")}')
            print(f'   職稱: {getattr(entry, "title", "未設定")}')
            print(f'   公司: {getattr(entry, "company", "未設定")}')
            if hasattr(entry, 'memberOf'):
                print(f'   群組成員: {entry.memberOf}')
        
        # 2. 搜尋包含 AD070 的群組
        print('\n2. 搜尋 AD070 相關群組:')
        result = conn.search('DC=heysong,DC=com,DC=tw', '(cn=*AD070*)', ldap3.SUBTREE,
                           attributes=['distinguishedName', 'cn', 'description'])
        
        if result and conn.entries:
            print(f'   找到 {len(conn.entries)} 個 AD070 相關群組:')
            for entry in conn.entries:
                print(f'     - {entry.cn}: {entry.distinguishedName}')
        else:
            print('   未找到 AD070 相關群組')
        
        # 3. 搜尋 OU=AD070
        print('\n3. 搜尋 OU=AD070 組織單位:')
        try:
            result = conn.search('OU=AD070,DC=heysong,DC=com,DC=tw', '(objectClass=*)', ldap3.BASE,
                               attributes=['distinguishedName', 'ou', 'description'])
            
            if result and conn.entries:
                print('   ✅ 找到 OU=AD070 組織單位')
                entry = conn.entries[0]
                print(f'     DN: {entry.distinguishedName}')
                print(f'     描述: {getattr(entry, "description", "未設定")}')
            else:
                print('   ❌ 未找到 OU=AD070 組織單位')
        except Exception as e:
            print(f'   ❌ 搜尋 OU=AD070 錯誤: {e}')
        
        # 4. 搜尋在 AD070 OU 下的使用者
        print('\n4. 搜尋 AD070 組織單位下的使用者:')
        try:
            result = conn.search('OU=AD070,DC=heysong,DC=com,DC=tw', 
                               '(&(objectCategory=Person)(objectClass=User))', ldap3.SUBTREE,
                               attributes=['distinguishedName', 'sAMAccountName', 'cn'])
            
            if result and conn.entries:
                print(f'   ✅ 找到 {len(conn.entries)} 個 AD070 部門使用者:')
                for entry in conn.entries[:5]:  # 只顯示前5個
                    print(f'     - {entry.sAMAccountName}: {entry.cn}')
            else:
                print('   ❌ 未找到 AD070 部門使用者')
        except Exception as e:
            print(f'   ❌ 搜尋 AD070 使用者錯誤: {e}')
        
        # 5. 搜尋部門屬性包含 AD070 的使用者
        print('\n5. 搜尋部門屬性包含 AD070 的使用者:')
        filters = [
            '(&(objectCategory=Person)(objectClass=User)(department=*AD070*))',
            '(&(objectCategory=Person)(objectClass=User)(departmentNumber=*AD070*))',
            '(&(objectCategory=Person)(objectClass=User)(description=*AD070*))'
        ]
        
        for i, search_filter in enumerate(filters, 1):
            print(f'   測試篩選器 {i}: {search_filter}')
            try:
                result = conn.search('DC=heysong,DC=com,DC=tw', search_filter, ldap3.SUBTREE,
                                   attributes=['distinguishedName', 'sAMAccountName', 'department', 'departmentNumber'])
                
                if result and conn.entries:
                    print(f'     ✅ 找到 {len(conn.entries)} 個使用者')
                    for entry in conn.entries[:3]:  # 只顯示前3個
                        print(f'       - {entry.sAMAccountName}')
                else:
                    print('     ❌ 未找到使用者')
            except Exception as e:
                print(f'     ❌ 搜尋錯誤: {e}')
        
        conn.unbind()
        
        # 6. 建議的 Gitea 設定
        print('\n💡 建議的 Gitea LDAP 限制設定:')
        print('根據上述測試結果，選擇最適合的方案:')
        print()
        print('方案 1 - 如果 AD070 是組織單位 (OU):')
        print('  用戶搜尋基準: OU=AD070,DC=heysong,DC=com,DC=tw')
        print('  使用者篩選器: (&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s))')
        print()
        print('方案 2 - 如果 AD070 是部門屬性:')
        print('  用戶搜尋基準: DC=heysong,DC=com,DC=tw')
        print('  使用者篩選器: (&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s)(department=*AD070*))')
        print()
        print('方案 3 - 如果 AD070 是群組:')
        print('  用戶搜尋基準: DC=heysong,DC=com,DC=tw')
        print('  使用者篩選器: (&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s)(memberOf=CN=AD070群組名稱,OU=...,DC=heysong,DC=com,DC=tw))')
        
    else:
        print('❌ LDAP 連接失敗')

if __name__ == "__main__":
    check_ad070_structure()
