2025-04-10 14:00:59,442 - PowerAutomateAutomation - INFO - ==================================================
2025-04-10 14:00:59,442 - PowerAutomateAutomation - INFO - 自動化Power Automate腳本啟動
2025-04-10 14:00:59,454 - PowerAutomateAutomation - INFO - 已加載配置: {'power_automate_path': 'C:\\Program Files (x86)\\Power Automate Desktop\\PAD.exe', 'timeout': 120, 'flows': [{'name': '測試API', 'wait_time': 30}, {'name': '另一個流程', 'wait_time': 60}], 'timing_mode': 'normal'}
2025-04-10 14:00:59,454 - PowerAutomateAutomation - INFO - 正在啟動或連接到Power Automate...
2025-04-10 14:01:09,321 - PowerAutomateAutomation - INFO - 沒有找到運行中的Power Automate，啟動新實例
2025-04-10 14:01:09,322 - PowerAutomateAutomation - WARNING - 嘗試 1/3 失敗: Could not create the process "C:\Program Files (x86)\Power Automate Desktop\PAD.exe"
Error returned by CreateProcess: (2, 'CreateProcess', '系統找不到指定的檔案。')
2025-04-10 14:01:09,322 - PowerAutomateAutomation - INFO - 等待 2 秒後重試...
2025-04-10 14:01:11,323 - PowerAutomateAutomation - INFO - 正在啟動或連接到Power Automate...
2025-04-10 14:01:21,548 - PowerAutomateAutomation - INFO - 沒有找到運行中的Power Automate，啟動新實例
2025-04-10 14:01:21,548 - PowerAutomateAutomation - WARNING - 嘗試 2/3 失敗: Could not create the process "C:\Program Files (x86)\Power Automate Desktop\PAD.exe"
Error returned by CreateProcess: (2, 'CreateProcess', '系統找不到指定的檔案。')
2025-04-10 14:01:21,549 - PowerAutomateAutomation - INFO - 等待 2 秒後重試...
2025-04-10 14:01:23,549 - PowerAutomateAutomation - INFO - 正在啟動或連接到Power Automate...
2025-04-10 14:01:30,482 - PowerAutomateAutomation - INFO - 沒有找到運行中的Power Automate，啟動新實例
2025-04-10 14:01:30,482 - PowerAutomateAutomation - WARNING - 嘗試 3/3 失敗: Could not create the process "C:\Program Files (x86)\Power Automate Desktop\PAD.exe"
Error returned by CreateProcess: (2, 'CreateProcess', '系統找不到指定的檔案。')
2025-04-10 14:01:30,482 - PowerAutomateAutomation - ERROR - 達到最大重試次數。最後錯誤: Could not create the process "C:\Program Files (x86)\Power Automate Desktop\PAD.exe"
Error returned by CreateProcess: (2, 'CreateProcess', '系統找不到指定的檔案。')
2025-04-10 14:01:30,482 - PowerAutomateAutomation - ERROR - 自動化過程中出現未處理的錯誤: Could not create the process "C:\Program Files (x86)\Power Automate Desktop\PAD.exe"
Error returned by CreateProcess: (2, 'CreateProcess', '系統找不到指定的檔案。')
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\TEST\venv\Lib\site-packages\pywinauto\timings.py", line 436, in wait_until_passes
    func_val = func(*args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\PycharmProjects\TEST\venv\Lib\site-packages\pywinauto\application.py", line 1458, in process_from_module
    raise ProcessNotFoundError(message)
pywinauto.application.ProcessNotFoundError: Could not find any accessible process with a module of 'PAD.exe'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\TEST\venv\Lib\site-packages\pywinauto\application.py", line 976, in connect
    self.process = timings.wait_until_passes(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\PycharmProjects\TEST\venv\Lib\site-packages\pywinauto\timings.py", line 458, in wait_until_passes
    raise err
pywinauto.timings.TimeoutError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\TEST\main29.py", line 109, in start_power_automate
    self.app = Application(backend="uia").connect(path=os.path.basename(self.config["power_automate_path"]))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\PycharmProjects\TEST\venv\Lib\site-packages\pywinauto\application.py", line 981, in connect
    raise ProcessNotFoundError('Process "{}" not found!'.format(kwargs['path']))
pywinauto.application.ProcessNotFoundError: Process "PAD.exe" not found!

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\TEST\venv\Lib\site-packages\pywinauto\application.py", line 1038, in start
    (h_process, _, dw_process_id, _) = win32process.CreateProcess(
                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^
pywintypes.error: (2, 'CreateProcess', '系統找不到指定的檔案。')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\TEST\main29.py", line 349, in <module>
    automation.start_power_automate()
  File "C:\Users\<USER>\PycharmProjects\TEST\main29.py", line 52, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\PycharmProjects\TEST\main29.py", line 114, in start_power_automate
    self.app = Application(backend="uia").start(self.config["power_automate_path"])
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\PycharmProjects\TEST\venv\Lib\site-packages\pywinauto\application.py", line 1052, in start
    raise AppStartError(message)
pywinauto.application.AppStartError: Could not create the process "C:\Program Files (x86)\Power Automate Desktop\PAD.exe"
Error returned by CreateProcess: (2, 'CreateProcess', '系統找不到指定的檔案。')
