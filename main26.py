import os
import sys
import mammoth
import re
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading


class DocxToMarkdownApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Word轉Markdown工具")
        self.root.geometry("600x350")
        self.root.resizable(False, False)

        # 主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 標題
        title_label = ttk.Label(main_frame, text="Word檔案轉Markdown工具", font=("微軟正黑體", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # 檔案選擇框架
        file_frame = ttk.Frame(main_frame)
        file_frame.pack(fill=tk.X, pady=5)

        ttk.Label(file_frame, text="Word檔案:").pack(side=tk.LEFT, padx=(0, 5))

        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=50)
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        browse_button = ttk.Button(file_frame, text="瀏覽...", command=self.browse_file)
        browse_button.pack(side=tk.LEFT)

        # 輸出位置框架
        output_frame = ttk.Frame(main_frame)
        output_frame.pack(fill=tk.X, pady=5)

        ttk.Label(output_frame, text="輸出位置:").pack(side=tk.LEFT, padx=(0, 5))

        self.output_path_var = tk.StringVar(value=os.path.join(os.path.expanduser("~"), "Desktop"))
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, width=50)
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        output_browse_button = ttk.Button(output_frame, text="瀏覽...", command=self.browse_output_dir)
        output_browse_button.pack(side=tk.LEFT)

        # 轉換選項框架
        options_frame = ttk.Frame(main_frame)
        options_frame.pack(fill=tk.X, pady=5)

        self.table_format_var = tk.StringVar(value="html")
        ttk.Label(options_frame, text="表格處理方式:").pack(side=tk.LEFT, padx=(0, 5))

        html_radio = ttk.Radiobutton(options_frame, text="保留HTML表格",
                                     variable=self.table_format_var, value="html")
        html_radio.pack(side=tk.LEFT, padx=(0, 10))

        md_radio = ttk.Radiobutton(options_frame, text="轉換為Markdown表格",
                                   variable=self.table_format_var, value="markdown")
        md_radio.pack(side=tk.LEFT)

        # 進度條
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=10)

        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(progress_frame, orient=tk.HORIZONTAL,
                                        length=100, mode='determinate',
                                        variable=self.progress_var)
        self.progress.pack(fill=tk.X, pady=5)

        self.status_var = tk.StringVar(value="請選擇Word檔案以開始轉換")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var, foreground="gray")
        status_label.pack(anchor=tk.W)

        # 按鈕框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        self.convert_button = ttk.Button(button_frame, text="開始轉換",
                                         command=self.start_conversion, width=20)
        self.convert_button.pack(side=tk.LEFT, padx=5)

        exit_button = ttk.Button(button_frame, text="結束程式",
                                 command=root.destroy, width=20)
        exit_button.pack(side=tk.RIGHT, padx=5)

        # 底部說明
        note_label = ttk.Label(main_frame, text="注意：轉換後的檔案預設會儲存在桌面上",
                               foreground="gray", font=("微軟正黑體", 9))
        note_label.pack(pady=(10, 0), anchor=tk.W)

        # 轉換線程
        self.convert_thread = None

    def browse_file(self):
        """瀏覽並選擇Word檔案"""
        file_types = [("Word檔案", "*.docx"), ("所有檔案", "*.*")]
        file_path = filedialog.askopenfilename(filetypes=file_types, title="選擇Word檔案")

        if file_path:
            self.file_path_var.set(file_path)
            # 更新預設輸出檔名
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_dir = self.output_path_var.get()
            self.output_path_var.set(os.path.join(output_dir, f"{base_name}.md"))
            self.status_var.set("已選擇Word檔案，點擊「開始轉換」按鈕開始處理")

    def browse_output_dir(self):
        """瀏覽並選擇輸出目錄"""
        output_dir = filedialog.askdirectory(title="選擇輸出目錄")

        if output_dir:
            self.output_path_var.set(output_dir)
            # 如果已選擇了Word檔案，同步更新輸出檔名
            file_path = self.file_path_var.get()
            if file_path:
                base_name = os.path.splitext(os.path.basename(file_path))[0]
                self.output_path_var.set(os.path.join(output_dir, f"{base_name}.md"))

    def update_progress(self, value, message):
        """更新進度條和狀態訊息"""

        def update():
            self.progress_var.set(value)
            self.status_var.set(message)

            if value == 100:  # 完成
                self.convert_button.config(state=tk.NORMAL)
                messagebox.showinfo("完成", f"轉換完成！\n檔案已儲存至：\n{self.output_path_var.get()}")

        self.root.after(0, update)

    def start_conversion(self):
        """開始轉換程序"""
        docx_path = self.file_path_var.get()
        output_path = self.output_path_var.get()

        if not docx_path:
            messagebox.showerror("錯誤", "請選擇Word檔案！")
            return

        if not os.path.isfile(docx_path):
            messagebox.showerror("錯誤", "無法找到指定的Word檔案！")
            return

        # 如果output_path是目錄，則組合完整路徑
        if os.path.isdir(output_path):
            base_name = os.path.splitext(os.path.basename(docx_path))[0]
            output_path = os.path.join(output_path, f"{base_name}.md")

        self.convert_button.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.status_var.set("開始轉換...")

        # 在新線程中執行轉換
        self.convert_thread = threading.Thread(
            target=self.run_conversion,
            args=(docx_path, output_path, self.table_format_var.get())
        )
        self.convert_thread.daemon = True
        self.convert_thread.start()

    def parse_html_table_to_markdown(self, html_table):
        """嘗試將HTML表格轉換為Markdown格式"""
        try:
            # 提取表格行
            rows = re.findall(r'<tr>(.*?)</tr>', html_table, re.DOTALL)
            if not rows:
                return html_table

            markdown_rows = []
            header_cells = []

            # 處理第一行（假設為表頭）
            header_match = re.findall(r'<t[hd][^>]*>(.*?)</t[hd]>', rows[0], re.DOTALL)
            for cell in header_match:
                # 清理單元格內容（移除HTML標籤）
                clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                header_cells.append(clean_cell or " ")

            if header_cells:
                # 添加表頭行
                markdown_rows.append("| " + " | ".join(header_cells) + " |")
                # 添加分隔線
                markdown_rows.append("| " + " | ".join(["---"] * len(header_cells)) + " |")

                # 處理剩餘行
                for row in rows[1:]:
                    cells = re.findall(r'<t[hd][^>]*>(.*?)</t[hd]>', row, re.DOTALL)
                    clean_cells = []
                    for cell in cells:
                        clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                        clean_cells.append(clean_cell or " ")

                    if clean_cells:
                        # 確保所有行具有相同數量的單元格
                        while len(clean_cells) < len(header_cells):
                            clean_cells.append(" ")
                        markdown_rows.append("| " + " | ".join(clean_cells[:len(header_cells)]) + " |")

                return "\n".join(markdown_rows)

            return html_table
        except Exception as e:
            print(f"轉換表格時出錯：{e}")
            return html_table  # 如果轉換失敗，返回原始HTML

    def beautify_html_table(self, html_table):
        """美化HTML表格"""
        # 添加邊框和樣式
        html_table = html_table.replace('<table>', '<table border="1" style="border-collapse: collapse; width: 100%;">')
        # 美化表格單元格
        html_table = re.sub(r'<td>', '<td style="border: 1px solid black; padding: 8px;">', html_table)
        html_table = re.sub(r'<th>', '<th style="border: 1px solid black; padding: 8px; background-color: #f2f2f2;">',
                            html_table)
        return html_table

    def run_conversion(self, docx_path, output_path, table_format="html"):
        """在線程中執行轉換"""
        try:
            self.update_progress(10, "正在讀取Word檔案...")

            # 自定義樣式映射
            style_map = """
            p[style-name='Heading 1'] => # #{value}
            p[style-name='Heading 2'] => ## #{value}
            p[style-name='Heading 3'] => ### #{value}
            table => !table!{value}!table!
            """

            # 讀取Word文件並轉換
            with open(docx_path, "rb") as docx_file:
                self.update_progress(30, "正在轉換為HTML...")
                # 先轉換為HTML以處理表格
                result_html = mammoth.convert_to_html(docx_file)
                html = result_html.value

                self.update_progress(50, "正在轉換為Markdown...")
                # 然後使用相同的文件轉換為Markdown
                docx_file.seek(0)  # 重置文件指針
                result_md = mammoth.convert_to_markdown(docx_file, style_map=style_map)
                markdown = result_md.value

            self.update_progress(70, "正在處理表格...")

            # 提取HTML中的表格
            tables = re.findall(r'<table>.*?</table>', html, re.DOTALL)
            table_count = 0

            # 替換Markdown中的表格標記
            while "!table!!table!" in markdown:
                if table_count < len(tables):
                    table_html = tables[table_count]

                    if table_format == "markdown":
                        # 嘗試轉換為Markdown表格
                        table_content = self.parse_html_table_to_markdown(table_html)
                    else:
                        # 保留為美化的HTML表格
                        table_content = "\n\n" + self.beautify_html_table(table_html) + "\n\n"

                    # 替換第一個找到的表格標記
                    markdown = markdown.replace("!table!!table!", table_content, 1)
                    table_count += 1
                else:
                    # 如果標記比實際表格多，移除多餘的標記
                    markdown = markdown.replace("!table!!table!", "", 1)

            self.update_progress(90, "正在儲存檔案...")
            # 保存處理後的Markdown文件
            with open(output_path, 'w', encoding='utf-8') as md_file:
                md_file.write(markdown)

            self.update_progress(100, f"轉換完成！檔案已儲存至: {output_path}")

        except Exception as e:
            self.update_progress(0, f"轉換過程中發生錯誤: {str(e)}")
            messagebox.showerror("錯誤", f"轉換過程中發生錯誤:\n{str(e)}")
            self.convert_button.config(state=tk.NORMAL)


def main():
    root = tk.Tk()
    app = DocxToMarkdownApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()