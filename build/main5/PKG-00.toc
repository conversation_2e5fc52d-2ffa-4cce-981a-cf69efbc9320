('C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main5\\main5.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True},
 [('PYZ-00.pyz',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main5\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main5\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main5\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main5\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main5\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main5\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('main5', 'C:\\Users\\<USER>\\PycharmProjects\\TEST\\main5.py', 'PYSOURCE'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('python37.dll', 'C:\\python3.7\\python37.dll', 'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\python3.7\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libegl.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libegl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libglesv2.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libglesv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('_lzma.pyd', 'C:\\python3.7\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\python3.7\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\python3.7\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\python3.7\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\python3.7\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\python3.7\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\python3.7\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\python3.7\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\python3.7\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\python3.7\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'C:\\python3.7\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\python3.7\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\sip.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_imagingft.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\_cffi_backend.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll', 'C:\\python3.7\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'C:\\python3.7\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('python3.dll', 'C:\\python3.7\\python3.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main5\\base_library.zip',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\LICENSE',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\LICENSE',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\RECORD',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\RECORD',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\METADATA',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\METADATA',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\WHEEL',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\WHEEL',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('wheel-0.41.2.dist-info\\METADATA',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.41.2.dist-info\\LICENSE.txt',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.41.2.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.41.2.dist-info\\entry_points.txt',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.41.2.dist-info\\RECORD',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.41.2.dist-info\\WHEEL',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\WHEEL',
   'DATA')],
 False,
 False,
 True,
 [],
 None,
 None,
 None)
