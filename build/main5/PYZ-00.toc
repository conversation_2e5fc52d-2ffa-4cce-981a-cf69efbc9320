('C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main5\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'C:\\python3.7\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\python3.7\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'C:\\python3.7\\lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_dummy_thread', 'C:\\python3.7\\lib\\_dummy_thread.py', 'PYMODULE'),
  ('_osx_support', 'C:\\python3.7\\lib\\_osx_support.py', 'PYMODULE'),
  ('_py_abc', 'C:\\python3.7\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\python3.7\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_sitebuiltins', 'C:\\python3.7\\lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'C:\\python3.7\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'C:\\python3.7\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'C:\\python3.7\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\python3.7\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\python3.7\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\python3.7\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\python3.7\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\python3.7\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\python3.7\\lib\\calendar.py', 'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'C:\\python3.7\\lib\\cgi.py', 'PYMODULE'),
  ('cmd', 'C:\\python3.7\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\python3.7\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\python3.7\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'C:\\python3.7\\lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'C:\\python3.7\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'C:\\python3.7\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\python3.7\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\python3.7\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\python3.7\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'C:\\python3.7\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'C:\\python3.7\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\python3.7\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\python3.7\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\python3.7\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\python3.7\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\python3.7\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\python3.7\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'C:\\python3.7\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\python3.7\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\python3.7\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\python3.7\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\python3.7\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('dataclasses', 'C:\\python3.7\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\python3.7\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\python3.7\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\python3.7\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\python3.7\\lib\\dis.py', 'PYMODULE'),
  ('distutils', 'C:\\python3.7\\lib\\distutils\\__init__.py', 'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\python3.7\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\python3.7\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\python3.7\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd', 'C:\\python3.7\\lib\\distutils\\cmd.py', 'PYMODULE'),
  ('distutils.command',
   'C:\\python3.7\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\python3.7\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\python3.7\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\python3.7\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\python3.7\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config', 'C:\\python3.7\\lib\\distutils\\config.py', 'PYMODULE'),
  ('distutils.core', 'C:\\python3.7\\lib\\distutils\\core.py', 'PYMODULE'),
  ('distutils.debug', 'C:\\python3.7\\lib\\distutils\\debug.py', 'PYMODULE'),
  ('distutils.dep_util',
   'C:\\python3.7\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\python3.7\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist', 'C:\\python3.7\\lib\\distutils\\dist.py', 'PYMODULE'),
  ('distutils.errors', 'C:\\python3.7\\lib\\distutils\\errors.py', 'PYMODULE'),
  ('distutils.extension',
   'C:\\python3.7\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\python3.7\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\python3.7\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\python3.7\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log', 'C:\\python3.7\\lib\\distutils\\log.py', 'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\python3.7\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn', 'C:\\python3.7\\lib\\distutils\\spawn.py', 'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\python3.7\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\python3.7\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util', 'C:\\python3.7\\lib\\distutils\\util.py', 'PYMODULE'),
  ('distutils.version',
   'C:\\python3.7\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\python3.7\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'C:\\python3.7\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'C:\\python3.7\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\python3.7\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\python3.7\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'C:\\python3.7\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\python3.7\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'C:\\python3.7\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'C:\\python3.7\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\python3.7\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'C:\\python3.7\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'C:\\python3.7\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\python3.7\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'C:\\python3.7\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'C:\\python3.7\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\python3.7\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\python3.7\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'C:\\python3.7\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\python3.7\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\python3.7\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\python3.7\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'C:\\python3.7\\lib\\email\\utils.py', 'PYMODULE'),
  ('fractions', 'C:\\python3.7\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\python3.7\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\python3.7\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\python3.7\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\python3.7\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\python3.7\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\python3.7\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\python3.7\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\python3.7\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\python3.7\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\python3.7\\lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'C:\\python3.7\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\python3.7\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\python3.7\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'C:\\python3.7\\lib\\http\\server.py', 'PYMODULE'),
  ('imp', 'C:\\python3.7\\lib\\imp.py', 'PYMODULE'),
  ('importlib', 'C:\\python3.7\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\python3.7\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\python3.7\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\python3.7\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\python3.7\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\python3.7\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\python3.7\\lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib_metadata',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('inspect', 'C:\\python3.7\\lib\\inspect.py', 'PYMODULE'),
  ('json', 'C:\\python3.7\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\python3.7\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\python3.7\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\python3.7\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\python3.7\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\python3.7\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\python3.7\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\python3.7\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\python3.7\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\python3.7\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\python3.7\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\python3.7\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\python3.7\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\python3.7\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\python3.7\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\python3.7\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\python3.7\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\python3.7\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\python3.7\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\python3.7\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\python3.7\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\python3.7\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\python3.7\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\python3.7\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'C:\\python3.7\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\python3.7\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\python3.7\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\python3.7\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\python3.7\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\python3.7\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\python3.7\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\python3.7\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\python3.7\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\python3.7\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pdb', 'C:\\python3.7\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\python3.7\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\python3.7\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\python3.7\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'C:\\python3.7\\lib\\plistlib.py', 'PYMODULE'),
  ('png',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\png.py',
   'PYMODULE'),
  ('pprint', 'C:\\python3.7\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\python3.7\\lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'C:\\python3.7\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'C:\\python3.7\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\python3.7\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('qrcode',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\__init__.py',
   'PYMODULE'),
  ('qrcode.LUT',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\LUT.py',
   'PYMODULE'),
  ('qrcode.base',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\base.py',
   'PYMODULE'),
  ('qrcode.compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\compat\\__init__.py',
   'PYMODULE'),
  ('qrcode.compat.pil',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\compat\\pil.py',
   'PYMODULE'),
  ('qrcode.constants',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\constants.py',
   'PYMODULE'),
  ('qrcode.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\exceptions.py',
   'PYMODULE'),
  ('qrcode.image',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\__init__.py',
   'PYMODULE'),
  ('qrcode.image.base',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\base.py',
   'PYMODULE'),
  ('qrcode.image.pil',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\pil.py',
   'PYMODULE'),
  ('qrcode.image.pure',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\pure.py',
   'PYMODULE'),
  ('qrcode.image.styledpil',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\styledpil.py',
   'PYMODULE'),
  ('qrcode.image.styles',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\styles\\__init__.py',
   'PYMODULE'),
  ('qrcode.image.styles.colormasks',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\styles\\colormasks.py',
   'PYMODULE'),
  ('qrcode.image.styles.moduledrawers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\__init__.py',
   'PYMODULE'),
  ('qrcode.image.styles.moduledrawers.base',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\base.py',
   'PYMODULE'),
  ('qrcode.image.styles.moduledrawers.pil',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\pil.py',
   'PYMODULE'),
  ('qrcode.main',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\main.py',
   'PYMODULE'),
  ('qrcode.util',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\qrcode\\util.py',
   'PYMODULE'),
  ('queue', 'C:\\python3.7\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\python3.7\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\python3.7\\lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\python3.7\\lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'C:\\python3.7\\lib\\runpy.py', 'PYMODULE'),
  ('selectors', 'C:\\python3.7\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\python3.7\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\python3.7\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\python3.7\\lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\python3.7\\lib\\site.py', 'PYMODULE'),
  ('socket', 'C:\\python3.7\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\python3.7\\lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'C:\\python3.7\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'C:\\python3.7\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\python3.7\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\python3.7\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\python3.7\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\python3.7\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\python3.7\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\python3.7\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\python3.7\\lib\\threading.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\python3.7\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'C:\\python3.7\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\python3.7\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'C:\\python3.7\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.case', 'C:\\python3.7\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\python3.7\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'C:\\python3.7\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'C:\\python3.7\\lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'C:\\python3.7\\lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\python3.7\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'C:\\python3.7\\lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\python3.7\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'C:\\python3.7\\lib\\unittest\\util.py', 'PYMODULE'),
  ('uu', 'C:\\python3.7\\lib\\uu.py', 'PYMODULE'),
  ('webbrowser', 'C:\\python3.7\\lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'C:\\python3.7\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers', 'C:\\python3.7\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\python3.7\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\python3.7\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\python3.7\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\python3.7\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'C:\\python3.7\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\python3.7\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\python3.7\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\python3.7\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\python3.7\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'C:\\python3.7\\lib\\zipfile.py', 'PYMODULE'),
  ('zipp',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.py310compat',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE')])
