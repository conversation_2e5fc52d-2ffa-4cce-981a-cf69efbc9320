('C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main\\main.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True},
 [('PYZ-00.pyz',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'C:\\Users\\<USER>\\PycharmProjects\\TEST\\main.py', 'PYSOURCE'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('python37.dll', 'C:\\python3.7\\python37.dll', 'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\python3.7\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('_lzma.pyd', 'C:\\python3.7\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\python3.7\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\python3.7\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\python3.7\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\python3.7\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\python3.7\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\python3.7\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\python3.7\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\python3.7\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\python3.7\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'C:\\python3.7\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\python3.7\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('PIL\\_imagingft.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_imagingft.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\_cffi_backend.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\.venv\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'C:\\python3.7\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('libcrypto-1_1.dll', 'C:\\python3.7\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'C:\\python3.7\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('tk86t.dll', 'C:\\python3.7\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'C:\\python3.7\\DLLs\\tcl86t.dll', 'BINARY'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\PycharmProjects\\TEST\\build\\main\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tk\\msgs\\de.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tk\\msgs\\cs.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tk\\unsupported.tcl', 'C:\\python3.7\\tcl\\tk8.6\\unsupported.tcl', 'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\GMT', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('tcl\\package.tcl', 'C:\\python3.7\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\msgs\\eo.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\eo.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl\\tzdata\\Japan', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Japan', 'DATA'),
  ('tcl\\msgs\\eu.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\eu.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\msgs\\et.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\et.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\msgs\\tr.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\tr.msg', 'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\RECORD',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\RECORD',
   'DATA'),
  ('tk\\focus.tcl', 'C:\\python3.7\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\msgs\\kl.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\kl.msg', 'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\GB', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\msgs\\sr.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\sr.msg', 'DATA'),
  ('tk\\ttk\\ttk.tcl', 'C:\\python3.7\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tk\\spinbox.tcl', 'C:\\python3.7\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\init.tcl', 'C:\\python3.7\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('tk\\msgs\\sv.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tk\\text.tcl', 'C:\\python3.7\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tk\\ttk\\entry.tcl', 'C:\\python3.7\\tcl\\tk8.6\\ttk\\entry.tcl', 'DATA'),
  ('tcl\\msgs\\it.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\it.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\msgs\\mk.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\mk.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\msgs\\te.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\te.msg', 'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tk\\mkpsenc.tcl', 'C:\\python3.7\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tk\\xmfbox.tcl', 'C:\\python3.7\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Turkey', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Turkey', 'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tk\\ttk\\button.tcl', 'C:\\python3.7\\tcl\\tk8.6\\ttk\\button.tcl', 'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tzdata\\EET', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\msgs\\es.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tk\\button.tcl', 'C:\\python3.7\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('tk\\msgs\\fr.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('tcl\\clock.tcl', 'C:\\python3.7\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\GMT+0', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\GMT+0', 'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\python3.7\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\msgs\\ga.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ga.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tk\\choosedir.tcl', 'C:\\python3.7\\tcl\\tk8.6\\choosedir.tcl', 'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\msgs\\ja.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ja.msg', 'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\msgs\\de.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\de.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\msgs\\fr.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fr.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tzdata\\PRC', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tk\\msgs\\hu.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tk\\ttk\\fonts.tcl', 'C:\\python3.7\\tcl\\tk8.6\\ttk\\fonts.tcl', 'DATA'),
  ('tcl\\msgs\\hu.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\hu.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\msgs\\ru.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ru.msg', 'DATA'),
  ('tcl\\msgs\\kok.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\kok.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\msgs\\ro.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ro.msg', 'DATA'),
  ('tk\\tclIndex', 'C:\\python3.7\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\Israel', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Israel', 'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\CET', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\UCT', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tk\\console.tcl', 'C:\\python3.7\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tclIndex', 'C:\\python3.7\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tk\\dialog.tcl', 'C:\\python3.7\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\msgs\\fa.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fa.msg', 'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\parray.tcl', 'C:\\python3.7\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\msgs\\hi.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\hi.msg', 'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\msgs\\bg.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\bg.msg', 'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\msgs\\mr.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\mr.msg', 'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\msgs\\sv.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\sv.msg', 'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tk\\bgerror.tcl', 'C:\\python3.7\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tk\\msgs\\pt.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('tk\\menu.tcl', 'C:\\python3.7\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\msgs\\kw.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\kw.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\msgs\\mt.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\mt.msg', 'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'C:\\python3.7\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('tcl\\msgs\\lt.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\lt.msg', 'DATA'),
  ('tk\\tearoff.tcl', 'C:\\python3.7\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\tzdata\\Poland', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Poland', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\msgs\\sh.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\sh.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tk\\comdlg.tcl', 'C:\\python3.7\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tk\\msgs\\pl.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\msgs\\lv.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\lv.msg', 'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\METADATA',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\METADATA',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tk\\msgs\\es.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tk\\fontchooser.tcl', 'C:\\python3.7\\tcl\\tk8.6\\fontchooser.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\python3.7\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\python3.7\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tk\\msgs\\en.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tk\\optMenu.tcl', 'C:\\python3.7\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\msgs\\cs.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\cs.msg', 'DATA'),
  ('tcl\\tzdata\\GMT-0', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\GMT-0', 'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\msgs\\af.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\af.msg', 'DATA'),
  ('tk\\ttk\\utils.tcl', 'C:\\python3.7\\tcl\\tk8.6\\ttk\\utils.tcl', 'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\python3.7\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\msgs\\sq.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\sq.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\NZ', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tcl\\msgs\\hr.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\hr.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\msgs\\gl.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\gl.msg', 'DATA'),
  ('tcl\\msgs\\be.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\be.msg', 'DATA'),
  ('tcl\\msgs\\ca.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ca.msg', 'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\history.tcl', 'C:\\python3.7\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\msgs\\th.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\th.msg', 'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\python3.7\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\msgs\\fo.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fo.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tk\\license.terms', 'C:\\python3.7\\tcl\\tk8.6\\license.terms', 'DATA'),
  ('tcl\\msgs\\vi.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\vi.msg', 'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tk\\icons.tcl', 'C:\\python3.7\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\msgs\\sl.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\sl.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tm.tcl', 'C:\\python3.7\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\Zulu', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Zulu', 'DATA'),
  ('tcl\\msgs\\nb.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\nb.msg', 'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\python3.7\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('tk\\entry.tcl', 'C:\\python3.7\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\GMT0', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\GMT0', 'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tk\\msgs\\ru.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tzdata\\Libya', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Libya', 'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\msgs\\nn.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\nn.msg', 'DATA'),
  ('tk\\msgbox.tcl', 'C:\\python3.7\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'C:\\python3.7\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\msgs\\ta.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ta.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\msgs\\ko.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ko.msg', 'DATA'),
  ('tk\\palette.tcl', 'C:\\python3.7\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tk\\tk.tcl', 'C:\\python3.7\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\msgs\\zh.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\zh.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Iran', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Iran', 'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tk\\msgs\\da.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\auto.tcl', 'C:\\python3.7\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tk\\iconlist.tcl', 'C:\\python3.7\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\python3.7\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\msgs\\id.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\id.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tk\\ttk\\scale.tcl', 'C:\\python3.7\\tcl\\tk8.6\\ttk\\scale.tcl', 'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\tzdata\\WET', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\msgs\\ar.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ar.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tk\\msgs\\el.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tzdata\\MST', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\LICENSE',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\LICENSE',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\WHEEL',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\WHEEL',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tk\\tkfbox.tcl', 'C:\\python3.7\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tk\\panedwindow.tcl', 'C:\\python3.7\\tcl\\tk8.6\\panedwindow.tcl', 'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\W-SU', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\W-SU', 'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\ROK', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\python3.7\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\safe.tcl', 'C:\\python3.7\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\msgs\\da.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\da.msg', 'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tk\\scrlbar.tcl', 'C:\\python3.7\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tk\\clrpick.tcl', 'C:\\python3.7\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\msgs\\sk.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\sk.msg', 'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\msgs\\fi.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fi.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tk\\safetk.tcl', 'C:\\python3.7\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\msgs\\pt.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\pt.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\python3.7\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Cuba', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Cuba', 'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\msgs\\is.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\is.msg', 'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\msgs\\ms.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ms.msg', 'DATA'),
  ('tk\\images\\README', 'C:\\python3.7\\tcl\\tk8.6\\images\\README', 'DATA'),
  ('tk\\megawidget.tcl', 'C:\\python3.7\\tcl\\tk8.6\\megawidget.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Navajo', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Navajo', 'DATA'),
  ('tk\\msgs\\eo.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\word.tcl', 'C:\\python3.7\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\msgs\\bn.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\bn.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tk\\msgs\\it.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tk\\listbox.tcl', 'C:\\python3.7\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\tzdata\\Eire', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Eire', 'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\EST', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\msgs\\el.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\el.msg', 'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tk\\scale.tcl', 'C:\\python3.7\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\UTC', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\msgs\\sw.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\sw.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tk\\obsolete.tcl', 'C:\\python3.7\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\msgs\\he.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\he.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('tk\\msgs\\nl.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('tcl\\tzdata\\MET', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('tcl\\msgs\\uk.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\uk.msg', 'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\python3.7\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Egypt', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Egypt', 'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\ROC', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\msgs\\nl.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\nl.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tk\\msgs\\en_gb.msg', 'C:\\python3.7\\tcl\\tk8.6\\msgs\\en_gb.msg', 'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\msgs\\gv.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\gv.msg', 'DATA'),
  ('tcl\\msgs\\pl.msg', 'C:\\python3.7\\tcl\\tcl8.6\\msgs\\pl.msg', 'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'C:\\python3.7\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\HST', 'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\python3.7\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\python3.7\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tk\\pkgIndex.tcl', 'C:\\python3.7\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\python3.7\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('wheel-0.41.2.dist-info\\METADATA',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.41.2.dist-info\\WHEEL',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.41.2.dist-info\\entry_points.txt',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.41.2.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.41.2.dist-info\\RECORD',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.41.2.dist-info\\LICENSE.txt',
   'c:\\users\\<USER>\\pycharmprojects\\test\\.venv\\lib\\site-packages\\wheel-0.41.2.dist-info\\LICENSE.txt',
   'DATA')],
 False,
 False,
 True,
 [],
 None,
 None,
 None)
