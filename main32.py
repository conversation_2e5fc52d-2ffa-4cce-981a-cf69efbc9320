import json
import re
import csv
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Set, Optional, Any, Union

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("aaraco_process.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 嘗試導入 fuzzywuzzy
try:
    from fuzzywuzzy import fuzz
    from fuzzywuzzy import process

    FUZZY_AVAILABLE = True
except ImportError:
    logger.warning(
        "未安裝 fuzzywuzzy 套件，將無法使用模糊匹配功能。安裝請執行: pip install fuzzywuzzy python-Levenshtein")
    FUZZY_AVAILABLE = False


class AaracoProcessor:
    """處理全聯門店地區代碼(AARACO)的主類"""

    def __init__(self, output_dir: str = "output", test_table: str = "ASUSER.ASAA_TEST"):
        """初始化處理器

        Args:
            output_dir: 輸出檔案的目錄
            test_table: 測試資料表名稱
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.test_table = test_table

        # 臺灣郵遞區號對應字首的對照表
        self.zipcode_prefixes = self._build_zipcode_prefixes()

    def _build_zipcode_prefixes(self) -> Dict[str, str]:
        """建立郵遞區號對字首的對照表"""
        prefixes = {}

        # 使用區塊初始化來增加可讀性
        # 臺北市 (A)
        for code in ['100', '103', '104', '105', '106', '108', '110', '111', '112', '114', '115', '116']:
            prefixes[code] = 'A'

        # 基隆市 (C)
        for code in ['200', '201', '202', '203', '204', '205', '206']:
            prefixes[code] = 'C'

        # 新北市 (F)
        for code in ['207', '208', '220', '221', '222', '223', '224', '226', '227', '228', '231', '232',
                     '233', '234', '235', '236', '237', '238', '239', '241', '242', '243', '244', '247',
                     '248', '249', '251', '252', '253']:
            prefixes[code] = 'F'

        # 宜蘭縣 (G)
        for code in ['260', '261', '262', '263', '264', '265', '266', '267', '268', '269', '270', '272']:
            prefixes[code] = 'G'

        # 新竹市 (Q)
        for code in ['300', '301', '302']:
            prefixes[code] = 'Q'

        # 新竹縣 (J)
        for code in ['302', '303', '304', '305', '306', '307', '308', '310', '311', '312', '313', '314', '315']:
            prefixes[code] = 'J'

        # 桃園市 (H)
        for code in ['320', '324', '325', '326', '327', '328', '330', '333', '334', '335', '336', '337', '338']:
            prefixes[code] = 'H'

        # 苗栗縣 (K)
        for code in ['350', '351', '352', '353', '354', '356', '357', '358', '360', '361', '362', '363',
                     '364', '365', '366', '367', '368', '369']:
            prefixes[code] = 'K'

        # 臺中市 (B, L)
        for code in ['400', '401', '402', '403', '404', '406', '407', '408']:
            prefixes[code] = 'B'
        for code in ['411', '412', '413', '414', '420', '421', '422', '423', '424', '426', '427', '428',
                     '429', '432', '433', '434', '435', '436', '437', '438', '439']:
            prefixes[code] = 'L'

        # 彰化縣 (N)
        for code in ['500', '502', '503', '504', '505', '506', '507', '508', '509', '510', '511', '512',
                     '513', '514', '515', '516', '520', '521', '522', '523', '524', '525', '526', '527',
                     '528', '530']:
            prefixes[code] = 'N'

        # 南投縣 (M)
        for code in ['540', '541', '542', '544', '545', '546', '551', '552', '553', '555', '556', '557', '558']:
            prefixes[code] = 'M'

        # 嘉義市 (I)
        prefixes['600'] = 'I'

        # 嘉義縣 (O)
        for code in ['602', '603', '604', '605', '606', '607', '608', '611', '612', '613', '614', '615',
                     '616', '621', '622', '623', '624', '625']:
            prefixes[code] = 'O'

        # 雲林縣 (P)
        for code in ['630', '631', '632', '633', '634', '635', '636', '637', '638', '640', '643', '646',
                     '647', '648', '649', '651', '652', '653', '654', '655']:
            prefixes[code] = 'P'

        # 臺南市 (D, R)
        for code in ['700', '701', '702', '704', '708', '709']:
            prefixes[code] = 'D'
        for code in ['710', '711', '712', '713', '714', '715', '716', '717', '718', '719', '720', '721',
                     '722', '723', '724', '725', '726', '727', '730', '731', '732', '733', '734', '735',
                     '736', '737', '741', '742', '743', '744', '745']:
            prefixes[code] = 'R'

        # 高雄市 (E, S)
        for code in ['800', '801', '802', '803', '804', '805', '806', '807', '811', '812', '813']:
            prefixes[code] = 'E'
        for code in ['814', '815', '820', '821', '822', '823', '824', '825', '826', '827', '828', '829',
                     '830', '831', '832', '833', '840', '842', '843', '844', '845', '846', '847', '848',
                     '849', '851', '852']:
            prefixes[code] = 'S'

        # 澎湖縣 (X)
        for code in ['880', '881', '882', '883', '884', '885']:
            prefixes[code] = 'X'

        # 屏東縣 (T)
        for code in ['900', '901', '902', '903', '904', '905', '906', '907', '908', '909', '911', '912',
                     '913', '920', '921', '922', '923', '924', '925', '926', '927', '928', '929', '931',
                     '932', '940', '941', '942', '943', '944', '945', '946', '947']:
            prefixes[code] = 'T'

        # 臺東縣 (V)
        for code in ['950', '951', '952', '953', '954', '955', '956', '957', '958', '959', '961', '962',
                     '963', '964', '965', '966']:
            prefixes[code] = 'V'

        # 花蓮縣 (U)
        for code in ['970', '971', '972', '973', '974', '975', '976', '977', '978', '979', '981', '982', '983']:
            prefixes[code] = 'U'

        # 金門縣 (W)
        for code in ['890', '891', '892', '893', '894', '896']:
            prefixes[code] = 'W'

        # 連江縣 (Z)
        for code in ['209', '210', '211', '212']:
            prefixes[code] = 'Z'

        return prefixes

    def normalize_aaraco(self, aaraco: str) -> str:
        """標準化 AARACO 值，確保符合資料庫欄位長度限制

        Args:
            aaraco: 原始 AARACO 值

        Returns:
            標準化後的 AARACO 值，確保長度不超過4個字元
        """
        if not aaraco:
            return ""

        # 將全形字符轉換為半形字符
        result = ""
        for char in aaraco:
            # 全形數字 (０-９) 轉換為半形數字 (0-9)
            if '\uff10' <= char <= '\uff19':
                result += chr(ord(char) - ord('\uff10') + ord('0'))
            # 全形大寫字母 (Ａ-Ｚ) 轉換為半形大寫字母 (A-Z)
            elif '\uff21' <= char <= '\uff3a':
                result += chr(ord(char) - ord('\uff21') + ord('A'))
            else:
                result += char

        # 確保長度不超過4個字元
        if len(result) > 4:
            # 如果超出長度，保留前4個字元
            result = result[:4]

        # 確保符合 A000 格式 (一個大寫字母後跟三個數字)
        if len(result) == 4 and result[0].isalpha() and all(c.isdigit() for c in result[1:]):
            return result
        else:
            # 如果不符合格式，嘗試修復
            if len(result) > 0 and result[0].isalpha():
                prefix = result[0]
                digits = ''.join([c for c in result[1:] if c.isdigit()])

                # 如果有足夠的數字，組合成正確格式
                if len(digits) >= 3:
                    return f"{prefix}{digits[:3]}"
                # 不足3個數字則用0填充
                elif len(digits) > 0:
                    return f"{prefix}{digits.ljust(3, '0')}"

            # 如果無法修復，返回空字串
            return ""

    def load_json_data(self, file_path: str) -> Dict[str, List[Any]]:
        """讀取JSON檔案

        Args:
            file_path: JSON檔案路徑

        Returns:
            解析後的JSON數據，如果發生錯誤則返回空記錄
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                if isinstance(data, list):
                    # 如果資料是列表，轉換為標準格式
                    return {"RECORDS": data}
                return data
        except FileNotFoundError:
            logger.error(f"錯誤：找不到檔案 {file_path}")
            return {"RECORDS": []}
        except json.JSONDecodeError:
            logger.error(f"錯誤：檔案 {file_path} 不是有效的 JSON 格式")
            return {"RECORDS": []}
        except Exception as e:
            logger.error(f"讀取JSON檔案時發生未預期錯誤: {e}", exc_info=True)
            return {"RECORDS": []}

    def load_csv_data(self, file_path: str) -> List[Dict[str, str]]:
        """讀取CSV檔案

        Args:
            file_path: CSV檔案路徑

        Returns:
            解析後的CSV數據，如果發生錯誤則返回空列表
        """
        try:
            data = []
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(row)
            return data
        except FileNotFoundError:
            logger.error(f"錯誤：找不到檔案 {file_path}")
            return []
        except Exception as e:
            logger.error(f"讀取CSV檔案時發生未預期錯誤: {e}", exc_info=True)
            return []

    def get_area_codes(self) -> Tuple[Dict[str, str], Dict[str, str], Dict[str, List[str]]]:
        """從 CMBE 表中獲取地區代碼

        Returns:
            三個字典的元組:
            - area_codes: 代碼對區域名稱的映射
            - area_name_to_code: 區域名稱對代碼的映射
            - city_to_codes: 縣市對代碼列表的映射
        """
        area_codes = {}
        area_name_to_code = {}
        city_to_codes = {}

        try:
            # 優先嘗試從JSON檔案讀取
            cmbe_data = self.load_json_data('ASUSER_CMBE.json')
            if cmbe_data.get("RECORDS"):
                for item in cmbe_data.get("RECORDS", []):
                    code = item.get('BEKEY2', '')
                    area_name = item.get('BEDSKC', '')

                    if code and area_name:
                        area_codes[code] = area_name
                        area_name_to_code[area_name] = code

                        # 解析出縣市並建立縣市到代碼的映射
                        match = re.match(r'(.+?[縣市])(.+)', area_name)
                        if match:
                            city = match.group(1)  # 例如 '臺北市', '新北市'
                            if city not in city_to_codes:
                                city_to_codes[city] = []
                            city_to_codes[city].append(code)
                logger.info(f"從 ASUSER_CMBE.json 成功讀取了 {len(area_codes)} 筆地區代碼")
                return area_codes, area_name_to_code, city_to_codes

            # 如果JSON載入失敗，嘗試從 paste.txt 讀取
            with open('paste.txt', 'r', encoding='utf-8') as f:
                for line in f:
                    match = re.search(r"VALUES \('18', '([A-Z]\d{3})', '(.+?)'", line)
                    if match:
                        code = match.group(1)
                        area_name = match.group(2)
                        area_codes[code] = area_name
                        area_name_to_code[area_name] = code

                        # 解析出縣市並建立縣市到代碼的映射
                        match = re.match(r'(.+?[縣市])(.+)', area_name)
                        if match:
                            city = match.group(1)  # 例如 '臺北市', '新北市'
                            if city not in city_to_codes:
                                city_to_codes[city] = []
                            city_to_codes[city].append(code)
            logger.info(f"從 paste.txt 成功讀取了 {len(area_codes)} 筆地區代碼")
        except FileNotFoundError:
            logger.warning("警告：找不到區域代碼資料檔案，使用硬編碼的區域代碼")
            # 硬編碼一些常見的對應關係
            area_codes = {
                'A100': '臺北市中正區', 'A106': '臺北市大安區', 'F220': '新北市板橋區',
                'H330': '桃園市桃園區', 'B400': '臺中市中區', 'D700': '臺南市中西區',
                'E800': '高雄市新興區', 'T900': '屏東縣屏東市', 'U970': '花蓮縣花蓮市'
            }
            for code, name in area_codes.items():
                area_name_to_code[name] = code
                match = re.match(r'(.+?[縣市])(.+)', name)
                if match:
                    city = match.group(1)
                    if city not in city_to_codes:
                        city_to_codes[city] = []
                    city_to_codes[city].append(code)
        except Exception as e:
            logger.error(f"讀取地區代碼時發生未預期錯誤: {e}", exc_info=True)
            # 使用空值繼續

        return area_codes, area_name_to_code, city_to_codes

    @staticmethod
    def normalize_name(name: str) -> str:
        """標準化店名，移除常見前後綴和一些變體字

        Args:
            name: 原始店名

        Returns:
            標準化後的店名
        """
        if not name:
            return ""

        # 移除常見前後綴
        name = re.sub(r'^全聯社|^全聯實業|分社$|分公司$', '', name)

        # 標準化一些常見變體
        name = name.replace('台', '臺')

        # 移除空白
        name = re.sub(r'\s', '', name)

        return name

    @staticmethod
    def normalize_address(address: str) -> str:
        """標準化地址格式，移除空白和一些變體字

        Args:
            address: 原始地址

        Returns:
            標準化後的地址
        """
        if not address:
            return ""

        # 移除郵遞區號
        address = re.sub(r'\(\d+\)', '', address)

        # 標準化一些常見變體
        address = address.replace('台', '臺')
        address = address.replace('镇', '鎮')
        address = address.replace('乡', '鄉')

        # 移除空白、逗號等
        address = re.sub(r'[\s,，、]', '', address)

        return address

    def find_matching_records(self, asaa_data: Dict[str, List[Dict[str, Any]]],
                              px_data: Dict[str, List[Dict[str, Any]]],
                              area_codes: Dict[str, str],
                              area_name_to_code: Dict[str, str],
                              city_to_codes: Dict[str, List[str]],
                              manual_corrections: Dict[str, str] = None) -> Tuple[
        List[Dict[str, Any]], List[Dict[str, Any]]]:
        """尋找 ASAA 和 PX_STORES 之間匹配的記錄

        Args:
            asaa_data: ASAA資料
            px_data: PX_STORES資料
            area_codes: 代碼對區域名稱的映射
            area_name_to_code: 區域名稱對代碼的映射
            city_to_codes: 縣市對代碼列表的映射
            manual_corrections: 手動修正的AARACO值（店號=>AARACO）

        Returns:
            兩個列表的元組:
            - matches: 匹配成功的記錄
            - no_matches: 匹配失敗的記錄
        """
        matches = []
        no_matches = []

        # 確保手動修正字典存在
        if manual_corrections is None:
            manual_corrections = {}

        # 預處理資料，建立索引以加速查詢
        logger.info("預處理資料中...")
        px_records = px_data.get('RECORDS', [])
        asaa_records = asaa_data.get('RECORDS', [])

        # 建立 PX_STORES 店號索引
        px_by_code = {store['STORE_CODE'].strip(): store for store in px_records if 'STORE_CODE' in store}

        # 標準化 PX_STORES 店名和地址用於模糊匹配
        px_normalized_names = {}
        px_normalized_addresses = {}

        for store in px_records:
            if 'STORE_NAME' in store and store['STORE_NAME']:
                norm_name = self.normalize_name(store['STORE_NAME'])
                if norm_name:
                    px_normalized_names[norm_name] = store

            if 'FULL_ADDRESS' in store and store['FULL_ADDRESS']:
                norm_addr = self.normalize_address(store['FULL_ADDRESS'])
                if norm_addr:
                    px_normalized_addresses[norm_addr] = store

        logger.info(
            f"預處理完成: {len(px_by_code)} 個店號, {len(px_normalized_names)} 個標準化店名, {len(px_normalized_addresses)} 個標準化地址")
        logger.info(f"載入了 {len(manual_corrections)} 個手動修正的區域代碼")

        # 計數器
        match_methods = {
            "手動修正": 0,
            "店號完全匹配": 0,
            "店名模糊匹配": 0,
            "地址模糊匹配": 0,
            "從ASAA地址猜測": 0
        }

        total = len(asaa_records)
        logger.info(f"開始處理 {total} 筆 ASAA 記錄...")

        # 批次處理，每1000筆輸出一次進度
        batch_size = 1000

        # 處理每筆 ASAA 記錄
        for i, asaa in enumerate(asaa_records):
            if i % batch_size == 0 and i > 0:
                logger.info(f"已處理 {i}/{total} 筆記錄 ({(i / total * 100):.1f}%)")

            asaa_code = asaa.get('AACSTN', '').strip()
            asaa_name = asaa.get('AANAME', '').strip()
            asaa_addr = asaa.get('AAADDR', '').strip()

            matched_px = None
            match_method = None
            confidence = 0

            # 優先檢查手動修正表
            if asaa_code in manual_corrections:
                aaraco = manual_corrections[asaa_code]
                aaraco = self.normalize_aaraco(aaraco)  # 標準化AARACO值

                if aaraco:  # 確保有有效值
                    matches.append({
                        'ASAA': asaa,
                        'PX': None,
                        'AARACO': aaraco,
                        'METHOD': "手動修正",
                        'CONFIDENCE': 100
                    })
                    match_methods["手動修正"] += 1
                    continue

            # 方法1: 嘗試用店號完全匹配
            if asaa_code in px_by_code:
                matched_px = px_by_code[asaa_code]
                match_method = "店號完全匹配"
                confidence = 100
                match_methods[match_method] += 1
            elif FUZZY_AVAILABLE:  # 確保 fuzzywuzzy 已安裝
                # 方法2: 嘗試店名模糊匹配
                norm_asaa_name = self.normalize_name(asaa_name)
                if norm_asaa_name:
                    # 使用 process.extractOne 一次性找出最佳匹配
                    best_match = process.extractOne(
                        norm_asaa_name,
                        list(px_normalized_names.keys()),
                        scorer=fuzz.ratio,
                        score_cutoff=85  # 提高店名匹配閾值，提高精確度
                    )

                    if best_match:
                        matched_name, best_score = best_match
                        matched_px = px_normalized_names[matched_name]
                        match_method = "店名模糊匹配"
                        confidence = best_score
                        match_methods[match_method] += 1
                    else:
                        # 方法3: 嘗試地址模糊匹配
                        norm_asaa_addr = self.normalize_address(asaa_addr)
                        if norm_asaa_addr:
                            best_match = process.extractOne(
                                norm_asaa_addr,
                                list(px_normalized_addresses.keys()),
                                scorer=fuzz.ratio,
                                score_cutoff=75  # 增加地址匹配閾值，減少誤匹配
                            )

                            if best_match:
                                matched_addr, best_score = best_match
                                matched_px = px_normalized_addresses[matched_addr]
                                match_method = "地址模糊匹配"
                                confidence = best_score
                                match_methods[match_method] += 1

            # 找到匹配的記錄，確定 AARACO 值
            if matched_px:
                city = matched_px.get('CITY', '')
                area = matched_px.get('AREA', '')
                zipcode = matched_px.get('ZIPCODE', '')

                aaraco = self.determine_aaraco(city, area, zipcode, area_codes, area_name_to_code, city_to_codes)

                if aaraco:  # 只在有有效值時添加
                    matches.append({
                        'ASAA': asaa,
                        'PX': matched_px,
                        'AARACO': aaraco,
                        'METHOD': match_method,
                        'CONFIDENCE': confidence
                    })
                else:
                    no_matches.append(asaa)
            else:
                # 嘗試從 ASAA 地址中提取縣市區域，並猜測 AARACO
                aaraco = self.guess_aaraco_from_asaa(asaa, area_codes, area_name_to_code, city_to_codes)

                if aaraco:
                    matches.append({
                        'ASAA': asaa,
                        'PX': None,
                        'AARACO': aaraco,
                        'METHOD': "從ASAA地址猜測",
                        'CONFIDENCE': 50
                    })
                    match_methods["從ASAA地址猜測"] += 1
                else:
                    no_matches.append(asaa)

        # 輸出匹配方法統計
        logger.info("匹配方法統計:")
        for method, count in match_methods.items():
            if count > 0:  # 只顯示有用到的方法
                logger.info(f"  {method}: {count} 筆 ({count / max(1, total) * 100:.1f}%)")

        logger.info(f"匹配完成: 成功匹配 {len(matches)} 筆, 無法匹配 {len(no_matches)} 筆")
        return matches, no_matches

    def load_manual_corrections(self, csv_file: str = None) -> Dict[str, str]:
        """從CSV檔案載入手動修正的AARACO值

        Args:
            csv_file: CSV檔案路徑，如果未提供將嘗試尋找最新的分析詳細報告

        Returns:
            店號對AARACO值的字典
        """
        corrections = {}

        try:
            # 如果未提供CSV檔案，嘗試找最新的分析報告
            if not csv_file:
                analysis_files = list(Path('.').glob('aaraco_analysis_detail_*.csv'))
                if analysis_files:
                    # 按修改時間排序，取最新的
                    csv_file = str(sorted(analysis_files, key=lambda x: x.stat().st_mtime, reverse=True)[0])
                    logger.info(f"找到最新的分析報告: {csv_file}")
                else:
                    logger.warning("找不到現有的分析報告，無法載入手動修正")
                    return corrections

            # 讀取CSV檔案
            with open(csv_file, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if '類別' in row and row['類別'] == '手動修正':
                        store_code = row.get('店號', '').strip()
                        aaraco = row.get('AARACO', '').strip()
                        if store_code and aaraco:
                            aaraco = self.normalize_aaraco(aaraco)  # 標準化AARACO值
                            if aaraco:  # 只在有有效值時添加
                                corrections[store_code] = aaraco

            logger.info(f"從 {csv_file} 載入了 {len(corrections)} 筆手動修正")

        except Exception as e:
            logger.error(f"載入手動修正時發生錯誤: {e}", exc_info=True)

        return corrections

    def guess_aaraco_from_asaa(self, asaa: Dict[str, Any],
                               area_codes: Dict[str, str],
                               area_name_to_code: Dict[str, str],
                               city_to_codes: Dict[str, List[str]]) -> Optional[str]:
        """從 ASAA 地址中提取縣市區域，並猜測 AARACO

        Args:
            asaa: ASAA記錄
            area_codes: 代碼對區域名稱的映射
            area_name_to_code: 區域名稱對代碼的映射
            city_to_codes: 縣市對代碼列表的映射

        Returns:
            猜測的 AARACO 值，如果無法猜測則返回 None
        """
        addr = asaa.get('AAADDR', '')

        if not addr:
            return None

        # 嘗試從地址中提取縣市
        city_patterns = [
            r'(臺北市|台北市|臺中市|台中市|臺南市|台南市|高雄市|基隆市|新北市|宜蘭縣|桃園市|新竹縣|新竹市|苗栗縣|彰化縣|南投縣|雲林縣|嘉義縣|嘉義市|屏東縣|臺東縣|台東縣|花蓮縣|金門縣|連江縣|澎湖縣)',
            r'(北市|中市|南市|高市|基市|竹市|嘉市)'
        ]

        city = None
        for pattern in city_patterns:
            match = re.search(pattern, addr)
            if match:
                city = match.group(1)
                # 標準化簡寫
                city_mapping = {
                    '北市': '臺北市',
                    '中市': '臺中市',
                    '南市': '臺南市',
                    '高市': '高雄市',
                    '基市': '基隆市',
                    '竹市': '新竹市',
                    '嘉市': '嘉義市',
                    '台北市': '臺北市',
                    '台中市': '臺中市',
                    '台南市': '臺南市',
                    '台東縣': '臺東縣'
                }
                if city in city_mapping:
                    city = city_mapping[city]
                break

        # 嘗試從地址中提取區域
        district = None
        if city:
            # 移除縣市部分，以便更容易匹配區域
            addr_without_city = addr.replace(city, '', 1)
            district_pattern = r'([^\s]{1,3}[區鄉鎮市])'
            district_match = re.search(district_pattern, addr_without_city)
            if district_match:
                district = district_match.group(1)

        # 嘗試從地址中提取郵遞區號
        zipcode = None
        # 改進郵遞區號的提取方式
        zipcode_pattern = r'(\d{3,6})'
        zipcode_matches = re.findall(zipcode_pattern, addr)
        if zipcode_matches:
            # 找出最有可能是郵遞區號的數字
            for match in zipcode_matches:
                if len(match) == 3 or len(match) == 5 or len(match) == 6:
                    if match[:3] in self.zipcode_prefixes:
                        zipcode = match
                        break

            # 如果沒找到符合條件的，取第一個
            if not zipcode and zipcode_matches:
                zipcode = zipcode_matches[0]

        # 嘗試根據已提取的信息判斷
        result = None

        # 如果有縣市與區域，嘗試完全匹配
        if city and district:
            full_area_name = f"{city}{district}"
            if full_area_name in area_name_to_code:
                code = area_name_to_code[full_area_name]
                if zipcode and len(zipcode) >= 3:
                    result = f"{code[0]}{zipcode[:3]}"
                else:
                    result = code

        # 如果只有縣市，找出該縣市的第一個區碼
        if not result and city and city in city_to_codes and city_to_codes[city]:
            # 嘗試根據郵遞區號找出更匹配的區域代碼
            if zipcode and len(zipcode) >= 3:
                zip_prefix = zipcode[:3]
                for code in city_to_codes[city]:
                    if code[1:4] == zip_prefix:
                        result = code
                        break

            # 如果沒有找到對應的郵遞區號，使用第一個區碼
            if not result:
                first_code = city_to_codes[city][0]
                if zipcode and len(zipcode) >= 3:
                    result = f"{first_code[0]}{zipcode[:3]}"
                else:
                    result = first_code

        # 如果有郵遞區號，根據郵遞區號猜測
        if not result and zipcode and len(zipcode) >= 3:
            zip_prefix = zipcode[:3]
            prefix = self.zipcode_prefixes.get(zip_prefix)
            if prefix:
                result = f"{prefix}{zip_prefix}"

        # 嘗試從 AARACO 欄位獲取資訊
        if not result:
            existing_aaraco = asaa.get('AARACO', '')
            if existing_aaraco and len(existing_aaraco) >= 1:
                # 如果已有 AARACO 值但格式不正確，嘗試保留其第一個字母
                prefix = existing_aaraco[0] if existing_aaraco[0].isalpha() else None
                if prefix and zipcode and len(zipcode) >= 3:
                    result = f"{prefix}{zipcode[:3]}"

        # 標準化並返回結果
        return self.normalize_aaraco(result) if result else None

    def determine_aaraco(self, city: str, area: str, zipcode: str,
                         area_codes: Dict[str, str],
                         area_name_to_code: Dict[str, str],
                         city_to_codes: Dict[str, List[str]]) -> Optional[str]:
        """根據縣市、區域和郵遞區號確定 AARACO 值

        Args:
            city: 縣市名稱
            area: 區域名稱
            zipcode: 郵遞區號
            area_codes: 代碼對區域名稱的映射
            area_name_to_code: 區域名稱對代碼的映射
            city_to_codes: 縣市對代碼列表的映射

        Returns:
            確定的 AARACO 值，如果無法確定則返回 None
        """
        # 標準化縣市名稱
        city_mapping = {
            '北市': '臺北市',
            '中市': '臺中市',
            '南市': '臺南市',
            '高市': '高雄市',
            '基市': '基隆市',
            '竹市': '新竹市',
            '嘉市': '嘉義市',
            '台北市': '臺北市',
            '台中市': '臺中市',
            '台南市': '臺南市',
            '台東縣': '臺東縣'
        }
        if city in city_mapping:
            city = city_mapping[city]

        # 標準化區域名稱
        area = area.replace('台', '臺') if area else area

        # 初始化結果
        result = None

        # 嘗試完全匹配
        if city and area:
            full_area_name = f"{city}{area}"
            if full_area_name in area_name_to_code:
                code = area_name_to_code[full_area_name]
                if zipcode and len(zipcode) >= 3:
                    result = f"{code[0]}{zipcode[:3]}"
                else:
                    result = code

        # 嘗試模糊匹配區域名稱
        if not result and city and FUZZY_AVAILABLE:
            best_match = None
            best_score = 0

            # 篩選出該縣市的所有區域
            city_areas = []
            for area_name in area_name_to_code:
                if area_name.startswith(city):
                    city_areas.append(area_name)

            if city_areas and area:
                # 使用 process.extractOne 一次性找出最佳匹配
                best_match_result = process.extractOne(
                    f"{city}{area}",
                    city_areas,
                    scorer=fuzz.ratio,
                    score_cutoff=85  # 提高區域匹配閾值
                )

                if best_match_result:
                    matched_area, score = best_match_result
                    best_match = area_name_to_code[matched_area]
                    best_score = score

            if best_match:
                if zipcode and len(zipcode) >= 3:
                    result = f"{best_match[0]}{zipcode[:3]}"
                else:
                    result = best_match

        # 如果有郵遞區號，嘗試根據郵遞區號和城市匹配
        if not result and city and zipcode and len(zipcode) >= 3:
            zip_prefix = zipcode[:3]
            # 檢查該城市下是否有匹配此郵遞區號的代碼
            if city in city_to_codes:
                for code in city_to_codes[city]:
                    if code[1:4] == zip_prefix:
                        result = code
                        break

        # 只用縣市判斷（取第一個符合的代碼）
        if not result and city in city_to_codes and city_to_codes[city]:
            first_code = city_to_codes[city][0]
            if zipcode and len(zipcode) >= 3:
                result = f"{first_code[0]}{zipcode[:3]}"
            else:
                result = first_code

        # 如果以上方法都失敗，使用郵遞區號前三碼猜測
        if not result and zipcode and len(zipcode) >= 3:
            zip_prefix = zipcode[:3]

            # 尋找與郵遞區號前三碼匹配的代碼
            for code in area_codes:
                if code[1:4] == zip_prefix:
                    result = code
                    break

            # 如果找不到匹配的，嘗試根據郵遞區號範圍猜測字首
            if not result:
                prefix = self.zipcode_prefixes.get(zip_prefix)
                if prefix:
                    result = f"{prefix}{zip_prefix}"

        # 標準化並返回結果
        return self.normalize_aaraco(result) if result else None

    def create_test_table_sql(self) -> str:
        """產生創建測試表的SQL語句

        Returns:
            SQL語句
        """
        sql = f"""
-- 創建全聯門店測試表
CREATE TABLE {self.test_table} (
  "AAVENN" VARCHAR2(5 BYTE) NOT NULL,
  "AACSTN" VARCHAR2(10 BYTE) NOT NULL,
  "AANAME" VARCHAR2(22 BYTE) NOT NULL,
  "AAROUT" VARCHAR2(5 BYTE),
  "AAKIND" VARCHAR2(1 BYTE),
  "AABOSS" VARCHAR2(12 BYTE),
  "AAADDR" VARCHAR2(54 BYTE),
  "AAEMPO" VARCHAR2(6 BYTE),
  "AATELN" VARCHAR2(20 BYTE),
  "AADATE" VARCHAR2(7 BYTE),
  "AASTOP" VARCHAR2(7 BYTE),
  "AARACO" VARCHAR2(4 BYTE),
  "AADISP" VARCHAR2(1 BYTE) DEFAULT ' ',
  "AAHYCS" VARCHAR2(10 BYTE),
  "AAUPDD" VARCHAR2(7 BYTE),
  "AAITEM" VARCHAR2(2 BYTE),
  "AACSTN1" VARCHAR2(10 BYTE),
  "AAYYMM" VARCHAR2(5 BYTE),
  "AAINST" VARCHAR2(2 BYTE) DEFAULT 'AS',
  "AADIRECT" VARCHAR2(1 BYTE) DEFAULT 'N',
  "AATOKEN" VARCHAR2(255 BYTE),
  "AAADDRW" NVARCHAR2(54),
  "MATCHING_METHOD" VARCHAR2(20 BYTE),
  "CONFIDENCE" NUMBER(3)
);

-- 添加註解
COMMENT ON TABLE {self.test_table} IS '全聯門店區域代碼測試表';
COMMENT ON COLUMN {self.test_table}."AARACO" IS '鄉鎮區域別';
COMMENT ON COLUMN {self.test_table}."MATCHING_METHOD" IS '匹配方法';
COMMENT ON COLUMN {self.test_table}."CONFIDENCE" IS '匹配信心度(百分比)';

-- 創建索引
CREATE INDEX {self.test_table.split('.')[-1]}_IDX1 ON {self.test_table} ("AACSTN");
CREATE INDEX {self.test_table.split('.')[-1]}_IDX2 ON {self.test_table} ("MATCHING_METHOD");
CREATE INDEX {self.test_table.split('.')[-1]}_IDX3 ON {self.test_table} ("CONFIDENCE");
"""
        return sql

    def generate_insert_statements(self, matches: List[Dict[str, Any]]) -> List[str]:
        """產生插入測試表的SQL語句

        Args:
            matches: 匹配成功的記錄列表

        Returns:
            SQL語句列表
        """
        sql_statements = []

        current_date = datetime.now().strftime('%Y%m%d')

        for match in matches:
            asaa = match['ASAA']

            # 確保 AARACO 值符合資料庫欄位限制
            raw_aaraco = match['AARACO'] if match['AARACO'] else ''
            aaraco = self.normalize_aaraco(raw_aaraco)

            method = match['METHOD']
            confidence = match['CONFIDENCE']

            # 基本資料
            fields = [
                ('AAVENN', asaa.get('AAVENN', '')),
                ('AACSTN', asaa.get('AACSTN', '')),
                ('AANAME', asaa.get('AANAME', '')),
                ('AAROUT', asaa.get('AAROUT', '')),
                ('AAKIND', asaa.get('AAKIND', '')),
                ('AABOSS', asaa.get('AABOSS', '')),
                ('AAADDR', asaa.get('AAADDR', '')),
                ('AAEMPO', asaa.get('AAEMPO', '')),
                ('AATELN', asaa.get('AATELN', '')),
                ('AADATE', asaa.get('AADATE', '')),
                ('AASTOP', asaa.get('AASTOP', '')),
                ('AARACO', aaraco),  # 使用規範化後的值
                ('AADISP', asaa.get('AADISP', ' ')),
                ('AAHYCS', asaa.get('AAHYCS', '')),
                ('AAUPDD', current_date[-7:]),  # 使用當前日期的後7位
                ('AAITEM', asaa.get('AAITEM', '')),
                ('AACSTN1', asaa.get('AACSTN1', '')),
                ('AAYYMM', asaa.get('AAYYMM', '')),
                ('AAINST', asaa.get('AAINST', 'AS')),
                ('AADIRECT', asaa.get('AADIRECT', 'N')),
                ('AATOKEN', asaa.get('AATOKEN', '')),
                ('AAADDRW', asaa.get('AAADDRW', '')),
                ('MATCHING_METHOD', method),  # 新增欄位：匹配方法
                ('CONFIDENCE', str(confidence))  # 新增欄位：信心度
            ]

            # 產生INSERT語句
            fields_str = ', '.join([f'"{name}"' for name, _ in fields])
            values = []

            for _, value in fields:
                if value is None:
                    values.append('NULL')
                else:
                    # 處理可能包含單引號的字串
                    value_str = str(value).replace("'", "''")
                    values.append(f"'{value_str}'")

            values_str = ', '.join(values)

            sql = f"INSERT INTO {self.test_table} ({fields_str}) VALUES ({values_str});"
            sql_statements.append(sql)

        return sql_statements

    def generate_insert_statements_for_unmatched(self, no_matches: List[Dict[str, Any]]) -> List[str]:
        """為無法匹配的記錄產生插入SQL語句，保留原始資料

        Args:
            no_matches: 無法匹配的記錄列表

        Returns:
            SQL語句列表
        """
        sql_statements = []

        for asaa in no_matches:
            # 保留原始的AARACO值，但確保符合長度限制
            original_aaraco = asaa.get('AARACO', '')
            # 只有在值超過4個字符時才進行標準化
            aaraco = original_aaraco
            if len(original_aaraco) > 4:
                aaraco = self.normalize_aaraco(original_aaraco)

            # 基本資料，盡可能保留原始資料
            fields = [
                ('AAVENN', asaa.get('AAVENN', '')),
                ('AACSTN', asaa.get('AACSTN', '')),
                ('AANAME', asaa.get('AANAME', '')),
                ('AAROUT', asaa.get('AAROUT', '')),
                ('AAKIND', asaa.get('AAKIND', '')),
                ('AABOSS', asaa.get('AABOSS', '')),
                ('AAADDR', asaa.get('AAADDR', '')),
                ('AAEMPO', asaa.get('AAEMPO', '')),
                ('AATELN', asaa.get('AATELN', '')),
                ('AADATE', asaa.get('AADATE', '')),
                ('AASTOP', asaa.get('AASTOP', '')),
                ('AARACO', aaraco),  # 保留原始值或標準化後的值
                ('AADISP', asaa.get('AADISP', ' ')),
                ('AAHYCS', asaa.get('AAHYCS', '')),
                ('AAUPDD', asaa.get('AAUPDD', '')),  # 保留原始更新日期
                ('AAITEM', asaa.get('AAITEM', '')),
                ('AACSTN1', asaa.get('AACSTN1', '')),
                ('AAYYMM', asaa.get('AAYYMM', '')),
                ('AAINST', asaa.get('AAINST', 'AS')),
                ('AADIRECT', asaa.get('AADIRECT', 'N')),
                ('AATOKEN', asaa.get('AATOKEN', '')),
                ('AAADDRW', asaa.get('AAADDRW', '')),
                ('MATCHING_METHOD', '無法匹配'),  # 標記為無法匹配
                ('CONFIDENCE', '0')  # 信心度為0
            ]

            # 產生INSERT語句
            fields_str = ', '.join([f'"{name}"' for name, _ in fields])
            values = []

            for _, value in fields:
                if value is None:
                    values.append('NULL')
                else:
                    # 處理可能包含單引號的字串
                    value_str = str(value).replace("'", "''")
                    values.append(f"'{value_str}'")

            values_str = ', '.join(values)

            sql = f"INSERT INTO {self.test_table} ({fields_str}) VALUES ({values_str});"
            sql_statements.append(sql)

        return sql_statements

    def generate_validation_report(self, matches: List[Dict[str, Any]],
                                   no_matches: List[Dict[str, Any]]) -> Dict[str, str]:
        """產生詳細的驗證報告，以CSV格式輸出

        Args:
            matches: 匹配成功的記錄列表
            no_matches: 匹配失敗的記錄列表

        Returns:
            報告檔案路徑的字典
        """
        # 產生主要的驗證報告CSV
        csv_path = self.output_dir / f'aaraco_validation_report_{self.timestamp}.csv'
        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(
                ['店號', '店名', '地址', '現有AARACO', '建議AARACO', '匹配方法', '信心度', '匹配店名', '匹配地址',
                 '變更'])

            for match in matches:
                asaa = match['ASAA']
                px = match['PX'] if match['PX'] else {}

                # 標準化AARACO值
                raw_aaraco = match['AARACO'] if match['AARACO'] else ''
                aaraco = self.normalize_aaraco(raw_aaraco)

                old_aaraco = asaa.get('AARACO', '').strip()

                # 判斷是否有變更
                change_type = '無變更' if old_aaraco == aaraco else '有變更'
                if not old_aaraco and aaraco:
                    change_type = '新增'

                writer.writerow([
                    asaa.get('AACSTN', ''),
                    asaa.get('AANAME', ''),
                    asaa.get('AAADDR', ''),
                    old_aaraco,
                    aaraco,
                    match['METHOD'],
                    match['CONFIDENCE'],
                    px.get('STORE_NAME', '') if px else '',
                    px.get('FULL_ADDRESS', '') if px else '',
                    change_type
                ])

        # 產生無法匹配記錄的CSV
        no_match_path = self.output_dir / f'aaraco_no_match_report_{self.timestamp}.csv'
        with open(no_match_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(['店號', '店名', '地址', '現有AARACO', '備註'])

            for asaa in no_matches:
                writer.writerow([
                    asaa.get('AACSTN', ''),
                    asaa.get('AANAME', ''),
                    asaa.get('AAADDR', ''),
                    asaa.get('AARACO', ''),
                    '需要手動修正'  # 新增備註
                ])

        # 產生按信心度分類的SQL檔案
        confidence_categories = {
            '高信心度(90-100%)': [],
            '中信心度(70-89%)': [],
            '低信心度(50-69%)': [],
            '極低信心度(0-49%)': []
        }

        for match in matches:
            confidence = match['CONFIDENCE']
            if confidence >= 90:
                confidence_categories['高信心度(90-100%)'].append(match)
            elif confidence >= 70:
                confidence_categories['中信心度(70-89%)'].append(match)
            elif confidence >= 50:
                confidence_categories['低信心度(50-69%)'].append(match)
            else:
                confidence_categories['極低信心度(0-49%)'].append(match)

        # 產生匯入測試表的SQL檔案
        test_table_sql_path = self.output_dir / f'create_test_table_{self.timestamp}.sql'
        with open(test_table_sql_path, 'w', encoding='utf-8') as f:
            f.write(self.create_test_table_sql())

        # 產生插入測試資料的SQL檔案
        insert_sql_path = self.output_dir / f'insert_test_data_{self.timestamp}.sql'
        with open(insert_sql_path, 'w', encoding='utf-8') as f:
            f.write(f"-- 插入全聯門店測試資料\n")
            f.write(f"-- 產生日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            total_records = len(matches) + len(no_matches)
            f.write(
                f"-- 本檔案包含 {total_records} 筆資料 (匹配成功: {len(matches)}筆, 無法匹配: {len(no_matches)}筆)\n\n")

            # 新增交易控制
            f.write("-- 開始交易\n")
            f.write("BEGIN;\n\n")

            # 清空測試表
            f.write(f"-- 清空測試表\n")
            f.write(f"DELETE FROM {self.test_table};\n\n")

            # 插入成功匹配的資料
            f.write("-- 插入成功匹配的資料\n")
            for sql in self.generate_insert_statements(matches):
                f.write(f"{sql}\n")

            # 插入無法匹配的資料
            if no_matches:
                f.write("\n-- 插入無法匹配的資料 (保留原始數據)\n")
                for sql in self.generate_insert_statements_for_unmatched(no_matches):
                    f.write(f"{sql}\n")

            # 提交交易
            f.write("\n-- 提交交易\n")
            f.write("COMMIT;\n")

        # 產生統計報告
        stats_path = self.output_dir / f'aaraco_update_statistics_{self.timestamp}.txt'
        with open(stats_path, 'w', encoding='utf-8') as f:
            f.write("全聯門店 AARACO 欄位更新統計報告\n")
            f.write("=" * 50 + "\n")
            f.write(f"產生日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("匹配統計:\n")
            total_records = len(matches) + len(no_matches)
            if total_records > 0:
                f.write(f"  總記錄數: {total_records}\n")
                f.write(f"  成功匹配: {len(matches)} 筆 ({len(matches) / total_records * 100:.1f}%)\n")
                f.write(f"  無法匹配: {len(no_matches)} 筆 ({len(no_matches) / total_records * 100:.1f}%)\n\n")
            else:
                f.write("  沒有記錄\n\n")

            f.write("匹配方法統計:\n")
            methods = {}
            for match in matches:
                method = match['METHOD']
                methods[method] = methods.get(method, 0) + 1

            for method, count in methods.items():
                f.write(f"  {method}: {count} 筆 ({count / len(matches) * 100:.1f}%)\n")

            f.write("\n信心度統計:\n")
            for category, matched_records in confidence_categories.items():
                if matches:
                    f.write(
                        f"  {category}: {len(matched_records)} 筆 ({len(matched_records) / len(matches) * 100:.1f}%)\n")
                else:
                    f.write(f"  {category}: {len(matched_records)} 筆 (0.0%)\n")

            f.write("\n更新類型統計:\n")
            change_types = {'無變更': 0, '有變更': 0, '新增': 0}
            for match in matches:
                asaa = match['ASAA']
                old_aaraco = asaa.get('AARACO', '').strip()

                # 標準化AARACO值
                raw_aaraco = match['AARACO'] if match['AARACO'] else ''
                new_aaraco = self.normalize_aaraco(raw_aaraco)

                if not old_aaraco and new_aaraco:
                    change_types['新增'] += 1
                elif old_aaraco == new_aaraco:
                    change_types['無變更'] += 1
                else:
                    change_types['有變更'] += 1

            for change_type, count in change_types.items():
                f.write(f"  {change_type}: {count} 筆 ({count / len(matches) * 100:.1f}%)\n")

            f.write("\n產生的檔案:\n")
            f.write(f"  1. {csv_path} - 匹配結果與建議更新\n")
            f.write(f"  2. {no_match_path} - 無法匹配的記錄報告\n")
            f.write(f"  3. {stats_path} - 此統計報告\n")
            f.write(f"  4. {test_table_sql_path} - 創建測試表SQL\n")
            f.write(f"  5. {insert_sql_path} - 插入測試資料SQL\n")

        # 產生手動修正模版
        manual_correction_path = self.output_dir / f'aaraco_manual_correction_template_{self.timestamp}.csv'
        with open(manual_correction_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(['類別', '店號', '店名', '地址', 'AARACO'])

            # 將無法匹配的記錄寫入模版
            for asaa in no_matches:
                writer.writerow([
                    '手動修正',  # 標記為手動修正
                    asaa.get('AACSTN', ''),
                    asaa.get('AANAME', ''),
                    asaa.get('AAADDR', ''),
                    asaa.get('AARACO', '')  # 使用原始AARACO值作為參考
                ])

            # 也將低信心度的記錄寫入，提供使用者修正
            for match in confidence_categories['極低信心度(0-49%)']:
                asaa = match['ASAA']
                # 標準化AARACO值
                raw_aaraco = match['AARACO'] if match['AARACO'] else ''
                aaraco = self.normalize_aaraco(raw_aaraco)

                writer.writerow([
                    '手動修正',
                    asaa.get('AACSTN', ''),
                    asaa.get('AANAME', ''),
                    asaa.get('AAADDR', ''),
                    aaraco  # 提供猜測的值作為參考
                ])

        return {
            'validation_report': str(csv_path),
            'no_match_report': str(no_match_path),
            'statistics_report': str(stats_path),
            'test_table_sql': str(test_table_sql_path),
            'insert_sql': str(insert_sql_path),
            'manual_correction_template': str(manual_correction_path)
        }

    def output_aaraco_analysis_report(self, analysis_results: Dict[str, List[Dict[str, Any]]]) -> Dict[str, str]:
        """輸出 AARACO 分析報告到檔案

        Args:
            analysis_results: 分析結果的字典

        Returns:
            報告檔案路徑的字典
        """
        report_path = self.output_dir / f'aaraco_analysis_report_{self.timestamp}.txt'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("ASAA 表格 AARACO 欄位分析報告\n")
            f.write("=" * 50 + "\n")
            f.write(f"產生日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            total = sum(len(v) for v in analysis_results.values())

            f.write("整體統計:\n")
            f.write(f"  總記錄數: {total}\n")
            f.write(
                f"  有效格式 (例如 A100): {len(analysis_results['valid'])} 筆 ({len(analysis_results['valid']) / total * 100:.1f}%)\n")
            f.write(
                f"  無效格式: {len(analysis_results['invalid'])} 筆 ({len(analysis_results['invalid']) / total * 100:.1f}%)\n")
            f.write(
                f"  部分有效: {len(analysis_results['partial'])} 筆 ({len(analysis_results['partial']) / total * 100:.1f}%)\n")
            f.write(
                f"  空值: {len(analysis_results['empty'])} 筆 ({len(analysis_results['empty']) / total * 100:.1f}%)\n\n")

            # 寫入每類的前 10 個範例
            categories = [
                ('有效格式', analysis_results['valid']),
                ('無效格式', analysis_results['invalid']),
                ('部分有效', analysis_results['partial']),
                ('空值', analysis_results['empty'])
            ]

            for title, records in categories:
                if records:
                    f.write(f"{title} (顯示前 10 筆):\n")
                    for i, asaa in enumerate(records[:10]):
                        f.write(
                            f"  {i + 1}. {asaa.get('AACSTN', '')} | {asaa.get('AANAME', '')} | {asaa.get('AAADDR', '')} | AARACO={asaa.get('AARACO', '')}\n")
                    f.write("\n")

        # 輸出 CSV 格式的詳細報告
        detail_path = self.output_dir / f'aaraco_analysis_detail_{self.timestamp}.csv'
        with open(detail_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(['類別', '店號', '店名', '地址', 'AARACO'])

            for category_name, records in categories:
                for asaa in records:
                    writer.writerow([
                        category_name,
                        asaa.get('AACSTN', ''),
                        asaa.get('AANAME', ''),
                        asaa.get('AAADDR', ''),
                        asaa.get('AARACO', '')
                    ])

        logger.info(f"已產生 AARACO 分析報告，保存到 {report_path} 和 {detail_path} 檔案")
        return {'report': str(report_path), 'detail': str(detail_path)}

    def analyze_existing_aaraco(self, asaa_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """分析 ASAA 資料中現有 AARACO 值的有效性，返回分類結果

        Args:
            asaa_data: ASAA 資料

        Returns:
            分類結果的字典
        """
        valid_pattern = re.compile(r'^[A-Z]\d{3}$')  # 正確格式：一個大寫字母後跟三個數字

        results = {
            'valid': [],  # 符合格式的 AARACO
            'invalid': [],  # 不符合格式的 AARACO
            'empty': [],  # 空值
            'partial': []  # 部分符合（例如只有字母或只有數字）
        }

        logger.info("分析現有 AARACO 值...")
        total = len(asaa_data.get('RECORDS', []))

        for asaa in asaa_data.get('RECORDS', []):
            aaraco = asaa.get('AARACO', '').strip()
            if not aaraco:
                results['empty'].append(asaa)
            elif valid_pattern.match(aaraco):
                results['valid'].append(asaa)
            elif re.match(r'^[A-Z][\d]{0,2}$', aaraco) or re.match(r'^[\d]{1,3}$', aaraco):
                results['partial'].append(asaa)
            else:
                results['invalid'].append(asaa)

        # 輸出分析結果
        logger.info(f"AARACO 值分析結果 (總筆數: {total}):")
        for category, items in results.items():
            percent = len(items) / total * 100 if total > 0 else 0
            logger.info(f"  {category}: {len(items)} 筆 ({percent:.1f}%)")

        return results

    def process(self, manual_correction_file: str = None) -> Dict[str, Any]:
        """執行完整的處理流程

        Args:
            manual_correction_file: 手動修正檔案路徑

        Returns:
            處理結果的字典
        """
        try:
            logger.info("===== 全聯門店 AARACO 欄位更新工具 =====")
            logger.info("正在載入資料...")

            # 載入 ASAA 和 PX_STORES 資料
            asaa_data = self.load_json_data('ASAA.json')
            px_data = self.load_json_data('PX_STORES.json')

            # 分析現有 AARACO 值
            logger.info("\n1. 正在分析現有 AARACO 值...")
            analysis_results = self.analyze_existing_aaraco(asaa_data)
            analysis_files = self.output_aaraco_analysis_report(analysis_results)

            # 獲取區域代碼對應關係
            logger.info("\n2. 正在讀取地區代碼對應表...")
            area_codes, area_name_to_code, city_to_codes = self.get_area_codes()
            logger.info(f"   讀取了 {len(area_codes)} 筆地區代碼")

            # 載入手動修正資料
            logger.info("\n3. 正在載入手動修正資料...")
            manual_corrections = self.load_manual_corrections(manual_correction_file)

            # 尋找匹配的記錄
            logger.info("\n4. 正在進行資料匹配...")
            matches, no_matches = self.find_matching_records(
                asaa_data, px_data, area_codes, area_name_to_code, city_to_codes, manual_corrections
            )
            logger.info(f"   成功匹配: {len(matches)} 筆")
            logger.info(f"   無法匹配: {len(no_matches)} 筆")

            # 產生驗證報告和測試表SQL
            logger.info("\n5. 正在產生驗證報告和測試表SQL...")
            report_files = self.generate_validation_report(matches, no_matches)

            logger.info("\n所有處理完成。以下是產生的檔案:")
            logger.info(f"1. {analysis_files['report']} - 現有 AARACO 值分析報告")
            logger.info(f"2. {analysis_files['detail']} - 現有 AARACO 值詳細清單")
            logger.info(f"3. {report_files['validation_report']} - 匹配結果與建議更新報告")
            logger.info(f"4. {report_files['no_match_report']} - 無法匹配的記錄報告")
            logger.info(f"5. {report_files['statistics_report']} - 更新統計資訊")
            logger.info(f"6. {report_files['test_table_sql']} - 創建測試表SQL")
            logger.info(f"7. {report_files['insert_sql']} - 插入測試資料SQL")
            logger.info(f"8. {report_files['manual_correction_template']} - 手動修正模板")

            logger.info("\n使用說明:")
            logger.info("1. 先執行「創建測試表SQL」創建測試表")
            logger.info("2. 再執行「插入測試資料SQL」將匹配結果導入測試表")
            logger.info("3. 檢查測試表中的資料，確認AARACO欄位是否正確")
            logger.info("4. 如有需要修正的記錄，可修改「手動修正模板」，然後重新執行程式")
            logger.info("5. 確認無誤後，可以產生正式更新語句應用到正式表中")

            return {
                'status': 'success',
                'analysis_files': analysis_files,
                'report_files': report_files,
                'matches': len(matches),
                'no_matches': len(no_matches),
                'test_table': self.test_table
            }

        except Exception as e:
            logger.error(f"\n執行過程中發生錯誤: {e}", exc_info=True)
            return {
                'status': 'error',
                'error': str(e)
            }

    def generate_real_update_sql(self, confidence_threshold: int = 80) -> str:
        """產生實際更新ASAA表的SQL語句

        Args:
            confidence_threshold: 信心度閾值，只有高於此閾值的記錄才會被更新

        Returns:
            SQL語句
        """
        try:
            # 從測試表讀取資料，產生實際更新語句
            sql = f"""
-- 全聯門店 AARACO 欄位更新語句
-- 產生日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
-- 僅包含信心度 >= {confidence_threshold}% 的記錄

-- 開始交易
BEGIN;

-- 更新 AARACO 欄位
UPDATE ASUSER.ASAA A
SET A.AARACO = (
    SELECT T.AARACO 
    FROM {self.test_table} T 
    WHERE T.AACSTN = A.AACSTN
    AND T.CONFIDENCE >= {confidence_threshold}
),
A.AAUPDD = TO_CHAR(SYSDATE, 'YYYYMMDD')
WHERE EXISTS (
    SELECT 1 
    FROM {self.test_table} T 
    WHERE T.AACSTN = A.AACSTN
    AND T.CONFIDENCE >= {confidence_threshold}
    AND (A.AARACO IS NULL OR A.AARACO <> T.AARACO)
);

-- 提交交易
COMMIT;

-- 檢查更新筆數
SELECT COUNT(*) AS UPDATED_ROWS
FROM {self.test_table}
WHERE CONFIDENCE >= {confidence_threshold};
"""
            return sql
        except Exception as e:
            logger.error(f"產生實際更新SQL時發生錯誤: {e}", exc_info=True)
            return "-- 產生SQL時發生錯誤，請檢查日誌"


def main():
    """主程式入口點"""
    import argparse

    parser = argparse.ArgumentParser(description='全聯門店地區代碼(AARACO)處理工具')
    parser.add_argument('--output', '-o', default='output', help='輸出目錄')
    parser.add_argument('--test-table', '-t', default='ASUSER.ASAA_TEST', help='測試表名稱')
    parser.add_argument('--manual', '-m', help='手動修正檔案路徑')
    parser.add_argument('--generate-update', '-g', action='store_true', help='產生實際更新SQL')
    parser.add_argument('--confidence', '-c', type=int, default=80, help='更新SQL的信心度閾值 (預設: 80)')

    args = parser.parse_args()

    processor = AaracoProcessor(output_dir=args.output, test_table=args.test_table)

    if args.generate_update:
        # 產生實際更新SQL
        sql = processor.generate_real_update_sql(args.confidence)
        output_file = Path(args.output) / f'update_real_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.sql'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql)
        print(f"\n已產生實際更新SQL: {output_file}")
    else:
        # 執行完整處理流程
        result = processor.process(args.manual)

        if result['status'] == 'success':
            print("\n處理完成！所有報告已產生到輸出目錄。")
            print(f"成功匹配: {result['matches']} 筆")
            print(f"無法匹配: {result['no_matches']} 筆")
            print(f"\n如要產生實際更新SQL，請使用 --generate-update 參數執行程式")
        else:
            print(f"\n處理失敗：{result['error']}")
            print("請檢查日誌檔案 aaraco_process.log 獲取詳細錯誤信息。")


if __name__ == "__main__":
    main()