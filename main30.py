# 導入所需庫
from pywinauto.application import Application
from pywinauto.timings import Timings
from pywinauto import mouse, keyboard
import time
import logging
import os
import sys
import argparse
import json
from datetime import datetime


# 設置日誌
def setup_logger(log_file="power_automate_automation.log"):
    """設置日誌配置"""
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)

    logger = logging.getLogger("PowerAutomateAutomation")
    logger.setLevel(logging.INFO)

    # 創建文件處理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 創建控制台處理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 創建格式器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加處理器到日誌記錄器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


# 重試裝飾器
def retry(max_attempts=3, delay=2):
    """重試函數執行的裝飾器"""

    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"嘗試 {attempt + 1}/{max_attempts} 失敗: {str(e)}")
                    if attempt < max_attempts - 1:
                        logger.info(f"等待 {delay} 秒後重試...")
                        time.sleep(delay)
                    else:
                        logger.error(f"達到最大重試次數。最後錯誤: {str(e)}")
                        raise

        return wrapper

    return decorator


# 加載配置
def load_config(config_file="power_automate_config.json"):
    """從配置文件加載設置"""
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {
        "power_automate_path": r"C:\Program Files (x86)\Power Automate Desktop\PAD.exe",
        "timeout": 120,
        "flows": [
            {"name": "測試API", "wait_time": 30}
        ],
        "timing_mode": "normal"  # 可以是 "fast", "normal", "slow"
    }


class PowerAutomateAutomation:
    def __init__(self, config):
        self.config = config
        self.set_timings(config.get("timing_mode", "normal"))
        self.app = None
        self.main_window = None

    def set_timings(self, mode):
        """設置pywinauto的計時模式"""
        if mode == "fast":
            Timings.fast()
        elif mode == "slow":
            Timings.slow()
        else:
            Timings.defaults()

        # 設置更長的窗口查找超時
        Timings.window_find_timeout = 10

    @retry(max_attempts=3, delay=2)
    def start_power_automate(self):
        """啟動或連接到Power Automate"""
        logger.info("正在啟動或連接到Power Automate...")

        try:
            # 嘗試連接到已運行的實例
            self.app = Application(backend="uia").connect(path=os.path.basename(self.config["power_automate_path"]))
            logger.info("成功連接到現有的Power Automate實例")
        except Exception:
            # 如果連接失敗，啟動新實例
            logger.info("沒有找到運行中的Power Automate，啟動新實例")
            self.app = Application(backend="uia").start(self.config["power_automate_path"])
            time.sleep(5)  # 等待啟動完成

        # 獲取主窗口
        self.main_window = self.app.window(title="Power Automate")
        self.main_window.set_focus()
        logger.info("Power Automate主窗口已獲取並設置為焦點")

        # 等待主界面加載完成
        timeout = self.config.get("timeout", 60)
        try:
            # 等待典型的元素出現確認界面已加載
            self.main_window.child_window(title="流程", control_type="Text").wait('visible', timeout=timeout)
            logger.info("Power Automate界面已完全加載")
        except Exception as e:
            logger.warning(f"等待界面加載時出現問題: {str(e)}")
            # 嘗試捕獲窗口截圖以進行調試
            try:
                screenshot_path = f"error_screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                self.main_window.capture_as_image().save(screenshot_path)
                logger.info(f"已保存錯誤截圖到: {screenshot_path}")
            except:
                pass
            raise

    def print_ui_structure(self):
        """打印UI結構以幫助調試"""
        logger.info("正在打印界面控件結構...")
        try:
            self.main_window.print_control_identifiers(filename="power_automate_controls.txt")
            logger.info("界面結構已保存到 power_automate_controls.txt")
        except Exception as e:
            logger.error(f"打印界面結構時出錯: {str(e)}")

    @retry(max_attempts=3, delay=2)
    def run_flow(self, flow_name, wait_time=30):
        """運行指定的流程"""
        logger.info(f"嘗試運行流程: {flow_name}")

        # 確保我們處於"我的流程"選項卡
        try:
            my_flows_tab = self.main_window.child_window(title="我的流程", control_type="TabItem")
            my_flows_tab.click_input()
            logger.info("已切換到'我的流程'選項卡")
        except Exception as e:
            logger.warning(f"切換到'我的流程'選項卡時出錯: {str(e)}")
            # 繼續嘗試，因為我們可能已經在正確的選項卡上

        # 嘗試查找並點擊流程項目
        found = False
        try:
            # 首先嘗試直接通過標題查找
            flow_item = self.main_window.child_window(title=flow_name, control_type="Custom")
            flow_item.click_input()
            found = True
            logger.info(f"找到並點擊了流程: {flow_name}")
        except Exception:
            logger.warning(f"無法通過標題直接找到流程: {flow_name}，嘗試替代方法")

        # 如果直接查找失敗，嘗試在列表中查找
        if not found:
            try:
                # 嘗試在數據網格/列表中查找
                data_grid = self.main_window.child_window(control_type="DataGrid")
                rows = data_grid.children(control_type="Custom")

                for row in rows:
                    try:
                        if flow_name in row.window_text():
                            row.click_input()
                            found = True
                            logger.info(f"在數據網格中找到並點擊了流程: {flow_name}")
                            break
                    except:
                        continue
            except Exception as e:
                logger.warning(f"在數據網格中查找流程時出錯: {str(e)}")

        # 如果還是找不到，嘗試使用搜索
        if not found:
            try:
                # 點擊搜索框
                search_box = self.main_window.child_window(title="搜尋", control_type="Edit")
                search_box.click_input()

                # 清除現有文本並輸入搜索內容
                search_box.set_edit_text("")
                search_box.type_keys(flow_name)
                time.sleep(2)  # 等待搜索結果

                # 嘗試點擊搜索結果
                results = self.main_window.descendants(control_type="Custom")
                for result in results:
                    try:
                        if flow_name in result.window_text():
                            result.click_input()
                            found = True
                            logger.info(f"通過搜索找到並點擊了流程: {flow_name}")
                            break
                    except:
                        continue
            except Exception as e:
                logger.error(f"使用搜索查找流程時出錯: {str(e)}")

        if not found:
            error_msg = f"無法找到流程: {flow_name}"
            logger.error(error_msg)
            raise Exception(error_msg)

        # 點擊執行按鈕
        time.sleep(1)  # 確保界面已更新
        try:
            run_button = self.main_window.child_window(title="執行", control_type="Button")
            run_button.click_input()
            logger.info("點擊了執行按鈕")
        except Exception as e:
            # 如果無法找到"執行"按鈕，嘗試點擊播放圖標
            logger.warning(f"點擊'執行'按鈕失敗: {str(e)}，嘗試替代方法")
            try:
                play_button = self.main_window.child_window(title="執行", auto_id="PlayButton")
                play_button.click_input()
                logger.info("點擊了播放按鈕")
            except Exception as e2:
                logger.error(f"無法點擊任何執行按鈕: {str(e2)}")
                raise

        # 處理可能出現的對話框
        self.handle_dialogs()

        # 等待流程執行完成
        logger.info(f"等待流程執行完成，等待時間: {wait_time}秒")
        time.sleep(wait_time)

        # 檢查執行結果
        self.check_execution_result()

    def handle_dialogs(self):
        """處理可能出現的對話框"""
        # 等待一小段時間，看是否出現對話框
        time.sleep(2)

        # 處理可能的確認對話框
        try:
            dialog = self.app.window(title_re="確認|Confirm|提示|警告|Warning")
            if dialog.exists():
                logger.info(f"檢測到對話框: {dialog.window_text()}")
                # 嘗試點擊"是"或"確定"按鈕
                for button_title in ["是", "確定", "Yes", "OK", "確認", "允許"]:
                    try:
                        button = dialog.child_window(title=button_title, control_type="Button")
                        if button.exists():
                            button.click()
                            logger.info(f"已點擊對話框中的 '{button_title}' 按鈕")
                            time.sleep(1)
                            return
                    except:
                        pass

                # 如果找不到匹配的按鈕，嘗試按Enter鍵
                dialog.type_keys("{ENTER}")
                logger.info("在對話框中按了Enter鍵")
                time.sleep(1)
        except Exception as e:
            logger.info(f"沒有檢測到需要處理的對話框: {str(e)}")

    def check_execution_result(self):
        """檢查執行結果"""
        try:
            # 嘗試查找表明成功的元素
            success_indicator = self.main_window.child_window(title_re="執行成功|完成|Success")
            if success_indicator.exists():
                logger.info("流程執行成功")
                return True
        except:
            pass

        try:
            # 嘗試查找表明失敗的元素
            error_indicator = self.main_window.child_window(title_re="失敗|錯誤|Error|Failed")
            if error_indicator.exists():
                logger.warning("檢測到流程執行失敗")
                return False
        except:
            pass

        logger.info("無法確定流程執行結果，假設成功")
        return True

    def close_power_automate(self):
        """關閉Power Automate"""
        if self.main_window and self.main_window.exists():
            logger.info("正在關閉Power Automate")
            try:
                self.main_window.close()
                logger.info("Power Automate已關閉")
            except Exception as e:
                logger.error(f"關閉Power Automate時出錯: {str(e)}")
                # 嘗試使用Alt+F4強制關閉
                try:
                    self.main_window.set_focus()
                    keyboard.send_keys("%{F4}")
                    logger.info("使用Alt+F4關閉了Power Automate")
                except:
                    pass


def parse_arguments():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description="自動運行Power Automate流程")
    parser.add_argument("--config", default="power_automate_config.json", help="配置文件路徑")
    parser.add_argument("--flow", help="要運行的特定流程名稱")
    parser.add_argument("--log", default="power_automate_automation.log", help="日誌文件路徑")
    parser.add_argument("--debug", action="store_true", help="啟用調試模式")
    parser.add_argument("--no-close", action="store_true", help="運行後不關閉Power Automate")
    return parser.parse_args()


if __name__ == "__main__":
    # 解析命令行參數
    args = parse_arguments()

    # 設置日誌
    logger = setup_logger(args.log)
    logger.info("=" * 50)
    logger.info("自動化Power Automate腳本啟動")

    try:
        # 加載配置
        config = load_config(args.config)
        logger.info(f"已加載配置: {config}")

        # 創建自動化實例
        automation = PowerAutomateAutomation(config)

        # 啟動或連接到Power Automate
        automation.start_power_automate()

        # 如果啟用了調試模式，打印UI結構
        if args.debug:
            automation.print_ui_structure()

        # 確定要運行的流程
        if args.flow:
            flows_to_run = [{"name": args.flow, "wait_time": config.get("timeout", 30)}]
            logger.info(f"從命令行參數指定運行流程: {args.flow}")
        else:
            flows_to_run = config.get("flows", [])
            logger.info(f"從配置文件加載要運行的流程: {[flow['name'] for flow in flows_to_run]}")

        # 運行每個流程
        for flow in flows_to_run:
            flow_name = flow["name"]
            wait_time = flow.get("wait_time", config.get("timeout", 30))

            logger.info(f"開始運行流程 {flow_name}，等待時間 {wait_time}秒")
            try:
                automation.run_flow(flow_name, wait_time)
                logger.info(f"流程 {flow_name} 執行完成")
            except Exception as e:
                logger.error(f"運行流程 {flow_name} 時出錯: {str(e)}")

        # 如果不是指定了不關閉，則關閉Power Automate
        if not args.no_close:
            automation.close_power_automate()

        logger.info("自動化腳本執行完成")

    except Exception as e:
        logger.error(f"自動化過程中出現未處理的錯誤: {str(e)}", exc_info=True)
        sys.exit(1)