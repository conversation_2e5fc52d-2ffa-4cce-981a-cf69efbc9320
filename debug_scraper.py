"""
調試版家樂福爬蟲 - 詳細檢查元素內容
"""

import asyncio
from playwright.async_api import async_playwright

async def debug_elements():
    """調試元素內容"""
    print("🔍 === 調試版家樂福爬蟲 ===")
    print("📱 即將打開瀏覽器，詳細檢查元素內容")
    print("=" * 50)
    
    target_url = "https://online.carrefour.com.tw/zh/%E5%93%81%E7%89%8C%E6%97%97%E8%89%A6%E9%A4%A8/%E9%BB%91%E6%9D%BE%E5%93%81%E7%89%8C%E9%A4%A8"
    
    async with async_playwright() as p:
        print("🌐 正在啟動瀏覽器...")
        
        browser = await p.chromium.launch(
            headless=False,  # 顯示瀏覽器
            slow_mo=1000,    # 每個操作間隔 1 秒
            args=['--start-maximized']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        page = await context.new_page()
        
        try:
            print(f"🔗 正在訪問目標網站...")
            await page.goto(target_url, timeout=60000)
            print("✅ 網站載入完成")
            
            # 等待頁面載入
            await page.wait_for_timeout(10000)
            
            # 滾動頁面
            print("📜 滾動頁面...")
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(3000)
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(2000)
            
            # 檢查不同的選擇器
            selectors_to_test = [
                'div[class*="item"]',
                '[class*="product"]',
                'div:has(img)',
                'a:has(img)',
                '*:has-text("NT$")',
                '*:has-text("$")',
                'img',
                'a'
            ]
            
            for selector in selectors_to_test:
                try:
                    elements = await page.query_selector_all(selector)
                    print(f"\n🔍 選擇器 '{selector}': {len(elements)} 個元素")
                    
                    if elements and len(elements) > 0:
                        # 檢查前 3 個元素的內容
                        for i, element in enumerate(elements[:3]):
                            try:
                                # 獲取元素的 HTML
                                html = await element.inner_html()
                                text = await element.inner_text()
                                
                                print(f"   元素 {i+1}:")
                                print(f"     文字內容: {text[:100]}...")
                                print(f"     HTML 長度: {len(html)} 字符")
                                
                                # 檢查是否包含價格
                                if 'NT$' in text or '$' in text:
                                    print(f"     💰 包含價格資訊!")
                                
                                # 檢查是否有圖片
                                img_elements = await element.query_selector_all('img')
                                if img_elements:
                                    print(f"     🖼️  包含 {len(img_elements)} 個圖片")
                                
                                # 檢查是否有連結
                                link_elements = await element.query_selector_all('a')
                                if link_elements:
                                    print(f"     🔗 包含 {len(link_elements)} 個連結")
                                
                            except Exception as e:
                                print(f"     ⚠️  檢查元素時發生錯誤: {e}")
                
                except Exception as e:
                    print(f"   ❌ 選擇器 '{selector}' 發生錯誤: {e}")
            
            # 特別檢查包含價格的元素
            print(f"\n💰 === 詳細檢查價格元素 ===")
            try:
                price_elements = await page.query_selector_all('*:has-text("NT$"), *:has-text("$")')
                print(f"找到 {len(price_elements)} 個包含價格的元素")
                
                for i, element in enumerate(price_elements[:5]):
                    try:
                        text = await element.inner_text()
                        tag_name = await element.evaluate('el => el.tagName')
                        class_name = await element.get_attribute('class') or ''
                        
                        print(f"   價格元素 {i+1}:")
                        print(f"     標籤: {tag_name}")
                        print(f"     類別: {class_name}")
                        print(f"     文字: {text[:50]}...")
                        
                    except Exception as e:
                        print(f"     ⚠️  檢查價格元素時發生錯誤: {e}")
                        
            except Exception as e:
                print(f"❌ 檢查價格元素時發生錯誤: {e}")
            
            # 檢查圖片元素
            print(f"\n🖼️  === 詳細檢查圖片元素 ===")
            try:
                img_elements = await page.query_selector_all('img')
                print(f"找到 {len(img_elements)} 個圖片元素")
                
                for i, img in enumerate(img_elements[:5]):
                    try:
                        src = await img.get_attribute('src') or ''
                        alt = await img.get_attribute('alt') or ''
                        
                        print(f"   圖片 {i+1}:")
                        print(f"     來源: {src[:50]}...")
                        print(f"     描述: {alt[:50]}...")
                        
                    except Exception as e:
                        print(f"     ⚠️  檢查圖片時發生錯誤: {e}")
                        
            except Exception as e:
                print(f"❌ 檢查圖片元素時發生錯誤: {e}")
            
            # 嘗試使用 JavaScript 獲取更多資訊
            print(f"\n🔧 === 使用 JavaScript 檢查 ===")
            try:
                # 檢查頁面上的所有文字內容
                all_text = await page.evaluate("""
                    () => {
                        const elements = document.querySelectorAll('*');
                        const texts = [];
                        for (let el of elements) {
                            const text = el.innerText;
                            if (text && text.includes('NT$')) {
                                texts.push({
                                    tag: el.tagName,
                                    class: el.className,
                                    text: text.substring(0, 100)
                                });
                            }
                        }
                        return texts.slice(0, 10);
                    }
                """)
                
                print(f"JavaScript 找到 {len(all_text)} 個包含 NT$ 的元素:")
                for i, item in enumerate(all_text):
                    print(f"   {i+1}. {item['tag']}.{item['class']}: {item['text']}...")
                    
            except Exception as e:
                print(f"❌ JavaScript 檢查時發生錯誤: {e}")
            
            # 等待用戶觀看
            print("\n👀 請觀看瀏覽器視窗，15 秒後將關閉...")
            await page.wait_for_timeout(15000)
            
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")
            await page.wait_for_timeout(10000)
        
        finally:
            await browser.close()
            print("🔚 瀏覽器已關閉")

async def main():
    """主函數"""
    await debug_elements()
    print("\n🎉 調試完成！")
    print("💡 請查看上面的輸出，了解網頁元素的詳細結構")

if __name__ == "__main__":
    asyncio.run(main())
