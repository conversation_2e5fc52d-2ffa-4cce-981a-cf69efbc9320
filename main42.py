import ldap3
import sys


def test_service_account():
    """測試服務帳號連接"""
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389, get_info=ldap3.ALL)

    print("=== 服務帳號測試 ===")
    print("請輸入服務帳號資訊 (如果沒有，請先聯繫IT申請)")

    service_dn = input("服務帳號 DN (例如: CN=gitea-service,OU=服務帳號,DC=heysong,DC=com,DC=tw): ").strip()
    if not service_dn:
        print("跳過服務帳號測試")
        return None

    service_password = input("服務帳號密碼: ").strip()
    if not service_password:
        print("跳過服務帳號測試")
        return None

    try:
        conn = ldap3.Connection(server, user=service_dn, password=service_password)
        if conn.bind():
            print("✓ 服務帳號認證成功！")

            # 測試搜尋權限
            search_filter = "(objectCategory=Person)"
            result = conn.search('DC=heysong,DC=com,DC=tw', search_filter, ldap3.SUBTREE,
                               attributes=['sAMAccountName', 'userPrincipalName'],
                               size_limit=5)

            if result:
                print(f"✓ 搜尋權限正常，找到 {len(conn.entries)} 個使用者")
                for entry in conn.entries:
                    print(f"  - {entry.sAMAccountName}: {entry.userPrincipalName}")
            else:
                print("✗ 搜尋權限可能不足")

            conn.unbind()
            return service_dn
        else:
            print("✗ 服務帳號認證失敗")
            return None
    except Exception as e:
        print(f"✗ 服務帳號測試錯誤: {e}")
        return None


def test_user_authentication(service_dn=None):
    """測試使用者認證流程"""
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389, get_info=ldap3.ALL)

    print("\n=== 使用者認證測試 ===")
    test_username = input("測試使用者帳號 (或按 Enter 使用 16613): ").strip() or "16613"
    test_password = input("測試使用者密碼: ").strip()

    if not test_password:
        print("跳過使用者認證測試")
        return

    # 如果有服務帳號，使用服務帳號搜尋
    if service_dn:
        service_password = input("服務帳號密碼: ").strip()
        if service_password:
            try:
                # 使用服務帳號連接
                service_conn = ldap3.Connection(server, user=service_dn, password=service_password)
                if service_conn.bind():
                    print("✓ 使用服務帳號搜尋使用者")

                    # 搜尋使用者
                    search_filter = f"(sAMAccountName={test_username})"
                    result = service_conn.search('DC=heysong,DC=com,DC=tw', search_filter, ldap3.SUBTREE,
                                               attributes=['distinguishedName', 'sAMAccountName', 'userPrincipalName', 'mail'])

                    if result and service_conn.entries:
                        user_dn = str(service_conn.entries[0].distinguishedName)
                        print(f"✓ 找到使用者: {user_dn}")

                        # 測試使用者密碼
                        user_conn = ldap3.Connection(server, user=user_dn, password=test_password)
                        if user_conn.bind():
                            print("✓ 使用者密碼驗證成功！")
                            print(f"  使用者資訊:")
                            print(f"    DN: {user_dn}")
                            print(f"    sAMAccountName: {service_conn.entries[0].sAMAccountName}")
                            print(f"    Email: {service_conn.entries[0].userPrincipalName}")
                            user_conn.unbind()
                        else:
                            print("✗ 使用者密碼驗證失敗")
                    else:
                        print(f"✗ 找不到使用者: {test_username}")

                    service_conn.unbind()
                else:
                    print("✗ 服務帳號連接失敗")
            except Exception as e:
                print(f"✗ 服務帳號測試錯誤: {e}")

    # 直接使用使用者帳號測試
    print("\n--- 直接使用者認證測試 ---")
    auth_methods = [
        f"{test_username}@heysong.com.tw",
        f"HEYSONG\\{test_username}"
    ]

    for auth_user in auth_methods:
        print(f"嘗試: {auth_user}")
        try:
            conn = ldap3.Connection(server, user=auth_user, password=test_password)
            if conn.bind():
                print("✓ 直接認證成功！")
                conn.unbind()
                break
            else:
                print("✗ 直接認證失敗")
        except Exception as e:
            print(f"✗ 認證錯誤: {e}")


def generate_gitea_config(service_dn=None):
    """產生 Gitea 設定指南"""
    print("\n" + "="*60)
    print("GITEA LDAP 設定指南")
    print("="*60)

    print("\n1. 登入 Gitea 管理介面")
    print("   - 以管理員身份登入")
    print("   - 進入 '網站管理' → '認證來源'")

    print("\n2. 新增認證來源")
    print("   - 點擊 '新增認證來源'")
    print("   - 認證類型: 'LDAP (via BindDN)'")

    print("\n3. 基本設定")
    print("   認證名稱: 黑松 LDAP")
    print("   主機: HSWDC00.heysong.com.tw")
    print("   連接埠: 389")
    print("   安全協定: 未加密")

    print("\n4. 綁定設定")
    if service_dn:
        print(f"   Bind DN: {service_dn}")
        print("   Bind 密碼: [輸入服務帳號密碼]")
    else:
        print("   Bind DN: [需要IT提供服務帳號DN]")
        print("   Bind 密碼: [需要IT提供服務帳號密碼]")
        print("\n   ⚠️  請聯繫IT部門申請專用服務帳號")

    print("\n5. 使用者設定")
    print("   使用者搜尋基礎: DC=heysong,DC=com,DC=tw")
    print("   使用者篩選器: (&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s))")
    print("   使用者名稱屬性: sAMAccountName")
    print("   名字屬性: givenName")
    print("   姓氏屬性: sn")
    print("   電子郵件屬性: userPrincipalName")

    print("\n6. 進階設定")
    print("   啟用使用者同步: ✓")
    print("   同步使用者屬性: ✓")
    print("   啟用: ✓")

    if not service_dn:
        print("\n" + "="*60)
        print("IT 申請服務帳號範本")
        print("="*60)
        print("主旨: 申請 Gitea LDAP 服務帳號")
        print("\n內容:")
        print("您好，")
        print("\n我們需要為 Gitea 系統設定 LDAP 認證，")
        print("請協助建立一個專用的服務帳號，需求如下：")
        print("\n1. 帳號用途: Gitea LDAP 認證服務")
        print("2. 所需權限: 讀取 AD 使用者資訊")
        print("3. 搜尋範圍: DC=heysong,DC=com,DC=tw")
        print("4. 建議 OU: OU=服務帳號,DC=heysong,DC=com,DC=tw")
        print("5. 建議帳號名稱: gitea-service")
        print("\n請提供:")
        print("- 完整的服務帳號 DN")
        print("- 服務帳號密碼")
        print("\n謝謝！")


def quick_test():
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389, get_info=ldap3.ALL)

    # 測試匿名連線
    print("=== Anonymous Connection ===")
    conn = ldap3.Connection(server)
    if conn.bind():
        print("Anonymous bind: SUCCESS")
        print(f"Naming contexts: {server.info.naming_contexts}")
        print(f"Schema entry: {server.info.schema_entry}")

        # 測試根層級查詢
        result = conn.search('DC=heysong,DC=com,DC=tw', '(objectClass=*)', ldap3.BASE, attributes=['*'])
        print(f"Base search result: {result}")
        if conn.entries:
            print(f"Base entry: {conn.entries[0]}")
    else:
        print("Anonymous bind: FAILED")

    conn.unbind()

    # 測試認證連線
    print("\n=== Authenticated Connection ===")
    username = input("Enter username (or Enter for 16613): ").strip() or "16613"
    password = input("Enter password: ").strip()

    if password:
        auth_methods = [
            f"{username}@heysong.com.tw",
            f"HEYSONG\\{username}"
        ]

        for auth_user in auth_methods:
            print(f"\nTrying: {auth_user}")
            try:
                conn = ldap3.Connection(server, user=auth_user, password=password)
                if conn.bind():
                    print("Authentication: SUCCESS")

                    # 查詢使用者
                    search_filter = f"(sAMAccountName={username})"
                    result = conn.search('DC=heysong,DC=com,DC=tw', search_filter, ldap3.SUBTREE,
                                         attributes=['distinguishedName', 'sAMAccountName', 'userPrincipalName',
                                                     'mail'])

                    print(f"User search result: {result}")
                    if conn.entries:
                        for entry in conn.entries:
                            print(f"Found user: {entry.distinguishedName}")
                            print(f"  sAMAccountName: {entry.sAMAccountName}")
                            print(f"  userPrincipalName: {entry.userPrincipalName}")
                            print(f"  mail: {entry.mail}")

                    # 查詢OU結構
                    result = conn.search('DC=heysong,DC=com,DC=tw', '(objectClass=organizationalUnit)',
                                         ldap3.SUBTREE, attributes=['name', 'distinguishedName'])
                    print(f"OU search found: {len(conn.entries)} entries")
                    for i, entry in enumerate(conn.entries[:3]):
                        print(f"  {i + 1}. {entry.name} - {entry.distinguishedName}")

                    conn.unbind()
                    break
                else:
                    print("Authentication: FAILED")
            except Exception as e:
                print(f"Error: {e}")

    print("\n=== Gitea Configuration ===")
    print("Authentication Type: LDAP (via BindDN)")
    print("Security Protocol: Unencrypted")
    print("Host: HSWDC00.heysong.com.tw")
    print("Port: 389")
    print("Bind DN: [需要IT提供服務帳號]")
    print("User Search Base: DC=heysong,DC=com,DC=tw")
    print("User Filter: (&(objectCategory=Person)(objectClass=User)(sAMAccountName=%s))")
    print("Username Attribute: sAMAccountName")
    print("Email Attribute: userPrincipalName")


if __name__ == "__main__":
    print("GITEA LDAP 設定完整測試工具")
    print("="*50)

    # 1. 基本連接測試
    quick_test()

    # 2. 服務帳號測試
    service_dn = test_service_account()

    # 3. 使用者認證測試
    test_user_authentication(service_dn)

    # 4. 產生 Gitea 設定指南
    generate_gitea_config(service_dn)
