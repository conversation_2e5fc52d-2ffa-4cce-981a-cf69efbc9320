# Android USB 串口調試指南

## 問題診斷步驟

### 1. 在 Android 中添加調試日誌

在 `SimpleTerminalActivityV2.java` 或相關的韌體上傳方法中，添加以下日誌：

```java
// 在 startFirmwareUpload() 方法中
private void startFirmwareUpload() {
    Log.d(TAG, "=== 開始韌體上傳 ===");
    
    // 檢查串口狀態
    if (serialPort == null) {
        Log.e(TAG, "串口未初始化！");
        return;
    }
    
    // 構建進入 Bootloader 命令
    byte[] cmd = CustomBootloaderProtocol.buildBootloaderFirstCommand();
    Log.d(TAG, "準備發送進入 Bootloader 命令: " + bytesToHex(cmd));
    
    try {
        // 發送命令
        serialPort.write(cmd, WRITE_TIMEOUT);
        Log.d(TAG, "✅ 命令已發送，長度: " + cmd.length);
    } catch (Exception e) {
        Log.e(TAG, "❌ 發送失敗: " + e.getMessage());
    }
}
```

### 2. 在 USB 串口寫入方法中添加日誌

在 `UsbSerialPort` 實現類（可能是 `FtdiSerialDriver` 或 `CdcAcmSerialDriver`）中：

```java
@Override
public int write(byte[] src, int timeout) throws IOException {
    Log.d(TAG, "USB write 調用: " + bytesToHex(src));
    
    int result = connection.bulkTransfer(writeEndpoint, src, src.length, timeout);
    
    Log.d(TAG, "USB write 結果: " + result + " bytes");
    
    if (result < 0) {
        throw new IOException("USB write failed: " + result);
    }
    
    return result;
}
```

### 3. 檢查 USB 權限

```java
// 在連接前檢查權限
private void checkUsbPermission() {
    UsbManager usbManager = (UsbManager) getSystemService(Context.USB_SERVICE);
    UsbDevice device = ... // 獲取設備
    
    if (!usbManager.hasPermission(device)) {
        Log.e(TAG, "沒有 USB 權限！");
        // 請求權限
        PendingIntent pi = PendingIntent.getBroadcast(...);
        usbManager.requestPermission(device, pi);
    } else {
        Log.d(TAG, "已有 USB 權限");
    }
}
```

### 4. 驗證串口參數

```java
// 確保串口參數正確
private void configureSerialPort() {
    Log.d(TAG, "配置串口參數...");
    
    serialPort.setParameters(
        115200,                    // 波特率
        UsbSerialPort.DATABITS_8,  // 8 數據位
        UsbSerialPort.STOPBITS_1,  // 1 停止位
        UsbSerialPort.PARITY_NONE  // 無校驗
    );
    
    Log.d(TAG, "串口配置: 115200,8,N,1");
}
```

## 測試方案

### 方案 1：簡單回環測試

1. 在 Python 模擬器中添加立即回應：
```python
def process_received_data(self, data):
    # 立即回傳接收到的數據
    self.serial_port.write(data)
    self.log_message(f"📤 回環: {' '.join([f'{b:02X}' for b in data])}")
```

2. 在 Android 中發送測試數據：
```java
// 發送簡單測試
byte[] testData = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05};
serialPort.write(testData, 1000);
Log.d(TAG, "發送測試數據: " + bytesToHex(testData));
```

### 方案 2：使用 ADB 查看實時日誌

```bash
# 過濾相關日誌
adb logcat -s YourTAG:D UsbSerial:D
```

### 方案 3：檢查 Android Manifest

確保有正確的 USB 權限：
```xml
<uses-feature android:name="android.hardware.usb.host" />
<uses-permission android:name="android.permission.USB_PERMISSION" />
```

## 常見問題

### 1. 數據發送但 Python 沒收到
- 檢查 USB 線是否支持數據傳輸（不是只充電線）
- 確認串口參數匹配（波特率、數據位等）
- 檢查 USB OTG 是否正常工作

### 2. 權限被拒絕
- 在設置中手動授予 USB 權限
- 確保 intent-filter 正確配置

### 3. 串口打開失敗
- 其他應用可能佔用了串口
- USB 設備可能需要特定驅動

## 完整測試流程

1. **啟動 Python 模擬器**
   ```bash
   python stm32_simulator_cli_v2.py
   ```

2. **在 Android 中添加測試按鈕**
   ```java
   // 添加一個測試按鈕
   binding.btnTest.setOnClickListener(v -> {
       byte[] testCmd = new byte[]{0x01, 0x03, 0xA0, 0x55, 0xF7};
       try {
           serialPort.write(testCmd, 1000);
           showToast("測試命令已發送");
       } catch (Exception e) {
           showToast("發送失敗: " + e.getMessage());
       }
   });
   ```

3. **查看兩邊的日誌**
   - Python 端應該顯示接收到的數據
   - Android 端應該顯示發送成功

這樣可以快速定位問題是在 Android 發送端還是 Python 接收端。