# utils.py
"""工具函數模組"""
import time
from datetime import datetime, timedelta
from typing import List, Tuple
import ddddocr
import cv2
import numpy as np


def recognize_captcha(image_path: str) -> str:
    """
    驗證碼識別

    Args:
        image_path: 驗證碼圖片路徑

    Returns:
        str: 識別出的驗證碼文字
    """
    ocr = ddddocr.DdddOcr(show_ad=False)
    with open(image_path, 'rb') as f:
        image_bytes = f.read()
    result = ocr.classification(image_bytes)
    print(f"識別出的驗證碼: {result}")
    return result


def get_date_periods(target_date: datetime) -> List[Tuple[str, str]]:
    """
    根據指定日期範圍（當月1號 ~ target_date）切割成適當的區間。

    Args:
        target_date: 目標日期 (例如 2025-01-16)

    Returns:
        List[Tuple[str, str]]: 日期區間列表，每個元素為 (開始日期, 結束日期)
    """
    year = target_date.year
    month = target_date.month

    # 取得該月第一天
    first_day = datetime(year, month, 1)
    last_day = target_date  # 只計算到指定的 target_date，而不是整個月份

    # 計算總天數
    days_in_period = (last_day - first_day).days + 1

    # 🔹 設定切割邏輯
    if days_in_period > 27:  # 當 `target_date` 是整個月份，切 4 段
        num_splits = 4
    elif days_in_period > 14:  # 超過 14 天但不足 1 個月，切 2 段
        num_splits = 2
    else:  # 小於 14 天，直接 1 段
        num_splits = 1

    interval = days_in_period // num_splits  # 平均分割
    periods = []
    current = first_day

    for i in range(num_splits):
        # 計算結束日期
        if i == num_splits - 1:  # 最後一段
            end = last_day
        else:
            end = current + timedelta(days=interval - 1)

        if current > end:
            break

        start_str = current.strftime('%Y年%m月%d日')
        end_str = end.strftime('%Y年%m月%d日')

        periods.append((start_str, end_str))

        if end >= last_day:
            break

        current = end + timedelta(days=1)

    return periods


def format_filename(start_date: str, end_date: str, tax_id: str = None) -> str:
    """
    格式化檔案名稱，加入營業人統編

    Args:
        start_date: 開始日期 (YYYY年MM月DD日)
        end_date: 結束日期 (YYYY年MM月DD日)
        tax_id: 營業人統編 (選填)

    Returns:
        str: 格式化後的檔案名稱
    """
    start_clean = start_date.replace('年', '').replace('月', '').replace('日', '')
    end_clean = end_date.replace('年', '').replace('月', '').replace('日', '')

    if tax_id:
        return f"report_{tax_id}_{start_clean}_{end_clean}.xlsx"
    else:
        return f"report_{start_clean}_{end_clean}.xlsx"
