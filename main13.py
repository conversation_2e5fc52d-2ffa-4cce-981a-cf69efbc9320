import datetime
import json
import pandas as pd
import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import <PERSON><PERSON>, View
import cx_Oracle

# 初始化 Bot
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix="!", intents=intents)

# Oracle 資料庫連接設定
dsn = "system/vmmanager@172.20.160.132:1521/VMDB"
connection = cx_Oracle.connect(dsn)

# 指定頻道 ID
# TARGET_CHANNEL_ID = 1306132076684378215
TARGET_CHANNEL_ID = 1306803218356899900

@bot.event
async def on_ready():
    print(f"Bot 已上線，名稱：{bot.user}")
    try:
        synced = await bot.tree.sync()
        print(f"同步了 {len(synced)} 個指令。")
    except Exception as e:
        print(f"同步失敗：{e}")

# 檢查機台號碼是否存在
def check_machine_exists(machine_id):
    cursor = connection.cursor()
    query = "SELECT COUNT(1) FROM VSAG WHERE AGMTNO = :machine_id"
    cursor.execute(query, {'machine_id': machine_id})
    exists = cursor.fetchone()[0] > 0
    cursor.close()
    return exists

# 檢查日期格式是否為西元年 YYYYMMDD
def check_date_format(date):
    try:
        datetime.datetime.strptime(date, "%Y%m%d")
        return True
    except ValueError:
        return False

# 建立 /query_vm_sales 指令
@bot.tree.command(name="query_vm_sales", description="查詢指定銷售類別的資料")
@app_commands.describe(
    machine_id="輸入機台號碼 (例如：V060025)",
    date="查詢日期 (例如：20241113)"
)
async def query_sales(interaction: discord.Interaction, machine_id: str, date: str):
    # 檢查機台號碼格式與存在性
    if not machine_id.startswith("V") or len(machine_id) != 7:
        await interaction.response.send_message(
            "請輸入正確的機台號碼格式（格式：V+6碼，例如：V060025）", ephemeral=True
        )
        return

    if not check_machine_exists(machine_id):
        await interaction.response.send_message("查無此機台號碼，請確認機台是否存在。", ephemeral=True)
        return

    # 檢查日期格式
    if not check_date_format(date):
        await interaction.response.send_message("請輸入有效的日期格式（YYYYMMDD）。", ephemeral=True)
        return

    await interaction.response.defer(thinking=True)

    button = Button(label="查詢資料", style=discord.ButtonStyle.primary)

    async def button_callback(interact):
        await interact.response.defer(thinking=True)

        try:
            cursor = connection.cursor()
            query = """
                SELECT BHMTNO, BHIPNO, BHDATE, BHTIME, BHSLNO, BHSTAU, BHLINE, BHPNID, BHOICA, BHCONT, BHUPDT, BHPRIC, 
                       BHSQTY, BHSAMT, BHCHAN, BHTOAL, BHERMG, BHCRDT
                FROM VSBH 
                WHERE BHMTNO = :qmtno
                AND BHDATE = :qdate
            """

            cond = {'qmtno': machine_id, 'qdate': date}
            cursor.execute(query, cond)
            result = cursor.fetchone()

            if result:
                columns = ["機台號碼", "IP號碼", "日期", "時間", "銷售編號", "狀態", "線路", "PNID", "OICA", "合同",
                           "更新日期", "價格", "數量", "金額", "通道", "總額", "餘額", "信用"]

                # 將查詢結果轉為字典並處理 datetime 類型
                result_dict = {col: (val.strftime("%Y-%m-%d %H:%M:%S") if isinstance(val, datetime.datetime) else val)
                               for col, val in zip(columns, result)}

                # 將字典轉為 JSON 格式並縮排
                json_data = json.dumps(result_dict, indent=4, ensure_ascii=False)

                channel = interaction.guild.get_channel(TARGET_CHANNEL_ID)
                if channel:
                    await channel.send(f"查詢結果：\n```json\n{json_data}\n```")
            else:
                await interact.followup.send("查無資料。", ephemeral=True)

        except cx_Oracle.DatabaseError as e:
            error_msg = f"資料庫查詢失敗：{str(e)}"
            print(f"資料庫錯誤：{error_msg}")
            await interact.followup.send(error_msg, ephemeral=True)
        finally:
            if cursor:
                cursor.close()

    # 設定按鈕的回應
    button.callback = button_callback
    view = View()
    view.add_item(button)

    # 最終回應提示
    await interaction.followup.send("查詢條件正確，請點擊按鈕查看結果，結果將發送至指定頻道。", view=view)

# 錯誤處理
@bot.event
async def on_command_error(ctx, error):
    await ctx.send(f"執行指令時發生錯誤：{str(error)}")

# 啟動 Bot
# bot.run("MTMwNTcyMzUzODk5NjcyMzc5Mg.GnVjVx.76dw-63gzc2wEw3S7LILCmgvBzjS6uEfErSaMw")
bot.run("MTMwNjgwMjUxNjQzNTkyNzEzMA.GdqSZW.17hJA0Z6tv7aMkdOncQZxXISq-41AeU2YeO5Tc")
