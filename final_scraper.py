"""
最終版家樂福爬蟲 - 基於調試結果優化
"""

import asyncio
import json
import csv
import re
from datetime import datetime
from playwright.async_api import async_playwright

class FinalCarrefourScraper:
    def __init__(self):
        self.base_url = "https://online.carrefour.com.tw"
        self.target_url = "https://online.carrefour.com.tw/zh/%E5%93%81%E7%89%8C%E6%97%97%E8%89%A6%E9%A4%A8/%E9%BB%91%E6%9D%BE%E5%93%81%E7%89%8C%E9%A4%A8"
        self.products = []
        
    async def scrape_with_display(self, max_pages=2):
        """顯示瀏覽器的爬取過程"""
        print("🚀 === 最終版家樂福爬蟲 ===")
        print("📱 即將打開瀏覽器視窗，展示完整爬取過程")
        print("🎯 使用優化後的選擇器和提取邏輯")
        print("-" * 60)
        
        async with async_playwright() as p:
            print("🌐 正在啟動 Chrome 瀏覽器...")
            browser = await p.chromium.launch(
                headless=False,  # 顯示瀏覽器
                slow_mo=800,     # 每個操作間隔 0.8 秒
                args=[
                    '--start-maximized',
                    '--disable-blink-features=AutomationControlled'
                ]
            )
            
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            )
            page = await context.new_page()
            
            try:
                print(f"🔗 正在訪問目標網站...")
                await page.goto(self.target_url, wait_until="networkidle", timeout=60000)
                print("✅ 網站載入完成")
                
                # 等待頁面完全載入
                await page.wait_for_timeout(8000)
                
                # 處理彈窗
                await self.handle_popups(page)
                
                # 滾動載入所有內容
                await self.scroll_and_load(page)
                
                # 使用 JavaScript 直接提取產品資訊
                print("🔍 使用 JavaScript 提取產品資訊...")
                products = await self.extract_products_with_js(page)
                
                if products:
                    self.products = products
                    print(f"✅ 成功提取 {len(products)} 個產品")
                else:
                    print("⚠️  未找到產品，嘗試備用方法...")
                    # 備用方法：使用傳統選擇器
                    products = await self.extract_products_fallback(page)
                    self.products = products
                
                print(f"\n🎉 爬取完成！總共找到 {len(self.products)} 個產品")
                print("   瀏覽器將在 8 秒後關閉...")
                await page.wait_for_timeout(8000)
                
            except Exception as e:
                print(f"❌ 爬取過程中發生錯誤: {e}")
                print("   瀏覽器將在 10 秒後關閉...")
                await page.wait_for_timeout(10000)
            finally:
                await browser.close()
                
        return self.products
    
    async def handle_popups(self, page):
        """處理彈窗"""
        try:
            print("🔍 檢查並處理彈窗...")
            
            # 等待並關閉可能的彈窗
            popup_selectors = [
                'button:has-text("同意")',
                'button:has-text("接受")',
                'button:has-text("確定")',
                'button:has-text("關閉")',
                '.close-button',
                '[data-testid="accept-all"]'
            ]
            
            for selector in popup_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=2000)
                    if element:
                        await element.click()
                        print(f"   ✅ 已處理彈窗")
                        break
                except:
                    continue
                    
            await page.wait_for_timeout(2000)
            
        except Exception as e:
            print(f"   ⚠️  處理彈窗時發生錯誤: {e}")
    
    async def scroll_and_load(self, page):
        """滾動頁面載入所有內容"""
        try:
            print("📜 滾動頁面載入所有產品...")
            
            # 多次滾動確保載入所有內容
            for i in range(3):
                print(f"   📜 滾動第 {i+1} 次...")
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await page.wait_for_timeout(3000)
            
            # 滾動回頂部
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(2000)
            print("   ✅ 頁面滾動完成")
            
        except Exception as e:
            print(f"   ⚠️  滾動頁面時發生錯誤: {e}")
    
    async def extract_products_with_js(self, page):
        """使用 JavaScript 提取產品資訊"""
        try:
            products = await page.evaluate("""
                () => {
                    const products = [];
                    
                    // 尋找所有可能的產品容器
                    const selectors = [
                        'div[class*="product"]',
                        'div[class*="item"]',
                        'a[href*="product"]',
                        '.product-tile',
                        '.product-item',
                        '.product-card'
                    ];
                    
                    let allElements = [];
                    selectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            allElements = allElements.concat(Array.from(elements));
                        } catch (e) {
                            console.log('Selector error:', selector, e);
                        }
                    });
                    
                    // 去重
                    const uniqueElements = [...new Set(allElements)];
                    
                    uniqueElements.forEach((element, index) => {
                        try {
                            const text = element.innerText || '';
                            const html = element.innerHTML || '';
                            
                            // 檢查是否包含價格資訊
                            const priceMatch = text.match(/\\$\\s*([\\d,]+(?:\\.\\d{1,2})?)/);
                            if (!priceMatch) return;
                            
                            // 提取產品名稱
                            let name = '';
                            const nameSelectors = ['h1', 'h2', 'h3', 'h4', 'h5', '.product-name', '.title'];
                            for (let sel of nameSelectors) {
                                const nameEl = element.querySelector(sel);
                                if (nameEl && nameEl.innerText.trim()) {
                                    name = nameEl.innerText.trim();
                                    break;
                                }
                            }
                            
                            // 如果沒找到標題，使用第一行文字
                            if (!name) {
                                const lines = text.split('\\n').filter(line => line.trim());
                                name = lines[0] || '';
                            }
                            
                            // 提取價格
                            const price = parseFloat(priceMatch[1].replace(/,/g, ''));
                            
                            // 提取圖片
                            let imageUrl = '';
                            const img = element.querySelector('img');
                            if (img) {
                                imageUrl = img.src || img.getAttribute('data-src') || '';
                            }
                            
                            // 提取連結
                            let productUrl = '';
                            const link = element.querySelector('a') || (element.tagName === 'A' ? element : null);
                            if (link) {
                                productUrl = link.href || '';
                            }
                            
                            // 只保留有效的產品
                            if (name && price > 0) {
                                products.push({
                                    name: name.substring(0, 200),
                                    price: price,
                                    price_text: priceMatch[0],
                                    image_url: imageUrl,
                                    product_url: productUrl,
                                    scraped_at: new Date().toISOString()
                                });
                            }
                            
                        } catch (e) {
                            console.log('Element processing error:', e);
                        }
                    });
                    
                    return products;
                }
            """)
            
            return products
            
        except Exception as e:
            print(f"   ❌ JavaScript 提取失敗: {e}")
            return []
    
    async def extract_products_fallback(self, page):
        """備用提取方法"""
        try:
            print("🔄 使用備用方法提取產品...")
            
            # 尋找包含價格的元素
            price_elements = await page.query_selector_all('*')
            products = []
            
            for element in price_elements[:100]:  # 限制檢查數量
                try:
                    text = await element.inner_text()
                    if not text or '$' not in text:
                        continue
                    
                    # 檢查是否是產品元素（不是整個頁面）
                    if len(text) > 1000:  # 跳過太大的容器
                        continue
                    
                    # 提取價格
                    price_match = re.search(r'\$\s*([\d,]+(?:\.\d{1,2})?)', text)
                    if not price_match:
                        continue
                    
                    price = float(price_match.group(1).replace(',', ''))
                    
                    # 提取產品名稱（取第一行非空文字）
                    lines = [line.strip() for line in text.split('\n') if line.strip()]
                    name = lines[0] if lines else ''
                    
                    # 提取圖片
                    img_element = await element.query_selector('img')
                    image_url = ''
                    if img_element:
                        image_url = await img_element.get_attribute('src') or ''
                    
                    # 提取連結
                    link_element = await element.query_selector('a')
                    product_url = ''
                    if link_element:
                        product_url = await link_element.get_attribute('href') or ''
                    
                    if name and price > 0:
                        products.append({
                            'name': name[:200],
                            'price': price,
                            'price_text': price_match.group(0),
                            'image_url': image_url,
                            'product_url': product_url,
                            'scraped_at': datetime.now().isoformat()
                        })
                        
                        if len(products) >= 20:  # 限制數量
                            break
                
                except Exception as e:
                    continue
            
            return products
            
        except Exception as e:
            print(f"   ❌ 備用方法失敗: {e}")
            return []
    
    def save_results(self):
        """儲存結果"""
        if not self.products:
            print("❌ 沒有產品資料可儲存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"final_carrefour_products_{timestamp}.csv"
        json_filename = f"final_carrefour_products_{timestamp}.json"
        
        # 儲存 CSV
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['name', 'price', 'price_text', 'image_url', 'product_url', 'scraped_at']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for product in self.products:
                writer.writerow(product)
        
        # 儲存 JSON
        with open(json_filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.products, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"💾 已儲存 {len(self.products)} 個產品到:")
        print(f"   📄 CSV: {csv_filename}")
        print(f"   📄 JSON: {json_filename}")
    
    def print_summary(self):
        """列印結果摘要"""
        if not self.products:
            print("❌ 沒有找到任何產品")
            return
        
        print(f"\n🎉 === 爬取結果摘要 ===")
        print(f"📊 總共找到 {len(self.products)} 個產品")
        
        if self.products:
            prices = [p['price'] for p in self.products if p['price'] > 0]
            if prices:
                print(f"💰 價格範圍: $ {min(prices):.0f} - $ {max(prices):.0f}")
                print(f"💰 平均價格: $ {sum(prices)/len(prices):.0f}")
        
        print(f"\n📦 產品列表:")
        for i, product in enumerate(self.products[:10], 1):
            print(f"   {i}. {product['name'][:60]}{'...' if len(product['name']) > 60 else ''}")
            print(f"      💰 {product['price_text']}")
            if product['product_url']:
                print(f"      🔗 {product['product_url'][:50]}...")
            print()

async def main():
    """主函數"""
    print("🎬 === 最終版家樂福爬蟲展示 ===")
    print("📱 此版本使用優化後的提取邏輯")
    print("🎯 目標: 家樂福黑松品牌館")
    print("=" * 60)
    
    scraper = FinalCarrefourScraper()
    
    # 開始爬取
    products = await scraper.scrape_with_display(max_pages=1)
    
    # 顯示結果摘要
    scraper.print_summary()
    
    # 儲存結果
    if products:
        scraper.save_results()
    
    print("\n🎉 展示完成！")

if __name__ == "__main__":
    asyncio.run(main())
