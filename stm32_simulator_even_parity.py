#!/usr/bin/env python3
"""
STM32 Bootloader 模擬器 - Even Parity 版本
完全匹配 C# 的預設設置

重要發現：
- C# Initial_rs232() 默認使用 Even Parity
- Android 現在也改為默認使用 Even Parity
- 這是與 STM32 bootloader 通訊的關鍵！
"""

import serial
import time
import threading
from datetime import datetime
import sys

# 配置 - 完全匹配 C# Initial_rs232() 設定
PORT = 'COM8'  # 根據實際情況修改
BAUDRATE = 115200
# 重要！C# Initial_rs232() 設定：115200, Even, 8, 1
PARITY = serial.PARITY_EVEN
DATA_BITS = 8
STOP_BITS = serial.STOPBITS_ONE
TIMEOUT = 0.1

# DTR/RTS 設定匹配 C# 默認值
DTR_ENABLE = False  # C# DtrEnable 默認 false
RTS_ENABLE = False  # C# RtsEnable 默認 false

# 協議常數
STX = 0x01
CMD_BOOTLOADER = 0xB2
ACK = 0x79
NACK = 0x1F
SYNC = 0x7F

# 狀態
in_bootloader = False
write_count = 0
bytes_written = 0


def log(message):
    """輸出帶時間戳的日誌"""
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")


def calculate_checksum(data):
    """計算 XOR 校驗和"""
    checksum = 0
    for byte in data:
        checksum ^= byte
    return checksum


def handle_custom_protocol(ser, packet):
    """處理自定義協議（0xB2命令）"""
    global in_bootloader
    
    # 檢查封包格式
    if len(packet) < 4:
        log(f"❌ 封包太短: {len(packet)} bytes")
        return
    
    stx = packet[0]
    length = packet[1]
    cmd = packet[2]
    
    if stx != STX:
        log(f"❌ 錯誤的 STX: 0x{stx:02X} (預期 0x01)")
        return
    
    if cmd != CMD_BOOTLOADER:
        log(f"❌ 未知命令: 0x{cmd:02X}")
        return
    
    # 驗證長度
    expected_len = length + 2  # STX + LEN + 內容
    if len(packet) != expected_len:
        log(f"❌ 封包長度不符: 收到 {len(packet)}, 預期 {expected_len}")
        return
    
    # 取出數據和校驗和
    data = packet[3:-1]
    received_checksum = packet[-1]
    
    # 計算校驗和
    calc_checksum = length ^ cmd
    for byte in data:
        calc_checksum ^= byte
    
    if received_checksum != calc_checksum:
        log(f"❌ 校驗和錯誤: 收到 0x{received_checksum:02X}, 計算 0x{calc_checksum:02X}")
        return
    
    # 成功接收 0xB2 命令
    log("✅ 收到有效的 0xB2 命令，進入 Bootloader 模式")
    in_bootloader = True
    
    # 不立即回應，模擬 MCU 重啟
    log("⏱️  模擬 MCU 重啟中...")


def handle_stm32_bootloader(ser, byte):
    """處理 STM32 bootloader 協議"""
    global in_bootloader, write_count, bytes_written
    
    if byte == SYNC:
        log("📡 收到同步字節 0x7F")
        if in_bootloader:
            # 發送 ACK - 檢查 Parity 處理
            ack_byte = bytes([ACK])
            log(f"🔍 準備發送 ACK: 原始位元組=0x{ACK:02X}, 二進制={ACK:08b}")
            ser.write(ack_byte)
            log("✅ 發送 ACK (0x79) - Bootloader 已就緒")
            
            # 額外測試：發送一些測試位元組以驗證 Parity
            test_bytes = [0x79, 0x1F, 0x00, 0xFF]
            for tb in test_bytes:
                ser.write(bytes([tb]))
                log(f"🧪 測試位元組: 0x{tb:02X} ({tb:08b})")
        else:
            log("❌ 未在 Bootloader 模式，忽略同步請求")
    
    elif byte == 0x00:  # GET 命令
        if in_bootloader:
            log("📡 收到 GET 命令 (0x00)")
            ser.write(bytes([ACK]))  # 先發送 ACK
            # 發送 bootloader 信息
            ser.write(bytes([0x0B]))  # 版本
            ser.write(bytes([0x31, 0x00, 0x01, 0x02, 0x11, 0x21, 0x31, 0x43, 0x63, 0x73, 0x82, 0x92]))
            ser.write(bytes([ACK]))  # 結束 ACK
            log("✅ 發送 bootloader 信息")
    
    elif byte == 0x31:  # Write Memory 命令
        if in_bootloader:
            log("📡 收到 Write Memory 命令 (0x31)")
            write_count += 1
            # 這裡簡化處理，實際需要完整的寫入流程
            ser.write(bytes([ACK]))
            log(f"✅ Write #{write_count}")


def serial_reader(ser):
    """串口讀取線程"""
    buffer = bytearray()
    
    while True:
        try:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting)
                
                # 記錄原始數據（十六進制）
                hex_str = ' '.join(f'{b:02X}' for b in data)
                log(f"📥 收到 {len(data)} bytes: {hex_str}")
                
                # 處理數據
                for byte in data:
                    if not in_bootloader and byte == STX:
                        # 開始接收自定義協議封包
                        buffer = bytearray([byte])
                    elif not in_bootloader and len(buffer) > 0:
                        # 繼續接收封包
                        buffer.append(byte)
                        
                        # 檢查是否接收完整
                        if len(buffer) >= 2:
                            expected_len = buffer[1] + 2
                            if len(buffer) >= expected_len:
                                # 封包完整，處理它
                                handle_custom_protocol(ser, buffer)
                                buffer = bytearray()
                    else:
                        # Bootloader 模式或其他命令
                        handle_stm32_bootloader(ser, byte)
            
            time.sleep(0.01)
            
        except Exception as e:
            log(f"❌ 讀取錯誤: {e}")
            break


def main():
    """主程序"""
    log("=" * 60)
    log("STM32 Bootloader 模擬器 - Even Parity 版本")
    log("=" * 60)
    log(f"配置:")
    log(f"  端口: {PORT}")
    log(f"  波特率: {BAUDRATE}")
    log(f"  校驗位: EVEN (與 C# 相同)")
    log(f"  數據位: {DATA_BITS}")
    log(f"  停止位: {STOP_BITS}")
    log("=" * 60)
    
    try:
        # 打開串口 - 完全匹配 C# 設定
        ser = serial.Serial(
            port=PORT,
            baudrate=BAUDRATE,
            parity=PARITY,
            bytesize=DATA_BITS,
            stopbits=STOP_BITS,
            timeout=TIMEOUT,
            # 重要！匹配 C# DTR/RTS 默認設定
            dsrdtr=DTR_ENABLE,
            rtscts=RTS_ENABLE
        )
        
        log(f"✅ 串口 {PORT} 已打開")
        log("等待命令...")
        log("提示: 0xB2 命令進入 Bootloader, 然後發送 0x7F 同步")
        
        # 啟動讀取線程
        reader_thread = threading.Thread(target=serial_reader, args=(ser,))
        reader_thread.daemon = True
        reader_thread.start()
        
        # 主循環 - 處理用戶輸入
        try:
            while True:
                time.sleep(0.1)
                # 定期顯示狀態
                if in_bootloader and int(time.time()) % 5 == 0:
                    if write_count > 0:
                        log(f"📊 Bootloader 模式 - 寫入次數: {write_count}")
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            log("\n👋 收到退出信號")
            
    except serial.SerialException as e:
        log(f"❌ 串口錯誤: {e}")
        log("請檢查:")
        log("1. 端口號是否正確")
        log("2. 端口是否被其他程序佔用")
        log("3. 是否有權限訪問串口")
    
    finally:
        if 'ser' in locals() and ser.is_open:
            ser.close()
            log("串口已關閉")
        
        # 顯示統計
        log("\n📊 統計信息:")
        log(f"  寫入次數: {write_count}")
        log(f"  寫入字節: {bytes_written}")


if __name__ == "__main__":
    # 支持命令行參數
    if len(sys.argv) > 1:
        PORT = sys.argv[1]
    
    main()