from playwright.sync_api import Playwright, sync_playwright, expect, TimeoutError
import time
import cv2
import numpy as np
import ddddocr
import sys
import json
from datetime import datetime, timedelta

# 定義密碼清單
PASSWORDS = [
    "Aa123456789",  # 當前密碼
    # "Aa123456789",  # 備用密碼1
    # 可以繼續添加更多備用密碼
]


def select_tax_id(page):
    """處理統編選擇"""
    try:
        print("\n開始處理統編選擇...")

        # 等待並點擊選擇統編按鈕
        print("點擊選擇統編按鈕...")
        select_tax_id_button = page.locator('button:has-text("選擇統編")')
        select_tax_id_button.wait_for(state="visible", timeout=5000)
        select_tax_id_button.click()
        time.sleep(3)  # 增加等待時間，確保對話框完全載入

        # 等待對話框出現並檢查
        dialog_visible = page.evaluate("""() => {
            const dialog = document.querySelector('.modal-dialog');
            return dialog && window.getComputedStyle(dialog).display !== 'none';
        }""")

        if not dialog_visible:
            print("對話框未正確顯示")
            return False

        # 確保統編清單已載入
        print("等待統編清單載入...")
        time.sleep(3)  # 增加等待時間，確保列表完全載入

        # 先清除所有已選擇的項目
        page.evaluate("""() => {
            const checkboxes = document.querySelectorAll('.form-check-input');
            checkboxes.forEach(cb => {
                if(cb.checked) {
                    cb.checked = false;
                    const event = new Event('change', { bubbles: true });
                    cb.dispatchEvent(event);
                }
            });
        }""")
        time.sleep(1)

        # 輪流點選每個統編
        checkboxes_selected = page.evaluate("""() => {
            const checkboxes = Array.from(document.querySelectorAll('.form-check-input'));
            let selectedCount = 0;

            for(let checkbox of checkboxes) {
                if(checkbox.closest('tr') && !checkbox.checked) {
                    setTimeout(() => {
                        checkbox.checked = true;
                        const event = new Event('change', { bubbles: true });
                        checkbox.dispatchEvent(event);
                    }, selectedCount * 200);  // 每個選擇間隔200ms
                    selectedCount++;
                }
            }
            return selectedCount;
        }""")

        print(f"已找到 {checkboxes_selected} 個統編選項")
        time.sleep(3)  # 等待所有選擇完成

        # 確認選擇
        print("點擊確認按鈕...")
        confirm_button = page.locator('button.btn.btn-primary:has-text("確認")')
        if confirm_button.is_visible():
            confirm_button.click()
            time.sleep(2)

            # 驗證統編是否已選擇
            input_value = page.evaluate("""() => {
                const input = document.querySelector('#input02');
                return input ? input.value : '';
            }""")

            if input_value:
                print(f"統編已選擇成功: {input_value}")
                return True
            else:
                print("統編選擇可能未生效")
                return False
        else:
            print("找不到確認按鈕")
            return False

    except Exception as e:
        print(f"選擇統編時發生錯誤: {str(e)}")
        page.screenshot(path=f"tax_id_error_{time.strftime('%Y%m%d_%H%M%S')}.png")
        return False


def wait_and_download(page, start_date: str, end_date: str) -> bool:
    """等待並下載特定日期範圍的報表"""
    try:
        print("\n等待報表生成（30秒）...")
        time.sleep(30)  # 等待報表生成

        print("導航至下載頁面...")
        page.goto("https://www.einvoice.nat.gov.tw/dashboard/btb/btb411w/offline")

        # 等待頁面載入完成
        page.wait_for_load_state("networkidle")
        time.sleep(3)

        # 點擊查詢按鈕
        print("點擊查詢按鈕...")
        search_button = page.locator('button[title="查詢"]')
        if search_button.is_visible():
            search_button.click()
            time.sleep(3)
        else:
            print("找不到查詢按鈕")
            return False

        # 等待查詢結果
        print("等待查詢結果...")
        try:
            page.wait_for_selector("table", timeout=20000)
            time.sleep(2)
        except Exception as e:
            print(f"等待表格超時，繼續執行: {str(e)}")
            return False

        # 檢查並下載報表
        print(f"\n查找可下載的報表...")

        download_success = page.evaluate("""() => {
            const rows = Array.from(document.querySelectorAll('tbody tr'));
            console.log(`找到 ${rows.length} 筆記錄`);

            // 找出第一個狀態為「處理完成」的記錄
            for (const row of rows) {
                const statusCell = row.querySelector('td:nth-child(3)');
                const downloadBtn = row.querySelector('button.btn-primary');
                const conditionsCell = row.querySelector('td:nth-child(2)');

                const status = statusCell?.textContent?.trim();
                const conditions = conditionsCell?.textContent?.trim();

                console.log('檢查記錄:', {
                    conditions,
                    status,
                    hasDownloadBtn: !!downloadBtn
                });

                if (status === '處理完成' && downloadBtn) {
                    console.log('找到可下載的記錄，準備下載');
                    downloadBtn.click();
                    return true;
                }
            }

            console.log('未找到可下載的記錄');
            return false;
        }""")

        print(f"\n下載檢查結果: {'成功' if download_success else '失敗'}")

        if download_success:
            print("已點擊下載按鈕")
            time.sleep(5)  # 等待下載開始
            return True
        else:
            print("未找到最新且可下載的報表")
            page.screenshot(path=f"no_download_{time.strftime('%Y%m%d_%H%M%S')}.png")
            return False

    except Exception as e:
        print(f"下載過程中發生錯誤: {str(e)}")
        try:
            page.screenshot(path=f"download_error_{time.strftime('%Y%m%d_%H%M%S')}.png")
        except Exception as screenshot_error:
            print(f"保存錯誤截圖時發生錯誤: {str(screenshot_error)}")
        return False

def get_date_periods(year: int, month: int) -> list[tuple[str, str]]:
    """
    將一個月份分成4個區間，且結束日期不超過當日
    返回的日期格式為 'YYYY年MM月DD日'
    """
    from datetime import datetime, timedelta

    # 取得該月第一天
    first_day = datetime(year, month, 1)

    # 取得當前日期
    current_date = datetime.now()

    # 計算該月最後一天
    if month == 12:
        next_month = datetime(year + 1, 1, 1)
    else:
        next_month = datetime(year, month + 1, 1)
    last_day = min(next_month - timedelta(days=1), current_date)

    # 如果目標月份在未來，返回空列表
    if first_day > current_date:
        return []

    # 如果目標月份的第一天在當前日期之後，返回空列表
    if first_day.year > current_date.year or \
            (first_day.year == current_date.year and first_day.month > current_date.month):
        return []

    # 計算最後一天
    month_end = next_month - timedelta(days=1)
    days_in_month = month_end.day

    # 計算每個區間的天數（向上取整）
    interval = (days_in_month + 3) // 4  # 確保覆蓋所有天數

    # 生成區間
    periods = []
    current = first_day

    for i in range(4):
        # 計算這個區間的結束日期
        if i == 3:  # 最後一個區間
            end = month_end
        else:
            end = min(
                first_day + timedelta(days=interval * (i + 1) - 1),
                month_end
            )

        # 如果結束日期超過當前日期，調整為當前日期
        end = min(end, current_date)

        # 如果開始日期已經超過結束日期或當前日期，退出循環
        if current > end or current > current_date:
            break

        # 格式化日期
        start_str = current.strftime('%Y年%m月%d日')
        end_str = end.strftime('%Y年%m月%d日')

        periods.append((start_str, end_str))

        # 如果已達到當月最後一天或當前日期，退出循環
        if end >= month_end or end >= current_date:
            break

        # 設置下一區間的開始日期
        current = end + timedelta(days=1)

    return periods


def set_date_type_and_range(page, start_date: str, end_date: str):
    """
    設置日期類型和範圍

    參數:
    page: Playwright page 物件
    start_date: 開始日期，格式為 'YYYY年MM月DD日'
    end_date: 結束日期，格式為 'YYYY年MM月DD日'
    """
    try:
        print(f"\n設置日期範圍: {start_date} ~ {end_date}")

        # 設置為依日期
        print("設置查詢類型為依日期...")
        page.locator("#input01").select_option("D")
        time.sleep(1)

        # 點擊日期輸入框
        print("點擊日期輸入框...")
        date_input = page.get_by_label("依日期")
        date_input.click()
        time.sleep(1)

        def parse_tw_date(date_str):
            year = int(date_str.split('年')[0])
            month = int(date_str.split('年')[1].split('月')[0])
            day = int(date_str.split('月')[1].split('日')[0])
            return datetime(year, month, day)

        start = parse_tw_date(start_date)
        end = parse_tw_date(end_date)

        # 構建定位器並點擊日期
        print(f"選擇起始日期: {start_date}")
        start_date_str = start.strftime("%a %b %d %Y 00:00:00 GMT+0800 (台北標準時間)")
        start_locator = '[data-test="' + start_date_str.replace(":", r":") + '"]'
        page.locator(start_locator).get_by_text(str(start.day)).click()
        time.sleep(1)

        print(f"選擇結束日期: {end_date}")
        end_date_str = end.strftime("%a %b %d %Y 00:00:00 GMT+0800 (台北標準時間)")
        end_locator = '[data-test="' + end_date_str.replace(":", r":") + '"]'
        page.locator(end_locator).get_by_text(str(end.day)).click()
        time.sleep(1)

        print("日期範圍設置完成")
        return True

    except Exception as e:
        print(f"設置日期範圍時發生錯誤: {str(e)}")
        current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        page.screenshot(path=f"date_error_{current_time}.png")
        return False

def get_report_data(page, start_date: str, end_date: str) -> bool:
    """執行報表查詢"""
    try:
        print(f"\n開始執行報表查詢 ({start_date} ~ {end_date})...")
        page.wait_for_load_state("networkidle")
        time.sleep(2)

        # 先處理統編選擇
        if not select_tax_id(page):
            print("統編選擇失敗")
            return False

        # 設置日期類型和範圍
        if not set_date_type_and_range(page, start_date, end_date):
            print("日期設置失敗")
            return False

        # 設置銷項選擇
        print("\n設置銷項選擇...")
        page.evaluate("""() => {
            const salesRadio = document.querySelector('#queryInvType_2');
            if (salesRadio) {
                salesRadio.click();
                const event = new Event('change', { bubbles: true });
                salesRadio.dispatchEvent(event);
            }
        }""")
        time.sleep(1)

        # 設置為依日期
        print("設置依日期...")
        page.evaluate("""() => {
            const periodSelect = document.querySelector('#input01');
            if (periodSelect) {
                periodSelect.value = 'D';
                const event = new Event('change', { bubbles: true });
                periodSelect.dispatchEvent(event);
            }
        }""")
        time.sleep(1)

        # 設置檔案類型為EXCEL
        print("設置檔案類型為EXCEL...")
        page.evaluate("""() => {
            const excelRadio = document.querySelector('#fileType_EXCEL');
            if (excelRadio) {
                excelRadio.checked = true;
                const event = new Event('change', { bubbles: true });
                excelRadio.dispatchEvent(event);
            }
        }""")
        time.sleep(1)

        # 點擊儲存按鈕
        print("\n點擊儲存按鈕...")
        save_button = page.locator('button:has-text("儲存")')
        if save_button.is_visible():
            save_button.click()
            print("已點擊儲存按鈕")
            time.sleep(2)
            return wait_and_download(page, start_date, end_date)
        else:
            print("找不到儲存按鈕")
            return False

    except Exception as e:
        print(f"執行報表查詢時發生錯誤: {str(e)}")
        page.screenshot(path=f"error_{time.strftime('%Y%m%d_%H%M%S')}.png")
        return False

def recognize_captcha(image_path):
    # 使用 ddddocr 進行驗證碼識別
    ocr = ddddocr.DdddOcr(show_ad=False)

    # 讀取圖片
    with open(image_path, 'rb') as f:
        image_bytes = f.read()

    # 識別驗證碼
    result = ocr.classification(image_bytes)
    print(f"識別出的驗證碼: {result}")
    return result


def fill_input(page, placeholder: str, value: str, timeout: int = 5000) -> bool:
    try:
        print(f"正在填寫 {placeholder}: {value}")
        # 嘗試多種定位方式
        input_element = None
        selectors = [
            lambda: page.get_by_placeholder(placeholder),
            lambda: page.locator(f"input[placeholder='{placeholder}']"),
            lambda: page.get_by_role("textbox", name=placeholder),
            lambda: page.locator(f"//input[@placeholder='{placeholder}']")
        ]

        for selector in selectors:
            try:
                input_element = selector()
                if input_element.is_visible():
                    break
            except Exception:
                continue

        if not input_element:
            print(f"無法找到輸入欄位：{placeholder}")
            return False

        # 等待元素可見
        input_element.wait_for(state="visible", timeout=timeout)

        # 點擊並填入值
        input_element.click()
        input_element.fill("")  # 清空現有內容
        time.sleep(0.5)
        input_element.type(value, delay=100)
        time.sleep(0.5)

        # 檢查輸入值
        actual_value = input_element.input_value()
        print(f"{placeholder} 輸入值檢查 - 預期: {value}, 實際: {actual_value}")

        return actual_value == value
    except Exception as e:
        print(f"填寫 {placeholder} 時發生錯誤: {str(e)}")
        return False


def fill_captcha(page, captcha_text: str) -> bool:
    try:
        # 嘗試多種方式定位驗證碼輸入框
        input_selectors = [
            lambda: page.get_by_placeholder("圖形驗證碼"),
            lambda: page.locator("input[name='captcha']"),
            lambda: page.locator("input[name='captchaMTS']"),
            lambda: page.get_by_role("textbox", name="圖形驗證碼")
        ]

        for selector in input_selectors:
            try:
                captcha_input = selector()
                if captcha_input.is_visible():
                    captcha_input.click()
                    captcha_input.fill("")
                    time.sleep(0.5)
                    captcha_input.type(captcha_text, delay=100)
                    time.sleep(0.5)

                    actual_value = captcha_input.input_value()
                    print(f"驗證碼輸入值檢查 - 預期: {captcha_text}, 實際: {actual_value}")
                    return True
            except Exception as e:
                print(f"嘗試填入驗證碼失敗: {str(e)}")
                continue

        return False
    except Exception as e:
        print(f"填寫驗證碼時發生錯誤: {str(e)}")
        return False


def check_login_status(page) -> tuple[bool, bool]:
    """
    檢查登入狀態
    返回: (是否登入成功, 是否需要重試)
    """
    try:
        time.sleep(3)

        # 檢查是否成功登入到儀表板
        if "dashboard" in page.url:
            print("成功登入並重定向到儀表板")
            return True, False

        # 檢查常見錯誤訊息
        error_messages = {
            "登入失敗超過次數上限": (False, False),
            "帳號已鎖定": (False, False),
            "系統暫時無法服務": (False, True),
            "驗證碼錯誤": (False, True),
            "密碼錯誤": (False, False),
            "帳號密碼錯誤": (False, False)
        }

        for message, (success, retry) in error_messages.items():
            try:
                if page.get_by_text(message, exact=True).is_visible(timeout=1000):
                    print(f"發現錯誤訊息: {message}")
                    return success, retry
            except Exception:
                continue

        # 檢查是否還在登入頁面
        try:
            login_button = page.get_by_role("button", name="登入")
            if login_button.is_visible(timeout=1000):
                print("仍在登入頁面，可能需要重試")
                return False, True
        except Exception:
            pass

        # 如果沒有明確的錯誤訊息且不在登入頁面，可能已經成功登入
        print("沒有發現錯誤訊息且不在登入頁面，判定為登入成功")
        return True, False

    except Exception as e:
        print(f"檢查登入狀態時發生錯誤: {str(e)}")
        return False, True


def try_login(page, password: str, max_attempts: int = 2) -> bool:
    """
    嘗試使用指定密碼登入
    """
    print(f"\n嘗試使用密碼登入...")

    for attempt in range(max_attempts):
        try:
            if attempt > 0:
                print(f"登入重試 {attempt + 1}/{max_attempts}")
                # 重新載入頁面
                page.reload()
                time.sleep(2)

            # 填寫登入表單
            if not all([
                fill_input(page, "統一編號", "80706866"),
                fill_input(page, "帳號", "A959A728C41"),
                fill_input(page, "密碼", password)
            ]):
                print("填寫表單失敗")
                continue

            # 處理驗證碼
            captcha_img = page.get_by_role("img", name="圖形驗證碼", exact=True)
            captcha_img.wait_for()
            captcha_img.screenshot(path="captcha.png")

            captcha_text = recognize_captcha("captcha.png")
            if not fill_captcha(page, captcha_text):
                print("填寫驗證碼失敗")
                continue

            # 點擊登入按鈕
            submit_button = page.get_by_role("button", name="登入")
            submit_button.click()
            print("已點擊登入按鈕")

            # 檢查登入結果
            login_success, should_retry = check_login_status(page)
            if login_success:
                print("登入成功！")
                return True
            if not should_retry:
                print(f"登入失敗且無需重試")
                return False

        except Exception as e:
            print(f"登入嘗試失敗: {str(e)}")
            if attempt < max_attempts - 1:
                continue

    print(f"已達最大嘗試次數 {max_attempts}")
    return False


def run(playwright: Playwright) -> None:
    """主運行函數"""
    browser = None
    context = None
    page = None
    login_success = False
    download_success = False
    download_finished = False
    current_date_range = None  # 新增：用於追蹤當前日期範圍

    try:
        # 瀏覽器初始化
        print("\n=== 啟動瀏覽器 ===")
        browser = playwright.chromium.launch(
            headless=False,
            slow_mo=100
        )
        context = browser.new_context(accept_downloads=True)
        context.set_default_timeout(30000)
        page = context.new_page()

        # 設置下載處理器
        def handle_download(download):
            nonlocal download_finished, current_date_range
            try:
                if current_date_range:
                    start_date, end_date = current_date_range
                    # 移除日期中的年月日字符，只保留數字
                    start_clean = start_date.replace('年', '').replace('月', '').replace('日', '')
                    end_clean = end_date.replace('年', '').replace('月', '').replace('日', '')
                    filename = f"report_{start_clean}_to_{end_clean}.xlsx"
                else:
                    filename = f"report_{time.strftime('%Y%m%d_%H%M%S')}.xlsx"

                download.save_as(filename)
                print(f"檔案已下載: {filename}")
                download_finished = True
                return True
            except Exception as e:
                print(f"下載檔案時發生錯誤: {str(e)}")
                return False

        page.on("download", handle_download)

        # 執行登入流程
        print("\n=== 開始登入流程 ===")
        print("正在訪問網站...")
        page.goto("https://www.einvoice.nat.gov.tw/")
        time.sleep(2)
        page.get_by_role("link", name="登入").click()
        time.sleep(2)
        page.get_by_role("link", name="營業人/ 扣繳單位").click()
        time.sleep(3)

        # 登入嘗試
        for i, password in enumerate(PASSWORDS, 1):
            print(f"\n嘗試第 {i}/{len(PASSWORDS)} 個密碼")
            if try_login(page, password):
                login_success = True
                print("登入成功完成！")

                # 取得當前月份的四個時期
                current_date = datetime.now()
                periods = get_date_periods(current_date.year, current_date.month)

                # 依序處理每個時期
                for period_idx, (start_date, end_date) in enumerate(periods, 1):
                    print(f"\n=== 處理第 {period_idx}/{len(periods)} 個時期 ({start_date} ~ {end_date}) ===")
                    current_date_range = (start_date, end_date)  # 更新當前日期範圍

                    # 導航到報表頁面
                    print("導航到報表頁面...")
                    page.goto("https://www.einvoice.nat.gov.tw/dashboard/btb/btb411w/offline/add")
                    time.sleep(5)

                    # 執行報表查詢（最多重試3次）
                    retry_count = 3
                    download_success = False
                    download_finished = False

                    for retry in range(retry_count):
                        print(f"\n執行第 {retry + 1}/{retry_count} 次報表查詢...")
                        if get_report_data(page, start_date, end_date):
                            print("報表查詢執行成功！")

                            print("\n=== 開始下載流程 ===")
                            if wait_and_download(page, start_date, end_date):
                                download_success = True
                                print("報表下載成功！")

                                # 等待下載完成
                                print("\n等待下載完成...")
                                wait_start = time.time()
                                while not download_finished and time.time() - wait_start < 60:
                                    time.sleep(1)

                                if download_finished:
                                    print("下載已完成")
                                    time.sleep(5)
                                    break
                                else:
                                    print("下載超時")
                                    download_success = False
                            else:
                                print("報表下載失敗，重試中...")
                        else:
                            print(f"報表查詢執行失敗！重試 {retry + 1}/{retry_count}")
                            time.sleep(5)

                    # 檢查當前時期是否成功
                    if not download_success:
                        print(f"\n第 {period_idx} 期下載失敗")
                        continue

                    # 等待一段時間後處理下一期
                    if period_idx < len(periods):
                        print("\n等待30秒後處理下一期...")
                        time.sleep(30)

                break  # 登入成功後跳出密碼嘗試循環
            print(f"當前密碼登入失敗")

        if not login_success:
            print("\n=== 登入失敗 ===")
            print("所有密碼都嘗試失敗")
            sys.exit(1)

    except Exception as e:
        print(f"\n=== 發生錯誤 ===")
        print(f"錯誤訊息：{str(e)}")
        if page:
            error_screenshot = f"error_{time.strftime('%Y%m%d_%H%M%S')}.png"
            page.screenshot(path=error_screenshot)
            print(f"已保存錯誤截圖：{error_screenshot}")
        sys.exit(1)

    finally:
        if download_success:
            print("\n=== 等待下載完成後關閉瀏覽器 ===")
            if not download_finished:
                print("等待最後下載完成...")
                time.sleep(10)

        print("\n=== 清理資源 ===")
        if context:
            try:
                context.close()
            except Exception as e:
                print(f"關閉context時發生錯誤: {str(e)}")

        if browser:
            try:
                browser.close()
            except Exception as e:
                print(f"關閉瀏覽器時發生錯誤: {str(e)}")

        print("程式執行完成！")

        if not download_success:
            sys.exit(1)

if __name__ == "__main__":
    with sync_playwright() as playwright:
        run(playwright)