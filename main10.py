from dataclasses import dataclass
from datetime import date, time, datetime
from typing import Optional
import xml.etree.ElementTree as ET
from xml.dom import minidom
import os

# 定義命名空間
NAMESPACE = "urn:GEINV:eInvoiceMessage:A0202:4.0"
ET.register_namespace('', NAMESPACE)


@dataclass
class CancelInvoiceConfirm:
    cancel_invoice_number: str
    invoice_date: date
    buyer_id: str
    seller_id: str
    cancel_date: date
    cancel_time: time
    remark: Optional[str] = None

    def __post_init__(self):
        # 驗證輸入
        if not self.cancel_invoice_number or len(self.cancel_invoice_number) != 10:
            raise ValueError("作廢發票號碼必須是10位字符")

        if not self.buyer_id or len(self.buyer_id) != 8:
            raise ValueError("買方統一編號必須是8位字符")

        if not self.seller_id or len(self.seller_id) != 8:
            raise ValueError("賣方統一編號必須是8位字符")

        if self.remark and len(self.remark) > 200:
            raise ValueError("備註不能超過200個字符")

    def to_dict(self):
        return {
            "CancelInvoiceNumber": self.cancel_invoice_number,
            "InvoiceDate": self.invoice_date.strftime('%Y%m%d'),
            "BuyerId": self.buyer_id,
            "SellerId": self.seller_id,
            "CancelDate": self.cancel_date.strftime('%Y%m%d'),
            "CancelTime": self.cancel_time.strftime('%H:%M:%S'),
            "Remark": self.remark
        }

    def to_xml(self):
        # 創建根元素時使用命名空間
        root = ET.Element(f"{{{NAMESPACE}}}CancelInvoiceConfirm")

        # 添加子元素時也使用命名空間，並使用正確的日期和時間格式
        ET.SubElement(root, f"{{{NAMESPACE}}}CancelInvoiceNumber").text = self.cancel_invoice_number
        ET.SubElement(root, f"{{{NAMESPACE}}}InvoiceDate").text = self.invoice_date.strftime('%Y%m%d')
        ET.SubElement(root, f"{{{NAMESPACE}}}BuyerId").text = self.buyer_id
        ET.SubElement(root, f"{{{NAMESPACE}}}SellerId").text = self.seller_id
        ET.SubElement(root, f"{{{NAMESPACE}}}CancelDate").text = self.cancel_date.strftime('%Y%m%d')
        ET.SubElement(root, f"{{{NAMESPACE}}}CancelTime").text = self.cancel_time.strftime('%H:%M:%S')

        if self.remark:
            ET.SubElement(root, f"{{{NAMESPACE}}}Remark").text = self.remark

        # 將 XML 轉換為美化的字符串
        xml_str = minidom.parseString(ET.tostring(root)).toprettyxml(indent="  ")
        return xml_str

    def generate_filename(self):
        # 生成檔名
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"A0202_{self.seller_id}_{self.cancel_invoice_number}_{timestamp}.xml"

    def save_xml(self, directory="./output"):
        xml_content = self.to_xml()

        # 確保目錄存在
        os.makedirs(directory, exist_ok=True)

        # 生成檔名
        filename = self.generate_filename()
        file_path = os.path.join(directory, filename)

        # 寫入 XML 文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(xml_content)

        print(f"XML 已保存到: {file_path}")
        return file_path


# 使用示例
if __name__ == "__main__":
    confirm = CancelInvoiceConfirm(
        cancel_invoice_number="CW01015185", # 作廢發票號碼
        invoice_date=date(2024, 8, 27), # 發票日期
        buyer_id="80706866", # 買方統一編號
        seller_id="04279821", # 賣方統一編號
        cancel_date=date(2024, 9, 3), # 作廢日期
        cancel_time=time(3, 5), # 作廢時間
        remark="資料有誤，發票作廢" # 備註
    )

    # 顯示 XML 字符串
    print("XML 字符串:")
    print(confirm.to_xml())
    print()

    # 保存 XML 到文件
    saved_file_path = confirm.save_xml()
    print(f"檔案已保存: {saved_file_path}")

    # 讀取並顯示保存的文件內容
    print("\n保存的文件內容:")
    with open(saved_file_path, 'r', encoding='utf-8') as file:
        print(file.read())