# Tcl autoload index file, version 2.0
# This file is generated by the "auto_mkindex" command
# and sourced to set up indexing information for one or
# more commands.  Typically each line is a command that
# sets an element in the auto_index array, where the
# element name is the name of a command and the value is
# a script that loads the command.

set auto_index(MkChoosers) [list source [file join $dir MkChoose.tcl]]
set auto_index(MkCombo) [list source [file join $dir MkChoose.tcl]]
set auto_index(stCmd) [list source [file join $dir MkChoose.tcl]]
set auto_index(stValidate) [list source [file join $dir MkChoose.tcl]]
set auto_index(MkControl) [list source [file join $dir MkChoose.tcl]]
set auto_index(MkSelect) [list source [file join $dir MkChoose.tcl]]
set auto_index(MkOptMenu) [list source [file join $dir MkChoose.tcl]]
set auto_index(MkFileEnt) [list source [file join $dir MkChoose.tcl]]
set auto_index(MkFileBox) [list source [file join $dir MkChoose.tcl]]
set auto_index(MkToolBar) [list source [file join $dir MkChoose.tcl]]
set auto_index(MkTitle) [list source [file join $dir MkChoose.tcl]]
set auto_index(MkDirList) [list source [file join $dir MkDirLis.tcl]]
set auto_index(MkDirListWidget) [list source [file join $dir MkDirLis.tcl]]
set auto_index(MkExFileWidget) [list source [file join $dir MkDirLis.tcl]]
set auto_index(MkManager) [list source [file join $dir MkManag.tcl]]
set auto_index(MkPanedWindow) [list source [file join $dir MkManag.tcl]]
set auto_index(MkNoteBook) [list source [file join $dir MkManag.tcl]]
set auto_index(CreateCommonButtons) [list source [file join $dir MkManag.tcl]]
set auto_index(MkSample) [list source [file join $dir MkSample.tcl]]
set auto_index(AddSampleToHList) [list source [file join $dir MkSample.tcl]]
set auto_index(Sample:Action) [list source [file join $dir MkSample.tcl]]
set auto_index(RunProg) [list source [file join $dir MkSample.tcl]]
set auto_index(LoadFile) [list source [file join $dir MkSample.tcl]]
set auto_index(ReadFileWhenIdle) [list source [file join $dir MkSample.tcl]]
set auto_index(ReadFile) [list source [file join $dir MkSample.tcl]]
set auto_index(MkScroll) [list source [file join $dir MkScroll.tcl]]
set auto_index(MkSList) [list source [file join $dir MkScroll.tcl]]
set auto_index(SList:Reset) [list source [file join $dir MkScroll.tcl]]
set auto_index(MkSWindow) [list source [file join $dir MkScroll.tcl]]
set auto_index(SWindow:Reset) [list source [file join $dir MkScroll.tcl]]
set auto_index(MkSText) [list source [file join $dir MkScroll.tcl]]
set auto_index(SText:Reset) [list source [file join $dir MkScroll.tcl]]
set auto_index(tixDemo:MkMainWindow) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:MkMainMenu) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:MkMainNoteBook) [list source [file join $dir tixwidgets.tcl]]
set auto_index(txiDemo:CreatePage) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:MkMainStatus) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:Status) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:MkWelcome) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:MkWelcomeBar) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:MkWelcomeText) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:MainTextFont) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:FileOpen) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:FileOpen:Doit) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:BalloonHelp) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:SelfTest) [list source [file join $dir tixwidgets.tcl]]
set auto_index(tixDemo:Exit) [list source [file join $dir tixwidgets.tcl]]
