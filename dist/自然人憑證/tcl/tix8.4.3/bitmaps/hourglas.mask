#define hourglass_mask_width 32
#define hourglass_mask_height 32
#define hourglass_mask_x_hot 16
#define hourglass_mask_y_hot 15
static char hourglass_mask_bits[] = {
   0x00, 0x00, 0x00, 0x00, 0xfc, 0xff, 0xff, 0x7f, 0xfc, 0xff, 0xff, 0x7f,
   0x58, 0x00, 0x00, 0x34, 0x58, 0x00, 0x00, 0x34, 0x58, 0x00, 0x00, 0x34,
   0x98, 0x00, 0x00, 0x32, 0x98, 0x00, 0x00, 0x32, 0x98, 0x00, 0x00, 0x32,
   0x18, 0x01, 0x00, 0x31, 0x18, 0xfd, 0x7e, 0x31, 0x18, 0xfa, 0xbf, 0x30,
   0x18, 0xe4, 0x4f, 0x30, 0x18, 0xd8, 0x37, 0x30, 0x18, 0x20, 0x09, 0x30,
   0x18, 0x40, 0x05, 0x30, 0x18, 0x20, 0x08, 0x30, 0x18, 0x18, 0x31, 0x30,
   0x18, 0x04, 0x41, 0x30, 0x18, 0x02, 0x80, 0x30, 0x18, 0x01, 0x00, 0x31,
   0x18, 0x01, 0x00, 0x31, 0x98, 0x00, 0x01, 0x32, 0x98, 0x00, 0x01, 0x32,
   0x98, 0x80, 0x03, 0x32, 0x58, 0xc0, 0x07, 0x34, 0x58, 0xf0, 0x1f, 0x34,
   0x58, 0xfe, 0xff, 0x34, 0xf8, 0xff, 0xff, 0x3f, 0xfc, 0xff, 0xff, 0x7f,
   0xfc, 0xff, 0xff, 0x7f, 0x00, 0x00, 0x00, 0x00};
