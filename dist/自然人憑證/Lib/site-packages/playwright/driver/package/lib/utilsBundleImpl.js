"use strict";var Dh=Object.create;var ui=Object.defineProperty;var jh=Object.getOwnPropertyDescriptor;var qh=Object.getOwnPropertyNames;var Hh=Object.getPrototypeOf,Vh=Object.prototype.hasOwnProperty;var x=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),$h=(t,e)=>{for(var r in e)ui(t,r,{get:e[r],enumerable:!0})},Lo=(t,e,r,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of qh(e))!Vh.call(t,n)&&n!==r&&ui(t,n,{get:()=>e[n],enumerable:!(i=jh(e,n))||i.enumerable});return t};var Te=(t,e,r)=>(r=t!=null?Dh(Hh(t)):{},Lo(e||!t||!t.__esModule?ui(r,"default",{value:t,enumerable:!0}):r,t)),Gh=t=>Lo(ui({},"__esModule",{value:!0}),t);var Mo=x((f_,Fo)=>{var Po={};Fo.exports=Po;var No={reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],grey:[90,39],brightRed:[91,39],brightGreen:[92,39],brightYellow:[93,39],brightBlue:[94,39],brightMagenta:[95,39],brightCyan:[96,39],brightWhite:[97,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgGray:[100,49],bgGrey:[100,49],bgBrightRed:[101,49],bgBrightGreen:[102,49],bgBrightYellow:[103,49],bgBrightBlue:[104,49],bgBrightMagenta:[105,49],bgBrightCyan:[106,49],bgBrightWhite:[107,49],blackBG:[40,49],redBG:[41,49],greenBG:[42,49],yellowBG:[43,49],blueBG:[44,49],magentaBG:[45,49],cyanBG:[46,49],whiteBG:[47,49]};Object.keys(No).forEach(function(t){var e=No[t],r=Po[t]=[];r.open="\x1B["+e[0]+"m",r.close="\x1B["+e[1]+"m"})});var Do=x((h_,Uo)=>{"use strict";Uo.exports=function(t,e){e=e||process.argv;var r=e.indexOf("--"),i=/^-{1,2}/.test(t)?"":"--",n=e.indexOf(i+t);return n!==-1&&(r===-1?!0:n<r)}});var qo=x((p_,jo)=>{"use strict";var zh=require("os"),rt=Do(),Ve=process.env,Zt=void 0;rt("no-color")||rt("no-colors")||rt("color=false")?Zt=!1:(rt("color")||rt("colors")||rt("color=true")||rt("color=always"))&&(Zt=!0);"FORCE_COLOR"in Ve&&(Zt=Ve.FORCE_COLOR.length===0||parseInt(Ve.FORCE_COLOR,10)!==0);function Wh(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function Yh(t){if(Zt===!1)return 0;if(rt("color=16m")||rt("color=full")||rt("color=truecolor"))return 3;if(rt("color=256"))return 2;if(t&&!t.isTTY&&Zt!==!0)return 0;var e=Zt?1:0;if(process.platform==="win32"){var r=zh.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(r[0])>=10&&Number(r[2])>=10586?Number(r[2])>=14931?3:2:1}if("CI"in Ve)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(function(n){return n in Ve})||Ve.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in Ve)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(Ve.TEAMCITY_VERSION)?1:0;if("TERM_PROGRAM"in Ve){var i=parseInt((Ve.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(Ve.TERM_PROGRAM){case"iTerm.app":return i>=3?3:2;case"Hyper":return 3;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(Ve.TERM)?2:/^screen|^xterm|^vt100|^rxvt|color|ansi|cygwin|linux/i.test(Ve.TERM)||"COLORTERM"in Ve?1:(Ve.TERM==="dumb",e)}function Ji(t){var e=Yh(t);return Wh(e)}jo.exports={supportsColor:Ji,stdout:Ji(process.stdout),stderr:Ji(process.stderr)}});var Vo=x((d_,Ho)=>{Ho.exports=function(e,r){var i="";e=e||"Run the trap, drop the bass",e=e.split("");var n={a:["@","\u0104","\u023A","\u0245","\u0394","\u039B","\u0414"],b:["\xDF","\u0181","\u0243","\u026E","\u03B2","\u0E3F"],c:["\xA9","\u023B","\u03FE"],d:["\xD0","\u018A","\u0500","\u0501","\u0502","\u0503"],e:["\xCB","\u0115","\u018E","\u0258","\u03A3","\u03BE","\u04BC","\u0A6C"],f:["\u04FA"],g:["\u0262"],h:["\u0126","\u0195","\u04A2","\u04BA","\u04C7","\u050A"],i:["\u0F0F"],j:["\u0134"],k:["\u0138","\u04A0","\u04C3","\u051E"],l:["\u0139"],m:["\u028D","\u04CD","\u04CE","\u0520","\u0521","\u0D69"],n:["\xD1","\u014B","\u019D","\u0376","\u03A0","\u048A"],o:["\xD8","\xF5","\xF8","\u01FE","\u0298","\u047A","\u05DD","\u06DD","\u0E4F"],p:["\u01F7","\u048E"],q:["\u09CD"],r:["\xAE","\u01A6","\u0210","\u024C","\u0280","\u042F"],s:["\xA7","\u03DE","\u03DF","\u03E8"],t:["\u0141","\u0166","\u0373"],u:["\u01B1","\u054D"],v:["\u05D8"],w:["\u0428","\u0460","\u047C","\u0D70"],x:["\u04B2","\u04FE","\u04FC","\u04FD"],y:["\xA5","\u04B0","\u04CB"],z:["\u01B5","\u0240"]};return e.forEach(function(s){s=s.toLowerCase();var o=n[s]||[" "],a=Math.floor(Math.random()*o.length);typeof n[s]!="undefined"?i+=n[s][a]:i+=s}),i}});var Go=x((m_,$o)=>{$o.exports=function(e,r){e=e||"   he is here   ";var i={up:["\u030D","\u030E","\u0304","\u0305","\u033F","\u0311","\u0306","\u0310","\u0352","\u0357","\u0351","\u0307","\u0308","\u030A","\u0342","\u0313","\u0308","\u034A","\u034B","\u034C","\u0303","\u0302","\u030C","\u0350","\u0300","\u0301","\u030B","\u030F","\u0312","\u0313","\u0314","\u033D","\u0309","\u0363","\u0364","\u0365","\u0366","\u0367","\u0368","\u0369","\u036A","\u036B","\u036C","\u036D","\u036E","\u036F","\u033E","\u035B","\u0346","\u031A"],down:["\u0316","\u0317","\u0318","\u0319","\u031C","\u031D","\u031E","\u031F","\u0320","\u0324","\u0325","\u0326","\u0329","\u032A","\u032B","\u032C","\u032D","\u032E","\u032F","\u0330","\u0331","\u0332","\u0333","\u0339","\u033A","\u033B","\u033C","\u0345","\u0347","\u0348","\u0349","\u034D","\u034E","\u0353","\u0354","\u0355","\u0356","\u0359","\u035A","\u0323"],mid:["\u0315","\u031B","\u0300","\u0301","\u0358","\u0321","\u0322","\u0327","\u0328","\u0334","\u0335","\u0336","\u035C","\u035D","\u035E","\u035F","\u0360","\u0362","\u0338","\u0337","\u0361"," \u0489"]},n=[].concat(i.up,i.down,i.mid);function s(l){var c=Math.floor(Math.random()*l);return c}function o(l){var c=!1;return n.filter(function(u){c=u===l}),c}function a(l,c){var u="",f,h;c=c||{},c.up=typeof c.up!="undefined"?c.up:!0,c.mid=typeof c.mid!="undefined"?c.mid:!0,c.down=typeof c.down!="undefined"?c.down:!0,c.size=typeof c.size!="undefined"?c.size:"maxi",l=l.split("");for(h in l)if(!o(h)){switch(u=u+l[h],f={up:0,down:0,mid:0},c.size){case"mini":f.up=s(8),f.mid=s(2),f.down=s(8);break;case"maxi":f.up=s(16)+3,f.mid=s(4)+1,f.down=s(64)+3;break;default:f.up=s(8)+1,f.mid=s(6)/2,f.down=s(8)+1;break}var p=["up","mid","down"];for(var d in p)for(var m=p[d],g=0;g<=f[m];g++)c[m]&&(u=u+i[m][s(i[m].length)])}return u}return a(e,r)}});var Wo=x((g_,zo)=>{zo.exports=function(t){return function(e,r,i){if(e===" ")return e;switch(r%3){case 0:return t.red(e);case 1:return t.white(e);case 2:return t.blue(e)}}}});var Ko=x((v_,Yo)=>{Yo.exports=function(t){return function(e,r,i){return r%2===0?e:t.inverse(e)}}});var Xo=x((__,Zo)=>{Zo.exports=function(t){var e=["red","yellow","green","blue","magenta"];return function(r,i,n){return r===" "?r:t[e[i++%e.length]](r)}}});var Jo=x((x_,Qo)=>{Qo.exports=function(t){var e=["underline","inverse","grey","yellow","red","green","blue","white","cyan","magenta","brightYellow","brightRed","brightGreen","brightBlue","brightWhite","brightCyan","brightMagenta"];return function(r,i,n){return r===" "?r:t[e[Math.round(Math.random()*(e.length-2))]](r)}}});var sa=x((b_,na)=>{var ae={};na.exports=ae;ae.themes={};var Kh=require("util"),At=ae.styles=Mo(),ta=Object.defineProperties,Zh=new RegExp(/[\r\n]+/g);ae.supportsColor=qo().supportsColor;typeof ae.enabled=="undefined"&&(ae.enabled=ae.supportsColor()!==!1);ae.enable=function(){ae.enabled=!0};ae.disable=function(){ae.enabled=!1};ae.stripColors=ae.strip=function(t){return(""+t).replace(/\x1B\[\d+m/g,"")};var y_=ae.stylize=function(e,r){if(!ae.enabled)return e+"";var i=At[r];return!i&&r in ae?ae[r](e):i.open+e+i.close},Xh=/[|\\{}()[\]^$+*?.]/g,Qh=function(t){if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(Xh,"\\$&")};function ra(t){var e=function r(){return ep.apply(r,arguments)};return e._styles=t,e.__proto__=Jh,e}var ia=function(){var t={};return At.grey=At.gray,Object.keys(At).forEach(function(e){At[e].closeRe=new RegExp(Qh(At[e].close),"g"),t[e]={get:function(){return ra(this._styles.concat(e))}}}),t}(),Jh=ta(function(){},ia);function ep(){var t=Array.prototype.slice.call(arguments),e=t.map(function(o){return o!=null&&o.constructor===String?o:Kh.inspect(o)}).join(" ");if(!ae.enabled||!e)return e;for(var r=e.indexOf(`
`)!=-1,i=this._styles,n=i.length;n--;){var s=At[i[n]];e=s.open+e.replace(s.closeRe,s.open)+s.close,r&&(e=e.replace(Zh,function(o){return s.close+o+s.open}))}return e}ae.setTheme=function(t){if(typeof t=="string"){console.log("colors.setTheme now only accepts an object, not a string.  If you are trying to set a theme from a file, it is now your (the caller's) responsibility to require the file.  The old syntax looked like colors.setTheme(__dirname + '/../themes/generic-logging.js'); The new syntax looks like colors.setTheme(require(__dirname + '/../themes/generic-logging.js'));");return}for(var e in t)(function(r){ae[r]=function(i){if(typeof t[r]=="object"){var n=i;for(var s in t[r])n=ae[t[r][s]](n);return n}return ae[t[r]](i)}})(e)};function tp(){var t={};return Object.keys(ia).forEach(function(e){t[e]={get:function(){return ra([e])}}}),t}var rp=function(e,r){var i=r.split("");return i=i.map(e),i.join("")};ae.trap=Vo();ae.zalgo=Go();ae.maps={};ae.maps.america=Wo()(ae);ae.maps.zebra=Ko()(ae);ae.maps.rainbow=Xo()(ae);ae.maps.random=Jo()(ae);for(ea in ae.maps)(function(t){ae[t]=function(e){return rp(ae.maps[t],e)}})(ea);var ea;ta(ae,tp())});var aa=x((w_,oa)=>{var ip=sa();oa.exports=ip});var ca=x((E_,la)=>{var Xt=1e3,Qt=Xt*60,Jt=Qt*60,Rt=Jt*24,np=Rt*7,sp=Rt*365.25;la.exports=function(t,e){e=e||{};var r=typeof t;if(r==="string"&&t.length>0)return op(t);if(r==="number"&&isFinite(t))return e.long?lp(t):ap(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function op(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(!!e){var r=parseFloat(e[1]),i=(e[2]||"ms").toLowerCase();switch(i){case"years":case"year":case"yrs":case"yr":case"y":return r*sp;case"weeks":case"week":case"w":return r*np;case"days":case"day":case"d":return r*Rt;case"hours":case"hour":case"hrs":case"hr":case"h":return r*Jt;case"minutes":case"minute":case"mins":case"min":case"m":return r*Qt;case"seconds":case"second":case"secs":case"sec":case"s":return r*Xt;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function ap(t){var e=Math.abs(t);return e>=Rt?Math.round(t/Rt)+"d":e>=Jt?Math.round(t/Jt)+"h":e>=Qt?Math.round(t/Qt)+"m":e>=Xt?Math.round(t/Xt)+"s":t+"ms"}function lp(t){var e=Math.abs(t);return e>=Rt?fi(t,e,Rt,"day"):e>=Jt?fi(t,e,Jt,"hour"):e>=Qt?fi(t,e,Qt,"minute"):e>=Xt?fi(t,e,Xt,"second"):t+" ms"}function fi(t,e,r,i){var n=e>=r*1.5;return Math.round(t/r)+" "+i+(n?"s":"")}});var en=x((S_,ua)=>{function cp(t){r.debug=r,r.default=r,r.coerce=l,r.disable=s,r.enable=n,r.enabled=o,r.humanize=ca(),r.destroy=c,Object.keys(t).forEach(u=>{r[u]=t[u]}),r.names=[],r.skips=[],r.formatters={};function e(u){let f=0;for(let h=0;h<u.length;h++)f=(f<<5)-f+u.charCodeAt(h),f|=0;return r.colors[Math.abs(f)%r.colors.length]}r.selectColor=e;function r(u){let f,h=null,p,d;function m(...g){if(!m.enabled)return;let y=m,I=Number(new Date),w=I-(f||I);y.diff=w,y.prev=f,y.curr=I,f=I,g[0]=r.coerce(g[0]),typeof g[0]!="string"&&g.unshift("%O");let S=0;g[0]=g[0].replace(/%([a-zA-Z%])/g,($,C)=>{if($==="%%")return"%";S++;let U=r.formatters[C];if(typeof U=="function"){let E=g[S];$=U.call(y,E),g.splice(S,1),S--}return $}),r.formatArgs.call(y,g),(y.log||r.log).apply(y,g)}return m.namespace=u,m.useColors=r.useColors(),m.color=r.selectColor(u),m.extend=i,m.destroy=r.destroy,Object.defineProperty(m,"enabled",{enumerable:!0,configurable:!1,get:()=>h!==null?h:(p!==r.namespaces&&(p=r.namespaces,d=r.enabled(u)),d),set:g=>{h=g}}),typeof r.init=="function"&&r.init(m),m}function i(u,f){let h=r(this.namespace+(typeof f=="undefined"?":":f)+u);return h.log=this.log,h}function n(u){r.save(u),r.namespaces=u,r.names=[],r.skips=[];let f,h=(typeof u=="string"?u:"").split(/[\s,]+/),p=h.length;for(f=0;f<p;f++)!h[f]||(u=h[f].replace(/\*/g,".*?"),u[0]==="-"?r.skips.push(new RegExp("^"+u.slice(1)+"$")):r.names.push(new RegExp("^"+u+"$")))}function s(){let u=[...r.names.map(a),...r.skips.map(a).map(f=>"-"+f)].join(",");return r.enable(""),u}function o(u){if(u[u.length-1]==="*")return!0;let f,h;for(f=0,h=r.skips.length;f<h;f++)if(r.skips[f].test(u))return!1;for(f=0,h=r.names.length;f<h;f++)if(r.names[f].test(u))return!0;return!1}function a(u){return u.toString().substring(2,u.toString().length-2).replace(/\.\*\?$/,"*")}function l(u){return u instanceof Error?u.stack||u.message:u}function c(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}ua.exports=cp});var fa=x((We,hi)=>{We.formatArgs=fp;We.save=hp;We.load=pp;We.useColors=up;We.storage=dp();We.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();We.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function up(){return typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function fp(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+hi.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(r++,n==="%c"&&(i=r))}),t.splice(i,0,e)}We.log=console.debug||console.log||(()=>{});function hp(t){try{t?We.storage.setItem("debug",t):We.storage.removeItem("debug")}catch{}}function pp(){let t;try{t=We.storage.getItem("debug")}catch{}return!t&&typeof process!="undefined"&&"env"in process&&(t=process.env.DEBUG),t}function dp(){try{return localStorage}catch{}}hi.exports=en()(We);var{formatters:mp}=hi.exports;mp.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var pa=x((k_,ha)=>{"use strict";ha.exports=(t,e)=>{e=e||process.argv;let r=t.startsWith("-")?"":t.length===1?"-":"--",i=e.indexOf(r+t),n=e.indexOf("--");return i!==-1&&(n===-1?!0:i<n)}});var ma=x((C_,da)=>{"use strict";var gp=require("os"),it=pa(),je=process.env,er;it("no-color")||it("no-colors")||it("color=false")?er=!1:(it("color")||it("colors")||it("color=true")||it("color=always"))&&(er=!0);"FORCE_COLOR"in je&&(er=je.FORCE_COLOR.length===0||parseInt(je.FORCE_COLOR,10)!==0);function vp(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function _p(t){if(er===!1)return 0;if(it("color=16m")||it("color=full")||it("color=truecolor"))return 3;if(it("color=256"))return 2;if(t&&!t.isTTY&&er!==!0)return 0;let e=er?1:0;if(process.platform==="win32"){let r=gp.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(r[0])>=10&&Number(r[2])>=10586?Number(r[2])>=14931?3:2:1}if("CI"in je)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(r=>r in je)||je.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in je)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(je.TEAMCITY_VERSION)?1:0;if(je.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in je){let r=parseInt((je.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(je.TERM_PROGRAM){case"iTerm.app":return r>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(je.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(je.TERM)||"COLORTERM"in je?1:(je.TERM==="dumb",e)}function tn(t){let e=_p(t);return vp(e)}da.exports={supportsColor:tn,stdout:tn(process.stdout),stderr:tn(process.stderr)}});var va=x((Me,di)=>{var xp=require("tty"),pi=require("util");Me.init=Cp;Me.log=Ep;Me.formatArgs=bp;Me.save=Sp;Me.load=kp;Me.useColors=yp;Me.destroy=pi.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");Me.colors=[6,2,3,4,5,1];try{let t=ma();t&&(t.stderr||t).level>=2&&(Me.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}Me.inspectOpts=Object.keys(process.env).filter(t=>/^debug_/i.test(t)).reduce((t,e)=>{let r=e.substring(6).toLowerCase().replace(/_([a-z])/g,(n,s)=>s.toUpperCase()),i=process.env[e];return/^(yes|on|true|enabled)$/i.test(i)?i=!0:/^(no|off|false|disabled)$/i.test(i)?i=!1:i==="null"?i=null:i=Number(i),t[r]=i,t},{});function yp(){return"colors"in Me.inspectOpts?Boolean(Me.inspectOpts.colors):xp.isatty(process.stderr.fd)}function bp(t){let{namespace:e,useColors:r}=this;if(r){let i=this.color,n="\x1B[3"+(i<8?i:"8;5;"+i),s=`  ${n};1m${e} \x1B[0m`;t[0]=s+t[0].split(`
`).join(`
`+s),t.push(n+"m+"+di.exports.humanize(this.diff)+"\x1B[0m")}else t[0]=wp()+e+" "+t[0]}function wp(){return Me.inspectOpts.hideDate?"":new Date().toISOString()+" "}function Ep(...t){return process.stderr.write(pi.format(...t)+`
`)}function Sp(t){t?process.env.DEBUG=t:delete process.env.DEBUG}function kp(){return process.env.DEBUG}function Cp(t){t.inspectOpts={};let e=Object.keys(Me.inspectOpts);for(let r=0;r<e.length;r++)t.inspectOpts[e[r]]=Me.inspectOpts[e[r]]}di.exports=en()(Me);var{formatters:ga}=di.exports;ga.o=function(t){return this.inspectOpts.colors=this.useColors,pi.inspect(t,this.inspectOpts).split(`
`).map(e=>e.trim()).join(" ")};ga.O=function(t){return this.inspectOpts.colors=this.useColors,pi.inspect(t,this.inspectOpts)}});var tr=x((O_,rn)=>{typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?rn.exports=fa():rn.exports=va()});var xa=x(_a=>{"use strict";var Op=require("url").parse,Ip={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},Tp=String.prototype.endsWith||function(t){return t.length<=this.length&&this.indexOf(t,this.length-t.length)!==-1};function Ap(t){var e=typeof t=="string"?Op(t):t||{},r=e.protocol,i=e.host,n=e.port;if(typeof i!="string"||!i||typeof r!="string"||(r=r.split(":",1)[0],i=i.replace(/:\d*$/,""),n=parseInt(n)||Ip[r]||0,!Rp(i,n)))return"";var s=rr("npm_config_"+r+"_proxy")||rr(r+"_proxy")||rr("npm_config_proxy")||rr("all_proxy");return s&&s.indexOf("://")===-1&&(s=r+"://"+s),s}function Rp(t,e){var r=(rr("npm_config_no_proxy")||rr("no_proxy")).toLowerCase();return r?r==="*"?!1:r.split(/[,\s]/).every(function(i){if(!i)return!0;var n=i.match(/^(.+):(\d+)$/),s=n?n[1]:i,o=n?parseInt(n[2]):0;return o&&o!==e?!0:/^[.*]/.test(s)?(s.charAt(0)==="*"&&(s=s.slice(1)),!Tp.call(t,s)):t!==s}):!0}function rr(t){return process.env[t.toLowerCase()]||process.env[t.toUpperCase()]||""}_a.getProxyForUrl=Ap});var ya=x(nn=>{"use strict";Object.defineProperty(nn,"__esModule",{value:!0});function Bp(t){return function(e,r){return new Promise((i,n)=>{t.call(this,e,r,(s,o)=>{s?n(s):i(o)})})}}nn.default=Bp});var an=x((on,wa)=>{"use strict";var ba=on&&on.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},Lp=require("events"),Np=ba(tr()),Pp=ba(ya()),Lr=Np.default("agent-base");function Fp(t){return Boolean(t)&&typeof t.addRequest=="function"}function sn(){let{stack:t}=new Error;return typeof t!="string"?!1:t.split(`
`).some(e=>e.indexOf("(https.js:")!==-1||e.indexOf("node:https:")!==-1)}function mi(t,e){return new mi.Agent(t,e)}(function(t){class e extends Lp.EventEmitter{constructor(i,n){super();let s=n;typeof i=="function"?this.callback=i:i&&(s=i),this.timeout=null,s&&typeof s.timeout=="number"&&(this.timeout=s.timeout),this.maxFreeSockets=1,this.maxSockets=1,this.maxTotalSockets=1/0,this.sockets={},this.freeSockets={},this.requests={},this.options={}}get defaultPort(){return typeof this.explicitDefaultPort=="number"?this.explicitDefaultPort:sn()?443:80}set defaultPort(i){this.explicitDefaultPort=i}get protocol(){return typeof this.explicitProtocol=="string"?this.explicitProtocol:sn()?"https:":"http:"}set protocol(i){this.explicitProtocol=i}callback(i,n,s){throw new Error('"agent-base" has no default implementation, you must subclass and override `callback()`')}addRequest(i,n){let s=Object.assign({},n);typeof s.secureEndpoint!="boolean"&&(s.secureEndpoint=sn()),s.host==null&&(s.host="localhost"),s.port==null&&(s.port=s.secureEndpoint?443:80),s.protocol==null&&(s.protocol=s.secureEndpoint?"https:":"http:"),s.host&&s.path&&delete s.path,delete s.agent,delete s.hostname,delete s._defaultAgent,delete s.defaultPort,delete s.createConnection,i._last=!0,i.shouldKeepAlive=!1;let o=!1,a=null,l=s.timeout||this.timeout,c=p=>{i._hadError||(i.emit("error",p),i._hadError=!0)},u=()=>{a=null,o=!0;let p=new Error(`A "socket" was not created for HTTP request before ${l}ms`);p.code="ETIMEOUT",c(p)},f=p=>{o||(a!==null&&(clearTimeout(a),a=null),c(p))},h=p=>{if(o)return;if(a!=null&&(clearTimeout(a),a=null),Fp(p)){Lr("Callback returned another Agent instance %o",p.constructor.name),p.addRequest(i,s);return}if(p){p.once("free",()=>{this.freeSocket(p,s)}),i.onSocket(p);return}let d=new Error(`no Duplex stream was returned to agent-base for \`${i.method} ${i.path}\``);c(d)};if(typeof this.callback!="function"){c(new Error("`callback` is not defined"));return}this.promisifiedCallback||(this.callback.length>=3?(Lr("Converting legacy callback function to promise"),this.promisifiedCallback=Pp.default(this.callback)):this.promisifiedCallback=this.callback),typeof l=="number"&&l>0&&(a=setTimeout(u,l)),"port"in s&&typeof s.port!="number"&&(s.port=Number(s.port));try{Lr("Resolving socket for %o request: %o",s.protocol,`${i.method} ${i.path}`),Promise.resolve(this.promisifiedCallback(i,s)).then(h,f)}catch(p){Promise.reject(p).catch(f)}}freeSocket(i,n){Lr("Freeing socket %o %o",i.constructor.name,n),i.destroy()}destroy(){Lr("Destroying agent %o",this.constructor.name)}}t.Agent=e,t.prototype=t.Agent.prototype})(mi||(mi={}));wa.exports=mi});var Ea=x(Pr=>{"use strict";var Mp=Pr&&Pr.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Pr,"__esModule",{value:!0});var Up=Mp(tr()),Nr=Up.default("https-proxy-agent:parse-proxy-response");function Dp(t){return new Promise((e,r)=>{let i=0,n=[];function s(){let f=t.read();f?u(f):t.once("readable",s)}function o(){t.removeListener("end",l),t.removeListener("error",c),t.removeListener("close",a),t.removeListener("readable",s)}function a(f){Nr("onclose had error %o",f)}function l(){Nr("onend")}function c(f){o(),Nr("onerror %o",f),r(f)}function u(f){n.push(f),i+=f.length;let h=Buffer.concat(n,i);if(h.indexOf(`\r
\r
`)===-1){Nr("have not received end of HTTP headers yet..."),s();return}let d=h.toString("ascii",0,h.indexOf(`\r
`)),m=+d.split(" ")[1];Nr("got proxy server response: %o",d),e({statusCode:m,buffered:h})}t.on("error",c),t.on("close",a),t.on("end",l),s()})}Pr.default=Dp});var Ca=x(Bt=>{"use strict";var jp=Bt&&Bt.__awaiter||function(t,e,r,i){function n(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function a(u){try{c(i.next(u))}catch(f){o(f)}}function l(u){try{c(i.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((i=i.apply(t,e||[])).next())})},ir=Bt&&Bt.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Bt,"__esModule",{value:!0});var Sa=ir(require("net")),ka=ir(require("tls")),qp=ir(require("url")),Hp=ir(require("assert")),Vp=ir(tr()),$p=an(),Gp=ir(Ea()),Fr=Vp.default("https-proxy-agent:agent"),ln=class extends $p.Agent{constructor(e){let r;if(typeof e=="string"?r=qp.default.parse(e):r=e,!r)throw new Error("an HTTP(S) proxy server `host` and `port` must be specified!");Fr("creating new HttpsProxyAgent instance: %o",r),super(r);let i=Object.assign({},r);this.secureProxy=r.secureProxy||Yp(i.protocol),i.host=i.hostname||i.host,typeof i.port=="string"&&(i.port=parseInt(i.port,10)),!i.port&&i.host&&(i.port=this.secureProxy?443:80),this.secureProxy&&!("ALPNProtocols"in i)&&(i.ALPNProtocols=["http 1.1"]),i.host&&i.path&&(delete i.path,delete i.pathname),this.proxy=i}callback(e,r){return jp(this,void 0,void 0,function*(){let{proxy:i,secureProxy:n}=this,s;n?(Fr("Creating `tls.Socket`: %o",i),s=ka.default.connect(i)):(Fr("Creating `net.Socket`: %o",i),s=Sa.default.connect(i));let o=Object.assign({},i.headers),l=`CONNECT ${`${r.host}:${r.port}`} HTTP/1.1\r
`;i.auth&&(o["Proxy-Authorization"]=`Basic ${Buffer.from(i.auth).toString("base64")}`);let{host:c,port:u,secureEndpoint:f}=r;Wp(u,f)||(c+=`:${u}`),o.Host=c,o.Connection="close";for(let g of Object.keys(o))l+=`${g}: ${o[g]}\r
`;let h=Gp.default(s);s.write(`${l}\r
`);let{statusCode:p,buffered:d}=yield h;if(p===200){if(e.once("socket",zp),r.secureEndpoint){let g=r.servername||r.host;if(!g)throw new Error('Could not determine "servername"');return Fr("Upgrading socket connection to TLS"),ka.default.connect(Object.assign(Object.assign({},Kp(r,"host","hostname","path","port")),{socket:s,servername:g}))}return s}s.destroy();let m=new Sa.default.Socket;return m.readable=!0,e.once("socket",g=>{Fr("replaying proxy buffer for failed request"),Hp.default(g.listenerCount("data")>0),g.push(d),g.push(null)}),m})}};Bt.default=ln;function zp(t){t.resume()}function Wp(t,e){return Boolean(!e&&t===80||e&&t===443)}function Yp(t){return typeof t=="string"?/^https:?$/i.test(t):!1}function Kp(t,...e){let r={},i;for(i in t)e.includes(i)||(r[i]=t[i]);return r}});var Ia=x((fn,Oa)=>{"use strict";var Zp=fn&&fn.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},cn=Zp(Ca());function un(t){return new cn.default(t)}(function(t){t.HttpsProxyAgent=cn.default,t.prototype=cn.default.prototype})(un||(un={}));Oa.exports=un});var Ra=x((B_,gi)=>{var Aa=Aa||function(t){return Buffer.from(t).toString("base64")};function Xp(t){var e=this,r=Math.round,i=Math.floor,n=new Array(64),s=new Array(64),o=new Array(64),a=new Array(64),l,c,u,f,h=new Array(65535),p=new Array(65535),d=new Array(64),m=new Array(64),g=[],y=0,I=7,w=new Array(64),S=new Array(64),b=new Array(64),$=new Array(256),C=new Array(2048),U,E=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],T=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],B=[0,1,2,3,4,5,6,7,8,9,10,11],G=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],L=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],Q=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],R=[0,1,2,3,4,5,6,7,8,9,10,11],F=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],j=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function z(v){for(var D=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],W=0;W<64;W++){var V=i((D[W]*v+50)/100);V<1?V=1:V>255&&(V=255),n[E[W]]=V}for(var J=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],ee=0;ee<64;ee++){var he=i((J[ee]*v+50)/100);he<1?he=1:he>255&&(he=255),s[E[ee]]=he}for(var pe=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Oe=0,be=0;be<8;be++)for(var O=0;O<8;O++)o[Oe]=1/(n[E[Oe]]*pe[be]*pe[O]*8),a[Oe]=1/(s[E[Oe]]*pe[be]*pe[O]*8),Oe++}function N(v,D){for(var W=0,V=0,J=new Array,ee=1;ee<=16;ee++){for(var he=1;he<=v[ee];he++)J[D[V]]=[],J[D[V]][0]=W,J[D[V]][1]=ee,V++,W++;W*=2}return J}function Se(){l=N(T,B),c=N(Q,R),u=N(G,L),f=N(F,j)}function ce(){for(var v=1,D=2,W=1;W<=15;W++){for(var V=v;V<D;V++)p[32767+V]=W,h[32767+V]=[],h[32767+V][1]=W,h[32767+V][0]=V;for(var J=-(D-1);J<=-v;J++)p[32767+J]=W,h[32767+J]=[],h[32767+J][1]=W,h[32767+J][0]=D-1+J;v<<=1,D<<=1}}function ie(){for(var v=0;v<256;v++)C[v]=19595*v,C[v+256>>0]=38470*v,C[v+512>>0]=7471*v+32768,C[v+768>>0]=-11059*v,C[v+1024>>0]=-21709*v,C[v+1280>>0]=32768*v+8421375,C[v+1536>>0]=-27439*v,C[v+1792>>0]=-5329*v}function ne(v){for(var D=v[0],W=v[1]-1;W>=0;)D&1<<W&&(y|=1<<I),W--,I--,I<0&&(y==255?(_(255),_(0)):_(y),I=7,y=0)}function _(v){g.push(v)}function q(v){_(v>>8&255),_(v&255)}function ge(v,D){var W,V,J,ee,he,pe,Oe,be,O=0,M,Z=8,ke=64;for(M=0;M<Z;++M){W=v[O],V=v[O+1],J=v[O+2],ee=v[O+3],he=v[O+4],pe=v[O+5],Oe=v[O+6],be=v[O+7];var te=W+be,le=W-be,_e=V+Oe,Y=V-Oe,de=J+pe,Fe=J-pe,Ee=ee+he,Xe=ee-he,at=te+Ee,Tt=te-Ee,Yt=_e+de,Kt=_e-de;v[O]=at+Yt,v[O+4]=at-Yt;var Cr=(Kt+Tt)*.707106781;v[O+2]=Tt+Cr,v[O+6]=Tt-Cr,at=Xe+Fe,Yt=Fe+Y,Kt=Y+le;var Or=(at-Kt)*.382683433,oi=.5411961*at+Or,Ir=1.306562965*Kt+Or,Tr=Yt*.707106781,Ar=le+Tr,Rr=le-Tr;v[O+5]=Rr+oi,v[O+3]=Rr-oi,v[O+1]=Ar+Ir,v[O+7]=Ar-Ir,O+=8}for(O=0,M=0;M<Z;++M){W=v[O],V=v[O+8],J=v[O+16],ee=v[O+24],he=v[O+32],pe=v[O+40],Oe=v[O+48],be=v[O+56];var yo=W+be,Xi=W-be,bo=V+Oe,wo=V-Oe,Eo=J+pe,So=J-pe,ko=ee+he,Uh=ee-he,Br=yo+ko,Qi=yo-ko,ai=bo+Eo,li=bo-Eo;v[O]=Br+ai,v[O+32]=Br-ai;var Co=(li+Qi)*.707106781;v[O+16]=Qi+Co,v[O+48]=Qi-Co,Br=Uh+So,ai=So+wo,li=wo+Xi;var Oo=(Br-li)*.382683433,Io=.5411961*Br+Oo,To=1.306562965*li+Oo,Ao=ai*.707106781,Ro=Xi+Ao,Bo=Xi-Ao;v[O+40]=Bo+Io,v[O+24]=Bo-Io,v[O+8]=Ro+To,v[O+56]=Ro-To,O++}var ci;for(M=0;M<ke;++M)ci=v[M]*D[M],d[M]=ci>0?ci+.5|0:ci-.5|0;return d}function ve(){q(65504),q(16),_(74),_(70),_(73),_(70),_(0),_(1),_(1),_(0),q(1),q(1),_(0),_(0)}function ue(v){if(!!v){q(65505),v[0]===69&&v[1]===120&&v[2]===105&&v[3]===102?q(v.length+2):(q(v.length+5+2),_(69),_(120),_(105),_(102),_(0));for(var D=0;D<v.length;D++)_(v[D])}}function fe(v,D){q(65472),q(17),_(8),q(D),q(v),_(3),_(1),_(17),_(0),_(2),_(17),_(1),_(3),_(17),_(1)}function se(){q(65499),q(132),_(0);for(var v=0;v<64;v++)_(n[v]);_(1);for(var D=0;D<64;D++)_(s[D])}function P(){q(65476),q(418),_(0);for(var v=0;v<16;v++)_(T[v+1]);for(var D=0;D<=11;D++)_(B[D]);_(16);for(var W=0;W<16;W++)_(G[W+1]);for(var V=0;V<=161;V++)_(L[V]);_(1);for(var J=0;J<16;J++)_(Q[J+1]);for(var ee=0;ee<=11;ee++)_(R[ee]);_(17);for(var he=0;he<16;he++)_(F[he+1]);for(var pe=0;pe<=161;pe++)_(j[pe])}function A(v){typeof v=="undefined"||v.constructor!==Array||v.forEach(D=>{if(typeof D=="string"){q(65534);var W=D.length;q(W+2);var V;for(V=0;V<W;V++)_(D.charCodeAt(V))}})}function ye(){q(65498),q(12),_(3),_(1),_(0),_(2),_(17),_(3),_(17),_(0),_(63),_(0)}function K(v,D,W,V,J){for(var ee=J[0],he=J[240],pe,Oe=16,be=63,O=64,M=ge(v,D),Z=0;Z<O;++Z)m[E[Z]]=M[Z];var ke=m[0]-W;W=m[0],ke==0?ne(V[0]):(pe=32767+ke,ne(V[p[pe]]),ne(h[pe]));for(var te=63;te>0&&m[te]==0;te--);if(te==0)return ne(ee),W;for(var le=1,_e;le<=te;){for(var Y=le;m[le]==0&&le<=te;++le);var de=le-Y;if(de>=Oe){_e=de>>4;for(var Fe=1;Fe<=_e;++Fe)ne(he);de=de&15}pe=32767+m[le],ne(J[(de<<4)+p[pe]]),ne(h[pe]),le++}return te!=be&&ne(ee),W}function oe(){for(var v=String.fromCharCode,D=0;D<256;D++)$[D]=v(D)}this.encode=function(v,D){var W=new Date().getTime();D&&Ze(D),g=new Array,y=0,I=7,q(65496),ve(),A(v.comments),ue(v.exifBuffer),se(),fe(v.width,v.height),P(),ye();var V=0,J=0,ee=0;y=0,I=7,this.encode.displayName="_encode_";for(var he=v.data,pe=v.width,Oe=v.height,be=pe*4,O=pe*3,M,Z=0,ke,te,le,_e,Y,de,Fe,Ee;Z<Oe;){for(M=0;M<be;){for(_e=be*Z+M,Y=_e,de=-1,Fe=0,Ee=0;Ee<64;Ee++)Fe=Ee>>3,de=(Ee&7)*4,Y=_e+Fe*be+de,Z+Fe>=Oe&&(Y-=be*(Z+1+Fe-Oe)),M+de>=be&&(Y-=M+de-be+4),ke=he[Y++],te=he[Y++],le=he[Y++],w[Ee]=(C[ke]+C[te+256>>0]+C[le+512>>0]>>16)-128,S[Ee]=(C[ke+768>>0]+C[te+1024>>0]+C[le+1280>>0]>>16)-128,b[Ee]=(C[ke+1280>>0]+C[te+1536>>0]+C[le+1792>>0]>>16)-128;V=K(w,o,V,l,u),J=K(S,a,J,c,f),ee=K(b,a,ee,c,f),M+=32}Z+=8}if(I>=0){var Xe=[];Xe[1]=I+1,Xe[0]=(1<<I+1)-1,ne(Xe)}if(q(65497),typeof gi=="undefined")return new Uint8Array(g);return Buffer.from(g);var at,Tt};function Ze(v){if(v<=0&&(v=1),v>100&&(v=100),U!=v){var D=0;v<50?D=Math.floor(5e3/v):D=Math.floor(200-v*2),z(D),U=v}}function tt(){var v=new Date().getTime();t||(t=50),oe(),Se(),ce(),ie(),Ze(t);var D=new Date().getTime()-v}tt()}typeof gi!="undefined"?gi.exports=Ta:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].encode=Ta);function Ta(t,e){typeof e=="undefined"&&(e=50);var r=new Xp(e),i=r.encode(t,e);return{data:i,width:t.width,height:t.height}}});var La=x((L_,pn)=>{var hn=function(){"use strict";var e=new Int32Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),r=4017,i=799,n=3406,s=2276,o=1567,a=3784,l=5793,c=2896;function u(){}function f(I,w){for(var S=0,b=[],$,C,U=16;U>0&&!I[U-1];)U--;b.push({children:[],index:0});var E=b[0],T;for($=0;$<U;$++){for(C=0;C<I[$];C++){for(E=b.pop(),E.children[E.index]=w[S];E.index>0;){if(b.length===0)throw new Error("Could not recreate Huffman Table");E=b.pop()}for(E.index++,b.push(E);b.length<=$;)b.push(T={children:[],index:0}),E.children[E.index]=T.children,E=T;S++}$+1<U&&(b.push(T={children:[],index:0}),E.children[E.index]=T.children,E=T)}return b[0].children}function h(I,w,S,b,$,C,U,E,T,B){var G=S.precision,L=S.samplesPerLine,Q=S.scanLines,R=S.mcusPerLine,F=S.progressive,j=S.maxH,z=S.maxV,N=w,Se=0,ce=0;function ie(){if(ce>0)return ce--,Se>>ce&1;if(Se=I[w++],Se==255){var O=I[w++];if(O)throw new Error("unexpected marker: "+(Se<<8|O).toString(16))}return ce=7,Se>>>7}function ne(O){for(var M=O,Z;(Z=ie())!==null;){if(M=M[Z],typeof M=="number")return M;if(typeof M!="object")throw new Error("invalid huffman sequence")}return null}function _(O){for(var M=0;O>0;){var Z=ie();if(Z===null)return;M=M<<1|Z,O--}return M}function q(O){var M=_(O);return M>=1<<O-1?M:M+(-1<<O)+1}function ge(O,M){var Z=ne(O.huffmanTableDC),ke=Z===0?0:q(Z);M[0]=O.pred+=ke;for(var te=1;te<64;){var le=ne(O.huffmanTableAC),_e=le&15,Y=le>>4;if(_e===0){if(Y<15)break;te+=16;continue}te+=Y;var de=e[te];M[de]=q(_e),te++}}function ve(O,M){var Z=ne(O.huffmanTableDC),ke=Z===0?0:q(Z)<<T;M[0]=O.pred+=ke}function ue(O,M){M[0]|=ie()<<T}var fe=0;function se(O,M){if(fe>0){fe--;return}for(var Z=C,ke=U;Z<=ke;){var te=ne(O.huffmanTableAC),le=te&15,_e=te>>4;if(le===0){if(_e<15){fe=_(_e)+(1<<_e)-1;break}Z+=16;continue}Z+=_e;var Y=e[Z];M[Y]=q(le)*(1<<T),Z++}}var P=0,A;function ye(O,M){for(var Z=C,ke=U,te=0;Z<=ke;){var le=e[Z],_e=M[le]<0?-1:1;switch(P){case 0:var Y=ne(O.huffmanTableAC),de=Y&15,te=Y>>4;if(de===0)te<15?(fe=_(te)+(1<<te),P=4):(te=16,P=1);else{if(de!==1)throw new Error("invalid ACn encoding");A=q(de),P=te?2:3}continue;case 1:case 2:M[le]?M[le]+=(ie()<<T)*_e:(te--,te===0&&(P=P==2?3:0));break;case 3:M[le]?M[le]+=(ie()<<T)*_e:(M[le]=A<<T,P=0);break;case 4:M[le]&&(M[le]+=(ie()<<T)*_e);break}Z++}P===4&&(fe--,fe===0&&(P=0))}function K(O,M,Z,ke,te){var le=Z/R|0,_e=Z%R,Y=le*O.v+ke,de=_e*O.h+te;O.blocks[Y]===void 0&&B.tolerantDecoding||M(O,O.blocks[Y][de])}function oe(O,M,Z){var ke=Z/O.blocksPerLine|0,te=Z%O.blocksPerLine;O.blocks[ke]===void 0&&B.tolerantDecoding||M(O,O.blocks[ke][te])}var Ze=b.length,tt,v,D,W,V,J;F?C===0?J=E===0?ve:ue:J=E===0?se:ye:J=ge;var ee=0,he,pe;Ze==1?pe=b[0].blocksPerLine*b[0].blocksPerColumn:pe=R*S.mcusPerColumn,$||($=pe);for(var Oe,be;ee<pe;){for(v=0;v<Ze;v++)b[v].pred=0;if(fe=0,Ze==1)for(tt=b[0],V=0;V<$;V++)oe(tt,J,ee),ee++;else for(V=0;V<$;V++){for(v=0;v<Ze;v++)for(tt=b[v],Oe=tt.h,be=tt.v,D=0;D<be;D++)for(W=0;W<Oe;W++)K(tt,J,ee,D,W);if(ee++,ee===pe)break}if(ee===pe)do{if(I[w]===255&&I[w+1]!==0)break;w+=1}while(w<I.length-2);if(ce=0,he=I[w]<<8|I[w+1],he<65280)throw new Error("marker was not found");if(he>=65488&&he<=65495)w+=2;else break}return w-N}function p(I,w){var S=[],b=w.blocksPerLine,$=w.blocksPerColumn,C=b<<3,U=new Int32Array(64),E=new Uint8Array(64);function T(N,Se,ce){var ie=w.quantizationTable,ne,_,q,ge,ve,ue,fe,se,P,A=ce,ye;for(ye=0;ye<64;ye++)A[ye]=N[ye]*ie[ye];for(ye=0;ye<8;++ye){var K=8*ye;if(A[1+K]==0&&A[2+K]==0&&A[3+K]==0&&A[4+K]==0&&A[5+K]==0&&A[6+K]==0&&A[7+K]==0){P=l*A[0+K]+512>>10,A[0+K]=P,A[1+K]=P,A[2+K]=P,A[3+K]=P,A[4+K]=P,A[5+K]=P,A[6+K]=P,A[7+K]=P;continue}ne=l*A[0+K]+128>>8,_=l*A[4+K]+128>>8,q=A[2+K],ge=A[6+K],ve=c*(A[1+K]-A[7+K])+128>>8,se=c*(A[1+K]+A[7+K])+128>>8,ue=A[3+K]<<4,fe=A[5+K]<<4,P=ne-_+1>>1,ne=ne+_+1>>1,_=P,P=q*a+ge*o+128>>8,q=q*o-ge*a+128>>8,ge=P,P=ve-fe+1>>1,ve=ve+fe+1>>1,fe=P,P=se+ue+1>>1,ue=se-ue+1>>1,se=P,P=ne-ge+1>>1,ne=ne+ge+1>>1,ge=P,P=_-q+1>>1,_=_+q+1>>1,q=P,P=ve*s+se*n+2048>>12,ve=ve*n-se*s+2048>>12,se=P,P=ue*i+fe*r+2048>>12,ue=ue*r-fe*i+2048>>12,fe=P,A[0+K]=ne+se,A[7+K]=ne-se,A[1+K]=_+fe,A[6+K]=_-fe,A[2+K]=q+ue,A[5+K]=q-ue,A[3+K]=ge+ve,A[4+K]=ge-ve}for(ye=0;ye<8;++ye){var oe=ye;if(A[8+oe]==0&&A[16+oe]==0&&A[24+oe]==0&&A[32+oe]==0&&A[40+oe]==0&&A[48+oe]==0&&A[56+oe]==0){P=l*ce[ye+0]+8192>>14,A[0+oe]=P,A[8+oe]=P,A[16+oe]=P,A[24+oe]=P,A[32+oe]=P,A[40+oe]=P,A[48+oe]=P,A[56+oe]=P;continue}ne=l*A[0+oe]+2048>>12,_=l*A[32+oe]+2048>>12,q=A[16+oe],ge=A[48+oe],ve=c*(A[8+oe]-A[56+oe])+2048>>12,se=c*(A[8+oe]+A[56+oe])+2048>>12,ue=A[24+oe],fe=A[40+oe],P=ne-_+1>>1,ne=ne+_+1>>1,_=P,P=q*a+ge*o+2048>>12,q=q*o-ge*a+2048>>12,ge=P,P=ve-fe+1>>1,ve=ve+fe+1>>1,fe=P,P=se+ue+1>>1,ue=se-ue+1>>1,se=P,P=ne-ge+1>>1,ne=ne+ge+1>>1,ge=P,P=_-q+1>>1,_=_+q+1>>1,q=P,P=ve*s+se*n+2048>>12,ve=ve*n-se*s+2048>>12,se=P,P=ue*i+fe*r+2048>>12,ue=ue*r-fe*i+2048>>12,fe=P,A[0+oe]=ne+se,A[56+oe]=ne-se,A[8+oe]=_+fe,A[48+oe]=_-fe,A[16+oe]=q+ue,A[40+oe]=q-ue,A[24+oe]=ge+ve,A[32+oe]=ge-ve}for(ye=0;ye<64;++ye){var Ze=128+(A[ye]+8>>4);Se[ye]=Ze<0?0:Ze>255?255:Ze}}y(C*$*8);for(var B,G,L=0;L<$;L++){var Q=L<<3;for(B=0;B<8;B++)S.push(new Uint8Array(C));for(var R=0;R<b;R++){T(w.blocks[L][R],E,U);var F=0,j=R<<3;for(G=0;G<8;G++){var z=S[Q+G];for(B=0;B<8;B++)z[j+B]=E[F++]}}}return S}function d(I){return I<0?0:I>255?255:I}u.prototype={load:function(w){var S=new XMLHttpRequest;S.open("GET",w,!0),S.responseType="arraybuffer",S.onload=function(){var b=new Uint8Array(S.response||S.mozResponseArrayBuffer);this.parse(b),this.onload&&this.onload()}.bind(this),S.send(null)},parse:function(w){var S=this.opts.maxResolutionInMP*1e3*1e3,b=0,$=w.length;function C(){var Y=w[b]<<8|w[b+1];return b+=2,Y}function U(){var Y=C(),de=w.subarray(b,b+Y-2);return b+=de.length,de}function E(Y){var de=1,Fe=1,Ee,Xe;for(Xe in Y.components)Y.components.hasOwnProperty(Xe)&&(Ee=Y.components[Xe],de<Ee.h&&(de=Ee.h),Fe<Ee.v&&(Fe=Ee.v));var at=Math.ceil(Y.samplesPerLine/8/de),Tt=Math.ceil(Y.scanLines/8/Fe);for(Xe in Y.components)if(Y.components.hasOwnProperty(Xe)){Ee=Y.components[Xe];var Yt=Math.ceil(Math.ceil(Y.samplesPerLine/8)*Ee.h/de),Kt=Math.ceil(Math.ceil(Y.scanLines/8)*Ee.v/Fe),Cr=at*Ee.h,Or=Tt*Ee.v,oi=Or*Cr,Ir=[];y(oi*256);for(var Tr=0;Tr<Or;Tr++){for(var Ar=[],Rr=0;Rr<Cr;Rr++)Ar.push(new Int32Array(64));Ir.push(Ar)}Ee.blocksPerLine=Yt,Ee.blocksPerColumn=Kt,Ee.blocks=Ir}Y.maxH=de,Y.maxV=Fe,Y.mcusPerLine=at,Y.mcusPerColumn=Tt}var T=null,B=null,G=null,L,Q,R=[],F=[],j=[],z=[],N=C(),Se=-1;if(this.comments=[],N!=65496)throw new Error("SOI not found");for(N=C();N!=65497;){var ce,ie,ne;switch(N){case 65280:break;case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var _=U();if(N===65534){var q=String.fromCharCode.apply(null,_);this.comments.push(q)}N===65504&&_[0]===74&&_[1]===70&&_[2]===73&&_[3]===70&&_[4]===0&&(T={version:{major:_[5],minor:_[6]},densityUnits:_[7],xDensity:_[8]<<8|_[9],yDensity:_[10]<<8|_[11],thumbWidth:_[12],thumbHeight:_[13],thumbData:_.subarray(14,14+3*_[12]*_[13])}),N===65505&&_[0]===69&&_[1]===120&&_[2]===105&&_[3]===102&&_[4]===0&&(this.exifBuffer=_.subarray(5,_.length)),N===65518&&_[0]===65&&_[1]===100&&_[2]===111&&_[3]===98&&_[4]===101&&_[5]===0&&(B={version:_[6],flags0:_[7]<<8|_[8],flags1:_[9]<<8|_[10],transformCode:_[11]});break;case 65499:for(var ge=C(),ve=ge+b-2;b<ve;){var ue=w[b++];y(256);var fe=new Int32Array(64);if(ue>>4===0)for(ie=0;ie<64;ie++){var se=e[ie];fe[se]=w[b++]}else if(ue>>4===1)for(ie=0;ie<64;ie++){var se=e[ie];fe[se]=C()}else throw new Error("DQT: invalid table spec");R[ue&15]=fe}break;case 65472:case 65473:case 65474:C(),L={},L.extended=N===65473,L.progressive=N===65474,L.precision=w[b++],L.scanLines=C(),L.samplesPerLine=C(),L.components={},L.componentsOrder=[];var P=L.scanLines*L.samplesPerLine;if(P>S){var A=Math.ceil((P-S)/1e6);throw new Error(`maxResolutionInMP limit exceeded by ${A}MP`)}var ye=w[b++],K,oe=0,Ze=0;for(ce=0;ce<ye;ce++){K=w[b];var tt=w[b+1]>>4,v=w[b+1]&15,D=w[b+2];if(tt<=0||v<=0)throw new Error("Invalid sampling factor, expected values above 0");L.componentsOrder.push(K),L.components[K]={h:tt,v,quantizationIdx:D},b+=3}E(L),F.push(L);break;case 65476:var W=C();for(ce=2;ce<W;){var V=w[b++],J=new Uint8Array(16),ee=0;for(ie=0;ie<16;ie++,b++)ee+=J[ie]=w[b];y(16+ee);var he=new Uint8Array(ee);for(ie=0;ie<ee;ie++,b++)he[ie]=w[b];ce+=17+ee,(V>>4===0?z:j)[V&15]=f(J,he)}break;case 65501:C(),Q=C();break;case 65500:C(),C();break;case 65498:var pe=C(),Oe=w[b++],be=[],O;for(ce=0;ce<Oe;ce++){O=L.components[w[b++]];var M=w[b++];O.huffmanTableDC=z[M>>4],O.huffmanTableAC=j[M&15],be.push(O)}var Z=w[b++],ke=w[b++],te=w[b++],le=h(w,b,L,be,Q,Z,ke,te>>4,te&15,this.opts);b+=le;break;case 65535:w[b]!==255&&b--;break;default:if(w[b-3]==255&&w[b-2]>=192&&w[b-2]<=254){b-=3;break}else if(N===224||N==225){if(Se!==-1)throw new Error(`first unknown JPEG marker at offset ${Se.toString(16)}, second unknown JPEG marker ${N.toString(16)} at offset ${(b-1).toString(16)}`);Se=b-1;let Y=C();if(w[b+Y-2]===255){b+=Y-2;break}}throw new Error("unknown JPEG marker "+N.toString(16))}N=C()}if(F.length!=1)throw new Error("only single frame JPEGs supported");for(var ce=0;ce<F.length;ce++){var _e=F[ce].components;for(var ie in _e)_e[ie].quantizationTable=R[_e[ie].quantizationIdx],delete _e[ie].quantizationIdx}this.width=L.samplesPerLine,this.height=L.scanLines,this.jfif=T,this.adobe=B,this.components=[];for(var ce=0;ce<L.componentsOrder.length;ce++){var O=L.components[L.componentsOrder[ce]];this.components.push({lines:p(L,O),scaleX:O.h/L.maxH,scaleY:O.v/L.maxV})}},getData:function(w,S){var b=this.width/w,$=this.height/S,C,U,E,T,B,G,L,Q,R,F,j=0,z,N,Se,ce,ie,ne,_,q,ge,ve,ue,fe=w*S*this.components.length;y(fe);var se=new Uint8Array(fe);switch(this.components.length){case 1:for(C=this.components[0],F=0;F<S;F++)for(B=C.lines[0|F*C.scaleY*$],R=0;R<w;R++)z=B[0|R*C.scaleX*b],se[j++]=z;break;case 2:for(C=this.components[0],U=this.components[1],F=0;F<S;F++)for(B=C.lines[0|F*C.scaleY*$],G=U.lines[0|F*U.scaleY*$],R=0;R<w;R++)z=B[0|R*C.scaleX*b],se[j++]=z,z=G[0|R*U.scaleX*b],se[j++]=z;break;case 3:for(ue=!0,this.adobe&&this.adobe.transformCode?ue=!0:typeof this.opts.colorTransform!="undefined"&&(ue=!!this.opts.colorTransform),C=this.components[0],U=this.components[1],E=this.components[2],F=0;F<S;F++)for(B=C.lines[0|F*C.scaleY*$],G=U.lines[0|F*U.scaleY*$],L=E.lines[0|F*E.scaleY*$],R=0;R<w;R++)ue?(z=B[0|R*C.scaleX*b],N=G[0|R*U.scaleX*b],Se=L[0|R*E.scaleX*b],q=d(z+1.402*(Se-128)),ge=d(z-.3441363*(N-128)-.71413636*(Se-128)),ve=d(z+1.772*(N-128))):(q=B[0|R*C.scaleX*b],ge=G[0|R*U.scaleX*b],ve=L[0|R*E.scaleX*b]),se[j++]=q,se[j++]=ge,se[j++]=ve;break;case 4:if(!this.adobe)throw new Error("Unsupported color mode (4 components)");for(ue=!1,this.adobe&&this.adobe.transformCode?ue=!0:typeof this.opts.colorTransform!="undefined"&&(ue=!!this.opts.colorTransform),C=this.components[0],U=this.components[1],E=this.components[2],T=this.components[3],F=0;F<S;F++)for(B=C.lines[0|F*C.scaleY*$],G=U.lines[0|F*U.scaleY*$],L=E.lines[0|F*E.scaleY*$],Q=T.lines[0|F*T.scaleY*$],R=0;R<w;R++)ue?(z=B[0|R*C.scaleX*b],N=G[0|R*U.scaleX*b],Se=L[0|R*E.scaleX*b],ce=Q[0|R*T.scaleX*b],ie=255-d(z+1.402*(Se-128)),ne=255-d(z-.3441363*(N-128)-.71413636*(Se-128)),_=255-d(z+1.772*(N-128))):(ie=B[0|R*C.scaleX*b],ne=G[0|R*U.scaleX*b],_=L[0|R*E.scaleX*b],ce=Q[0|R*T.scaleX*b]),se[j++]=255-ie,se[j++]=255-ne,se[j++]=255-_,se[j++]=255-ce;break;default:throw new Error("Unsupported color mode")}return se},copyToImageData:function(w,S){var b=w.width,$=w.height,C=w.data,U=this.getData(b,$),E=0,T=0,B,G,L,Q,R,F,j,z,N;switch(this.components.length){case 1:for(G=0;G<$;G++)for(B=0;B<b;B++)L=U[E++],C[T++]=L,C[T++]=L,C[T++]=L,S&&(C[T++]=255);break;case 3:for(G=0;G<$;G++)for(B=0;B<b;B++)j=U[E++],z=U[E++],N=U[E++],C[T++]=j,C[T++]=z,C[T++]=N,S&&(C[T++]=255);break;case 4:for(G=0;G<$;G++)for(B=0;B<b;B++)R=U[E++],F=U[E++],L=U[E++],Q=U[E++],j=255-d(R*(1-Q/255)+Q),z=255-d(F*(1-Q/255)+Q),N=255-d(L*(1-Q/255)+Q),C[T++]=j,C[T++]=z,C[T++]=N,S&&(C[T++]=255);break;default:throw new Error("Unsupported color mode")}}};var m=0,g=0;function y(I=0){var w=m+I;if(w>g){var S=Math.ceil((w-g)/1024/1024);throw new Error(`maxMemoryUsageInMB limit exceeded by at least ${S}MB`)}m=w}return u.resetMaxMemoryUsage=function(I){m=0,g=I},u.getBytesAllocated=function(){return m},u.requestMemoryAllocation=y,u}();typeof pn!="undefined"?pn.exports=Ba:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].decode=Ba);function Ba(t,e={}){var r={colorTransform:void 0,useTArray:!1,formatAsRGBA:!0,tolerantDecoding:!0,maxResolutionInMP:100,maxMemoryUsageInMB:512},i={...r,...e},n=new Uint8Array(t),s=new hn;s.opts=i,hn.resetMaxMemoryUsage(i.maxMemoryUsageInMB*1024*1024),s.parse(n);var o=i.formatAsRGBA?4:3,a=s.width*s.height*o;try{hn.requestMemoryAllocation(a);var l={width:s.width,height:s.height,exifBuffer:s.exifBuffer,data:i.useTArray?new Uint8Array(a):Buffer.alloc(a)};s.comments.length>0&&(l.comments=s.comments)}catch(c){throw c instanceof RangeError?new Error("Could not allocate enough memory for the image. Required: "+a):c instanceof ReferenceError&&c.message==="Buffer is not defined"?new Error("Buffer is not globally defined in this environment. Consider setting useTArray to true"):c}return s.copyToImageData(l,i.formatAsRGBA),l}});var Pa=x((N_,Na)=>{var Qp=Ra(),Jp=La();Na.exports={encode:Qp,decode:Jp}});var Ma=x((P_,Fa)=>{"use strict";function vi(){this._types=Object.create(null),this._extensions=Object.create(null);for(let t=0;t<arguments.length;t++)this.define(arguments[t]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}vi.prototype.define=function(t,e){for(let r in t){let i=t[r].map(function(n){return n.toLowerCase()});r=r.toLowerCase();for(let n=0;n<i.length;n++){let s=i[n];if(s[0]!=="*"){if(!e&&s in this._types)throw new Error('Attempt to change mapping for "'+s+'" extension from "'+this._types[s]+'" to "'+r+'". Pass `force=true` to allow this, otherwise remove "'+s+'" from the list of extensions for "'+r+'".');this._types[s]=r}}if(e||!this._extensions[r]){let n=i[0];this._extensions[r]=n[0]!=="*"?n:n.substr(1)}}};vi.prototype.getType=function(t){t=String(t);let e=t.replace(/^.*[/\\]/,"").toLowerCase(),r=e.replace(/^.*\./,"").toLowerCase(),i=e.length<t.length;return(r.length<e.length-1||!i)&&this._types[r]||null};vi.prototype.getExtension=function(t){return t=/^\s*([^;\s]*)/.test(t)&&RegExp.$1,t&&this._extensions[t.toLowerCase()]||null};Fa.exports=vi});var Da=x((F_,Ua)=>{Ua.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}});var qa=x((M_,ja)=>{ja.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}});var Va=x((U_,Ha)=>{"use strict";var ed=Ma();Ha.exports=new ed(Da(),qa())});var Ga=x((D_,$a)=>{$a.exports=function(t,e){for(var r=[],i=0;i<t.length;i++){var n=e(t[i],i);td(n)?r.push.apply(r,n):r.push(n)}return r};var td=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"}});var Za=x((j_,Ka)=>{"use strict";Ka.exports=Wa;function Wa(t,e,r){t instanceof RegExp&&(t=za(t,r)),e instanceof RegExp&&(e=za(e,r));var i=Ya(t,e,r);return i&&{start:i[0],end:i[1],pre:r.slice(0,i[0]),body:r.slice(i[0]+t.length,i[1]),post:r.slice(i[1]+e.length)}}function za(t,e){var r=e.match(t);return r?r[0]:null}Wa.range=Ya;function Ya(t,e,r){var i,n,s,o,a,l=r.indexOf(t),c=r.indexOf(e,l+1),u=l;if(l>=0&&c>0){if(t===e)return[l,c];for(i=[],s=r.length;u>=0&&!a;)u==l?(i.push(u),l=r.indexOf(t,u+1)):i.length==1?a=[i.pop(),c]:(n=i.pop(),n<s&&(s=n,o=c),c=r.indexOf(e,u+1)),u=l<c&&l>=0?l:c;i.length&&(a=[s,o])}return a}});var nl=x((q_,il)=>{var rd=Ga(),Xa=Za();il.exports=sd;var Qa="\0SLASH"+Math.random()+"\0",Ja="\0OPEN"+Math.random()+"\0",mn="\0CLOSE"+Math.random()+"\0",el="\0COMMA"+Math.random()+"\0",tl="\0PERIOD"+Math.random()+"\0";function dn(t){return parseInt(t,10)==t?parseInt(t,10):t.charCodeAt(0)}function id(t){return t.split("\\\\").join(Qa).split("\\{").join(Ja).split("\\}").join(mn).split("\\,").join(el).split("\\.").join(tl)}function nd(t){return t.split(Qa).join("\\").split(Ja).join("{").split(mn).join("}").split(el).join(",").split(tl).join(".")}function rl(t){if(!t)return[""];var e=[],r=Xa("{","}",t);if(!r)return t.split(",");var i=r.pre,n=r.body,s=r.post,o=i.split(",");o[o.length-1]+="{"+n+"}";var a=rl(s);return s.length&&(o[o.length-1]+=a.shift(),o.push.apply(o,a)),e.push.apply(e,o),e}function sd(t){return t?(t.substr(0,2)==="{}"&&(t="\\{\\}"+t.substr(2)),nr(id(t),!0).map(nd)):[]}function od(t){return"{"+t+"}"}function ad(t){return/^-?0\d/.test(t)}function ld(t,e){return t<=e}function cd(t,e){return t>=e}function nr(t,e){var r=[],i=Xa("{","}",t);if(!i||/\$$/.test(i.pre))return[t];var n=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(i.body),s=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(i.body),o=n||s,a=i.body.indexOf(",")>=0;if(!o&&!a)return i.post.match(/,.*\}/)?(t=i.pre+"{"+i.body+mn+i.post,nr(t)):[t];var l;if(o)l=i.body.split(/\.\./);else if(l=rl(i.body),l.length===1&&(l=nr(l[0],!1).map(od),l.length===1)){var u=i.post.length?nr(i.post,!1):[""];return u.map(function(B){return i.pre+l[0]+B})}var c=i.pre,u=i.post.length?nr(i.post,!1):[""],f;if(o){var h=dn(l[0]),p=dn(l[1]),d=Math.max(l[0].length,l[1].length),m=l.length==3?Math.abs(dn(l[2])):1,g=ld,y=p<h;y&&(m*=-1,g=cd);var I=l.some(ad);f=[];for(var w=h;g(w,p);w+=m){var S;if(s)S=String.fromCharCode(w),S==="\\"&&(S="");else if(S=String(w),I){var b=d-S.length;if(b>0){var $=new Array(b+1).join("0");w<0?S="-"+$+S.slice(1):S=$+S}}f.push(S)}}else f=rd(l,function(T){return nr(T,!1)});for(var C=0;C<f.length;C++)for(var U=0;U<u.length;U++){var E=c+f[C]+u[U];(!e||o||E)&&r.push(E)}return r}});var Ur=x((H_,cl)=>{cl.exports=Ye;Ye.Minimatch=Ue;var Mr=function(){try{return require("path")}catch{}}()||{sep:"/"};Ye.sep=Mr.sep;var _n=Ye.GLOBSTAR=Ue.GLOBSTAR={},ud=nl(),sl={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},gn="[^/]",vn=gn+"*?",fd="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?",hd="(?:(?!(?:\\/|^)\\.).)*?",ol=pd("().*{}+?[]^$\\!");function pd(t){return t.split("").reduce(function(e,r){return e[r]=!0,e},{})}var al=/\/+/;Ye.filter=dd;function dd(t,e){return e=e||{},function(r,i,n){return Ye(r,t,e)}}function gt(t,e){e=e||{};var r={};return Object.keys(t).forEach(function(i){r[i]=t[i]}),Object.keys(e).forEach(function(i){r[i]=e[i]}),r}Ye.defaults=function(t){if(!t||typeof t!="object"||!Object.keys(t).length)return Ye;var e=Ye,r=function(n,s,o){return e(n,s,gt(t,o))};return r.Minimatch=function(n,s){return new e.Minimatch(n,gt(t,s))},r.Minimatch.defaults=function(n){return e.defaults(gt(t,n)).Minimatch},r.filter=function(n,s){return e.filter(n,gt(t,s))},r.defaults=function(n){return e.defaults(gt(t,n))},r.makeRe=function(n,s){return e.makeRe(n,gt(t,s))},r.braceExpand=function(n,s){return e.braceExpand(n,gt(t,s))},r.match=function(i,n,s){return e.match(i,n,gt(t,s))},r};Ue.defaults=function(t){return Ye.defaults(t).Minimatch};function Ye(t,e,r){return xi(e),r||(r={}),!r.nocomment&&e.charAt(0)==="#"?!1:new Ue(e,r).match(t)}function Ue(t,e){if(!(this instanceof Ue))return new Ue(t,e);xi(t),e||(e={}),t=t.trim(),!e.allowWindowsEscape&&Mr.sep!=="/"&&(t=t.split(Mr.sep).join("/")),this.options=e,this.set=[],this.pattern=t,this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.partial=!!e.partial,this.make()}Ue.prototype.debug=function(){};Ue.prototype.make=md;function md(){var t=this.pattern,e=this.options;if(!e.nocomment&&t.charAt(0)==="#"){this.comment=!0;return}if(!t){this.empty=!0;return}this.parseNegate();var r=this.globSet=this.braceExpand();e.debug&&(this.debug=function(){console.error.apply(console,arguments)}),this.debug(this.pattern,r),r=this.globParts=r.map(function(i){return i.split(al)}),this.debug(this.pattern,r),r=r.map(function(i,n,s){return i.map(this.parse,this)},this),this.debug(this.pattern,r),r=r.filter(function(i){return i.indexOf(!1)===-1}),this.debug(this.pattern,r),this.set=r}Ue.prototype.parseNegate=gd;function gd(){var t=this.pattern,e=!1,r=this.options,i=0;if(!r.nonegate){for(var n=0,s=t.length;n<s&&t.charAt(n)==="!";n++)e=!e,i++;i&&(this.pattern=t.substr(i)),this.negate=e}}Ye.braceExpand=function(t,e){return ll(t,e)};Ue.prototype.braceExpand=ll;function ll(t,e){return e||(this instanceof Ue?e=this.options:e={}),t=typeof t=="undefined"?this.pattern:t,xi(t),e.nobrace||!/\{(?:(?!\{).)*\}/.test(t)?[t]:ud(t)}var vd=1024*64,xi=function(t){if(typeof t!="string")throw new TypeError("invalid pattern");if(t.length>vd)throw new TypeError("pattern is too long")};Ue.prototype.parse=_d;var _i={};function _d(t,e){xi(t);var r=this.options;if(t==="**")if(r.noglobstar)t="*";else return _n;if(t==="")return"";var i="",n=!!r.nocase,s=!1,o=[],a=[],l,c=!1,u=-1,f=-1,h=t.charAt(0)==="."?"":r.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",p=this;function d(){if(l){switch(l){case"*":i+=vn,n=!0;break;case"?":i+=gn,n=!0;break;default:i+="\\"+l;break}p.debug("clearStateChar %j %j",l,i),l=!1}}for(var m=0,g=t.length,y;m<g&&(y=t.charAt(m));m++){if(this.debug("%s	%s %s %j",t,m,i,y),s&&ol[y]){i+="\\"+y,s=!1;continue}switch(y){case"/":return!1;case"\\":d(),s=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s	%s %s %j <-- stateChar",t,m,i,y),c){this.debug("  in class"),y==="!"&&m===f+1&&(y="^"),i+=y;continue}p.debug("call clearStateChar %j",l),d(),l=y,r.noext&&d();continue;case"(":if(c){i+="(";continue}if(!l){i+="\\(";continue}o.push({type:l,start:m-1,reStart:i.length,open:sl[l].open,close:sl[l].close}),i+=l==="!"?"(?:(?!(?:":"(?:",this.debug("plType %j %j",l,i),l=!1;continue;case")":if(c||!o.length){i+="\\)";continue}d(),n=!0;var I=o.pop();i+=I.close,I.type==="!"&&a.push(I),I.reEnd=i.length;continue;case"|":if(c||!o.length||s){i+="\\|",s=!1;continue}d(),i+="|";continue;case"[":if(d(),c){i+="\\"+y;continue}c=!0,f=m,u=i.length,i+=y;continue;case"]":if(m===f+1||!c){i+="\\"+y,s=!1;continue}var w=t.substring(f+1,m);try{RegExp("["+w+"]")}catch{var S=this.parse(w,_i);i=i.substr(0,u)+"\\["+S[0]+"\\]",n=n||S[1],c=!1;continue}n=!0,c=!1,i+=y;continue;default:d(),s?s=!1:ol[y]&&!(y==="^"&&c)&&(i+="\\"),i+=y}}for(c&&(w=t.substr(f+1),S=this.parse(w,_i),i=i.substr(0,u)+"\\["+S[0],n=n||S[1]),I=o.pop();I;I=o.pop()){var b=i.slice(I.reStart+I.open.length);this.debug("setting tail",i,I),b=b.replace(/((?:\\{2}){0,64})(\\?)\|/g,function(Se,ce,ie){return ie||(ie="\\"),ce+ce+ie+"|"}),this.debug(`tail=%j
   %s`,b,b,I,i);var $=I.type==="*"?vn:I.type==="?"?gn:"\\"+I.type;n=!0,i=i.slice(0,I.reStart)+$+"\\("+b}d(),s&&(i+="\\\\");var C=!1;switch(i.charAt(0)){case"[":case".":case"(":C=!0}for(var U=a.length-1;U>-1;U--){var E=a[U],T=i.slice(0,E.reStart),B=i.slice(E.reStart,E.reEnd-8),G=i.slice(E.reEnd-8,E.reEnd),L=i.slice(E.reEnd);G+=L;var Q=T.split("(").length-1,R=L;for(m=0;m<Q;m++)R=R.replace(/\)[+*?]?/,"");L=R;var F="";L===""&&e!==_i&&(F="$");var j=T+B+L+F+G;i=j}if(i!==""&&n&&(i="(?=.)"+i),C&&(i=h+i),e===_i)return[i,n];if(!n)return yd(t);var z=r.nocase?"i":"";try{var N=new RegExp("^"+i+"$",z)}catch{return new RegExp("$.")}return N._glob=t,N._src=i,N}Ye.makeRe=function(t,e){return new Ue(t,e||{}).makeRe()};Ue.prototype.makeRe=xd;function xd(){if(this.regexp||this.regexp===!1)return this.regexp;var t=this.set;if(!t.length)return this.regexp=!1,this.regexp;var e=this.options,r=e.noglobstar?vn:e.dot?fd:hd,i=e.nocase?"i":"",n=t.map(function(s){return s.map(function(o){return o===_n?r:typeof o=="string"?bd(o):o._src}).join("\\/")}).join("|");n="^(?:"+n+")$",this.negate&&(n="^(?!"+n+").*$");try{this.regexp=new RegExp(n,i)}catch{this.regexp=!1}return this.regexp}Ye.match=function(t,e,r){r=r||{};var i=new Ue(e,r);return t=t.filter(function(n){return i.match(n)}),i.options.nonull&&!t.length&&t.push(e),t};Ue.prototype.match=function(e,r){if(typeof r=="undefined"&&(r=this.partial),this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return e==="";if(e==="/"&&r)return!0;var i=this.options;Mr.sep!=="/"&&(e=e.split(Mr.sep).join("/")),e=e.split(al),this.debug(this.pattern,"split",e);var n=this.set;this.debug(this.pattern,"set",n);var s,o;for(o=e.length-1;o>=0&&(s=e[o],!s);o--);for(o=0;o<n.length;o++){var a=n[o],l=e;i.matchBase&&a.length===1&&(l=[s]);var c=this.matchOne(l,a,r);if(c)return i.flipNegate?!0:!this.negate}return i.flipNegate?!1:this.negate};Ue.prototype.matchOne=function(t,e,r){var i=this.options;this.debug("matchOne",{this:this,file:t,pattern:e}),this.debug("matchOne",t.length,e.length);for(var n=0,s=0,o=t.length,a=e.length;n<o&&s<a;n++,s++){this.debug("matchOne loop");var l=e[s],c=t[n];if(this.debug(e,l,c),l===!1)return!1;if(l===_n){this.debug("GLOBSTAR",[e,l,c]);var u=n,f=s+1;if(f===a){for(this.debug("** at the end");n<o;n++)if(t[n]==="."||t[n]===".."||!i.dot&&t[n].charAt(0)===".")return!1;return!0}for(;u<o;){var h=t[u];if(this.debug(`
globstar while`,t,u,e,f,h),this.matchOne(t.slice(u),e.slice(f),r))return this.debug("globstar found match!",u,o,h),!0;if(h==="."||h===".."||!i.dot&&h.charAt(0)==="."){this.debug("dot detected!",t,u,e,f);break}this.debug("globstar swallow a segment, and continue"),u++}return!!(r&&(this.debug(`
>>> no match, partial?`,t,u,e,f),u===o))}var p;if(typeof l=="string"?(p=c===l,this.debug("string match",l,c,p)):(p=c.match(l),this.debug("pattern match",l,c,p)),!p)return!1}if(n===o&&s===a)return!0;if(n===o)return r;if(s===a)return n===o-1&&t[n]==="";throw new Error("wtf?")};function yd(t){return t.replace(/\\(.)/g,"$1")}function bd(t){return t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}});var yn=x((V_,fl)=>{"use strict";var ul=require("fs"),xn;function wd(){try{return ul.statSync("/.dockerenv"),!0}catch{return!1}}function Ed(){try{return ul.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}fl.exports=()=>(xn===void 0&&(xn=wd()||Ed()),xn)});var dl=x(($_,bn)=>{"use strict";var Sd=require("os"),kd=require("fs"),hl=yn(),pl=()=>{if(process.platform!=="linux")return!1;if(Sd.release().toLowerCase().includes("microsoft"))return!hl();try{return kd.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!hl():!1}catch{return!1}};process.env.__IS_WSL_TEST__?bn.exports=pl:bn.exports=pl()});var gl=x((G_,ml)=>{"use strict";ml.exports=(t,e,r)=>{let i=n=>Object.defineProperty(t,e,{value:n,enumerable:!0,writable:!0});return Object.defineProperty(t,e,{configurable:!0,enumerable:!0,get(){let n=r();return i(n),n},set(n){i(n)}}),t}});var El=x((z_,wl)=>{var Cd=require("path"),Od=require("child_process"),{promises:wn,constants:bl}=require("fs"),yi=dl(),Id=yn(),En=gl(),vl=Cd.join(__dirname,"xdg-open"),{platform:sr,arch:_l}=process,Td=(()=>{let t="/mnt/",e;return async function(){if(e)return e;let r="/etc/wsl.conf",i=!1;try{await wn.access(r,bl.F_OK),i=!0}catch{}if(!i)return t;let n=await wn.readFile(r,{encoding:"utf8"}),s=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(n);return s?(e=s.groups.mountPoint.trim(),e=e.endsWith("/")?e:`${e}/`,e):t}})(),xl=async(t,e)=>{let r;for(let i of t)try{return await e(i)}catch(n){r=n}throw r},bi=async t=>{if(t={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...t},Array.isArray(t.app))return xl(t.app,a=>bi({...t,app:a}));let{name:e,arguments:r=[]}=t.app||{};if(r=[...r],Array.isArray(e))return xl(e,a=>bi({...t,app:{name:a,arguments:r}}));let i,n=[],s={};if(sr==="darwin")i="open",t.wait&&n.push("--wait-apps"),t.background&&n.push("--background"),t.newInstance&&n.push("--new"),e&&n.push("-a",e);else if(sr==="win32"||yi&&!Id()){let a=await Td();i=yi?`${a}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,n.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),yi||(s.windowsVerbatimArguments=!0);let l=["Start"];t.wait&&l.push("-Wait"),e?(l.push(`"\`"${e}\`""`,"-ArgumentList"),t.target&&r.unshift(t.target)):t.target&&l.push(`"${t.target}"`),r.length>0&&(r=r.map(c=>`"\`"${c}\`""`),l.push(r.join(","))),t.target=Buffer.from(l.join(" "),"utf16le").toString("base64")}else{if(e)i=e;else{let a=!__dirname||__dirname==="/",l=!1;try{await wn.access(vl,bl.X_OK),l=!0}catch{}i=process.versions.electron||sr==="android"||a||!l?"xdg-open":vl}r.length>0&&n.push(...r),t.wait||(s.stdio="ignore",s.detached=!0)}t.target&&n.push(t.target),sr==="darwin"&&r.length>0&&n.push("--args",...r);let o=Od.spawn(i,n,s);return t.wait?new Promise((a,l)=>{o.once("error",l),o.once("close",c=>{if(t.allowNonzeroExitCode&&c>0){l(new Error(`Exited with code ${c}`));return}a(o)})}):(o.unref(),o)},Sn=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a `target`");return bi({...e,target:t})},Ad=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a `name`");let{arguments:r=[]}=e||{};if(r!=null&&!Array.isArray(r))throw new TypeError("Expected `appArguments` as Array type");return bi({...e,app:{name:t,arguments:r}})};function yl(t){if(typeof t=="string"||Array.isArray(t))return t;let{[_l]:e}=t;if(!e)throw new Error(`${_l} is not supported`);return e}function kn({[sr]:t},{wsl:e}){if(e&&yi)return yl(e);if(!t)throw new Error(`${sr} is not supported`);return yl(t)}var wi={};En(wi,"chrome",()=>kn({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));En(wi,"firefox",()=>kn({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));En(wi,"edge",()=>kn({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));Sn.apps=wi;Sn.openApp=Ad;wl.exports=Sn});var Cn=x((W_,kl)=>{"use strict";var Rd=require("util"),Sl=require("stream"),nt=kl.exports=function(){Sl.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};Rd.inherits(nt,Sl);nt.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),process.nextTick(function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}.bind(this))};nt.prototype.write=function(t,e){if(!this.writable)return this.emit("error",new Error("Stream not writable")),!1;let r;return Buffer.isBuffer(t)?r=t:r=Buffer.from(t,e||this._encoding),this._buffers.push(r),this._buffered+=r.length,this._process(),this._reads&&this._reads.length===0&&(this._paused=!0),this.writable&&!this._paused};nt.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(this._buffers.length===0?this._end():(this._buffers.push(null),this._process()))};nt.prototype.destroySoon=nt.prototype.end;nt.prototype._end=function(){this._reads.length>0&&this.emit("error",new Error("Unexpected end of input")),this.destroy()};nt.prototype.destroy=function(){!this._buffers||(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))};nt.prototype._processReadAllowingLess=function(t){this._reads.shift();let e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))};nt.prototype._processRead=function(t){this._reads.shift();let e=0,r=0,i=Buffer.alloc(t.length);for(;e<t.length;){let n=this._buffers[r++],s=Math.min(n.length,t.length-e);n.copy(i,e,0,s),e+=s,s!==n.length&&(this._buffers[--r]=n.slice(s))}r>0&&this._buffers.splice(0,r),this._buffered-=t.length,t.func.call(this,i)};nt.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else if(this._buffered>=t.length)this._processRead(t);else break}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}});var In=x(On=>{"use strict";var vt=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];On.getImagePasses=function(t,e){let r=[],i=t%8,n=e%8,s=(t-i)/8,o=(e-n)/8;for(let a=0;a<vt.length;a++){let l=vt[a],c=s*l.x.length,u=o*l.y.length;for(let f=0;f<l.x.length&&l.x[f]<i;f++)c++;for(let f=0;f<l.y.length&&l.y[f]<n;f++)u++;c>0&&u>0&&r.push({width:c,height:u,index:a})}return r};On.getInterlaceIterator=function(t){return function(e,r,i){let n=e%vt[i].x.length,s=(e-n)/vt[i].x.length*8+vt[i].x[n],o=r%vt[i].y.length,a=(r-o)/vt[i].y.length*8+vt[i].y[o];return s*4+a*t*4}}});var Tn=x((K_,Cl)=>{"use strict";Cl.exports=function(e,r,i){let n=e+r-i,s=Math.abs(n-e),o=Math.abs(n-r),a=Math.abs(n-i);return s<=o&&s<=a?e:o<=a?r:i}});var An=x((Z_,Il)=>{"use strict";var Bd=In(),Ld=Tn();function Ol(t,e,r){let i=t*e;return r!==8&&(i=Math.ceil(i/(8/r))),i}var or=Il.exports=function(t,e){let r=t.width,i=t.height,n=t.interlace,s=t.bpp,o=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],n){let a=Bd.getImagePasses(r,i);for(let l=0;l<a.length;l++)this._images.push({byteWidth:Ol(a[l].width,s,o),height:a[l].height,lineIndex:0})}else this._images.push({byteWidth:Ol(r,s,o),height:i,lineIndex:0});o===8?this._xComparison=s:o===16?this._xComparison=s*2:this._xComparison=1};or.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))};or.prototype._unFilterType1=function(t,e,r){let i=this._xComparison,n=i-1;for(let s=0;s<r;s++){let o=t[1+s],a=s>n?e[s-i]:0;e[s]=o+a}};or.prototype._unFilterType2=function(t,e,r){let i=this._lastLine;for(let n=0;n<r;n++){let s=t[1+n],o=i?i[n]:0;e[n]=s+o}};or.prototype._unFilterType3=function(t,e,r){let i=this._xComparison,n=i-1,s=this._lastLine;for(let o=0;o<r;o++){let a=t[1+o],l=s?s[o]:0,c=o>n?e[o-i]:0,u=Math.floor((c+l)/2);e[o]=a+u}};or.prototype._unFilterType4=function(t,e,r){let i=this._xComparison,n=i-1,s=this._lastLine;for(let o=0;o<r;o++){let a=t[1+o],l=s?s[o]:0,c=o>n?e[o-i]:0,u=o>n&&s?s[o-i]:0,f=Ld(c,l,u);e[o]=a+f}};or.prototype._reverseFilterLine=function(t){let e=t[0],r,i=this._images[this._imageIndex],n=i.byteWidth;if(e===0)r=t.slice(1,n+1);else switch(r=Buffer.alloc(n),e){case 1:this._unFilterType1(t,r,n);break;case 2:this._unFilterType2(t,r,n);break;case 3:this._unFilterType3(t,r,n);break;case 4:this._unFilterType4(t,r,n);break;default:throw new Error("Unrecognised filter type - "+e)}this.write(r),i.lineIndex++,i.lineIndex>=i.height?(this._lastLine=null,this._imageIndex++,i=this._images[this._imageIndex]):this._lastLine=r,i?this.read(i.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}});var Rl=x((X_,Al)=>{"use strict";var Nd=require("util"),Tl=Cn(),Pd=An(),Fd=Al.exports=function(t){Tl.call(this);let e=[],r=this;this._filter=new Pd(t,{read:this.read.bind(this),write:function(i){e.push(i)},complete:function(){r.emit("complete",Buffer.concat(e))}}),this._filter.start()};Nd.inherits(Fd,Tl)});var ar=x((Q_,Bl)=>{"use strict";Bl.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}});var Ln=x((J_,Ll)=>{"use strict";var Rn=[];(function(){for(let t=0;t<256;t++){let e=t;for(let r=0;r<8;r++)e&1?e=3988292384^e>>>1:e=e>>>1;Rn[t]=e}})();var Bn=Ll.exports=function(){this._crc=-1};Bn.prototype.write=function(t){for(let e=0;e<t.length;e++)this._crc=Rn[(this._crc^t[e])&255]^this._crc>>>8;return!0};Bn.prototype.crc32=function(){return this._crc^-1};Bn.crc32=function(t){let e=-1;for(let r=0;r<t.length;r++)e=Rn[(e^t[r])&255]^e>>>8;return e^-1}});var Nn=x((ex,Nl)=>{"use strict";var Ae=ar(),Md=Ln(),Le=Nl.exports=function(t,e){this._options=t,t.checkCRC=t.checkCRC!==!1,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[Ae.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[Ae.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[Ae.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[Ae.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[Ae.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[Ae.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};Le.prototype.start=function(){this.read(Ae.PNG_SIGNATURE.length,this._parseSignature.bind(this))};Le.prototype._parseSignature=function(t){let e=Ae.PNG_SIGNATURE;for(let r=0;r<e.length;r++)if(t[r]!==e[r]){this.error(new Error("Invalid file signature"));return}this.read(8,this._parseChunkBegin.bind(this))};Le.prototype._parseChunkBegin=function(t){let e=t.readUInt32BE(0),r=t.readUInt32BE(4),i="";for(let s=4;s<8;s++)i+=String.fromCharCode(t[s]);let n=Boolean(t[4]&32);if(!this._hasIHDR&&r!==Ae.TYPE_IHDR){this.error(new Error("Expected IHDR on beggining"));return}if(this._crc=new Md,this._crc.write(Buffer.from(i)),this._chunks[r])return this._chunks[r](e);if(!n){this.error(new Error("Unsupported critical chunk type "+i));return}this.read(e+4,this._skipChunk.bind(this))};Le.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))};Le.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))};Le.prototype._parseChunkEnd=function(t){let e=t.readInt32BE(0),r=this._crc.crc32();if(this._options.checkCRC&&r!==e){this.error(new Error("Crc error - "+e+" - "+r));return}this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))};Le.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))};Le.prototype._parseIHDR=function(t){this._crc.write(t);let e=t.readUInt32BE(0),r=t.readUInt32BE(4),i=t[8],n=t[9],s=t[10],o=t[11],a=t[12];if(i!==8&&i!==4&&i!==2&&i!==1&&i!==16){this.error(new Error("Unsupported bit depth "+i));return}if(!(n in Ae.COLORTYPE_TO_BPP_MAP)){this.error(new Error("Unsupported color type"));return}if(s!==0){this.error(new Error("Unsupported compression method"));return}if(o!==0){this.error(new Error("Unsupported filter method"));return}if(a!==0&&a!==1){this.error(new Error("Unsupported interlace method"));return}this._colorType=n;let l=Ae.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:r,depth:i,interlace:Boolean(a),palette:Boolean(n&Ae.COLORTYPE_PALETTE),color:Boolean(n&Ae.COLORTYPE_COLOR),alpha:Boolean(n&Ae.COLORTYPE_ALPHA),bpp:l,colorType:n}),this._handleChunkEnd()};Le.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))};Le.prototype._parsePLTE=function(t){this._crc.write(t);let e=Math.floor(t.length/3);for(let r=0;r<e;r++)this._palette.push([t[r*3],t[r*3+1],t[r*3+2],255]);this.palette(this._palette),this._handleChunkEnd()};Le.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))};Le.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===Ae.COLORTYPE_PALETTE_COLOR){if(this._palette.length===0){this.error(new Error("Transparency chunk must be after palette"));return}if(t.length>this._palette.length){this.error(new Error("More transparent colors than palette size"));return}for(let e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===Ae.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===Ae.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()};Le.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))};Le.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/Ae.GAMMA_DIVISION),this._handleChunkEnd()};Le.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))};Le.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===Ae.COLORTYPE_PALETTE_COLOR&&this._palette.length===0)throw new Error("Expected palette not found");this.inflateData(e);let r=t-e.length;r>0?this._handleIDAT(r):this._handleChunkEnd()};Le.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))};Le.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}});var Pn=x(Fl=>{"use strict";var Pl=In(),Ud=[function(){},function(t,e,r,i){if(i===e.length)throw new Error("Ran out of data");let n=e[i];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=255},function(t,e,r,i){if(i+1>=e.length)throw new Error("Ran out of data");let n=e[i];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=e[i+1]},function(t,e,r,i){if(i+2>=e.length)throw new Error("Ran out of data");t[r]=e[i],t[r+1]=e[i+1],t[r+2]=e[i+2],t[r+3]=255},function(t,e,r,i){if(i+3>=e.length)throw new Error("Ran out of data");t[r]=e[i],t[r+1]=e[i+1],t[r+2]=e[i+2],t[r+3]=e[i+3]}],Dd=[function(){},function(t,e,r,i){let n=e[0];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=i},function(t,e,r){let i=e[0];t[r]=i,t[r+1]=i,t[r+2]=i,t[r+3]=e[1]},function(t,e,r,i){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=i},function(t,e,r){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=e[3]}];function jd(t,e){let r=[],i=0;function n(){if(i===t.length)throw new Error("Ran out of data");let s=t[i];i++;let o,a,l,c,u,f,h,p;switch(e){default:throw new Error("unrecognised depth");case 16:h=t[i],i++,r.push((s<<8)+h);break;case 4:h=s&15,p=s>>4,r.push(p,h);break;case 2:u=s&3,f=s>>2&3,h=s>>4&3,p=s>>6&3,r.push(p,h,f,u);break;case 1:o=s&1,a=s>>1&1,l=s>>2&1,c=s>>3&1,u=s>>4&1,f=s>>5&1,h=s>>6&1,p=s>>7&1,r.push(p,h,f,u,c,l,a,o);break}}return{get:function(s){for(;r.length<s;)n();let o=r.slice(0,s);return r=r.slice(s),o},resetAfterLine:function(){r.length=0},end:function(){if(i!==t.length)throw new Error("extra data found")}}}function qd(t,e,r,i,n,s){let o=t.width,a=t.height,l=t.index;for(let c=0;c<a;c++)for(let u=0;u<o;u++){let f=r(u,c,l);Ud[i](e,n,f,s),s+=i}return s}function Hd(t,e,r,i,n,s){let o=t.width,a=t.height,l=t.index;for(let c=0;c<a;c++){for(let u=0;u<o;u++){let f=n.get(i),h=r(u,c,l);Dd[i](e,f,h,s)}n.resetAfterLine()}}Fl.dataToBitMap=function(t,e){let r=e.width,i=e.height,n=e.depth,s=e.bpp,o=e.interlace,a;n!==8&&(a=jd(t,n));let l;n<=8?l=Buffer.alloc(r*i*4):l=new Uint16Array(r*i*4);let c=Math.pow(2,n)-1,u=0,f,h;if(o)f=Pl.getImagePasses(r,i),h=Pl.getInterlaceIterator(r,i);else{let p=0;h=function(){let d=p;return p+=4,d},f=[{width:r,height:i}]}for(let p=0;p<f.length;p++)n===8?u=qd(f[p],l,h,s,t,u):Hd(f[p],l,h,s,a,c);if(n===8){if(u!==t.length)throw new Error("extra data found")}else a.end();return l}});var Fn=x((rx,Ml)=>{"use strict";function Vd(t,e,r,i,n){let s=0;for(let o=0;o<i;o++)for(let a=0;a<r;a++){let l=n[t[s]];if(!l)throw new Error("index "+t[s]+" not in palette");for(let c=0;c<4;c++)e[s+c]=l[c];s+=4}}function $d(t,e,r,i,n){let s=0;for(let o=0;o<i;o++)for(let a=0;a<r;a++){let l=!1;if(n.length===1?n[0]===t[s]&&(l=!0):n[0]===t[s]&&n[1]===t[s+1]&&n[2]===t[s+2]&&(l=!0),l)for(let c=0;c<4;c++)e[s+c]=0;s+=4}}function Gd(t,e,r,i,n){let s=255,o=Math.pow(2,n)-1,a=0;for(let l=0;l<i;l++)for(let c=0;c<r;c++){for(let u=0;u<4;u++)e[a+u]=Math.floor(t[a+u]*s/o+.5);a+=4}}Ml.exports=function(t,e,r=!1){let i=e.depth,n=e.width,s=e.height,o=e.colorType,a=e.transColor,l=e.palette,c=t;return o===3?Vd(t,c,n,s,l):(a&&$d(t,c,n,s,a),i!==8&&!r&&(i===16&&(c=Buffer.alloc(n*s*4)),Gd(t,c,n,s,i))),c}});var jl=x((ix,Dl)=>{"use strict";var zd=require("util"),Mn=require("zlib"),Ul=Cn(),Wd=Rl(),Yd=Nn(),Kd=Pn(),Zd=Fn(),lt=Dl.exports=function(t){Ul.call(this),this._parser=new Yd(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};zd.inherits(lt,Ul);lt.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0};lt.prototype._inflateData=function(t){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=Mn.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let r=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,i=Math.max(r,Mn.Z_MIN_CHUNK);this._inflate=Mn.createInflate({chunkSize:i});let n=r,s=this.emit.bind(this,"error");this._inflate.on("error",function(a){!n||s(a)}),this._filter.on("complete",this._complete.bind(this));let o=this._filter.write.bind(this._filter);this._inflate.on("data",function(a){!n||(a.length>n&&(a=a.slice(0,n)),n-=a.length,o(a))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(t)};lt.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new Wd(this._bitmapInfo)};lt.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t};lt.prototype._handlePalette=function(t){this._bitmapInfo.palette=t};lt.prototype._simpleTransparency=function(){this._metaData.alpha=!0};lt.prototype._headersFinished=function(){this.emit("metadata",this._metaData)};lt.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))};lt.prototype._complete=function(t){if(this.errord)return;let e;try{let r=Kd.dataToBitMap(t,this._bitmapInfo);e=Zd(r,this._bitmapInfo,this._options.skipRescale),r=null}catch(r){this._handleError(r);return}this.emit("parsed",e)}});var Hl=x((nx,ql)=>{"use strict";var Qe=ar();ql.exports=function(t,e,r,i){let n=[Qe.COLORTYPE_COLOR_ALPHA,Qe.COLORTYPE_ALPHA].indexOf(i.colorType)!==-1;if(i.colorType===i.inputColorType){let d=function(){let m=new ArrayBuffer(2);return new DataView(m).setInt16(0,256,!0),new Int16Array(m)[0]!==256}();if(i.bitDepth===8||i.bitDepth===16&&d)return t}let s=i.bitDepth!==16?t:new Uint16Array(t.buffer),o=255,a=Qe.COLORTYPE_TO_BPP_MAP[i.inputColorType];a===4&&!i.inputHasAlpha&&(a=3);let l=Qe.COLORTYPE_TO_BPP_MAP[i.colorType];i.bitDepth===16&&(o=65535,l*=2);let c=Buffer.alloc(e*r*l),u=0,f=0,h=i.bgColor||{};h.red===void 0&&(h.red=o),h.green===void 0&&(h.green=o),h.blue===void 0&&(h.blue=o);function p(){let d,m,g,y=o;switch(i.inputColorType){case Qe.COLORTYPE_COLOR_ALPHA:y=s[u+3],d=s[u],m=s[u+1],g=s[u+2];break;case Qe.COLORTYPE_COLOR:d=s[u],m=s[u+1],g=s[u+2];break;case Qe.COLORTYPE_ALPHA:y=s[u+1],d=s[u],m=d,g=d;break;case Qe.COLORTYPE_GRAYSCALE:d=s[u],m=d,g=d;break;default:throw new Error("input color type:"+i.inputColorType+" is not supported at present")}return i.inputHasAlpha&&(n||(y/=o,d=Math.min(Math.max(Math.round((1-y)*h.red+y*d),0),o),m=Math.min(Math.max(Math.round((1-y)*h.green+y*m),0),o),g=Math.min(Math.max(Math.round((1-y)*h.blue+y*g),0),o))),{red:d,green:m,blue:g,alpha:y}}for(let d=0;d<r;d++)for(let m=0;m<e;m++){let g=p(s,u);switch(i.colorType){case Qe.COLORTYPE_COLOR_ALPHA:case Qe.COLORTYPE_COLOR:i.bitDepth===8?(c[f]=g.red,c[f+1]=g.green,c[f+2]=g.blue,n&&(c[f+3]=g.alpha)):(c.writeUInt16BE(g.red,f),c.writeUInt16BE(g.green,f+2),c.writeUInt16BE(g.blue,f+4),n&&c.writeUInt16BE(g.alpha,f+6));break;case Qe.COLORTYPE_ALPHA:case Qe.COLORTYPE_GRAYSCALE:{let y=(g.red+g.green+g.blue)/3;i.bitDepth===8?(c[f]=y,n&&(c[f+1]=g.alpha)):(c.writeUInt16BE(y,f),n&&c.writeUInt16BE(g.alpha,f+2));break}default:throw new Error("unrecognised color Type "+i.colorType)}u+=a,f+=l}return c}});var Gl=x((sx,$l)=>{"use strict";var Vl=Tn();function Xd(t,e,r,i,n){for(let s=0;s<r;s++)i[n+s]=t[e+s]}function Qd(t,e,r){let i=0,n=e+r;for(let s=e;s<n;s++)i+=Math.abs(t[s]);return i}function Jd(t,e,r,i,n,s){for(let o=0;o<r;o++){let a=o>=s?t[e+o-s]:0,l=t[e+o]-a;i[n+o]=l}}function em(t,e,r,i){let n=0;for(let s=0;s<r;s++){let o=s>=i?t[e+s-i]:0,a=t[e+s]-o;n+=Math.abs(a)}return n}function tm(t,e,r,i,n){for(let s=0;s<r;s++){let o=e>0?t[e+s-r]:0,a=t[e+s]-o;i[n+s]=a}}function rm(t,e,r){let i=0,n=e+r;for(let s=e;s<n;s++){let o=e>0?t[s-r]:0,a=t[s]-o;i+=Math.abs(a)}return i}function im(t,e,r,i,n,s){for(let o=0;o<r;o++){let a=o>=s?t[e+o-s]:0,l=e>0?t[e+o-r]:0,c=t[e+o]-(a+l>>1);i[n+o]=c}}function nm(t,e,r,i){let n=0;for(let s=0;s<r;s++){let o=s>=i?t[e+s-i]:0,a=e>0?t[e+s-r]:0,l=t[e+s]-(o+a>>1);n+=Math.abs(l)}return n}function sm(t,e,r,i,n,s){for(let o=0;o<r;o++){let a=o>=s?t[e+o-s]:0,l=e>0?t[e+o-r]:0,c=e>0&&o>=s?t[e+o-(r+s)]:0,u=t[e+o]-Vl(a,l,c);i[n+o]=u}}function om(t,e,r,i){let n=0;for(let s=0;s<r;s++){let o=s>=i?t[e+s-i]:0,a=e>0?t[e+s-r]:0,l=e>0&&s>=i?t[e+s-(r+i)]:0,c=t[e+s]-Vl(o,a,l);n+=Math.abs(c)}return n}var am={0:Xd,1:Jd,2:tm,3:im,4:sm},lm={0:Qd,1:em,2:rm,3:nm,4:om};$l.exports=function(t,e,r,i,n){let s;if(!("filterType"in i)||i.filterType===-1)s=[0,1,2,3,4];else if(typeof i.filterType=="number")s=[i.filterType];else throw new Error("unrecognised filter types");i.bitDepth===16&&(n*=2);let o=e*n,a=0,l=0,c=Buffer.alloc((o+1)*r),u=s[0];for(let f=0;f<r;f++){if(s.length>1){let h=1/0;for(let p=0;p<s.length;p++){let d=lm[s[p]](t,l,o,n);d<h&&(u=s[p],h=d)}}c[a]=u,a++,am[u](t,l,o,c,a,n),a+=o,l+=o}return c}});var Un=x((ox,zl)=>{"use strict";var qe=ar(),cm=Ln(),um=Hl(),fm=Gl(),hm=require("zlib"),_t=zl.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32*1024,t.deflateLevel=t.deflateLevel!=null?t.deflateLevel:9,t.deflateStrategy=t.deflateStrategy!=null?t.deflateStrategy:3,t.inputHasAlpha=t.inputHasAlpha!=null?t.inputHasAlpha:!0,t.deflateFactory=t.deflateFactory||hm.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType=typeof t.colorType=="number"?t.colorType:qe.COLORTYPE_COLOR_ALPHA,t.inputColorType=typeof t.inputColorType=="number"?t.inputColorType:qe.COLORTYPE_COLOR_ALPHA,[qe.COLORTYPE_GRAYSCALE,qe.COLORTYPE_COLOR,qe.COLORTYPE_COLOR_ALPHA,qe.COLORTYPE_ALPHA].indexOf(t.colorType)===-1)throw new Error("option color type:"+t.colorType+" is not supported at present");if([qe.COLORTYPE_GRAYSCALE,qe.COLORTYPE_COLOR,qe.COLORTYPE_COLOR_ALPHA,qe.COLORTYPE_ALPHA].indexOf(t.inputColorType)===-1)throw new Error("option input color type:"+t.inputColorType+" is not supported at present");if(t.bitDepth!==8&&t.bitDepth!==16)throw new Error("option bit depth:"+t.bitDepth+" is not supported at present")};_t.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}};_t.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())};_t.prototype.filterData=function(t,e,r){let i=um(t,e,r,this._options),n=qe.COLORTYPE_TO_BPP_MAP[this._options.colorType];return fm(i,e,r,this._options,n)};_t.prototype._packChunk=function(t,e){let r=e?e.length:0,i=Buffer.alloc(r+12);return i.writeUInt32BE(r,0),i.writeUInt32BE(t,4),e&&e.copy(i,8),i.writeInt32BE(cm.crc32(i.slice(4,i.length-4)),i.length-4),i};_t.prototype.packGAMA=function(t){let e=Buffer.alloc(4);return e.writeUInt32BE(Math.floor(t*qe.GAMMA_DIVISION),0),this._packChunk(qe.TYPE_gAMA,e)};_t.prototype.packIHDR=function(t,e){let r=Buffer.alloc(13);return r.writeUInt32BE(t,0),r.writeUInt32BE(e,4),r[8]=this._options.bitDepth,r[9]=this._options.colorType,r[10]=0,r[11]=0,r[12]=0,this._packChunk(qe.TYPE_IHDR,r)};_t.prototype.packIDAT=function(t){return this._packChunk(qe.TYPE_IDAT,t)};_t.prototype.packIEND=function(){return this._packChunk(qe.TYPE_IEND,null)}});var Zl=x((ax,Kl)=>{"use strict";var pm=require("util"),Wl=require("stream"),dm=ar(),mm=Un(),Yl=Kl.exports=function(t){Wl.call(this);let e=t||{};this._packer=new mm(e),this._deflate=this._packer.createDeflate(),this.readable=!0};pm.inherits(Yl,Wl);Yl.prototype.pack=function(t,e,r,i){this.emit("data",Buffer.from(dm.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,r)),i&&this.emit("data",this._packer.packGAMA(i));let n=this._packer.filterData(t,e,r);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(s){this.emit("data",this._packer.packIDAT(s))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(n)}});var rc=x((Dr,tc)=>{"use strict";var Xl=require("assert").ok,lr=require("zlib"),gm=require("util"),Ql=require("buffer").kMaxLength;function Lt(t){if(!(this instanceof Lt))return new Lt(t);t&&t.chunkSize<lr.Z_MIN_CHUNK&&(t.chunkSize=lr.Z_MIN_CHUNK),lr.Inflate.call(this,t),this._offset=this._offset===void 0?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&t.maxLength!=null&&(this._maxLength=t.maxLength)}function vm(t){return new Lt(t)}function Jl(t,e){e&&process.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}Lt.prototype._processChunk=function(t,e,r){if(typeof r=="function")return lr.Inflate._processChunk.call(this,t,e,r);let i=this,n=t&&t.length,s=this._chunkSize-this._offset,o=this._maxLength,a=0,l=[],c=0,u;this.on("error",function(d){u=d});function f(d,m){if(i._hadError)return;let g=s-m;if(Xl(g>=0,"have should not go down"),g>0){let y=i._buffer.slice(i._offset,i._offset+g);if(i._offset+=g,y.length>o&&(y=y.slice(0,o)),l.push(y),c+=y.length,o-=y.length,o===0)return!1}return(m===0||i._offset>=i._chunkSize)&&(s=i._chunkSize,i._offset=0,i._buffer=Buffer.allocUnsafe(i._chunkSize)),m===0?(a+=n-d,n=d,!0):!1}Xl(this._handle,"zlib binding closed");let h;do h=this._handle.writeSync(e,t,a,n,this._buffer,this._offset,s),h=h||this._writeState;while(!this._hadError&&f(h[0],h[1]));if(this._hadError)throw u;if(c>=Ql)throw Jl(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+Ql.toString(16)+" bytes");let p=Buffer.concat(l,c);return Jl(this),p};gm.inherits(Lt,lr.Inflate);function _m(t,e){if(typeof e=="string"&&(e=Buffer.from(e)),!(e instanceof Buffer))throw new TypeError("Not a string or buffer");let r=t._finishFlushFlag;return r==null&&(r=lr.Z_FINISH),t._processChunk(e,r)}function ec(t,e){return _m(new Lt(e),t)}tc.exports=Dr=ec;Dr.Inflate=Lt;Dr.createInflate=vm;Dr.inflateSync=ec});var Dn=x((lx,nc)=>{"use strict";var ic=nc.exports=function(t){this._buffer=t,this._reads=[]};ic.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})};ic.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let t=this._reads[0];if(this._buffer.length&&(this._buffer.length>=t.length||t.allowLess)){this._reads.shift();let e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}else break}if(this._reads.length>0)throw new Error("There are some read requests waitng on finished stream");if(this._buffer.length>0)throw new Error("unrecognised content at end of stream")}});var oc=x(sc=>{"use strict";var xm=Dn(),ym=An();sc.process=function(t,e){let r=[],i=new xm(t);return new ym(e,{read:i.read.bind(i),write:function(s){r.push(s)},complete:function(){}}).start(),i.process(),Buffer.concat(r)}});var uc=x((ux,cc)=>{"use strict";var ac=!0,lc=require("zlib"),bm=rc();lc.deflateSync||(ac=!1);var wm=Dn(),Em=oc(),Sm=Nn(),km=Pn(),Cm=Fn();cc.exports=function(t,e){if(!ac)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let r;function i(S){r=S}let n;function s(S){n=S}function o(S){n.transColor=S}function a(S){n.palette=S}function l(){n.alpha=!0}let c;function u(S){c=S}let f=[];function h(S){f.push(S)}let p=new wm(t);if(new Sm(e,{read:p.read.bind(p),error:i,metadata:s,gamma:u,palette:a,transColor:o,inflateData:h,simpleTransparency:l}).start(),p.process(),r)throw r;let m=Buffer.concat(f);f.length=0;let g;if(n.interlace)g=lc.inflateSync(m);else{let b=((n.width*n.bpp*n.depth+7>>3)+1)*n.height;g=bm(m,{chunkSize:b,maxLength:b})}if(m=null,!g||!g.length)throw new Error("bad png - invalid inflate data response");let y=Em.process(g,n);m=null;let I=km.dataToBitMap(y,n);y=null;let w=Cm(I,n,e.skipRescale);return n.data=w,n.gamma=c||0,n}});var dc=x((fx,pc)=>{"use strict";var fc=!0,hc=require("zlib");hc.deflateSync||(fc=!1);var Om=ar(),Im=Un();pc.exports=function(t,e){if(!fc)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let r=e||{},i=new Im(r),n=[];n.push(Buffer.from(Om.PNG_SIGNATURE)),n.push(i.packIHDR(t.width,t.height)),t.gamma&&n.push(i.packGAMA(t.gamma));let s=i.filterData(t.data,t.width,t.height),o=hc.deflateSync(s,i.getDeflateOptions());if(s=null,!o||!o.length)throw new Error("bad png - invalid compressed data response");return n.push(i.packIDAT(o)),n.push(i.packIEND()),Buffer.concat(n)}});var mc=x(jn=>{"use strict";var Tm=uc(),Am=dc();jn.read=function(t,e){return Tm(t,e||{})};jn.write=function(t,e){return Am(t,e)}});var _c=x(vc=>{"use strict";var Rm=require("util"),gc=require("stream"),Bm=jl(),Lm=Zl(),Nm=mc(),$e=vc.PNG=function(t){gc.call(this),t=t||{},this.width=t.width|0,this.height=t.height|0,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new Bm(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(e){this.data=e,this.emit("parsed",e)}.bind(this)),this._packer=new Lm(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};Rm.inherits($e,gc);$e.sync=Nm;$e.prototype.pack=function(){return!this.data||!this.data.length?(this.emit("error","No data provided"),this):(process.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)),this)};$e.prototype.parse=function(t,e){if(e){let r,i;r=function(n){this.removeListener("error",i),this.data=n,e(null,this)}.bind(this),i=function(n){this.removeListener("parsed",r),e(n,null)}.bind(this),this.once("parsed",r),this.once("error",i)}return this.end(t),this};$e.prototype.write=function(t){return this._parser.write(t),!0};$e.prototype.end=function(t){this._parser.end(t)};$e.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)};$e.prototype._gamma=function(t){this.gamma=t};$e.prototype._handleClose=function(){!this._parser.writable&&!this._packer.readable&&this.emit("close")};$e.bitblt=function(t,e,r,i,n,s,o,a){if(r|=0,i|=0,n|=0,s|=0,o|=0,a|=0,r>t.width||i>t.height||r+n>t.width||i+s>t.height)throw new Error("bitblt reading outside image");if(o>e.width||a>e.height||o+n>e.width||a+s>e.height)throw new Error("bitblt writing outside image");for(let l=0;l<s;l++)t.data.copy(e.data,(a+l)*e.width+o<<2,(i+l)*t.width+r<<2,(i+l)*t.width+r+n<<2)};$e.prototype.bitblt=function(t,e,r,i,n,s,o){return $e.bitblt(this,t,e,r,i,n,s,o),this};$e.adjustGamma=function(t){if(t.gamma){for(let e=0;e<t.height;e++)for(let r=0;r<t.width;r++){let i=t.width*e+r<<2;for(let n=0;n<3;n++){let s=t.data[i+n]/255;s=Math.pow(s,1/2.2/t.gamma),t.data[i+n]=Math.round(s*255)}}t.gamma=0}};$e.prototype.adjustGamma=function(){$e.adjustGamma(this)}});var jr=x(Hn=>{var Ei=class extends Error{constructor(e,r,i){super(i),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name,this.code=r,this.exitCode=e,this.nestedError=void 0}},qn=class extends Ei{constructor(e){super(1,"commander.invalidArgument",e),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name}};Hn.CommanderError=Ei;Hn.InvalidArgumentError=qn});var Si=x($n=>{var{InvalidArgumentError:Pm}=jr(),Vn=class{constructor(e,r){switch(this.description=r||"",this.variadic=!1,this.parseArg=void 0,this.defaultValue=void 0,this.defaultValueDescription=void 0,this.argChoices=void 0,e[0]){case"<":this.required=!0,this._name=e.slice(1,-1);break;case"[":this.required=!1,this._name=e.slice(1,-1);break;default:this.required=!0,this._name=e;break}this._name.length>3&&this._name.slice(-3)==="..."&&(this.variadic=!0,this._name=this._name.slice(0,-3))}name(){return this._name}_concatValue(e,r){return r===this.defaultValue||!Array.isArray(r)?[e]:r.concat(e)}default(e,r){return this.defaultValue=e,this.defaultValueDescription=r,this}argParser(e){return this.parseArg=e,this}choices(e){return this.argChoices=e,this.parseArg=(r,i)=>{if(!e.includes(r))throw new Pm(`Allowed choices are ${e.join(", ")}.`);return this.variadic?this._concatValue(r,i):r},this}argRequired(){return this.required=!0,this}argOptional(){return this.required=!1,this}};function Fm(t){let e=t.name()+(t.variadic===!0?"...":"");return t.required?"<"+e+">":"["+e+"]"}$n.Argument=Vn;$n.humanReadableArgName=Fm});var zn=x(xc=>{var{humanReadableArgName:Mm}=Si(),Gn=class{constructor(){this.helpWidth=void 0,this.sortSubcommands=!1,this.sortOptions=!1}visibleCommands(e){let r=e.commands.filter(i=>!i._hidden);if(e._hasImplicitHelpCommand()){let[,i,n]=e._helpCommandnameAndArgs.match(/([^ ]+) *(.*)/),s=e.createCommand(i).helpOption(!1);s.description(e._helpCommandDescription),n&&s.arguments(n),r.push(s)}return this.sortSubcommands&&r.sort((i,n)=>i.name().localeCompare(n.name())),r}visibleOptions(e){let r=e.options.filter(s=>!s.hidden),i=e._hasHelpOption&&e._helpShortFlag&&!e._findOption(e._helpShortFlag),n=e._hasHelpOption&&!e._findOption(e._helpLongFlag);if(i||n){let s;i?n?s=e.createOption(e._helpFlags,e._helpDescription):s=e.createOption(e._helpShortFlag,e._helpDescription):s=e.createOption(e._helpLongFlag,e._helpDescription),r.push(s)}if(this.sortOptions){let s=o=>o.short?o.short.replace(/^-/,""):o.long.replace(/^--/,"");r.sort((o,a)=>s(o).localeCompare(s(a)))}return r}visibleArguments(e){return e._argsDescription&&e._args.forEach(r=>{r.description=r.description||e._argsDescription[r.name()]||""}),e._args.find(r=>r.description)?e._args:[]}subcommandTerm(e){let r=e._args.map(i=>Mm(i)).join(" ");return e._name+(e._aliases[0]?"|"+e._aliases[0]:"")+(e.options.length?" [options]":"")+(r?" "+r:"")}optionTerm(e){return e.flags}argumentTerm(e){return e.name()}longestSubcommandTermLength(e,r){return r.visibleCommands(e).reduce((i,n)=>Math.max(i,r.subcommandTerm(n).length),0)}longestOptionTermLength(e,r){return r.visibleOptions(e).reduce((i,n)=>Math.max(i,r.optionTerm(n).length),0)}longestArgumentTermLength(e,r){return r.visibleArguments(e).reduce((i,n)=>Math.max(i,r.argumentTerm(n).length),0)}commandUsage(e){let r=e._name;e._aliases[0]&&(r=r+"|"+e._aliases[0]);let i="";for(let n=e.parent;n;n=n.parent)i=n.name()+" "+i;return i+r+" "+e.usage()}commandDescription(e){return e.description()}subcommandDescription(e){return e.description()}optionDescription(e){let r=[];return e.argChoices&&!e.negate&&r.push(`choices: ${e.argChoices.map(i=>JSON.stringify(i)).join(", ")}`),e.defaultValue!==void 0&&!e.negate&&r.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),e.envVar!==void 0&&r.push(`env: ${e.envVar}`),r.length>0?`${e.description} (${r.join(", ")})`:e.description}argumentDescription(e){let r=[];if(e.argChoices&&r.push(`choices: ${e.argChoices.map(i=>JSON.stringify(i)).join(", ")}`),e.defaultValue!==void 0&&r.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),r.length>0){let i=`(${r.join(", ")})`;return e.description?`${e.description} ${i}`:i}return e.description}formatHelp(e,r){let i=r.padWidth(e,r),n=r.helpWidth||80,s=2,o=2;function a(d,m){if(m){let g=`${d.padEnd(i+o)}${m}`;return r.wrap(g,n-s,i+o)}return d}function l(d){return d.join(`
`).replace(/^/gm," ".repeat(s))}let c=[`Usage: ${r.commandUsage(e)}`,""],u=r.commandDescription(e);u.length>0&&(c=c.concat([u,""]));let f=r.visibleArguments(e).map(d=>a(r.argumentTerm(d),r.argumentDescription(d)));f.length>0&&(c=c.concat(["Arguments:",l(f),""]));let h=r.visibleOptions(e).map(d=>a(r.optionTerm(d),r.optionDescription(d)));h.length>0&&(c=c.concat(["Options:",l(h),""]));let p=r.visibleCommands(e).map(d=>a(r.subcommandTerm(d),r.subcommandDescription(d)));return p.length>0&&(c=c.concat(["Commands:",l(p),""])),c.join(`
`)}padWidth(e,r){return Math.max(r.longestOptionTermLength(e,r),r.longestSubcommandTermLength(e,r),r.longestArgumentTermLength(e,r))}wrap(e,r,i,n=40){if(e.match(/[\n]\s+/))return e;let s=r-i;if(s<n)return e;let o=e.substr(0,i),a=e.substr(i),l=" ".repeat(i),c=new RegExp(".{1,"+(s-1)+"}([\\s\u200B]|$)|[^\\s\u200B]+?([\\s\u200B]|$)","g"),u=a.match(c)||[];return o+u.map((f,h)=>(f.slice(-1)===`
`&&(f=f.slice(0,f.length-1)),(h>0?l:"")+f.trimRight())).join(`
`)}};xc.Help=Gn});var Kn=x(Yn=>{var{InvalidArgumentError:Um}=jr(),Wn=class{constructor(e,r){this.flags=e,this.description=r||"",this.required=e.includes("<"),this.optional=e.includes("["),this.variadic=/\w\.\.\.[>\]]$/.test(e),this.mandatory=!1;let i=yc(e);this.short=i.shortFlag,this.long=i.longFlag,this.negate=!1,this.long&&(this.negate=this.long.startsWith("--no-")),this.defaultValue=void 0,this.defaultValueDescription=void 0,this.envVar=void 0,this.parseArg=void 0,this.hidden=!1,this.argChoices=void 0}default(e,r){return this.defaultValue=e,this.defaultValueDescription=r,this}env(e){return this.envVar=e,this}argParser(e){return this.parseArg=e,this}makeOptionMandatory(e=!0){return this.mandatory=!!e,this}hideHelp(e=!0){return this.hidden=!!e,this}_concatValue(e,r){return r===this.defaultValue||!Array.isArray(r)?[e]:r.concat(e)}choices(e){return this.argChoices=e,this.parseArg=(r,i)=>{if(!e.includes(r))throw new Um(`Allowed choices are ${e.join(", ")}.`);return this.variadic?this._concatValue(r,i):r},this}name(){return this.long?this.long.replace(/^--/,""):this.short.replace(/^-/,"")}attributeName(){return Dm(this.name().replace(/^no-/,""))}is(e){return this.short===e||this.long===e}};function Dm(t){return t.split("-").reduce((e,r)=>e+r[0].toUpperCase()+r.slice(1))}function yc(t){let e,r,i=t.split(/[ |,]+/);return i.length>1&&!/^[[<]/.test(i[1])&&(e=i.shift()),r=i.shift(),!e&&/^-[^-]$/.test(r)&&(e=r,r=void 0),{shortFlag:e,longFlag:r}}Yn.Option=Wn;Yn.splitOptionFlags=yc});var wc=x(bc=>{function jm(t,e){if(Math.abs(t.length-e.length)>3)return Math.max(t.length,e.length);let r=[];for(let i=0;i<=t.length;i++)r[i]=[i];for(let i=0;i<=e.length;i++)r[0][i]=i;for(let i=1;i<=e.length;i++)for(let n=1;n<=t.length;n++){let s=1;t[n-1]===e[i-1]?s=0:s=1,r[n][i]=Math.min(r[n-1][i]+1,r[n][i-1]+1,r[n-1][i-1]+s),n>1&&i>1&&t[n-1]===e[i-2]&&t[n-2]===e[i-1]&&(r[n][i]=Math.min(r[n][i],r[n-2][i-2]+1))}return r[t.length][e.length]}function qm(t,e){if(!e||e.length===0)return"";e=Array.from(new Set(e));let r=t.startsWith("--");r&&(t=t.slice(2),e=e.map(o=>o.slice(2)));let i=[],n=3,s=.4;return e.forEach(o=>{if(o.length<=1)return;let a=jm(t,o),l=Math.max(t.length,o.length);(l-a)/l>s&&(a<n?(n=a,i=[o]):a===n&&i.push(o))}),i.sort((o,a)=>o.localeCompare(a)),r&&(i=i.map(o=>`--${o}`)),i.length>1?`
(Did you mean one of ${i.join(", ")}?)`:i.length===1?`
(Did you mean ${i[0]}?)`:""}bc.suggestSimilar=qm});var Oc=x(Cc=>{var Hm=require("events").EventEmitter,Zn=require("child_process"),Nt=require("path"),Xn=require("fs"),{Argument:Vm,humanReadableArgName:$m}=Si(),{CommanderError:Qn}=jr(),{Help:Gm}=zn(),{Option:zm,splitOptionFlags:Wm}=Kn(),{suggestSimilar:Ec}=wc(),qr=class extends Hm{constructor(e){super(),this.commands=[],this.options=[],this.parent=null,this._allowUnknownOption=!1,this._allowExcessArguments=!0,this._args=[],this.args=[],this.rawArgs=[],this.processedArgs=[],this._scriptPath=null,this._name=e||"",this._optionValues={},this._optionValueSources={},this._storeOptionsAsProperties=!1,this._actionHandler=null,this._executableHandler=!1,this._executableFile=null,this._defaultCommandName=null,this._exitCallback=null,this._aliases=[],this._combineFlagAndOptionalValue=!0,this._description="",this._argsDescription=void 0,this._enablePositionalOptions=!1,this._passThroughOptions=!1,this._lifeCycleHooks={},this._showHelpAfterError=!1,this._showSuggestionAfterError=!1,this._outputConfiguration={writeOut:r=>process.stdout.write(r),writeErr:r=>process.stderr.write(r),getOutHelpWidth:()=>process.stdout.isTTY?process.stdout.columns:void 0,getErrHelpWidth:()=>process.stderr.isTTY?process.stderr.columns:void 0,outputError:(r,i)=>i(r)},this._hidden=!1,this._hasHelpOption=!0,this._helpFlags="-h, --help",this._helpDescription="display help for command",this._helpShortFlag="-h",this._helpLongFlag="--help",this._addImplicitHelpCommand=void 0,this._helpCommandName="help",this._helpCommandnameAndArgs="help [command]",this._helpCommandDescription="display help for command",this._helpConfiguration={}}copyInheritedSettings(e){return this._outputConfiguration=e._outputConfiguration,this._hasHelpOption=e._hasHelpOption,this._helpFlags=e._helpFlags,this._helpDescription=e._helpDescription,this._helpShortFlag=e._helpShortFlag,this._helpLongFlag=e._helpLongFlag,this._helpCommandName=e._helpCommandName,this._helpCommandnameAndArgs=e._helpCommandnameAndArgs,this._helpCommandDescription=e._helpCommandDescription,this._helpConfiguration=e._helpConfiguration,this._exitCallback=e._exitCallback,this._storeOptionsAsProperties=e._storeOptionsAsProperties,this._combineFlagAndOptionalValue=e._combineFlagAndOptionalValue,this._allowExcessArguments=e._allowExcessArguments,this._enablePositionalOptions=e._enablePositionalOptions,this._showHelpAfterError=e._showHelpAfterError,this._showSuggestionAfterError=e._showSuggestionAfterError,this}command(e,r,i){let n=r,s=i;typeof n=="object"&&n!==null&&(s=n,n=null),s=s||{};let[,o,a]=e.match(/([^ ]+) *(.*)/),l=this.createCommand(o);return n&&(l.description(n),l._executableHandler=!0),s.isDefault&&(this._defaultCommandName=l._name),l._hidden=!!(s.noHelp||s.hidden),l._executableFile=s.executableFile||null,a&&l.arguments(a),this.commands.push(l),l.parent=this,l.copyInheritedSettings(this),n?this:l}createCommand(e){return new qr(e)}createHelp(){return Object.assign(new Gm,this.configureHelp())}configureHelp(e){return e===void 0?this._helpConfiguration:(this._helpConfiguration=e,this)}configureOutput(e){return e===void 0?this._outputConfiguration:(Object.assign(this._outputConfiguration,e),this)}showHelpAfterError(e=!0){return typeof e!="string"&&(e=!!e),this._showHelpAfterError=e,this}showSuggestionAfterError(e=!0){return this._showSuggestionAfterError=!!e,this}addCommand(e,r){if(!e._name)throw new Error("Command passed to .addCommand() must have a name");function i(n){n.forEach(s=>{if(s._executableHandler&&!s._executableFile)throw new Error(`Must specify executableFile for deeply nested executable: ${s.name()}`);i(s.commands)})}return i(e.commands),r=r||{},r.isDefault&&(this._defaultCommandName=e._name),(r.noHelp||r.hidden)&&(e._hidden=!0),this.commands.push(e),e.parent=this,this}createArgument(e,r){return new Vm(e,r)}argument(e,r,i,n){let s=this.createArgument(e,r);return typeof i=="function"?s.default(n).argParser(i):s.default(i),this.addArgument(s),this}arguments(e){return e.split(/ +/).forEach(r=>{this.argument(r)}),this}addArgument(e){let r=this._args.slice(-1)[0];if(r&&r.variadic)throw new Error(`only the last argument can be variadic '${r.name()}'`);if(e.required&&e.defaultValue!==void 0&&e.parseArg===void 0)throw new Error(`a default value for a required argument is never used: '${e.name()}'`);return this._args.push(e),this}addHelpCommand(e,r){return e===!1?this._addImplicitHelpCommand=!1:(this._addImplicitHelpCommand=!0,typeof e=="string"&&(this._helpCommandName=e.split(" ")[0],this._helpCommandnameAndArgs=e),this._helpCommandDescription=r||this._helpCommandDescription),this}_hasImplicitHelpCommand(){return this._addImplicitHelpCommand===void 0?this.commands.length&&!this._actionHandler&&!this._findCommand("help"):this._addImplicitHelpCommand}hook(e,r){let i=["preAction","postAction"];if(!i.includes(e))throw new Error(`Unexpected value for event passed to hook : '${e}'.
Expecting one of '${i.join("', '")}'`);return this._lifeCycleHooks[e]?this._lifeCycleHooks[e].push(r):this._lifeCycleHooks[e]=[r],this}exitOverride(e){return e?this._exitCallback=e:this._exitCallback=r=>{if(r.code!=="commander.executeSubCommandAsync")throw r},this}_exit(e,r,i){this._exitCallback&&this._exitCallback(new Qn(e,r,i)),process.exit(e)}action(e){let r=i=>{let n=this._args.length,s=i.slice(0,n);return this._storeOptionsAsProperties?s[n]=this:s[n]=this.opts(),s.push(this),e.apply(this,s)};return this._actionHandler=r,this}createOption(e,r){return new zm(e,r)}addOption(e){let r=e.name(),i=e.attributeName(),n=e.defaultValue;if(e.negate||e.optional||e.required||typeof n=="boolean"){if(e.negate){let o=e.long.replace(/^--no-/,"--");n=this._findOption(o)?this.getOptionValue(i):!0}n!==void 0&&this.setOptionValueWithSource(i,n,"default")}this.options.push(e);let s=(o,a,l)=>{let c=this.getOptionValue(i);if(o!==null&&e.parseArg)try{o=e.parseArg(o,c===void 0?n:c)}catch(u){if(u.code==="commander.invalidArgument"){let f=`${a} ${u.message}`;this._displayError(u.exitCode,u.code,f)}throw u}else o!==null&&e.variadic&&(o=e._concatValue(o,c));typeof c=="boolean"||typeof c=="undefined"?o==null?this.setOptionValueWithSource(i,e.negate?!1:n||!0,l):this.setOptionValueWithSource(i,o,l):o!==null&&this.setOptionValueWithSource(i,e.negate?!1:o,l)};return this.on("option:"+r,o=>{let a=`error: option '${e.flags}' argument '${o}' is invalid.`;s(o,a,"cli")}),e.envVar&&this.on("optionEnv:"+r,o=>{let a=`error: option '${e.flags}' value '${o}' from env '${e.envVar}' is invalid.`;s(o,a,"env")}),this}_optionEx(e,r,i,n,s){let o=this.createOption(r,i);if(o.makeOptionMandatory(!!e.mandatory),typeof n=="function")o.default(s).argParser(n);else if(n instanceof RegExp){let a=n;n=(l,c)=>{let u=a.exec(l);return u?u[0]:c},o.default(s).argParser(n)}else o.default(n);return this.addOption(o)}option(e,r,i,n){return this._optionEx({},e,r,i,n)}requiredOption(e,r,i,n){return this._optionEx({mandatory:!0},e,r,i,n)}combineFlagAndOptionalValue(e=!0){return this._combineFlagAndOptionalValue=!!e,this}allowUnknownOption(e=!0){return this._allowUnknownOption=!!e,this}allowExcessArguments(e=!0){return this._allowExcessArguments=!!e,this}enablePositionalOptions(e=!0){return this._enablePositionalOptions=!!e,this}passThroughOptions(e=!0){if(this._passThroughOptions=!!e,!!this.parent&&e&&!this.parent._enablePositionalOptions)throw new Error("passThroughOptions can not be used without turning on enablePositionalOptions for parent command(s)");return this}storeOptionsAsProperties(e=!0){if(this._storeOptionsAsProperties=!!e,this.options.length)throw new Error("call .storeOptionsAsProperties() before adding options");return this}getOptionValue(e){return this._storeOptionsAsProperties?this[e]:this._optionValues[e]}setOptionValue(e,r){return this._storeOptionsAsProperties?this[e]=r:this._optionValues[e]=r,this}setOptionValueWithSource(e,r,i){return this.setOptionValue(e,r),this._optionValueSources[e]=i,this}getOptionValueSource(e){return this._optionValueSources[e]}_prepareUserArgs(e,r){if(e!==void 0&&!Array.isArray(e))throw new Error("first parameter to parse must be array or undefined");r=r||{},e===void 0&&(e=process.argv,process.versions&&process.versions.electron&&(r.from="electron")),this.rawArgs=e.slice();let i;switch(r.from){case void 0:case"node":this._scriptPath=e[1],i=e.slice(2);break;case"electron":process.defaultApp?(this._scriptPath=e[1],i=e.slice(2)):i=e.slice(1);break;case"user":i=e.slice(0);break;default:throw new Error(`unexpected parse option { from: '${r.from}' }`)}return!this._scriptPath&&require.main&&(this._scriptPath=require.main.filename),this._name=this._name||this._scriptPath&&Nt.basename(this._scriptPath,Nt.extname(this._scriptPath)),i}parse(e,r){let i=this._prepareUserArgs(e,r);return this._parseCommand([],i),this}async parseAsync(e,r){let i=this._prepareUserArgs(e,r);return await this._parseCommand([],i),this}_executeSubCommand(e,r){r=r.slice();let i=!1,n=[".js",".ts",".tsx",".mjs",".cjs"];this._checkForMissingMandatoryOptions();let s=this._scriptPath;!s&&require.main&&(s=require.main.filename);let o;try{let h=Xn.realpathSync(s);o=Nt.dirname(h)}catch{o="."}let a=Nt.basename(s,Nt.extname(s))+"-"+e._name;e._executableFile&&(a=e._executableFile);let l=Nt.join(o,a);Xn.existsSync(l)?a=l:n.forEach(h=>{Xn.existsSync(`${l}${h}`)&&(a=`${l}${h}`)}),i=n.includes(Nt.extname(a));let c;process.platform!=="win32"?i?(r.unshift(a),r=kc(process.execArgv).concat(r),c=Zn.spawn(process.argv[0],r,{stdio:"inherit"})):c=Zn.spawn(a,r,{stdio:"inherit"}):(r.unshift(a),r=kc(process.execArgv).concat(r),c=Zn.spawn(process.execPath,r,{stdio:"inherit"})),["SIGUSR1","SIGUSR2","SIGTERM","SIGINT","SIGHUP"].forEach(h=>{process.on(h,()=>{c.killed===!1&&c.exitCode===null&&c.kill(h)})});let f=this._exitCallback;f?c.on("close",()=>{f(new Qn(process.exitCode||0,"commander.executeSubCommandAsync","(close)"))}):c.on("close",process.exit.bind(process)),c.on("error",h=>{if(h.code==="ENOENT"){let p=`'${a}' does not exist
 - if '${e._name}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead
 - if the default executable name is not suitable, use the executableFile option to supply a custom name`;throw new Error(p)}else if(h.code==="EACCES")throw new Error(`'${a}' not executable`);if(!f)process.exit(1);else{let p=new Qn(1,"commander.executeSubCommandAsync","(error)");p.nestedError=h,f(p)}}),this.runningCommand=c}_dispatchSubcommand(e,r,i){let n=this._findCommand(e);if(n||this.help({error:!0}),n._executableHandler)this._executeSubCommand(n,r.concat(i));else return n._parseCommand(r,i)}_checkNumberOfArguments(){this._args.forEach((e,r)=>{e.required&&this.args[r]==null&&this.missingArgument(e.name())}),!(this._args.length>0&&this._args[this._args.length-1].variadic)&&this.args.length>this._args.length&&this._excessArguments(this.args)}_processArguments(){let e=(i,n,s)=>{let o=n;if(n!==null&&i.parseArg)try{o=i.parseArg(n,s)}catch(a){if(a.code==="commander.invalidArgument"){let l=`error: command-argument value '${n}' is invalid for argument '${i.name()}'. ${a.message}`;this._displayError(a.exitCode,a.code,l)}throw a}return o};this._checkNumberOfArguments();let r=[];this._args.forEach((i,n)=>{let s=i.defaultValue;i.variadic?n<this.args.length?(s=this.args.slice(n),i.parseArg&&(s=s.reduce((o,a)=>e(i,a,o),i.defaultValue))):s===void 0&&(s=[]):n<this.args.length&&(s=this.args[n],i.parseArg&&(s=e(i,s,i.defaultValue))),r[n]=s}),this.processedArgs=r}_chainOrCall(e,r){return e&&e.then&&typeof e.then=="function"?e.then(()=>r()):r()}_chainOrCallHooks(e,r){let i=e,n=[];return Jn(this).reverse().filter(s=>s._lifeCycleHooks[r]!==void 0).forEach(s=>{s._lifeCycleHooks[r].forEach(o=>{n.push({hookedCommand:s,callback:o})})}),r==="postAction"&&n.reverse(),n.forEach(s=>{i=this._chainOrCall(i,()=>s.callback(s.hookedCommand,this))}),i}_parseCommand(e,r){let i=this.parseOptions(r);if(this._parseOptionsEnv(),e=e.concat(i.operands),r=i.unknown,this.args=e.concat(r),e&&this._findCommand(e[0]))return this._dispatchSubcommand(e[0],e.slice(1),r);if(this._hasImplicitHelpCommand()&&e[0]===this._helpCommandName)return e.length===1&&this.help(),this._dispatchSubcommand(e[1],[],[this._helpLongFlag]);if(this._defaultCommandName)return Sc(this,r),this._dispatchSubcommand(this._defaultCommandName,e,r);this.commands.length&&this.args.length===0&&!this._actionHandler&&!this._defaultCommandName&&this.help({error:!0}),Sc(this,i.unknown),this._checkForMissingMandatoryOptions();let n=()=>{i.unknown.length>0&&this.unknownOption(i.unknown[0])},s=`command:${this.name()}`;if(this._actionHandler){n(),this._processArguments();let o;return o=this._chainOrCallHooks(o,"preAction"),o=this._chainOrCall(o,()=>this._actionHandler(this.processedArgs)),this.parent&&this.parent.emit(s,e,r),o=this._chainOrCallHooks(o,"postAction"),o}if(this.parent&&this.parent.listenerCount(s))n(),this._processArguments(),this.parent.emit(s,e,r);else if(e.length){if(this._findCommand("*"))return this._dispatchSubcommand("*",e,r);this.listenerCount("command:*")?this.emit("command:*",e,r):this.commands.length?this.unknownCommand():(n(),this._processArguments())}else this.commands.length?(n(),this.help({error:!0})):(n(),this._processArguments())}_findCommand(e){if(!!e)return this.commands.find(r=>r._name===e||r._aliases.includes(e))}_findOption(e){return this.options.find(r=>r.is(e))}_checkForMissingMandatoryOptions(){for(let e=this;e;e=e.parent)e.options.forEach(r=>{r.mandatory&&e.getOptionValue(r.attributeName())===void 0&&e.missingMandatoryOptionValue(r)})}parseOptions(e){let r=[],i=[],n=r,s=e.slice();function o(l){return l.length>1&&l[0]==="-"}let a=null;for(;s.length;){let l=s.shift();if(l==="--"){n===i&&n.push(l),n.push(...s);break}if(a&&!o(l)){this.emit(`option:${a.name()}`,l);continue}if(a=null,o(l)){let c=this._findOption(l);if(c){if(c.required){let u=s.shift();u===void 0&&this.optionMissingArgument(c),this.emit(`option:${c.name()}`,u)}else if(c.optional){let u=null;s.length>0&&!o(s[0])&&(u=s.shift()),this.emit(`option:${c.name()}`,u)}else this.emit(`option:${c.name()}`);a=c.variadic?c:null;continue}}if(l.length>2&&l[0]==="-"&&l[1]!=="-"){let c=this._findOption(`-${l[1]}`);if(c){c.required||c.optional&&this._combineFlagAndOptionalValue?this.emit(`option:${c.name()}`,l.slice(2)):(this.emit(`option:${c.name()}`),s.unshift(`-${l.slice(2)}`));continue}}if(/^--[^=]+=/.test(l)){let c=l.indexOf("="),u=this._findOption(l.slice(0,c));if(u&&(u.required||u.optional)){this.emit(`option:${u.name()}`,l.slice(c+1));continue}}if(o(l)&&(n=i),(this._enablePositionalOptions||this._passThroughOptions)&&r.length===0&&i.length===0){if(this._findCommand(l)){r.push(l),s.length>0&&i.push(...s);break}else if(l===this._helpCommandName&&this._hasImplicitHelpCommand()){r.push(l),s.length>0&&r.push(...s);break}else if(this._defaultCommandName){i.push(l),s.length>0&&i.push(...s);break}}if(this._passThroughOptions){n.push(l),s.length>0&&n.push(...s);break}n.push(l)}return{operands:r,unknown:i}}opts(){if(this._storeOptionsAsProperties){let e={},r=this.options.length;for(let i=0;i<r;i++){let n=this.options[i].attributeName();e[n]=n===this._versionOptionName?this._version:this[n]}return e}return this._optionValues}_displayError(e,r,i){this._outputConfiguration.outputError(`${i}
`,this._outputConfiguration.writeErr),typeof this._showHelpAfterError=="string"?this._outputConfiguration.writeErr(`${this._showHelpAfterError}
`):this._showHelpAfterError&&(this._outputConfiguration.writeErr(`
`),this.outputHelp({error:!0})),this._exit(e,r,i)}_parseOptionsEnv(){this.options.forEach(e=>{if(e.envVar&&e.envVar in process.env){let r=e.attributeName();(this.getOptionValue(r)===void 0||["default","config","env"].includes(this.getOptionValueSource(r)))&&(e.required||e.optional?this.emit(`optionEnv:${e.name()}`,process.env[e.envVar]):this.emit(`optionEnv:${e.name()}`))}})}missingArgument(e){let r=`error: missing required argument '${e}'`;this._displayError(1,"commander.missingArgument",r)}optionMissingArgument(e){let r=`error: option '${e.flags}' argument missing`;this._displayError(1,"commander.optionMissingArgument",r)}missingMandatoryOptionValue(e){let r=`error: required option '${e.flags}' not specified`;this._displayError(1,"commander.missingMandatoryOptionValue",r)}unknownOption(e){if(this._allowUnknownOption)return;let r="";if(e.startsWith("--")&&this._showSuggestionAfterError){let n=[],s=this;do{let o=s.createHelp().visibleOptions(s).filter(a=>a.long).map(a=>a.long);n=n.concat(o),s=s.parent}while(s&&!s._enablePositionalOptions);r=Ec(e,n)}let i=`error: unknown option '${e}'${r}`;this._displayError(1,"commander.unknownOption",i)}_excessArguments(e){if(this._allowExcessArguments)return;let r=this._args.length,i=r===1?"":"s",s=`error: too many arguments${this.parent?` for '${this.name()}'`:""}. Expected ${r} argument${i} but got ${e.length}.`;this._displayError(1,"commander.excessArguments",s)}unknownCommand(){let e=this.args[0],r="";if(this._showSuggestionAfterError){let n=[];this.createHelp().visibleCommands(this).forEach(s=>{n.push(s.name()),s.alias()&&n.push(s.alias())}),r=Ec(e,n)}let i=`error: unknown command '${e}'${r}`;this._displayError(1,"commander.unknownCommand",i)}version(e,r,i){if(e===void 0)return this._version;this._version=e,r=r||"-V, --version",i=i||"output the version number";let n=this.createOption(r,i);return this._versionOptionName=n.attributeName(),this.options.push(n),this.on("option:"+n.name(),()=>{this._outputConfiguration.writeOut(`${e}
`),this._exit(0,"commander.version",e)}),this}description(e,r){return e===void 0&&r===void 0?this._description:(this._description=e,r&&(this._argsDescription=r),this)}alias(e){if(e===void 0)return this._aliases[0];let r=this;if(this.commands.length!==0&&this.commands[this.commands.length-1]._executableHandler&&(r=this.commands[this.commands.length-1]),e===r._name)throw new Error("Command alias can't be the same as its name");return r._aliases.push(e),this}aliases(e){return e===void 0?this._aliases:(e.forEach(r=>this.alias(r)),this)}usage(e){if(e===void 0){if(this._usage)return this._usage;let r=this._args.map(i=>$m(i));return[].concat(this.options.length||this._hasHelpOption?"[options]":[],this.commands.length?"[command]":[],this._args.length?r:[]).join(" ")}return this._usage=e,this}name(e){return e===void 0?this._name:(this._name=e,this)}helpInformation(e){let r=this.createHelp();return r.helpWidth===void 0&&(r.helpWidth=e&&e.error?this._outputConfiguration.getErrHelpWidth():this._outputConfiguration.getOutHelpWidth()),r.formatHelp(this,r)}_getHelpContext(e){e=e||{};let r={error:!!e.error},i;return r.error?i=n=>this._outputConfiguration.writeErr(n):i=n=>this._outputConfiguration.writeOut(n),r.write=e.write||i,r.command=this,r}outputHelp(e){let r;typeof e=="function"&&(r=e,e=void 0);let i=this._getHelpContext(e);Jn(this).reverse().forEach(s=>s.emit("beforeAllHelp",i)),this.emit("beforeHelp",i);let n=this.helpInformation(i);if(r&&(n=r(n),typeof n!="string"&&!Buffer.isBuffer(n)))throw new Error("outputHelp callback must return a string or a Buffer");i.write(n),this.emit(this._helpLongFlag),this.emit("afterHelp",i),Jn(this).forEach(s=>s.emit("afterAllHelp",i))}helpOption(e,r){if(typeof e=="boolean")return this._hasHelpOption=e,this;this._helpFlags=e||this._helpFlags,this._helpDescription=r||this._helpDescription;let i=Wm(this._helpFlags);return this._helpShortFlag=i.shortFlag,this._helpLongFlag=i.longFlag,this}help(e){this.outputHelp(e);let r=process.exitCode||0;r===0&&e&&typeof e!="function"&&e.error&&(r=1),this._exit(r,"commander.help","(outputHelp)")}addHelpText(e,r){let i=["beforeAll","before","after","afterAll"];if(!i.includes(e))throw new Error(`Unexpected value for position to addHelpText.
Expecting one of '${i.join("', '")}'`);let n=`${e}Help`;return this.on(n,s=>{let o;typeof r=="function"?o=r({error:s.error,command:s.command}):o=r,o&&s.write(`${o}
`)}),this}};function Sc(t,e){t._hasHelpOption&&e.find(i=>i===t._helpLongFlag||i===t._helpShortFlag)&&(t.outputHelp(),t._exit(0,"commander.helpDisplayed","(outputHelp)"))}function kc(t){return t.map(e=>{if(!e.startsWith("--inspect"))return e;let r,i="127.0.0.1",n="9229",s;return(s=e.match(/^(--inspect(-brk)?)$/))!==null?r=s[1]:(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+)$/))!==null?(r=s[1],/^\d+$/.test(s[3])?n=s[3]:i=s[3]):(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+):(\d+)$/))!==null&&(r=s[1],i=s[3],n=s[4]),r&&n!=="0"?`${r}=${i}:${parseInt(n)+1}`:e})}function Jn(t){let e=[];for(let r=t;r;r=r.parent)e.push(r);return e}Cc.Command=qr});var Rc=x((st,Ac)=>{var{Argument:Ym}=Si(),{Command:Ic}=Oc(),{CommanderError:Km,InvalidArgumentError:Tc}=jr(),{Help:Zm}=zn(),{Option:Xm}=Kn();st=Ac.exports=new Ic;st.program=st;st.Argument=Ym;st.Command=Ic;st.CommanderError=Km;st.Help=Zm;st.InvalidArgumentError=Tc;st.InvalidOptionArgumentError=Tc;st.Option=Xm});var Nc=x((Bc,Lc)=>{Bc=Lc.exports=cr;function cr(t,e){if(this.stream=e.stream||process.stderr,typeof e=="number"){var r=e;e={},e.total=r}else{if(e=e||{},typeof t!="string")throw new Error("format required");if(typeof e.total!="number")throw new Error("total required")}this.fmt=t,this.curr=e.curr||0,this.total=e.total,this.width=e.width||this.total,this.clear=e.clear,this.chars={complete:e.complete||"=",incomplete:e.incomplete||"-",head:e.head||e.complete||"="},this.renderThrottle=e.renderThrottle!==0?e.renderThrottle||16:0,this.lastRender=-1/0,this.callback=e.callback||function(){},this.tokens={},this.lastDraw=""}cr.prototype.tick=function(t,e){if(t!==0&&(t=t||1),typeof t=="object"&&(e=t,t=1),e&&(this.tokens=e),this.curr==0&&(this.start=new Date),this.curr+=t,this.render(),this.curr>=this.total){this.render(void 0,!0),this.complete=!0,this.terminate(),this.callback(this);return}};cr.prototype.render=function(t,e){if(e=e!==void 0?e:!1,t&&(this.tokens=t),!!this.stream.isTTY){var r=Date.now(),i=r-this.lastRender;if(!(!e&&i<this.renderThrottle)){this.lastRender=r;var n=this.curr/this.total;n=Math.min(Math.max(n,0),1);var s=Math.floor(n*100),o,a,l,c=new Date-this.start,u=s==100?0:c*(this.total/this.curr-1),f=this.curr/(c/1e3),h=this.fmt.replace(":current",this.curr).replace(":total",this.total).replace(":elapsed",isNaN(c)?"0.0":(c/1e3).toFixed(1)).replace(":eta",isNaN(u)||!isFinite(u)?"0.0":(u/1e3).toFixed(1)).replace(":percent",s.toFixed(0)+"%").replace(":rate",Math.round(f)),p=Math.max(0,this.stream.columns-h.replace(":bar","").length);p&&process.platform==="win32"&&(p=p-1);var d=Math.min(this.width,p);if(l=Math.round(d*n),a=Array(Math.max(0,l+1)).join(this.chars.complete),o=Array(Math.max(0,d-l+1)).join(this.chars.incomplete),l>0&&(a=a.slice(0,-1)+this.chars.head),h=h.replace(":bar",a+o),this.tokens)for(var m in this.tokens)h=h.replace(":"+m,this.tokens[m]);this.lastDraw!==h&&(this.stream.cursorTo(0),this.stream.write(h),this.stream.clearLine(1),this.lastDraw=h)}}};cr.prototype.update=function(t,e){var r=Math.floor(t*this.total),i=r-this.curr;this.tick(i,e)};cr.prototype.interrupt=function(t){this.stream.clearLine(),this.stream.cursorTo(0),this.stream.write(t),this.stream.write(`
`),this.stream.write(this.lastDraw)};cr.prototype.terminate=function(){this.clear?this.stream.clearLine&&(this.stream.clearLine(),this.stream.cursorTo(0)):this.stream.write(`
`)}});var Fc=x((yx,Pc)=>{Pc.exports=Nc()});var Mc=x(es=>{var Pt=require("path"),yt=process.platform==="win32",xt=require("fs"),Qm=process.env.NODE_DEBUG&&/fs/.test(process.env.NODE_DEBUG);function Jm(){var t;if(Qm){var e=new Error;t=r}else t=i;return t;function r(n){n&&(e.message=n.message,n=e,i(n))}function i(n){if(n){if(process.throwDeprecation)throw n;if(!process.noDeprecation){var s="fs: missing callback "+(n.stack||n.message);process.traceDeprecation?console.trace(s):console.error(s)}}}}function eg(t){return typeof t=="function"?t:Jm()}var bx=Pt.normalize;yt?ft=/(.*?)(?:[\/\\]+|$)/g:ft=/(.*?)(?:[\/]+|$)/g;var ft;yt?Hr=/^(?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/][^\\\/]+)?[\\\/]*/:Hr=/^[\/]*/;var Hr;es.realpathSync=function(e,r){if(e=Pt.resolve(e),r&&Object.prototype.hasOwnProperty.call(r,e))return r[e];var i=e,n={},s={},o,a,l,c;u();function u(){var g=Hr.exec(e);o=g[0].length,a=g[0],l=g[0],c="",yt&&!s[l]&&(xt.lstatSync(l),s[l]=!0)}for(;o<e.length;){ft.lastIndex=o;var f=ft.exec(e);if(c=a,a+=f[0],l=c+f[1],o=ft.lastIndex,!(s[l]||r&&r[l]===l)){var h;if(r&&Object.prototype.hasOwnProperty.call(r,l))h=r[l];else{var p=xt.lstatSync(l);if(!p.isSymbolicLink()){s[l]=!0,r&&(r[l]=l);continue}var d=null;if(!yt){var m=p.dev.toString(32)+":"+p.ino.toString(32);n.hasOwnProperty(m)&&(d=n[m])}d===null&&(xt.statSync(l),d=xt.readlinkSync(l)),h=Pt.resolve(c,d),r&&(r[l]=h),yt||(n[m]=d)}e=Pt.resolve(h,e.slice(o)),u()}}return r&&(r[i]=e),e};es.realpath=function(e,r,i){if(typeof i!="function"&&(i=eg(r),r=null),e=Pt.resolve(e),r&&Object.prototype.hasOwnProperty.call(r,e))return process.nextTick(i.bind(null,null,r[e]));var n=e,s={},o={},a,l,c,u;f();function f(){var g=Hr.exec(e);a=g[0].length,l=g[0],c=g[0],u="",yt&&!o[c]?xt.lstat(c,function(y){if(y)return i(y);o[c]=!0,h()}):process.nextTick(h)}function h(){if(a>=e.length)return r&&(r[n]=e),i(null,e);ft.lastIndex=a;var g=ft.exec(e);return u=l,l+=g[0],c=u+g[1],a=ft.lastIndex,o[c]||r&&r[c]===c?process.nextTick(h):r&&Object.prototype.hasOwnProperty.call(r,c)?m(r[c]):xt.lstat(c,p)}function p(g,y){if(g)return i(g);if(!y.isSymbolicLink())return o[c]=!0,r&&(r[c]=c),process.nextTick(h);if(!yt){var I=y.dev.toString(32)+":"+y.ino.toString(32);if(s.hasOwnProperty(I))return d(null,s[I],c)}xt.stat(c,function(w){if(w)return i(w);xt.readlink(c,function(S,b){yt||(s[I]=b),d(S,b)})})}function d(g,y,I){if(g)return i(g);var w=Pt.resolve(u,y);r&&(r[I]=w),m(w)}function m(g){e=Pt.resolve(g,e.slice(a)),f()}}});var ns=x((Ex,qc)=>{qc.exports=bt;bt.realpath=bt;bt.sync=is;bt.realpathSync=is;bt.monkeypatch=rg;bt.unmonkeypatch=ig;var ur=require("fs"),ts=ur.realpath,rs=ur.realpathSync,tg=process.version,Uc=/^v[0-5]\./.test(tg),Dc=Mc();function jc(t){return t&&t.syscall==="realpath"&&(t.code==="ELOOP"||t.code==="ENOMEM"||t.code==="ENAMETOOLONG")}function bt(t,e,r){if(Uc)return ts(t,e,r);typeof e=="function"&&(r=e,e=null),ts(t,e,function(i,n){jc(i)?Dc.realpath(t,e,r):r(i,n)})}function is(t,e){if(Uc)return rs(t,e);try{return rs(t,e)}catch(r){if(jc(r))return Dc.realpathSync(t,e);throw r}}function rg(){ur.realpath=bt,ur.realpathSync=is}function ig(){ur.realpath=ts,ur.realpathSync=rs}});var Hc=x((Sx,ss)=>{typeof Object.create=="function"?ss.exports=function(e,r){r&&(e.super_=r,e.prototype=Object.create(r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:ss.exports=function(e,r){if(r){e.super_=r;var i=function(){};i.prototype=r.prototype,e.prototype=new i,e.prototype.constructor=e}}});var Vc=x((kx,as)=>{try{if(os=require("util"),typeof os.inherits!="function")throw"";as.exports=os.inherits}catch{as.exports=Hc()}var os});var Ci=x((Cx,ki)=>{"use strict";function $c(t){return t.charAt(0)==="/"}function Gc(t){var e=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/,r=e.exec(t),i=r[1]||"",n=Boolean(i&&i.charAt(1)!==":");return Boolean(r[2]||n)}ki.exports=process.platform==="win32"?Gc:$c;ki.exports.posix=$c;ki.exports.win32=Gc});var cs=x(wt=>{wt.setopts=cg;wt.ownProp=zc;wt.makeAbs=Vr;wt.finish=ug;wt.mark=fg;wt.isIgnored=Yc;wt.childrenIgnored=hg;function zc(t,e){return Object.prototype.hasOwnProperty.call(t,e)}var ng=require("fs"),fr=require("path"),sg=Ur(),Wc=Ci(),ls=sg.Minimatch;function og(t,e){return t.localeCompare(e,"en")}function ag(t,e){t.ignore=e.ignore||[],Array.isArray(t.ignore)||(t.ignore=[t.ignore]),t.ignore.length&&(t.ignore=t.ignore.map(lg))}function lg(t){var e=null;if(t.slice(-3)==="/**"){var r=t.replace(/(\/\*\*)+$/,"");e=new ls(r,{dot:!0})}return{matcher:new ls(t,{dot:!0}),gmatcher:e}}function cg(t,e,r){if(r||(r={}),r.matchBase&&e.indexOf("/")===-1){if(r.noglobstar)throw new Error("base matching requires globstar");e="**/"+e}t.silent=!!r.silent,t.pattern=e,t.strict=r.strict!==!1,t.realpath=!!r.realpath,t.realpathCache=r.realpathCache||Object.create(null),t.follow=!!r.follow,t.dot=!!r.dot,t.mark=!!r.mark,t.nodir=!!r.nodir,t.nodir&&(t.mark=!0),t.sync=!!r.sync,t.nounique=!!r.nounique,t.nonull=!!r.nonull,t.nosort=!!r.nosort,t.nocase=!!r.nocase,t.stat=!!r.stat,t.noprocess=!!r.noprocess,t.absolute=!!r.absolute,t.fs=r.fs||ng,t.maxLength=r.maxLength||1/0,t.cache=r.cache||Object.create(null),t.statCache=r.statCache||Object.create(null),t.symlinks=r.symlinks||Object.create(null),ag(t,r),t.changedCwd=!1;var i=process.cwd();zc(r,"cwd")?(t.cwd=fr.resolve(r.cwd),t.changedCwd=t.cwd!==i):t.cwd=i,t.root=r.root||fr.resolve(t.cwd,"/"),t.root=fr.resolve(t.root),process.platform==="win32"&&(t.root=t.root.replace(/\\/g,"/")),t.cwdAbs=Wc(t.cwd)?t.cwd:Vr(t,t.cwd),process.platform==="win32"&&(t.cwdAbs=t.cwdAbs.replace(/\\/g,"/")),t.nomount=!!r.nomount,r.nonegate=!0,r.nocomment=!0,t.minimatch=new ls(e,r),t.options=t.minimatch.options}function ug(t){for(var e=t.nounique,r=e?[]:Object.create(null),i=0,n=t.matches.length;i<n;i++){var s=t.matches[i];if(!s||Object.keys(s).length===0){if(t.nonull){var o=t.minimatch.globSet[i];e?r.push(o):r[o]=!0}}else{var a=Object.keys(s);e?r.push.apply(r,a):a.forEach(function(l){r[l]=!0})}}if(e||(r=Object.keys(r)),t.nosort||(r=r.sort(og)),t.mark){for(var i=0;i<r.length;i++)r[i]=t._mark(r[i]);t.nodir&&(r=r.filter(function(l){var c=!/\/$/.test(l),u=t.cache[l]||t.cache[Vr(t,l)];return c&&u&&(c=u!=="DIR"&&!Array.isArray(u)),c}))}t.ignore.length&&(r=r.filter(function(l){return!Yc(t,l)})),t.found=r}function fg(t,e){var r=Vr(t,e),i=t.cache[r],n=e;if(i){var s=i==="DIR"||Array.isArray(i),o=e.slice(-1)==="/";if(s&&!o?n+="/":!s&&o&&(n=n.slice(0,-1)),n!==e){var a=Vr(t,n);t.statCache[a]=t.statCache[r],t.cache[a]=t.cache[r]}}return n}function Vr(t,e){var r=e;return e.charAt(0)==="/"?r=fr.join(t.root,e):Wc(e)||e===""?r=e:t.changedCwd?r=fr.resolve(t.cwd,e):r=fr.resolve(e),process.platform==="win32"&&(r=r.replace(/\\/g,"/")),r}function Yc(t,e){return t.ignore.length?t.ignore.some(function(r){return r.matcher.match(e)||!!(r.gmatcher&&r.gmatcher.match(e))}):!1}function hg(t,e){return t.ignore.length?t.ignore.some(function(r){return!!(r.gmatcher&&r.gmatcher.match(e))}):!1}});var Jc=x((Rx,Qc)=>{Qc.exports=Xc;Xc.GlobSync=Re;var pg=ns(),Kc=Ur(),Ix=Kc.Minimatch,Tx=hs().Glob,Ax=require("util"),us=require("path"),Zc=require("assert"),Oi=Ci(),Ft=cs(),dg=Ft.setopts,fs=Ft.ownProp,mg=Ft.childrenIgnored,gg=Ft.isIgnored;function Xc(t,e){if(typeof e=="function"||arguments.length===3)throw new TypeError(`callback provided to sync glob
See: https://github.com/isaacs/node-glob/issues/167`);return new Re(t,e).found}function Re(t,e){if(!t)throw new Error("must provide pattern");if(typeof e=="function"||arguments.length===3)throw new TypeError(`callback provided to sync glob
See: https://github.com/isaacs/node-glob/issues/167`);if(!(this instanceof Re))return new Re(t,e);if(dg(this,t,e),this.noprocess)return this;var r=this.minimatch.set.length;this.matches=new Array(r);for(var i=0;i<r;i++)this._process(this.minimatch.set[i],i,!1);this._finish()}Re.prototype._finish=function(){if(Zc(this instanceof Re),this.realpath){var t=this;this.matches.forEach(function(e,r){var i=t.matches[r]=Object.create(null);for(var n in e)try{n=t._makeAbs(n);var s=pg.realpathSync(n,t.realpathCache);i[s]=!0}catch(o){if(o.syscall==="stat")i[t._makeAbs(n)]=!0;else throw o}})}Ft.finish(this)};Re.prototype._process=function(t,e,r){Zc(this instanceof Re);for(var i=0;typeof t[i]=="string";)i++;var n;switch(i){case t.length:this._processSimple(t.join("/"),e);return;case 0:n=null;break;default:n=t.slice(0,i).join("/");break}var s=t.slice(i),o;n===null?o=".":((Oi(n)||Oi(t.join("/")))&&(!n||!Oi(n))&&(n="/"+n),o=n);var a=this._makeAbs(o);if(!mg(this,o)){var l=s[0]===Kc.GLOBSTAR;l?this._processGlobStar(n,o,a,s,e,r):this._processReaddir(n,o,a,s,e,r)}};Re.prototype._processReaddir=function(t,e,r,i,n,s){var o=this._readdir(r,s);if(!!o){for(var a=i[0],l=!!this.minimatch.negate,c=a._glob,u=this.dot||c.charAt(0)===".",f=[],h=0;h<o.length;h++){var p=o[h];if(p.charAt(0)!=="."||u){var d;l&&!t?d=!p.match(a):d=p.match(a),d&&f.push(p)}}var m=f.length;if(m!==0){if(i.length===1&&!this.mark&&!this.stat){this.matches[n]||(this.matches[n]=Object.create(null));for(var h=0;h<m;h++){var p=f[h];t&&(t.slice(-1)!=="/"?p=t+"/"+p:p=t+p),p.charAt(0)==="/"&&!this.nomount&&(p=us.join(this.root,p)),this._emitMatch(n,p)}return}i.shift();for(var h=0;h<m;h++){var p=f[h],g;t?g=[t,p]:g=[p],this._process(g.concat(i),n,s)}}}};Re.prototype._emitMatch=function(t,e){if(!gg(this,e)){var r=this._makeAbs(e);if(this.mark&&(e=this._mark(e)),this.absolute&&(e=r),!this.matches[t][e]){if(this.nodir){var i=this.cache[r];if(i==="DIR"||Array.isArray(i))return}this.matches[t][e]=!0,this.stat&&this._stat(e)}}};Re.prototype._readdirInGlobStar=function(t){if(this.follow)return this._readdir(t,!1);var e,r,i;try{r=this.fs.lstatSync(t)}catch(s){if(s.code==="ENOENT")return null}var n=r&&r.isSymbolicLink();return this.symlinks[t]=n,!n&&r&&!r.isDirectory()?this.cache[t]="FILE":e=this._readdir(t,!1),e};Re.prototype._readdir=function(t,e){var r;if(e&&!fs(this.symlinks,t))return this._readdirInGlobStar(t);if(fs(this.cache,t)){var i=this.cache[t];if(!i||i==="FILE")return null;if(Array.isArray(i))return i}try{return this._readdirEntries(t,this.fs.readdirSync(t))}catch(n){return this._readdirError(t,n),null}};Re.prototype._readdirEntries=function(t,e){if(!this.mark&&!this.stat)for(var r=0;r<e.length;r++){var i=e[r];t==="/"?i=t+i:i=t+"/"+i,this.cache[i]=!0}return this.cache[t]=e,e};Re.prototype._readdirError=function(t,e){switch(e.code){case"ENOTSUP":case"ENOTDIR":var r=this._makeAbs(t);if(this.cache[r]="FILE",r===this.cwdAbs){var i=new Error(e.code+" invalid cwd "+this.cwd);throw i.path=this.cwd,i.code=e.code,i}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(t)]=!1;break;default:if(this.cache[this._makeAbs(t)]=!1,this.strict)throw e;this.silent||console.error("glob error",e);break}};Re.prototype._processGlobStar=function(t,e,r,i,n,s){var o=this._readdir(r,s);if(!!o){var a=i.slice(1),l=t?[t]:[],c=l.concat(a);this._process(c,n,!1);var u=o.length,f=this.symlinks[r];if(!(f&&s))for(var h=0;h<u;h++){var p=o[h];if(!(p.charAt(0)==="."&&!this.dot)){var d=l.concat(o[h],a);this._process(d,n,!0);var m=l.concat(o[h],i);this._process(m,n,!0)}}}};Re.prototype._processSimple=function(t,e){var r=this._stat(t);if(this.matches[e]||(this.matches[e]=Object.create(null)),!!r){if(t&&Oi(t)&&!this.nomount){var i=/[\/\\]$/.test(t);t.charAt(0)==="/"?t=us.join(this.root,t):(t=us.resolve(this.root,t),i&&(t+="/"))}process.platform==="win32"&&(t=t.replace(/\\/g,"/")),this._emitMatch(e,t)}};Re.prototype._stat=function(t){var e=this._makeAbs(t),r=t.slice(-1)==="/";if(t.length>this.maxLength)return!1;if(!this.stat&&fs(this.cache,e)){var o=this.cache[e];if(Array.isArray(o)&&(o="DIR"),!r||o==="DIR")return o;if(r&&o==="FILE")return!1}var i,n=this.statCache[e];if(!n){var s;try{s=this.fs.lstatSync(e)}catch(a){if(a&&(a.code==="ENOENT"||a.code==="ENOTDIR"))return this.statCache[e]=!1,!1}if(s&&s.isSymbolicLink())try{n=this.fs.statSync(e)}catch{n=s}else n=s}this.statCache[e]=n;var o=!0;return n&&(o=n.isDirectory()?"DIR":"FILE"),this.cache[e]=this.cache[e]||o,r&&o==="FILE"?!1:o};Re.prototype._mark=function(t){return Ft.mark(this,t)};Re.prototype._makeAbs=function(t){return Ft.makeAbs(this,t)}});var ps=x((Bx,tu)=>{tu.exports=eu;function eu(t,e){if(t&&e)return eu(t)(e);if(typeof t!="function")throw new TypeError("need wrapper function");return Object.keys(t).forEach(function(i){r[i]=t[i]}),r;function r(){for(var i=new Array(arguments.length),n=0;n<i.length;n++)i[n]=arguments[n];var s=t.apply(this,i),o=i[i.length-1];return typeof s=="function"&&s!==o&&Object.keys(o).forEach(function(a){s[a]=o[a]}),s}}});var ms=x((Lx,ds)=>{var ru=ps();ds.exports=ru(Ii);ds.exports.strict=ru(iu);Ii.proto=Ii(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return Ii(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return iu(this)},configurable:!0})});function Ii(t){var e=function(){return e.called?e.value:(e.called=!0,e.value=t.apply(this,arguments))};return e.called=!1,e}function iu(t){var e=function(){if(e.called)throw new Error(e.onceError);return e.called=!0,e.value=t.apply(this,arguments)},r=t.name||"Function wrapped with `once`";return e.onceError=r+" shouldn't be called more than once",e.called=!1,e}});var su=x((Nx,nu)=>{var vg=ps(),$r=Object.create(null),_g=ms();nu.exports=vg(xg);function xg(t,e){return $r[t]?($r[t].push(e),null):($r[t]=[e],yg(t))}function yg(t){return _g(function e(){var r=$r[t],i=r.length,n=bg(arguments);try{for(var s=0;s<i;s++)r[s].apply(null,n)}finally{r.length>i?(r.splice(0,i),process.nextTick(function(){e.apply(null,n)})):delete $r[t]}})}function bg(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i]=t[i];return r}});var hs=x((Mx,au)=>{au.exports=Mt;var wg=ns(),ou=Ur(),Px=ou.Minimatch,Eg=Vc(),Sg=require("events").EventEmitter,gs=require("path"),vs=require("assert"),Gr=Ci(),xs=Jc(),Ut=cs(),kg=Ut.setopts,_s=Ut.ownProp,ys=su(),Fx=require("util"),Cg=Ut.childrenIgnored,Og=Ut.isIgnored,Ig=ms();function Mt(t,e,r){if(typeof e=="function"&&(r=e,e={}),e||(e={}),e.sync){if(r)throw new TypeError("callback provided to sync glob");return xs(t,e)}return new me(t,e,r)}Mt.sync=xs;var Tg=Mt.GlobSync=xs.GlobSync;Mt.glob=Mt;function Ag(t,e){if(e===null||typeof e!="object")return t;for(var r=Object.keys(e),i=r.length;i--;)t[r[i]]=e[r[i]];return t}Mt.hasMagic=function(t,e){var r=Ag({},e);r.noprocess=!0;var i=new me(t,r),n=i.minimatch.set;if(!t)return!1;if(n.length>1)return!0;for(var s=0;s<n[0].length;s++)if(typeof n[0][s]!="string")return!0;return!1};Mt.Glob=me;Eg(me,Sg);function me(t,e,r){if(typeof e=="function"&&(r=e,e=null),e&&e.sync){if(r)throw new TypeError("callback provided to sync glob");return new Tg(t,e)}if(!(this instanceof me))return new me(t,e,r);kg(this,t,e),this._didRealPath=!1;var i=this.minimatch.set.length;this.matches=new Array(i),typeof r=="function"&&(r=Ig(r),this.on("error",r),this.on("end",function(l){r(null,l)}));var n=this;if(this._processing=0,this._emitQueue=[],this._processQueue=[],this.paused=!1,this.noprocess)return this;if(i===0)return a();for(var s=!0,o=0;o<i;o++)this._process(this.minimatch.set[o],o,!1,a);s=!1;function a(){--n._processing,n._processing<=0&&(s?process.nextTick(function(){n._finish()}):n._finish())}}me.prototype._finish=function(){if(vs(this instanceof me),!this.aborted){if(this.realpath&&!this._didRealpath)return this._realpath();Ut.finish(this),this.emit("end",this.found)}};me.prototype._realpath=function(){if(this._didRealpath)return;this._didRealpath=!0;var t=this.matches.length;if(t===0)return this._finish();for(var e=this,r=0;r<this.matches.length;r++)this._realpathSet(r,i);function i(){--t===0&&e._finish()}};me.prototype._realpathSet=function(t,e){var r=this.matches[t];if(!r)return e();var i=Object.keys(r),n=this,s=i.length;if(s===0)return e();var o=this.matches[t]=Object.create(null);i.forEach(function(a,l){a=n._makeAbs(a),wg.realpath(a,n.realpathCache,function(c,u){c?c.syscall==="stat"?o[a]=!0:n.emit("error",c):o[u]=!0,--s===0&&(n.matches[t]=o,e())})})};me.prototype._mark=function(t){return Ut.mark(this,t)};me.prototype._makeAbs=function(t){return Ut.makeAbs(this,t)};me.prototype.abort=function(){this.aborted=!0,this.emit("abort")};me.prototype.pause=function(){this.paused||(this.paused=!0,this.emit("pause"))};me.prototype.resume=function(){if(this.paused){if(this.emit("resume"),this.paused=!1,this._emitQueue.length){var t=this._emitQueue.slice(0);this._emitQueue.length=0;for(var e=0;e<t.length;e++){var r=t[e];this._emitMatch(r[0],r[1])}}if(this._processQueue.length){var i=this._processQueue.slice(0);this._processQueue.length=0;for(var e=0;e<i.length;e++){var n=i[e];this._processing--,this._process(n[0],n[1],n[2],n[3])}}}};me.prototype._process=function(t,e,r,i){if(vs(this instanceof me),vs(typeof i=="function"),!this.aborted){if(this._processing++,this.paused){this._processQueue.push([t,e,r,i]);return}for(var n=0;typeof t[n]=="string";)n++;var s;switch(n){case t.length:this._processSimple(t.join("/"),e,i);return;case 0:s=null;break;default:s=t.slice(0,n).join("/");break}var o=t.slice(n),a;s===null?a=".":((Gr(s)||Gr(t.join("/")))&&(!s||!Gr(s))&&(s="/"+s),a=s);var l=this._makeAbs(a);if(Cg(this,a))return i();var c=o[0]===ou.GLOBSTAR;c?this._processGlobStar(s,a,l,o,e,r,i):this._processReaddir(s,a,l,o,e,r,i)}};me.prototype._processReaddir=function(t,e,r,i,n,s,o){var a=this;this._readdir(r,s,function(l,c){return a._processReaddir2(t,e,r,i,n,s,c,o)})};me.prototype._processReaddir2=function(t,e,r,i,n,s,o,a){if(!o)return a();for(var l=i[0],c=!!this.minimatch.negate,u=l._glob,f=this.dot||u.charAt(0)===".",h=[],p=0;p<o.length;p++){var d=o[p];if(d.charAt(0)!=="."||f){var m;c&&!t?m=!d.match(l):m=d.match(l),m&&h.push(d)}}var g=h.length;if(g===0)return a();if(i.length===1&&!this.mark&&!this.stat){this.matches[n]||(this.matches[n]=Object.create(null));for(var p=0;p<g;p++){var d=h[p];t&&(t!=="/"?d=t+"/"+d:d=t+d),d.charAt(0)==="/"&&!this.nomount&&(d=gs.join(this.root,d)),this._emitMatch(n,d)}return a()}i.shift();for(var p=0;p<g;p++){var d=h[p],y;t&&(t!=="/"?d=t+"/"+d:d=t+d),this._process([d].concat(i),n,s,a)}a()};me.prototype._emitMatch=function(t,e){if(!this.aborted&&!Og(this,e)){if(this.paused){this._emitQueue.push([t,e]);return}var r=Gr(e)?e:this._makeAbs(e);if(this.mark&&(e=this._mark(e)),this.absolute&&(e=r),!this.matches[t][e]){if(this.nodir){var i=this.cache[r];if(i==="DIR"||Array.isArray(i))return}this.matches[t][e]=!0;var n=this.statCache[r];n&&this.emit("stat",e,n),this.emit("match",e)}}};me.prototype._readdirInGlobStar=function(t,e){if(this.aborted)return;if(this.follow)return this._readdir(t,!1,e);var r="lstat\0"+t,i=this,n=ys(r,s);n&&i.fs.lstat(t,n);function s(o,a){if(o&&o.code==="ENOENT")return e();var l=a&&a.isSymbolicLink();i.symlinks[t]=l,!l&&a&&!a.isDirectory()?(i.cache[t]="FILE",e()):i._readdir(t,!1,e)}};me.prototype._readdir=function(t,e,r){if(!this.aborted&&(r=ys("readdir\0"+t+"\0"+e,r),!!r)){if(e&&!_s(this.symlinks,t))return this._readdirInGlobStar(t,r);if(_s(this.cache,t)){var i=this.cache[t];if(!i||i==="FILE")return r();if(Array.isArray(i))return r(null,i)}var n=this;n.fs.readdir(t,Rg(this,t,r))}};function Rg(t,e,r){return function(i,n){i?t._readdirError(e,i,r):t._readdirEntries(e,n,r)}}me.prototype._readdirEntries=function(t,e,r){if(!this.aborted){if(!this.mark&&!this.stat)for(var i=0;i<e.length;i++){var n=e[i];t==="/"?n=t+n:n=t+"/"+n,this.cache[n]=!0}return this.cache[t]=e,r(null,e)}};me.prototype._readdirError=function(t,e,r){if(!this.aborted){switch(e.code){case"ENOTSUP":case"ENOTDIR":var i=this._makeAbs(t);if(this.cache[i]="FILE",i===this.cwdAbs){var n=new Error(e.code+" invalid cwd "+this.cwd);n.path=this.cwd,n.code=e.code,this.emit("error",n),this.abort()}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(t)]=!1;break;default:this.cache[this._makeAbs(t)]=!1,this.strict&&(this.emit("error",e),this.abort()),this.silent||console.error("glob error",e);break}return r()}};me.prototype._processGlobStar=function(t,e,r,i,n,s,o){var a=this;this._readdir(r,s,function(l,c){a._processGlobStar2(t,e,r,i,n,s,c,o)})};me.prototype._processGlobStar2=function(t,e,r,i,n,s,o,a){if(!o)return a();var l=i.slice(1),c=t?[t]:[],u=c.concat(l);this._process(u,n,!1,a);var f=this.symlinks[r],h=o.length;if(f&&s)return a();for(var p=0;p<h;p++){var d=o[p];if(!(d.charAt(0)==="."&&!this.dot)){var m=c.concat(o[p],l);this._process(m,n,!0,a);var g=c.concat(o[p],i);this._process(g,n,!0,a)}}a()};me.prototype._processSimple=function(t,e,r){var i=this;this._stat(t,function(n,s){i._processSimple2(t,e,n,s,r)})};me.prototype._processSimple2=function(t,e,r,i,n){if(this.matches[e]||(this.matches[e]=Object.create(null)),!i)return n();if(t&&Gr(t)&&!this.nomount){var s=/[\/\\]$/.test(t);t.charAt(0)==="/"?t=gs.join(this.root,t):(t=gs.resolve(this.root,t),s&&(t+="/"))}process.platform==="win32"&&(t=t.replace(/\\/g,"/")),this._emitMatch(e,t),n()};me.prototype._stat=function(t,e){var r=this._makeAbs(t),i=t.slice(-1)==="/";if(t.length>this.maxLength)return e();if(!this.stat&&_s(this.cache,r)){var n=this.cache[r];if(Array.isArray(n)&&(n="DIR"),!i||n==="DIR")return e(null,n);if(i&&n==="FILE")return e()}var s,o=this.statCache[r];if(o!==void 0){if(o===!1)return e(null,o);var a=o.isDirectory()?"DIR":"FILE";return i&&a==="FILE"?e():e(null,a,o)}var l=this,c=ys("stat\0"+r,u);c&&l.fs.lstat(r,c);function u(f,h){if(h&&h.isSymbolicLink())return l.fs.stat(r,function(p,d){p?l._stat2(t,r,null,h,e):l._stat2(t,r,p,d,e)});l._stat2(t,r,f,h,e)}};me.prototype._stat2=function(t,e,r,i,n){if(r&&(r.code==="ENOENT"||r.code==="ENOTDIR"))return this.statCache[e]=!1,n();var s=t.slice(-1)==="/";if(this.statCache[e]=i,e.slice(-1)==="/"&&i&&!i.isDirectory())return n(null,!1,i);var o=!0;return i&&(o=i.isDirectory()?"DIR":"FILE"),this.cache[e]=this.cache[e]||o,s&&o==="FILE"?n():n(null,o,i)}});var mu=x((Ux,du)=>{var xe=require("assert"),fu=require("path"),lu=require("fs"),hr;try{hr=hs()}catch{}var Bg={nosort:!0,silent:!0},bs=0,zr=process.platform==="win32",hu=t=>{if(["unlink","chmod","stat","lstat","rmdir","readdir"].forEach(r=>{t[r]=t[r]||lu[r],r=r+"Sync",t[r]=t[r]||lu[r]}),t.maxBusyTries=t.maxBusyTries||3,t.emfileWait=t.emfileWait||1e3,t.glob===!1&&(t.disableGlob=!0),t.disableGlob!==!0&&hr===void 0)throw Error("glob dependency not found, set `options.disableGlob = true` if intentional");t.disableGlob=t.disableGlob||!1,t.glob=t.glob||Bg},Es=(t,e,r)=>{typeof e=="function"&&(r=e,e={}),xe(t,"rimraf: missing path"),xe.equal(typeof t,"string","rimraf: path should be a string"),xe.equal(typeof r,"function","rimraf: callback function required"),xe(e,"rimraf: invalid options argument provided"),xe.equal(typeof e,"object","rimraf: options should be object"),hu(e);let i=0,n=null,s=0,o=l=>{n=n||l,--s===0&&r(n)},a=(l,c)=>{if(l)return r(l);if(s=c.length,s===0)return r();c.forEach(u=>{let f=h=>{if(h){if((h.code==="EBUSY"||h.code==="ENOTEMPTY"||h.code==="EPERM")&&i<e.maxBusyTries)return i++,setTimeout(()=>ws(u,e,f),i*100);if(h.code==="EMFILE"&&bs<e.emfileWait)return setTimeout(()=>ws(u,e,f),bs++);h.code==="ENOENT"&&(h=null)}bs=0,o(h)};ws(u,e,f)})};if(e.disableGlob||!hr.hasMagic(t))return a(null,[t]);e.lstat(t,(l,c)=>{if(!l)return a(null,[t]);hr(t,e.glob,a)})},ws=(t,e,r)=>{xe(t),xe(e),xe(typeof r=="function"),e.lstat(t,(i,n)=>{if(i&&i.code==="ENOENT")return r(null);if(i&&i.code==="EPERM"&&zr&&cu(t,e,i,r),n&&n.isDirectory())return Ti(t,e,i,r);e.unlink(t,s=>{if(s){if(s.code==="ENOENT")return r(null);if(s.code==="EPERM")return zr?cu(t,e,s,r):Ti(t,e,s,r);if(s.code==="EISDIR")return Ti(t,e,s,r)}return r(s)})})},cu=(t,e,r,i)=>{xe(t),xe(e),xe(typeof i=="function"),e.chmod(t,438,n=>{n?i(n.code==="ENOENT"?null:r):e.stat(t,(s,o)=>{s?i(s.code==="ENOENT"?null:r):o.isDirectory()?Ti(t,e,r,i):e.unlink(t,i)})})},uu=(t,e,r)=>{xe(t),xe(e);try{e.chmodSync(t,438)}catch(n){if(n.code==="ENOENT")return;throw r}let i;try{i=e.statSync(t)}catch(n){if(n.code==="ENOENT")return;throw r}i.isDirectory()?Ai(t,e,r):e.unlinkSync(t)},Ti=(t,e,r,i)=>{xe(t),xe(e),xe(typeof i=="function"),e.rmdir(t,n=>{n&&(n.code==="ENOTEMPTY"||n.code==="EEXIST"||n.code==="EPERM")?Lg(t,e,i):n&&n.code==="ENOTDIR"?i(r):i(n)})},Lg=(t,e,r)=>{xe(t),xe(e),xe(typeof r=="function"),e.readdir(t,(i,n)=>{if(i)return r(i);let s=n.length;if(s===0)return e.rmdir(t,r);let o;n.forEach(a=>{Es(fu.join(t,a),e,l=>{if(!o){if(l)return r(o=l);--s===0&&e.rmdir(t,r)}})})})},pu=(t,e)=>{e=e||{},hu(e),xe(t,"rimraf: missing path"),xe.equal(typeof t,"string","rimraf: path should be a string"),xe(e,"rimraf: missing options"),xe.equal(typeof e,"object","rimraf: options should be object");let r;if(e.disableGlob||!hr.hasMagic(t))r=[t];else try{e.lstatSync(t),r=[t]}catch{r=hr.sync(t,e.glob)}if(!!r.length)for(let i=0;i<r.length;i++){let n=r[i],s;try{s=e.lstatSync(n)}catch(o){if(o.code==="ENOENT")return;o.code==="EPERM"&&zr&&uu(n,e,o)}try{s&&s.isDirectory()?Ai(n,e,null):e.unlinkSync(n)}catch(o){if(o.code==="ENOENT")return;if(o.code==="EPERM")return zr?uu(n,e,o):Ai(n,e,o);if(o.code!=="EISDIR")throw o;Ai(n,e,o)}}},Ai=(t,e,r)=>{xe(t),xe(e);try{e.rmdirSync(t)}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR")throw r;(i.code==="ENOTEMPTY"||i.code==="EEXIST"||i.code==="EPERM")&&Ng(t,e)}},Ng=(t,e)=>{xe(t),xe(e),e.readdirSync(t).forEach(n=>pu(fu.join(t,n),e));let r=zr?100:1,i=0;do{let n=!0;try{let s=e.rmdirSync(t,e);return n=!1,s}finally{if(++i<r&&n)continue}}while(!0)};du.exports=Es;Es.sync=pu});var vu=x(gu=>{var H=gu,{Buffer:Ri}=require("buffer"),Pg=require("os");H.toBuffer=function(t,e,r){r=~~r;let i;if(this.isV4Format(t))i=e||Ri.alloc(r+4),t.split(/\./g).map(n=>{i[r++]=parseInt(n,10)&255});else if(this.isV6Format(t)){let n=t.split(":",8),s;for(s=0;s<n.length;s++){let o=this.isV4Format(n[s]),a;o&&(a=this.toBuffer(n[s]),n[s]=a.slice(0,2).toString("hex")),a&&++s<8&&n.splice(s,0,a.slice(2,4).toString("hex"))}if(n[0]==="")for(;n.length<8;)n.unshift("0");else if(n[n.length-1]==="")for(;n.length<8;)n.push("0");else if(n.length<8){for(s=0;s<n.length&&n[s]!=="";s++);let o=[s,1];for(s=9-n.length;s>0;s--)o.push("0");n.splice(...o)}for(i=e||Ri.alloc(r+16),s=0;s<n.length;s++){let o=parseInt(n[s],16);i[r++]=o>>8&255,i[r++]=o&255}}if(!i)throw Error(`Invalid ip address: ${t}`);return i};H.toString=function(t,e,r){e=~~e,r=r||t.length-e;let i=[];if(r===4){for(let n=0;n<r;n++)i.push(t[e+n]);i=i.join(".")}else if(r===16){for(let n=0;n<r;n+=2)i.push(t.readUInt16BE(e+n).toString(16));i=i.join(":"),i=i.replace(/(^|:)0(:0)*:0(:|$)/,"$1::$3"),i=i.replace(/:{3,4}/,"::")}return i};var Fg=/^(\d{1,3}\.){3,3}\d{1,3}$/,Mg=/^(::)?(((\d{1,3}\.){3}(\d{1,3}){1})?([0-9a-f]){0,4}:{0,2}){1,8}(::)?$/i;H.isV4Format=function(t){return Fg.test(t)};H.isV6Format=function(t){return Mg.test(t)};function Wr(t){return t===4?"ipv4":t===6?"ipv6":t?t.toLowerCase():"ipv4"}H.fromPrefixLen=function(t,e){t>32?e="ipv6":e=Wr(e);let r=4;e==="ipv6"&&(r=16);let i=Ri.alloc(r);for(let n=0,s=i.length;n<s;++n){let o=8;t<8&&(o=t),t-=o,i[n]=~(255>>o)&255}return H.toString(i)};H.mask=function(t,e){t=H.toBuffer(t),e=H.toBuffer(e);let r=Ri.alloc(Math.max(t.length,e.length)),i;if(t.length===e.length)for(i=0;i<t.length;i++)r[i]=t[i]&e[i];else if(e.length===4)for(i=0;i<e.length;i++)r[i]=t[t.length-4+i]&e[i];else{for(i=0;i<r.length-6;i++)r[i]=0;for(r[10]=255,r[11]=255,i=0;i<t.length;i++)r[i+12]=t[i]&e[i+12];i+=12}for(;i<r.length;i++)r[i]=0;return H.toString(r)};H.cidr=function(t){let e=t.split("/"),r=e[0];if(e.length!==2)throw new Error(`invalid CIDR subnet: ${r}`);let i=H.fromPrefixLen(parseInt(e[1],10));return H.mask(r,i)};H.subnet=function(t,e){let r=H.toLong(H.mask(t,e)),i=H.toBuffer(e),n=0;for(let o=0;o<i.length;o++)if(i[o]===255)n+=8;else{let a=i[o]&255;for(;a;)a=a<<1&255,n++}let s=2**(32-n);return{networkAddress:H.fromLong(r),firstAddress:s<=2?H.fromLong(r):H.fromLong(r+1),lastAddress:s<=2?H.fromLong(r+s-1):H.fromLong(r+s-2),broadcastAddress:H.fromLong(r+s-1),subnetMask:e,subnetMaskLength:n,numHosts:s<=2?s:s-2,length:s,contains(o){return r===H.toLong(H.mask(o,e))}}};H.cidrSubnet=function(t){let e=t.split("/"),r=e[0];if(e.length!==2)throw new Error(`invalid CIDR subnet: ${r}`);let i=H.fromPrefixLen(parseInt(e[1],10));return H.subnet(r,i)};H.not=function(t){let e=H.toBuffer(t);for(let r=0;r<e.length;r++)e[r]=255^e[r];return H.toString(e)};H.or=function(t,e){if(t=H.toBuffer(t),e=H.toBuffer(e),t.length===e.length){for(let s=0;s<t.length;++s)t[s]|=e[s];return H.toString(t)}let r=t,i=e;e.length>t.length&&(r=e,i=t);let n=r.length-i.length;for(let s=n;s<r.length;++s)r[s]|=i[s-n];return H.toString(r)};H.isEqual=function(t,e){if(t=H.toBuffer(t),e=H.toBuffer(e),t.length===e.length){for(let i=0;i<t.length;i++)if(t[i]!==e[i])return!1;return!0}if(e.length===4){let i=e;e=t,t=i}for(let i=0;i<10;i++)if(e[i]!==0)return!1;let r=e.readUInt16BE(10);if(r!==0&&r!==65535)return!1;for(let i=0;i<4;i++)if(t[i]!==e[i+12])return!1;return!0};H.isPrivate=function(t){return/^(::f{4}:)?10\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(t)||/^(::f{4}:)?192\.168\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(t)||/^(::f{4}:)?172\.(1[6-9]|2\d|30|31)\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(t)||/^(::f{4}:)?127\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(t)||/^(::f{4}:)?169\.254\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(t)||/^f[cd][0-9a-f]{2}:/i.test(t)||/^fe80:/i.test(t)||/^::1$/.test(t)||/^::$/.test(t)};H.isPublic=function(t){return!H.isPrivate(t)};H.isLoopback=function(t){return/^(::f{4}:)?127\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})/.test(t)||/^fe80::1$/.test(t)||/^::1$/.test(t)||/^::$/.test(t)};H.loopback=function(t){if(t=Wr(t),t!=="ipv4"&&t!=="ipv6")throw new Error("family must be ipv4 or ipv6");return t==="ipv4"?"127.0.0.1":"fe80::1"};H.address=function(t,e){let r=Pg.networkInterfaces();if(e=Wr(e),t&&t!=="private"&&t!=="public"){let n=r[t].filter(s=>Wr(s.family)===e);return n.length===0?void 0:n[0].address}let i=Object.keys(r).map(n=>{let s=r[n].filter(o=>(o.family=Wr(o.family),o.family!==e||H.isLoopback(o.address)?!1:t?t==="public"?H.isPrivate(o.address):H.isPublic(o.address):!0));return s.length?s[0].address:void 0}).filter(Boolean);return i.length?i[0]:H.loopback(e)};H.toLong=function(t){let e=0;return t.split(".").forEach(r=>{e<<=8,e+=parseInt(r)}),e>>>0};H.fromLong=function(t){return`${t>>>24}.${t>>16&255}.${t>>8&255}.${t&255}`}});var bu=x(ht=>{"use strict";Object.defineProperty(ht,"__esModule",{value:!0});var _u=require("buffer"),Dt={INVALID_ENCODING:"Invalid encoding provided. Please specify a valid encoding the internal Node.js Buffer supports.",INVALID_SMARTBUFFER_SIZE:"Invalid size provided. Size must be a valid integer greater than zero.",INVALID_SMARTBUFFER_BUFFER:"Invalid Buffer provided in SmartBufferOptions.",INVALID_SMARTBUFFER_OBJECT:"Invalid SmartBufferOptions object supplied to SmartBuffer constructor or factory methods.",INVALID_OFFSET:"An invalid offset value was provided.",INVALID_OFFSET_NON_NUMBER:"An invalid offset value was provided. A numeric value is required.",INVALID_LENGTH:"An invalid length value was provided.",INVALID_LENGTH_NON_NUMBER:"An invalid length value was provived. A numeric value is required.",INVALID_TARGET_OFFSET:"Target offset is beyond the bounds of the internal SmartBuffer data.",INVALID_TARGET_LENGTH:"Specified length value moves cursor beyong the bounds of the internal SmartBuffer data.",INVALID_READ_BEYOND_BOUNDS:"Attempted to read beyond the bounds of the managed data.",INVALID_WRITE_BEYOND_BOUNDS:"Attempted to write beyond the bounds of the managed data."};ht.ERRORS=Dt;function Ug(t){if(!_u.Buffer.isEncoding(t))throw new Error(Dt.INVALID_ENCODING)}ht.checkEncoding=Ug;function xu(t){return typeof t=="number"&&isFinite(t)&&Hg(t)}ht.isFiniteInteger=xu;function yu(t,e){if(typeof t=="number"){if(!xu(t)||t<0)throw new Error(e?Dt.INVALID_OFFSET:Dt.INVALID_LENGTH)}else throw new Error(e?Dt.INVALID_OFFSET_NON_NUMBER:Dt.INVALID_LENGTH_NON_NUMBER)}function Dg(t){yu(t,!1)}ht.checkLengthValue=Dg;function jg(t){yu(t,!0)}ht.checkOffsetValue=jg;function qg(t,e){if(t<0||t>e.length)throw new Error(Dt.INVALID_TARGET_OFFSET)}ht.checkTargetOffset=qg;function Hg(t){return typeof t=="number"&&isFinite(t)&&Math.floor(t)===t}function Vg(t){if(typeof BigInt=="undefined")throw new Error("Platform does not support JS BigInt type.");if(typeof _u.Buffer.prototype[t]=="undefined")throw new Error(`Platform does not support Buffer.prototype.${t}.`)}ht.bigIntAndBufferInt64Check=Vg});var Eu=x(Ss=>{"use strict";Object.defineProperty(Ss,"__esModule",{value:!0});var re=bu(),wu=4096,$g="utf8",Yr=class{constructor(e){if(this.length=0,this._encoding=$g,this._writeOffset=0,this._readOffset=0,Yr.isSmartBufferOptions(e))if(e.encoding&&(re.checkEncoding(e.encoding),this._encoding=e.encoding),e.size)if(re.isFiniteInteger(e.size)&&e.size>0)this._buff=Buffer.allocUnsafe(e.size);else throw new Error(re.ERRORS.INVALID_SMARTBUFFER_SIZE);else if(e.buff)if(Buffer.isBuffer(e.buff))this._buff=e.buff,this.length=e.buff.length;else throw new Error(re.ERRORS.INVALID_SMARTBUFFER_BUFFER);else this._buff=Buffer.allocUnsafe(wu);else{if(typeof e!="undefined")throw new Error(re.ERRORS.INVALID_SMARTBUFFER_OBJECT);this._buff=Buffer.allocUnsafe(wu)}}static fromSize(e,r){return new this({size:e,encoding:r})}static fromBuffer(e,r){return new this({buff:e,encoding:r})}static fromOptions(e){return new this(e)}static isSmartBufferOptions(e){let r=e;return r&&(r.encoding!==void 0||r.size!==void 0||r.buff!==void 0)}readInt8(e){return this._readNumberValue(Buffer.prototype.readInt8,1,e)}readInt16BE(e){return this._readNumberValue(Buffer.prototype.readInt16BE,2,e)}readInt16LE(e){return this._readNumberValue(Buffer.prototype.readInt16LE,2,e)}readInt32BE(e){return this._readNumberValue(Buffer.prototype.readInt32BE,4,e)}readInt32LE(e){return this._readNumberValue(Buffer.prototype.readInt32LE,4,e)}readBigInt64BE(e){return re.bigIntAndBufferInt64Check("readBigInt64BE"),this._readNumberValue(Buffer.prototype.readBigInt64BE,8,e)}readBigInt64LE(e){return re.bigIntAndBufferInt64Check("readBigInt64LE"),this._readNumberValue(Buffer.prototype.readBigInt64LE,8,e)}writeInt8(e,r){return this._writeNumberValue(Buffer.prototype.writeInt8,1,e,r),this}insertInt8(e,r){return this._insertNumberValue(Buffer.prototype.writeInt8,1,e,r)}writeInt16BE(e,r){return this._writeNumberValue(Buffer.prototype.writeInt16BE,2,e,r)}insertInt16BE(e,r){return this._insertNumberValue(Buffer.prototype.writeInt16BE,2,e,r)}writeInt16LE(e,r){return this._writeNumberValue(Buffer.prototype.writeInt16LE,2,e,r)}insertInt16LE(e,r){return this._insertNumberValue(Buffer.prototype.writeInt16LE,2,e,r)}writeInt32BE(e,r){return this._writeNumberValue(Buffer.prototype.writeInt32BE,4,e,r)}insertInt32BE(e,r){return this._insertNumberValue(Buffer.prototype.writeInt32BE,4,e,r)}writeInt32LE(e,r){return this._writeNumberValue(Buffer.prototype.writeInt32LE,4,e,r)}insertInt32LE(e,r){return this._insertNumberValue(Buffer.prototype.writeInt32LE,4,e,r)}writeBigInt64BE(e,r){return re.bigIntAndBufferInt64Check("writeBigInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigInt64BE,8,e,r)}insertBigInt64BE(e,r){return re.bigIntAndBufferInt64Check("writeBigInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigInt64BE,8,e,r)}writeBigInt64LE(e,r){return re.bigIntAndBufferInt64Check("writeBigInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigInt64LE,8,e,r)}insertBigInt64LE(e,r){return re.bigIntAndBufferInt64Check("writeBigInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigInt64LE,8,e,r)}readUInt8(e){return this._readNumberValue(Buffer.prototype.readUInt8,1,e)}readUInt16BE(e){return this._readNumberValue(Buffer.prototype.readUInt16BE,2,e)}readUInt16LE(e){return this._readNumberValue(Buffer.prototype.readUInt16LE,2,e)}readUInt32BE(e){return this._readNumberValue(Buffer.prototype.readUInt32BE,4,e)}readUInt32LE(e){return this._readNumberValue(Buffer.prototype.readUInt32LE,4,e)}readBigUInt64BE(e){return re.bigIntAndBufferInt64Check("readBigUInt64BE"),this._readNumberValue(Buffer.prototype.readBigUInt64BE,8,e)}readBigUInt64LE(e){return re.bigIntAndBufferInt64Check("readBigUInt64LE"),this._readNumberValue(Buffer.prototype.readBigUInt64LE,8,e)}writeUInt8(e,r){return this._writeNumberValue(Buffer.prototype.writeUInt8,1,e,r)}insertUInt8(e,r){return this._insertNumberValue(Buffer.prototype.writeUInt8,1,e,r)}writeUInt16BE(e,r){return this._writeNumberValue(Buffer.prototype.writeUInt16BE,2,e,r)}insertUInt16BE(e,r){return this._insertNumberValue(Buffer.prototype.writeUInt16BE,2,e,r)}writeUInt16LE(e,r){return this._writeNumberValue(Buffer.prototype.writeUInt16LE,2,e,r)}insertUInt16LE(e,r){return this._insertNumberValue(Buffer.prototype.writeUInt16LE,2,e,r)}writeUInt32BE(e,r){return this._writeNumberValue(Buffer.prototype.writeUInt32BE,4,e,r)}insertUInt32BE(e,r){return this._insertNumberValue(Buffer.prototype.writeUInt32BE,4,e,r)}writeUInt32LE(e,r){return this._writeNumberValue(Buffer.prototype.writeUInt32LE,4,e,r)}insertUInt32LE(e,r){return this._insertNumberValue(Buffer.prototype.writeUInt32LE,4,e,r)}writeBigUInt64BE(e,r){return re.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,r)}insertBigUInt64BE(e,r){return re.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,r)}writeBigUInt64LE(e,r){return re.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,r)}insertBigUInt64LE(e,r){return re.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,r)}readFloatBE(e){return this._readNumberValue(Buffer.prototype.readFloatBE,4,e)}readFloatLE(e){return this._readNumberValue(Buffer.prototype.readFloatLE,4,e)}writeFloatBE(e,r){return this._writeNumberValue(Buffer.prototype.writeFloatBE,4,e,r)}insertFloatBE(e,r){return this._insertNumberValue(Buffer.prototype.writeFloatBE,4,e,r)}writeFloatLE(e,r){return this._writeNumberValue(Buffer.prototype.writeFloatLE,4,e,r)}insertFloatLE(e,r){return this._insertNumberValue(Buffer.prototype.writeFloatLE,4,e,r)}readDoubleBE(e){return this._readNumberValue(Buffer.prototype.readDoubleBE,8,e)}readDoubleLE(e){return this._readNumberValue(Buffer.prototype.readDoubleLE,8,e)}writeDoubleBE(e,r){return this._writeNumberValue(Buffer.prototype.writeDoubleBE,8,e,r)}insertDoubleBE(e,r){return this._insertNumberValue(Buffer.prototype.writeDoubleBE,8,e,r)}writeDoubleLE(e,r){return this._writeNumberValue(Buffer.prototype.writeDoubleLE,8,e,r)}insertDoubleLE(e,r){return this._insertNumberValue(Buffer.prototype.writeDoubleLE,8,e,r)}readString(e,r){let i;typeof e=="number"?(re.checkLengthValue(e),i=Math.min(e,this.length-this._readOffset)):(r=e,i=this.length-this._readOffset),typeof r!="undefined"&&re.checkEncoding(r);let n=this._buff.slice(this._readOffset,this._readOffset+i).toString(r||this._encoding);return this._readOffset+=i,n}insertString(e,r,i){return re.checkOffsetValue(r),this._handleString(e,!0,r,i)}writeString(e,r,i){return this._handleString(e,!1,r,i)}readStringNT(e){typeof e!="undefined"&&re.checkEncoding(e);let r=this.length;for(let n=this._readOffset;n<this.length;n++)if(this._buff[n]===0){r=n;break}let i=this._buff.slice(this._readOffset,r);return this._readOffset=r+1,i.toString(e||this._encoding)}insertStringNT(e,r,i){return re.checkOffsetValue(r),this.insertString(e,r,i),this.insertUInt8(0,r+e.length),this}writeStringNT(e,r,i){return this.writeString(e,r,i),this.writeUInt8(0,typeof r=="number"?r+e.length:this.writeOffset),this}readBuffer(e){typeof e!="undefined"&&re.checkLengthValue(e);let r=typeof e=="number"?e:this.length,i=Math.min(this.length,this._readOffset+r),n=this._buff.slice(this._readOffset,i);return this._readOffset=i,n}insertBuffer(e,r){return re.checkOffsetValue(r),this._handleBuffer(e,!0,r)}writeBuffer(e,r){return this._handleBuffer(e,!1,r)}readBufferNT(){let e=this.length;for(let i=this._readOffset;i<this.length;i++)if(this._buff[i]===0){e=i;break}let r=this._buff.slice(this._readOffset,e);return this._readOffset=e+1,r}insertBufferNT(e,r){return re.checkOffsetValue(r),this.insertBuffer(e,r),this.insertUInt8(0,r+e.length),this}writeBufferNT(e,r){return typeof r!="undefined"&&re.checkOffsetValue(r),this.writeBuffer(e,r),this.writeUInt8(0,typeof r=="number"?r+e.length:this._writeOffset),this}clear(){return this._writeOffset=0,this._readOffset=0,this.length=0,this}remaining(){return this.length-this._readOffset}get readOffset(){return this._readOffset}set readOffset(e){re.checkOffsetValue(e),re.checkTargetOffset(e,this),this._readOffset=e}get writeOffset(){return this._writeOffset}set writeOffset(e){re.checkOffsetValue(e),re.checkTargetOffset(e,this),this._writeOffset=e}get encoding(){return this._encoding}set encoding(e){re.checkEncoding(e),this._encoding=e}get internalBuffer(){return this._buff}toBuffer(){return this._buff.slice(0,this.length)}toString(e){let r=typeof e=="string"?e:this._encoding;return re.checkEncoding(r),this._buff.toString(r,0,this.length)}destroy(){return this.clear(),this}_handleString(e,r,i,n){let s=this._writeOffset,o=this._encoding;typeof i=="number"?s=i:typeof i=="string"&&(re.checkEncoding(i),o=i),typeof n=="string"&&(re.checkEncoding(n),o=n);let a=Buffer.byteLength(e,o);return r?this.ensureInsertable(a,s):this._ensureWriteable(a,s),this._buff.write(e,s,a,o),r?this._writeOffset+=a:typeof i=="number"?this._writeOffset=Math.max(this._writeOffset,s+a):this._writeOffset+=a,this}_handleBuffer(e,r,i){let n=typeof i=="number"?i:this._writeOffset;return r?this.ensureInsertable(e.length,n):this._ensureWriteable(e.length,n),e.copy(this._buff,n),r?this._writeOffset+=e.length:typeof i=="number"?this._writeOffset=Math.max(this._writeOffset,n+e.length):this._writeOffset+=e.length,this}ensureReadable(e,r){let i=this._readOffset;if(typeof r!="undefined"&&(re.checkOffsetValue(r),i=r),i<0||i+e>this.length)throw new Error(re.ERRORS.INVALID_READ_BEYOND_BOUNDS)}ensureInsertable(e,r){re.checkOffsetValue(r),this._ensureCapacity(this.length+e),r<this.length&&this._buff.copy(this._buff,r+e,r,this._buff.length),r+e>this.length?this.length=r+e:this.length+=e}_ensureWriteable(e,r){let i=typeof r=="number"?r:this._writeOffset;this._ensureCapacity(i+e),i+e>this.length&&(this.length=i+e)}_ensureCapacity(e){let r=this._buff.length;if(e>r){let i=this._buff,n=r*3/2+1;n<e&&(n=e),this._buff=Buffer.allocUnsafe(n),i.copy(this._buff,0,0,r)}}_readNumberValue(e,r,i){this.ensureReadable(r,i);let n=e.call(this._buff,typeof i=="number"?i:this._readOffset);return typeof i=="undefined"&&(this._readOffset+=r),n}_insertNumberValue(e,r,i,n){return re.checkOffsetValue(n),this.ensureInsertable(r,n),e.call(this._buff,i,n),this._writeOffset+=r,this}_writeNumberValue(e,r,i,n){if(typeof n=="number"){if(n<0)throw new Error(re.ERRORS.INVALID_WRITE_BEYOND_BOUNDS);re.checkOffsetValue(n)}let s=typeof n=="number"?n:this._writeOffset;return this._ensureWriteable(r,s),e.call(this._buff,i,s),typeof n=="number"?this._writeOffset=Math.max(this._writeOffset,s+r):this._writeOffset+=r,this}};Ss.SmartBuffer=Yr});var Rs=x(we=>{"use strict";Object.defineProperty(we,"__esModule",{value:!0});we.SOCKS5_NO_ACCEPTABLE_AUTH=we.SOCKS5_CUSTOM_AUTH_END=we.SOCKS5_CUSTOM_AUTH_START=we.SOCKS_INCOMING_PACKET_SIZES=we.SocksClientState=we.Socks5Response=we.Socks5HostType=we.Socks5Auth=we.Socks4Response=we.SocksCommand=we.ERRORS=we.DEFAULT_TIMEOUT=void 0;var Gg=3e4;we.DEFAULT_TIMEOUT=Gg;var zg={InvalidSocksCommand:"An invalid SOCKS command was provided. Valid options are connect, bind, and associate.",InvalidSocksCommandForOperation:"An invalid SOCKS command was provided. Only a subset of commands are supported for this operation.",InvalidSocksCommandChain:"An invalid SOCKS command was provided. Chaining currently only supports the connect command.",InvalidSocksClientOptionsDestination:"An invalid destination host was provided.",InvalidSocksClientOptionsExistingSocket:"An invalid existing socket was provided. This should be an instance of stream.Duplex.",InvalidSocksClientOptionsProxy:"Invalid SOCKS proxy details were provided.",InvalidSocksClientOptionsTimeout:"An invalid timeout value was provided. Please enter a value above 0 (in ms).",InvalidSocksClientOptionsProxiesLength:"At least two socks proxies must be provided for chaining.",InvalidSocksClientOptionsCustomAuthRange:"Custom auth must be a value between 0x80 and 0xFE.",InvalidSocksClientOptionsCustomAuthOptions:"When a custom_auth_method is provided, custom_auth_request_handler, custom_auth_response_size, and custom_auth_response_handler must also be provided and valid.",NegotiationError:"Negotiation error",SocketClosed:"Socket closed",ProxyConnectionTimedOut:"Proxy connection timed out",InternalError:"SocksClient internal error (this should not happen)",InvalidSocks4HandshakeResponse:"Received invalid Socks4 handshake response",Socks4ProxyRejectedConnection:"Socks4 Proxy rejected connection",InvalidSocks4IncomingConnectionResponse:"Socks4 invalid incoming connection response",Socks4ProxyRejectedIncomingBoundConnection:"Socks4 Proxy rejected incoming bound connection",InvalidSocks5InitialHandshakeResponse:"Received invalid Socks5 initial handshake response",InvalidSocks5IntiailHandshakeSocksVersion:"Received invalid Socks5 initial handshake (invalid socks version)",InvalidSocks5InitialHandshakeNoAcceptedAuthType:"Received invalid Socks5 initial handshake (no accepted authentication type)",InvalidSocks5InitialHandshakeUnknownAuthType:"Received invalid Socks5 initial handshake (unknown authentication type)",Socks5AuthenticationFailed:"Socks5 Authentication failed",InvalidSocks5FinalHandshake:"Received invalid Socks5 final handshake response",InvalidSocks5FinalHandshakeRejected:"Socks5 proxy rejected connection",InvalidSocks5IncomingConnectionResponse:"Received invalid Socks5 incoming connection response",Socks5ProxyRejectedIncomingBoundConnection:"Socks5 Proxy rejected incoming bound connection"};we.ERRORS=zg;var Wg={Socks5InitialHandshakeResponse:2,Socks5UserPassAuthenticationResponse:2,Socks5ResponseHeader:5,Socks5ResponseIPv4:10,Socks5ResponseIPv6:22,Socks5ResponseHostname:t=>t+7,Socks4Response:8};we.SOCKS_INCOMING_PACKET_SIZES=Wg;var ks;(function(t){t[t.connect=1]="connect",t[t.bind=2]="bind",t[t.associate=3]="associate"})(ks||(ks={}));we.SocksCommand=ks;var Cs;(function(t){t[t.Granted=90]="Granted",t[t.Failed=91]="Failed",t[t.Rejected=92]="Rejected",t[t.RejectedIdent=93]="RejectedIdent"})(Cs||(Cs={}));we.Socks4Response=Cs;var Os;(function(t){t[t.NoAuth=0]="NoAuth",t[t.GSSApi=1]="GSSApi",t[t.UserPass=2]="UserPass"})(Os||(Os={}));we.Socks5Auth=Os;var Yg=128;we.SOCKS5_CUSTOM_AUTH_START=Yg;var Kg=254;we.SOCKS5_CUSTOM_AUTH_END=Kg;var Zg=255;we.SOCKS5_NO_ACCEPTABLE_AUTH=Zg;var Is;(function(t){t[t.Granted=0]="Granted",t[t.Failure=1]="Failure",t[t.NotAllowed=2]="NotAllowed",t[t.NetworkUnreachable=3]="NetworkUnreachable",t[t.HostUnreachable=4]="HostUnreachable",t[t.ConnectionRefused=5]="ConnectionRefused",t[t.TTLExpired=6]="TTLExpired",t[t.CommandNotSupported=7]="CommandNotSupported",t[t.AddressNotSupported=8]="AddressNotSupported"})(Is||(Is={}));we.Socks5Response=Is;var Ts;(function(t){t[t.IPv4=1]="IPv4",t[t.Hostname=3]="Hostname",t[t.IPv6=4]="IPv6"})(Ts||(Ts={}));we.Socks5HostType=Ts;var As;(function(t){t[t.Created=0]="Created",t[t.Connecting=1]="Connecting",t[t.Connected=2]="Connected",t[t.SentInitialHandshake=3]="SentInitialHandshake",t[t.ReceivedInitialHandshakeResponse=4]="ReceivedInitialHandshakeResponse",t[t.SentAuthentication=5]="SentAuthentication",t[t.ReceivedAuthenticationResponse=6]="ReceivedAuthenticationResponse",t[t.SentFinalHandshake=7]="SentFinalHandshake",t[t.ReceivedFinalResponse=8]="ReceivedFinalResponse",t[t.BoundWaitingForConnection=9]="BoundWaitingForConnection",t[t.Established=10]="Established",t[t.Disconnected=11]="Disconnected",t[t.Error=99]="Error"})(As||(As={}));we.SocksClientState=As});var Ls=x(pr=>{"use strict";Object.defineProperty(pr,"__esModule",{value:!0});pr.shuffleArray=pr.SocksClientError=void 0;var Bs=class extends Error{constructor(e,r){super(e),this.options=r}};pr.SocksClientError=Bs;function Xg(t){for(let e=t.length-1;e>0;e--){let r=Math.floor(Math.random()*(e+1));[t[e],t[r]]=[t[r],t[e]]}}pr.shuffleArray=Xg});var Iu=x(dr=>{"use strict";Object.defineProperty(dr,"__esModule",{value:!0});dr.validateSocksClientChainOptions=dr.validateSocksClientOptions=void 0;var Ge=Ls(),Ne=Rs(),Qg=require("stream");function Jg(t,e=["connect","bind","associate"]){if(!Ne.SocksCommand[t.command])throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksCommand,t);if(e.indexOf(t.command)===-1)throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksCommandForOperation,t);if(!ku(t.destination))throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsDestination,t);if(!Cu(t.proxy))throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsProxy,t);if(Su(t.proxy,t),t.timeout&&!Ou(t.timeout))throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsTimeout,t);if(t.existing_socket&&!(t.existing_socket instanceof Qg.Duplex))throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsExistingSocket,t)}dr.validateSocksClientOptions=Jg;function e0(t){if(t.command!=="connect")throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksCommandChain,t);if(!ku(t.destination))throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsDestination,t);if(!(t.proxies&&Array.isArray(t.proxies)&&t.proxies.length>=2))throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsProxiesLength,t);if(t.proxies.forEach(e=>{if(!Cu(e))throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsProxy,t);Su(e,t)}),t.timeout&&!Ou(t.timeout))throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsTimeout,t)}dr.validateSocksClientChainOptions=e0;function Su(t,e){if(t.custom_auth_method!==void 0){if(t.custom_auth_method<Ne.SOCKS5_CUSTOM_AUTH_START||t.custom_auth_method>Ne.SOCKS5_CUSTOM_AUTH_END)throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsCustomAuthRange,e);if(t.custom_auth_request_handler===void 0||typeof t.custom_auth_request_handler!="function")throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(t.custom_auth_response_size===void 0)throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(t.custom_auth_response_handler===void 0||typeof t.custom_auth_response_handler!="function")throw new Ge.SocksClientError(Ne.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e)}}function ku(t){return t&&typeof t.host=="string"&&typeof t.port=="number"&&t.port>=0&&t.port<=65535}function Cu(t){return t&&(typeof t.host=="string"||typeof t.ipaddress=="string")&&typeof t.port=="number"&&t.port>=0&&t.port<=65535&&(t.type===4||t.type===5)}function Ou(t){return typeof t=="number"&&t>0}});var Tu=x(Bi=>{"use strict";Object.defineProperty(Bi,"__esModule",{value:!0});Bi.ReceiveBuffer=void 0;var Ns=class{constructor(e=4096){this.buffer=Buffer.allocUnsafe(e),this.offset=0,this.originalSize=e}get length(){return this.offset}append(e){if(!Buffer.isBuffer(e))throw new Error("Attempted to append a non-buffer instance to ReceiveBuffer.");if(this.offset+e.length>=this.buffer.length){let r=this.buffer;this.buffer=Buffer.allocUnsafe(Math.max(this.buffer.length+this.originalSize,this.buffer.length+e.length)),r.copy(this.buffer)}return e.copy(this.buffer,this.offset),this.offset+=e.length}peek(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");return this.buffer.slice(0,e)}get(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");let r=Buffer.allocUnsafe(e);return this.buffer.slice(0,e).copy(r),this.buffer.copyWithin(0,e,e+this.offset-e),this.offset-=e,r}};Bi.ReceiveBuffer=Ns});var Au=x(Et=>{"use strict";var mr=Et&&Et.__awaiter||function(t,e,r,i){function n(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function a(u){try{c(i.next(u))}catch(f){o(f)}}function l(u){try{c(i.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((i=i.apply(t,e||[])).next())})};Object.defineProperty(Et,"__esModule",{value:!0});Et.SocksClientError=Et.SocksClient=void 0;var t0=require("events"),gr=require("net"),Ke=vu(),ze=Eu(),k=Rs(),Ps=Iu(),r0=Tu(),Fs=Ls();Object.defineProperty(Et,"SocksClientError",{enumerable:!0,get:function(){return Fs.SocksClientError}});var vr=class extends t0.EventEmitter{constructor(e){super(),this.options=Object.assign({},e),(0,Ps.validateSocksClientOptions)(e),this.setState(k.SocksClientState.Created)}static createConnection(e,r){return new Promise((i,n)=>{try{(0,Ps.validateSocksClientOptions)(e,["connect"])}catch(o){return typeof r=="function"?(r(o),i(o)):n(o)}let s=new vr(e);s.connect(e.existing_socket),s.once("established",o=>{s.removeAllListeners(),typeof r=="function"&&r(null,o),i(o)}),s.once("error",o=>{s.removeAllListeners(),typeof r=="function"?(r(o),i(o)):n(o)})})}static createConnectionChain(e,r){return new Promise((i,n)=>mr(this,void 0,void 0,function*(){try{(0,Ps.validateSocksClientChainOptions)(e)}catch(o){return typeof r=="function"?(r(o),i(o)):n(o)}let s;e.randomizeChain&&(0,Fs.shuffleArray)(e.proxies);try{for(let o=0;o<e.proxies.length;o++){let a=e.proxies[o],l=o===e.proxies.length-1?e.destination:{host:e.proxies[o+1].host||e.proxies[o+1].ipaddress,port:e.proxies[o+1].port},c=yield vr.createConnection({command:"connect",proxy:a,destination:l});s||(s=c.socket)}typeof r=="function"?(r(null,{socket:s}),i({socket:s})):i({socket:s})}catch(o){typeof r=="function"?(r(o),i(o)):n(o)}}))}static createUDPFrame(e){let r=new ze.SmartBuffer;return r.writeUInt16BE(0),r.writeUInt8(e.frameNumber||0),gr.isIPv4(e.remoteHost.host)?(r.writeUInt8(k.Socks5HostType.IPv4),r.writeUInt32BE(Ke.toLong(e.remoteHost.host))):gr.isIPv6(e.remoteHost.host)?(r.writeUInt8(k.Socks5HostType.IPv6),r.writeBuffer(Ke.toBuffer(e.remoteHost.host))):(r.writeUInt8(k.Socks5HostType.Hostname),r.writeUInt8(Buffer.byteLength(e.remoteHost.host)),r.writeString(e.remoteHost.host)),r.writeUInt16BE(e.remoteHost.port),r.writeBuffer(e.data),r.toBuffer()}static parseUDPFrame(e){let r=ze.SmartBuffer.fromBuffer(e);r.readOffset=2;let i=r.readUInt8(),n=r.readUInt8(),s;n===k.Socks5HostType.IPv4?s=Ke.fromLong(r.readUInt32BE()):n===k.Socks5HostType.IPv6?s=Ke.toString(r.readBuffer(16)):s=r.readString(r.readUInt8());let o=r.readUInt16BE();return{frameNumber:i,remoteHost:{host:s,port:o},data:r.readBuffer()}}setState(e){this.state!==k.SocksClientState.Error&&(this.state=e)}connect(e){this.onDataReceived=i=>this.onDataReceivedHandler(i),this.onClose=()=>this.onCloseHandler(),this.onError=i=>this.onErrorHandler(i),this.onConnect=()=>this.onConnectHandler();let r=setTimeout(()=>this.onEstablishedTimeout(),this.options.timeout||k.DEFAULT_TIMEOUT);r.unref&&typeof r.unref=="function"&&r.unref(),e?this.socket=e:this.socket=new gr.Socket,this.socket.once("close",this.onClose),this.socket.once("error",this.onError),this.socket.once("connect",this.onConnect),this.socket.on("data",this.onDataReceived),this.setState(k.SocksClientState.Connecting),this.receiveBuffer=new r0.ReceiveBuffer,e?this.socket.emit("connect"):(this.socket.connect(this.getSocketOptions()),this.options.set_tcp_nodelay!==void 0&&this.options.set_tcp_nodelay!==null&&this.socket.setNoDelay(!!this.options.set_tcp_nodelay)),this.prependOnceListener("established",i=>{setImmediate(()=>{if(this.receiveBuffer.length>0){let n=this.receiveBuffer.get(this.receiveBuffer.length);i.socket.emit("data",n)}i.socket.resume()})})}getSocketOptions(){return Object.assign(Object.assign({},this.options.socket_options),{host:this.options.proxy.host||this.options.proxy.ipaddress,port:this.options.proxy.port})}onEstablishedTimeout(){this.state!==k.SocksClientState.Established&&this.state!==k.SocksClientState.BoundWaitingForConnection&&this.closeSocket(k.ERRORS.ProxyConnectionTimedOut)}onConnectHandler(){this.setState(k.SocksClientState.Connected),this.options.proxy.type===4?this.sendSocks4InitialHandshake():this.sendSocks5InitialHandshake(),this.setState(k.SocksClientState.SentInitialHandshake)}onDataReceivedHandler(e){this.receiveBuffer.append(e),this.processData()}processData(){for(;this.state!==k.SocksClientState.Established&&this.state!==k.SocksClientState.Error&&this.receiveBuffer.length>=this.nextRequiredPacketBufferSize;)if(this.state===k.SocksClientState.SentInitialHandshake)this.options.proxy.type===4?this.handleSocks4FinalHandshakeResponse():this.handleInitialSocks5HandshakeResponse();else if(this.state===k.SocksClientState.SentAuthentication)this.handleInitialSocks5AuthenticationHandshakeResponse();else if(this.state===k.SocksClientState.SentFinalHandshake)this.handleSocks5FinalHandshakeResponse();else if(this.state===k.SocksClientState.BoundWaitingForConnection)this.options.proxy.type===4?this.handleSocks4IncomingConnectionResponse():this.handleSocks5IncomingConnectionResponse();else{this.closeSocket(k.ERRORS.InternalError);break}}onCloseHandler(){this.closeSocket(k.ERRORS.SocketClosed)}onErrorHandler(e){this.closeSocket(e.message)}removeInternalSocketHandlers(){this.socket.pause(),this.socket.removeListener("data",this.onDataReceived),this.socket.removeListener("close",this.onClose),this.socket.removeListener("error",this.onError),this.socket.removeListener("connect",this.onConnect)}closeSocket(e){this.state!==k.SocksClientState.Error&&(this.setState(k.SocksClientState.Error),this.socket.destroy(),this.removeInternalSocketHandlers(),this.emit("error",new Fs.SocksClientError(e,this.options)))}sendSocks4InitialHandshake(){let e=this.options.proxy.userId||"",r=new ze.SmartBuffer;r.writeUInt8(4),r.writeUInt8(k.SocksCommand[this.options.command]),r.writeUInt16BE(this.options.destination.port),gr.isIPv4(this.options.destination.host)?(r.writeBuffer(Ke.toBuffer(this.options.destination.host)),r.writeStringNT(e)):(r.writeUInt8(0),r.writeUInt8(0),r.writeUInt8(0),r.writeUInt8(1),r.writeStringNT(e),r.writeStringNT(this.options.destination.host)),this.nextRequiredPacketBufferSize=k.SOCKS_INCOMING_PACKET_SIZES.Socks4Response,this.socket.write(r.toBuffer())}handleSocks4FinalHandshakeResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==k.Socks4Response.Granted)this.closeSocket(`${k.ERRORS.Socks4ProxyRejectedConnection} - (${k.Socks4Response[e[1]]})`);else if(k.SocksCommand[this.options.command]===k.SocksCommand.bind){let r=ze.SmartBuffer.fromBuffer(e);r.readOffset=2;let i={port:r.readUInt16BE(),host:Ke.fromLong(r.readUInt32BE())};i.host==="0.0.0.0"&&(i.host=this.options.proxy.ipaddress),this.setState(k.SocksClientState.BoundWaitingForConnection),this.emit("bound",{remoteHost:i,socket:this.socket})}else this.setState(k.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{socket:this.socket})}handleSocks4IncomingConnectionResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==k.Socks4Response.Granted)this.closeSocket(`${k.ERRORS.Socks4ProxyRejectedIncomingBoundConnection} - (${k.Socks4Response[e[1]]})`);else{let r=ze.SmartBuffer.fromBuffer(e);r.readOffset=2;let i={port:r.readUInt16BE(),host:Ke.fromLong(r.readUInt32BE())};this.setState(k.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:i,socket:this.socket})}}sendSocks5InitialHandshake(){let e=new ze.SmartBuffer,r=[k.Socks5Auth.NoAuth];(this.options.proxy.userId||this.options.proxy.password)&&r.push(k.Socks5Auth.UserPass),this.options.proxy.custom_auth_method!==void 0&&r.push(this.options.proxy.custom_auth_method),e.writeUInt8(5),e.writeUInt8(r.length);for(let i of r)e.writeUInt8(i);this.nextRequiredPacketBufferSize=k.SOCKS_INCOMING_PACKET_SIZES.Socks5InitialHandshakeResponse,this.socket.write(e.toBuffer()),this.setState(k.SocksClientState.SentInitialHandshake)}handleInitialSocks5HandshakeResponse(){let e=this.receiveBuffer.get(2);e[0]!==5?this.closeSocket(k.ERRORS.InvalidSocks5IntiailHandshakeSocksVersion):e[1]===k.SOCKS5_NO_ACCEPTABLE_AUTH?this.closeSocket(k.ERRORS.InvalidSocks5InitialHandshakeNoAcceptedAuthType):e[1]===k.Socks5Auth.NoAuth?(this.socks5ChosenAuthType=k.Socks5Auth.NoAuth,this.sendSocks5CommandRequest()):e[1]===k.Socks5Auth.UserPass?(this.socks5ChosenAuthType=k.Socks5Auth.UserPass,this.sendSocks5UserPassAuthentication()):e[1]===this.options.proxy.custom_auth_method?(this.socks5ChosenAuthType=this.options.proxy.custom_auth_method,this.sendSocks5CustomAuthentication()):this.closeSocket(k.ERRORS.InvalidSocks5InitialHandshakeUnknownAuthType)}sendSocks5UserPassAuthentication(){let e=this.options.proxy.userId||"",r=this.options.proxy.password||"",i=new ze.SmartBuffer;i.writeUInt8(1),i.writeUInt8(Buffer.byteLength(e)),i.writeString(e),i.writeUInt8(Buffer.byteLength(r)),i.writeString(r),this.nextRequiredPacketBufferSize=k.SOCKS_INCOMING_PACKET_SIZES.Socks5UserPassAuthenticationResponse,this.socket.write(i.toBuffer()),this.setState(k.SocksClientState.SentAuthentication)}sendSocks5CustomAuthentication(){return mr(this,void 0,void 0,function*(){this.nextRequiredPacketBufferSize=this.options.proxy.custom_auth_response_size,this.socket.write(yield this.options.proxy.custom_auth_request_handler()),this.setState(k.SocksClientState.SentAuthentication)})}handleSocks5CustomAuthHandshakeResponse(e){return mr(this,void 0,void 0,function*(){return yield this.options.proxy.custom_auth_response_handler(e)})}handleSocks5AuthenticationNoAuthHandshakeResponse(e){return mr(this,void 0,void 0,function*(){return e[1]===0})}handleSocks5AuthenticationUserPassHandshakeResponse(e){return mr(this,void 0,void 0,function*(){return e[1]===0})}handleInitialSocks5AuthenticationHandshakeResponse(){return mr(this,void 0,void 0,function*(){this.setState(k.SocksClientState.ReceivedAuthenticationResponse);let e=!1;this.socks5ChosenAuthType===k.Socks5Auth.NoAuth?e=yield this.handleSocks5AuthenticationNoAuthHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===k.Socks5Auth.UserPass?e=yield this.handleSocks5AuthenticationUserPassHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===this.options.proxy.custom_auth_method&&(e=yield this.handleSocks5CustomAuthHandshakeResponse(this.receiveBuffer.get(this.options.proxy.custom_auth_response_size))),e?this.sendSocks5CommandRequest():this.closeSocket(k.ERRORS.Socks5AuthenticationFailed)})}sendSocks5CommandRequest(){let e=new ze.SmartBuffer;e.writeUInt8(5),e.writeUInt8(k.SocksCommand[this.options.command]),e.writeUInt8(0),gr.isIPv4(this.options.destination.host)?(e.writeUInt8(k.Socks5HostType.IPv4),e.writeBuffer(Ke.toBuffer(this.options.destination.host))):gr.isIPv6(this.options.destination.host)?(e.writeUInt8(k.Socks5HostType.IPv6),e.writeBuffer(Ke.toBuffer(this.options.destination.host))):(e.writeUInt8(k.Socks5HostType.Hostname),e.writeUInt8(this.options.destination.host.length),e.writeString(this.options.destination.host)),e.writeUInt16BE(this.options.destination.port),this.nextRequiredPacketBufferSize=k.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.socket.write(e.toBuffer()),this.setState(k.SocksClientState.SentFinalHandshake)}handleSocks5FinalHandshakeResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==k.Socks5Response.Granted)this.closeSocket(`${k.ERRORS.InvalidSocks5FinalHandshakeRejected} - ${k.Socks5Response[e[1]]}`);else{let r=e[3],i,n;if(r===k.Socks5HostType.IPv4){let s=k.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=ze.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),i={host:Ke.fromLong(n.readUInt32BE()),port:n.readUInt16BE()},i.host==="0.0.0.0"&&(i.host=this.options.proxy.ipaddress)}else if(r===k.Socks5HostType.Hostname){let s=e[4],o=k.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=ze.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),i={host:n.readString(s),port:n.readUInt16BE()}}else if(r===k.Socks5HostType.IPv6){let s=k.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=ze.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),i={host:Ke.toString(n.readBuffer(16)),port:n.readUInt16BE()}}this.setState(k.SocksClientState.ReceivedFinalResponse),k.SocksCommand[this.options.command]===k.SocksCommand.connect?(this.setState(k.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:i,socket:this.socket})):k.SocksCommand[this.options.command]===k.SocksCommand.bind?(this.setState(k.SocksClientState.BoundWaitingForConnection),this.nextRequiredPacketBufferSize=k.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.emit("bound",{remoteHost:i,socket:this.socket})):k.SocksCommand[this.options.command]===k.SocksCommand.associate&&(this.setState(k.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:i,socket:this.socket}))}}handleSocks5IncomingConnectionResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==k.Socks5Response.Granted)this.closeSocket(`${k.ERRORS.Socks5ProxyRejectedIncomingBoundConnection} - ${k.Socks5Response[e[1]]}`);else{let r=e[3],i,n;if(r===k.Socks5HostType.IPv4){let s=k.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=ze.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),i={host:Ke.fromLong(n.readUInt32BE()),port:n.readUInt16BE()},i.host==="0.0.0.0"&&(i.host=this.options.proxy.ipaddress)}else if(r===k.Socks5HostType.Hostname){let s=e[4],o=k.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=ze.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),i={host:n.readString(s),port:n.readUInt16BE()}}else if(r===k.Socks5HostType.IPv6){let s=k.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=ze.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),i={host:Ke.toString(n.readBuffer(16)),port:n.readUInt16BE()}}this.setState(k.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:i,socket:this.socket})}}get socksClientOptions(){return Object.assign({},this.options)}};Et.SocksClient=vr});var Ru=x(jt=>{"use strict";var i0=jt&&jt.__createBinding||(Object.create?function(t,e,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}),n0=jt&&jt.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&i0(e,t,r)};Object.defineProperty(jt,"__esModule",{value:!0});n0(Au(),jt)});var Bu=x(qt=>{"use strict";var s0=qt&&qt.__awaiter||function(t,e,r,i){function n(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function a(u){try{c(i.next(u))}catch(f){o(f)}}function l(u){try{c(i.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((i=i.apply(t,e||[])).next())})},Li=qt&&qt.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(qt,"__esModule",{value:!0});var o0=Li(require("dns")),a0=Li(require("tls")),l0=Li(require("url")),c0=Li(tr()),u0=an(),f0=Ru(),Ms=c0.default("socks-proxy-agent");function h0(t){return new Promise((e,r)=>{o0.default.lookup(t,(i,n)=>{i?r(i):e(n)})})}function p0(t){let e=0,r=!1,i=5,n=t.hostname||t.host;if(!n)throw new TypeError('No "host"');if(typeof t.port=="number"?e=t.port:typeof t.port=="string"&&(e=parseInt(t.port,10)),e||(e=1080),t.protocol)switch(t.protocol.replace(":","")){case"socks4":r=!0;case"socks4a":i=4;break;case"socks5":r=!0;case"socks":case"socks5h":i=5;break;default:throw new TypeError(`A "socks" protocol must be specified! Got: ${t.protocol}`)}if(typeof t.type!="undefined")if(t.type===4||t.type===5)i=t.type;else throw new TypeError(`"type" must be 4 or 5, got: ${t.type}`);let s={host:n,port:e,type:i},o=t.userId||t.username,a=t.password;if(t.auth){let l=t.auth.split(":");o=l[0],a=l[1]}return o&&Object.defineProperty(s,"userId",{value:o,enumerable:!1}),a&&Object.defineProperty(s,"password",{value:a,enumerable:!1}),{lookup:r,proxy:s}}var Us=class extends u0.Agent{constructor(e){let r;if(typeof e=="string"?r=l0.default.parse(e):r=e,!r)throw new TypeError("a SOCKS proxy server `host` and `port` must be specified!");super(r);let i=p0(r);this.lookup=i.lookup,this.proxy=i.proxy,this.tlsConnectionOptions=r.tls||{}}callback(e,r){return s0(this,void 0,void 0,function*(){let{lookup:i,proxy:n}=this,{host:s,port:o,timeout:a}=r;if(!s)throw new Error("No `host` defined!");i&&(s=yield h0(s));let l={proxy:n,destination:{host:s,port:o},command:"connect",timeout:a};Ms("Creating socks proxy connection: %o",l);let{socket:c}=yield f0.SocksClient.createConnection(l);if(Ms("Successfully created socks proxy connection"),r.secureEndpoint){Ms("Upgrading socket connection to TLS");let u=r.servername||r.host;return a0.default.connect(Object.assign(Object.assign(Object.assign({},d0(r,"host","hostname","path","port")),{socket:c,servername:u}),this.tlsConnectionOptions))}return c})}};qt.default=Us;function d0(t,...e){let r={},i;for(i in t)e.includes(i)||(r[i]=t[i]);return r}});var Nu=x((qs,Lu)=>{"use strict";var m0=qs&&qs.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},Ds=m0(Bu());function js(t){return new Ds.default(t)}(function(t){t.SocksProxyAgent=Ds.default,t.prototype=Ds.default.prototype})(js||(js={}));Lu.exports=js});var Fu=x((Kx,Pu)=>{"use strict";var g0=/[|\\{}()[\]^$+*?.-]/g;Pu.exports=t=>{if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(g0,"\\$&")}});var ju=x((Zx,Du)=>{"use strict";var v0=Fu(),_0=typeof process=="object"&&process&&typeof process.cwd=="function"?process.cwd():".",Uu=[].concat(require("module").builtinModules,"bootstrap_node","node").map(t=>new RegExp(`(?:\\((?:node:)?${t}(?:\\.js)?:\\d+:\\d+\\)$|^\\s*at (?:node:)?${t}(?:\\.js)?:\\d+:\\d+$)`));Uu.push(/\((?:node:)?internal\/[^:]+:\d+:\d+\)$/,/\s*at (?:node:)?internal\/[^:]+:\d+:\d+$/,/\/\.node-spawn-wrap-\w+-\w+\/node:\d+:\d+\)?$/);var Kr=class{constructor(e){e={ignoredPackages:[],...e},"internals"in e||(e.internals=Kr.nodeInternals()),"cwd"in e||(e.cwd=_0),this._cwd=e.cwd.replace(/\\/g,"/"),this._internals=[].concat(e.internals,x0(e.ignoredPackages)),this._wrapCallSite=e.wrapCallSite||!1}static nodeInternals(){return[...Uu]}clean(e,r=0){r=" ".repeat(r),Array.isArray(e)||(e=e.split(`
`)),!/^\s*at /.test(e[0])&&/^\s*at /.test(e[1])&&(e=e.slice(1));let i=!1,n=null,s=[];return e.forEach(o=>{if(o=o.replace(/\\/g,"/"),this._internals.some(l=>l.test(o)))return;let a=/^\s*at /.test(o);i?o=o.trimEnd().replace(/^(\s+)at /,"$1"):(o=o.trim(),a&&(o=o.slice(3))),o=o.replace(`${this._cwd}/`,""),o&&(a?(n&&(s.push(n),n=null),s.push(o)):(i=!0,n=o))}),s.map(o=>`${r}${o}
`).join("")}captureString(e,r=this.captureString){typeof e=="function"&&(r=e,e=1/0);let{stackTraceLimit:i}=Error;e&&(Error.stackTraceLimit=e);let n={};Error.captureStackTrace(n,r);let{stack:s}=n;return Error.stackTraceLimit=i,this.clean(s)}capture(e,r=this.capture){typeof e=="function"&&(r=e,e=1/0);let{prepareStackTrace:i,stackTraceLimit:n}=Error;Error.prepareStackTrace=(a,l)=>this._wrapCallSite?l.map(this._wrapCallSite):l,e&&(Error.stackTraceLimit=e);let s={};Error.captureStackTrace(s,r);let{stack:o}=s;return Object.assign(Error,{prepareStackTrace:i,stackTraceLimit:n}),o}at(e=this.at){let[r]=this.capture(1,e);if(!r)return{};let i={line:r.getLineNumber(),column:r.getColumnNumber()};Mu(i,r.getFileName(),this._cwd),r.isConstructor()&&(i.constructor=!0),r.isEval()&&(i.evalOrigin=r.getEvalOrigin()),r.isNative()&&(i.native=!0);let n;try{n=r.getTypeName()}catch{}n&&n!=="Object"&&n!=="[object Object]"&&(i.type=n);let s=r.getFunctionName();s&&(i.function=s);let o=r.getMethodName();return o&&s!==o&&(i.method=o),i}parseLine(e){let r=e&&e.match(y0);if(!r)return null;let i=r[1]==="new",n=r[2],s=r[3],o=r[4],a=Number(r[5]),l=Number(r[6]),c=r[7],u=r[8],f=r[9],h=r[10]==="native",p=r[11]===")",d,m={};if(u&&(m.line=Number(u)),f&&(m.column=Number(f)),p&&c){let g=0;for(let y=c.length-1;y>0;y--)if(c.charAt(y)===")")g++;else if(c.charAt(y)==="("&&c.charAt(y-1)===" "&&(g--,g===-1&&c.charAt(y-1)===" ")){let I=c.slice(0,y-1);c=c.slice(y+1),n+=` (${I}`;break}}if(n){let g=n.match(b0);g&&(n=g[1],d=g[2])}return Mu(m,c,this._cwd),i&&(m.constructor=!0),s&&(m.evalOrigin=s,m.evalLine=a,m.evalColumn=l,m.evalFile=o&&o.replace(/\\/g,"/")),h&&(m.native=!0),n&&(m.function=n),d&&n!==d&&(m.method=d),m}};function Mu(t,e,r){e&&(e=e.replace(/\\/g,"/"),e.startsWith(`${r}/`)&&(e=e.slice(r.length+1)),t.file=e)}function x0(t){if(t.length===0)return[];let e=t.map(r=>v0(r));return new RegExp(`[/\\\\]node_modules[/\\\\](?:${e.join("|")})[/\\\\][^:]+:\\d+:\\d+`)}var y0=new RegExp("^(?:\\s*at )?(?:(new) )?(?:(.*?) \\()?(?:eval at ([^ ]+) \\((.+?):(\\d+):(\\d+)\\), )?(?:(.+?):(\\d+):(\\d+)|(native))(\\)?)$"),b0=/^(.*?) \[as (.*?)\]$/;Du.exports=Kr});var $u=x((Xx,Vu)=>{"use strict";var{Duplex:w0}=require("stream");function qu(t){t.emit("close")}function E0(){!this.destroyed&&this._writableState.finished&&this.destroy()}function Hu(t){this.removeListener("error",Hu),this.destroy(),this.listenerCount("error")===0&&this.emit("error",t)}function S0(t,e){let r=!0,i=new w0({...e,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return t.on("message",function(s,o){let a=!o&&i._readableState.objectMode?s.toString():s;i.push(a)||t.pause()}),t.once("error",function(s){i.destroyed||(r=!1,i.destroy(s))}),t.once("close",function(){i.destroyed||i.push(null)}),i._destroy=function(n,s){if(t.readyState===t.CLOSED){s(n),process.nextTick(qu,i);return}let o=!1;t.once("error",function(l){o=!0,s(l)}),t.once("close",function(){o||s(n),process.nextTick(qu,i)}),r&&t.terminate()},i._final=function(n){if(t.readyState===t.CONNECTING){t.once("open",function(){i._final(n)});return}t._socket!==null&&(t._socket._writableState.finished?(n(),i._readableState.endEmitted&&i.destroy()):(t._socket.once("finish",function(){n()}),t.close()))},i._read=function(){t.isPaused&&t.resume()},i._write=function(n,s,o){if(t.readyState===t.CONNECTING){t.once("open",function(){i._write(n,s,o)});return}t.send(n,o)},i.on("end",E0),i.on("error",Hu),i}Vu.exports=S0});var St=x((Qx,Gu)=>{"use strict";Gu.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});var Zr=x((Jx,Hs)=>{"use strict";var{EMPTY_BUFFER:k0}=St();function zu(t,e){if(t.length===0)return k0;if(t.length===1)return t[0];let r=Buffer.allocUnsafe(e),i=0;for(let n=0;n<t.length;n++){let s=t[n];r.set(s,i),i+=s.length}return i<e?r.slice(0,i):r}function Wu(t,e,r,i,n){for(let s=0;s<n;s++)r[i+s]=t[s]^e[s&3]}function Yu(t,e){for(let r=0;r<t.length;r++)t[r]^=e[r&3]}function Ku(t){return t.byteLength===t.buffer.byteLength?t.buffer:t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength)}function Ni(t){if(Ni.readOnly=!0,Buffer.isBuffer(t))return t;let e;return t instanceof ArrayBuffer?e=Buffer.from(t):ArrayBuffer.isView(t)?e=Buffer.from(t.buffer,t.byteOffset,t.byteLength):(e=Buffer.from(t),Ni.readOnly=!1),e}try{let t=require("bufferutil");Hs.exports={concat:zu,mask(e,r,i,n,s){s<48?Wu(e,r,i,n,s):t.mask(e,r,i,n,s)},toArrayBuffer:Ku,toBuffer:Ni,unmask(e,r){e.length<32?Yu(e,r):t.unmask(e,r)}}}catch{Hs.exports={concat:zu,mask:Wu,toArrayBuffer:Ku,toBuffer:Ni,unmask:Yu}}});var Qu=x((ey,Xu)=>{"use strict";var Zu=Symbol("kDone"),Vs=Symbol("kRun"),$s=class{constructor(e){this[Zu]=()=>{this.pending--,this[Vs]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[Vs]()}[Vs](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[Zu])}}};Xu.exports=$s});var Jr=x((ty,rf)=>{"use strict";var Xr=require("zlib"),Ju=Zr(),C0=Qu(),{kStatusCode:ef}=St(),O0=Buffer.from([0,0,255,255]),Mi=Symbol("permessage-deflate"),pt=Symbol("total-length"),Qr=Symbol("callback"),kt=Symbol("buffers"),Fi=Symbol("error"),Pi,Gs=class{constructor(e,r,i){if(this._maxPayload=i|0,this._options=e||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!r,this._deflate=null,this._inflate=null,this.params=null,!Pi){let n=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;Pi=new C0(n)}}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[Qr];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let r=this._options,i=e.find(n=>!(r.serverNoContextTakeover===!1&&n.server_no_context_takeover||n.server_max_window_bits&&(r.serverMaxWindowBits===!1||typeof r.serverMaxWindowBits=="number"&&r.serverMaxWindowBits>n.server_max_window_bits)||typeof r.clientMaxWindowBits=="number"&&!n.client_max_window_bits));if(!i)throw new Error("None of the extension offers can be accepted");return r.serverNoContextTakeover&&(i.server_no_context_takeover=!0),r.clientNoContextTakeover&&(i.client_no_context_takeover=!0),typeof r.serverMaxWindowBits=="number"&&(i.server_max_window_bits=r.serverMaxWindowBits),typeof r.clientMaxWindowBits=="number"?i.client_max_window_bits=r.clientMaxWindowBits:(i.client_max_window_bits===!0||r.clientMaxWindowBits===!1)&&delete i.client_max_window_bits,i}acceptAsClient(e){let r=e[0];if(this._options.clientNoContextTakeover===!1&&r.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!r.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(r.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&r.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return r}normalizeParams(e){return e.forEach(r=>{Object.keys(r).forEach(i=>{let n=r[i];if(n.length>1)throw new Error(`Parameter "${i}" must have only a single value`);if(n=n[0],i==="client_max_window_bits"){if(n!==!0){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${i}": ${n}`);n=s}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${i}": ${n}`)}else if(i==="server_max_window_bits"){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${i}": ${n}`);n=s}else if(i==="client_no_context_takeover"||i==="server_no_context_takeover"){if(n!==!0)throw new TypeError(`Invalid value for parameter "${i}": ${n}`)}else throw new Error(`Unknown parameter "${i}"`);r[i]=n})}),e}decompress(e,r,i){Pi.add(n=>{this._decompress(e,r,(s,o)=>{n(),i(s,o)})})}compress(e,r,i){Pi.add(n=>{this._compress(e,r,(s,o)=>{n(),i(s,o)})})}_decompress(e,r,i){let n=this._isServer?"client":"server";if(!this._inflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?Xr.Z_DEFAULT_WINDOWBITS:this.params[s];this._inflate=Xr.createInflateRaw({...this._options.zlibInflateOptions,windowBits:o}),this._inflate[Mi]=this,this._inflate[pt]=0,this._inflate[kt]=[],this._inflate.on("error",T0),this._inflate.on("data",tf)}this._inflate[Qr]=i,this._inflate.write(e),r&&this._inflate.write(O0),this._inflate.flush(()=>{let s=this._inflate[Fi];if(s){this._inflate.close(),this._inflate=null,i(s);return}let o=Ju.concat(this._inflate[kt],this._inflate[pt]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[pt]=0,this._inflate[kt]=[],r&&this.params[`${n}_no_context_takeover`]&&this._inflate.reset()),i(null,o)})}_compress(e,r,i){let n=this._isServer?"server":"client";if(!this._deflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?Xr.Z_DEFAULT_WINDOWBITS:this.params[s];this._deflate=Xr.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:o}),this._deflate[pt]=0,this._deflate[kt]=[],this._deflate.on("data",I0)}this._deflate[Qr]=i,this._deflate.write(e),this._deflate.flush(Xr.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let s=Ju.concat(this._deflate[kt],this._deflate[pt]);r&&(s=s.slice(0,s.length-4)),this._deflate[Qr]=null,this._deflate[pt]=0,this._deflate[kt]=[],r&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),i(null,s)})}};rf.exports=Gs;function I0(t){this[kt].push(t),this[pt]+=t.length}function tf(t){if(this[pt]+=t.length,this[Mi]._maxPayload<1||this[pt]<=this[Mi]._maxPayload){this[kt].push(t);return}this[Fi]=new RangeError("Max payload size exceeded"),this[Fi].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[Fi][ef]=1009,this.removeListener("data",tf),this.reset()}function T0(t){this[Mi]._inflate=null,t[ef]=1007,this[Qr](t)}});var ei=x((ry,zs)=>{"use strict";var nf=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function sf(t){return t>=1e3&&t<=1014&&t!==1004&&t!==1005&&t!==1006||t>=3e3&&t<=4999}function of(t){let e=t.length,r=0;for(;r<e;)if((t[r]&128)===0)r++;else if((t[r]&224)===192){if(r+1===e||(t[r+1]&192)!==128||(t[r]&254)===192)return!1;r+=2}else if((t[r]&240)===224){if(r+2>=e||(t[r+1]&192)!==128||(t[r+2]&192)!==128||t[r]===224&&(t[r+1]&224)===128||t[r]===237&&(t[r+1]&224)===160)return!1;r+=3}else if((t[r]&248)===240){if(r+3>=e||(t[r+1]&192)!==128||(t[r+2]&192)!==128||(t[r+3]&192)!==128||t[r]===240&&(t[r+1]&240)===128||t[r]===244&&t[r+1]>143||t[r]>244)return!1;r+=4}else return!1;return!0}try{let t=require("utf-8-validate");zs.exports={isValidStatusCode:sf,isValidUTF8(e){return e.length<150?of(e):t(e)},tokenChars:nf}}catch{zs.exports={isValidStatusCode:sf,isValidUTF8:of,tokenChars:nf}}});var Zs=x((iy,pf)=>{"use strict";var{Writable:A0}=require("stream"),af=Jr(),{BINARY_TYPES:R0,EMPTY_BUFFER:lf,kStatusCode:B0,kWebSocket:L0}=St(),{concat:Ws,toArrayBuffer:N0,unmask:P0}=Zr(),{isValidStatusCode:F0,isValidUTF8:cf}=ei(),ti=0,uf=1,ff=2,hf=3,Ys=4,M0=5,Ks=class extends A0{constructor(e={}){super(),this._binaryType=e.binaryType||R0[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=e.maxPayload|0,this._skipUTF8Validation=!!e.skipUTF8Validation,this[L0]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._state=ti,this._loop=!1}_write(e,r,i){if(this._opcode===8&&this._state==ti)return i();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(i)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let i=this._buffers[0];return this._buffers[0]=i.slice(e),i.slice(0,e)}let r=Buffer.allocUnsafe(e);do{let i=this._buffers[0],n=r.length-e;e>=i.length?r.set(this._buffers.shift(),n):(r.set(new Uint8Array(i.buffer,i.byteOffset,e),n),this._buffers[0]=i.slice(e)),e-=i.length}while(e>0);return r}startLoop(e){let r;this._loop=!0;do switch(this._state){case ti:r=this.getInfo();break;case uf:r=this.getPayloadLength16();break;case ff:r=this.getPayloadLength64();break;case hf:this.getMask();break;case Ys:r=this.getData(e);break;default:this._loop=!1;return}while(this._loop);e(r)}getInfo(){if(this._bufferedBytes<2){this._loop=!1;return}let e=this.consume(2);if((e[0]&48)!==0)return this._loop=!1,Be(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");let r=(e[0]&64)===64;if(r&&!this._extensions[af.extensionName])return this._loop=!1,Be(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(this._fin=(e[0]&128)===128,this._opcode=e[0]&15,this._payloadLength=e[1]&127,this._opcode===0){if(r)return this._loop=!1,Be(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(!this._fragmented)return this._loop=!1,Be(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented)return this._loop=!1,Be(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");this._compressed=r}else if(this._opcode>7&&this._opcode<11){if(!this._fin)return this._loop=!1,Be(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");if(r)return this._loop=!1,Be(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");if(this._payloadLength>125)return this._loop=!1,Be(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH")}else return this._loop=!1,Be(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(e[1]&128)===128,this._isServer){if(!this._masked)return this._loop=!1,Be(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK")}else if(this._masked)return this._loop=!1,Be(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");if(this._payloadLength===126)this._state=uf;else if(this._payloadLength===127)this._state=ff;else return this.haveLength()}getPayloadLength16(){if(this._bufferedBytes<2){this._loop=!1;return}return this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength()}getPayloadLength64(){if(this._bufferedBytes<8){this._loop=!1;return}let e=this.consume(8),r=e.readUInt32BE(0);return r>Math.pow(2,53-32)-1?(this._loop=!1,Be(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH")):(this._payloadLength=r*Math.pow(2,32)+e.readUInt32BE(4),this.haveLength())}haveLength(){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return this._loop=!1,Be(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");this._masked?this._state=hf:this._state=Ys}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=Ys}getData(e){let r=lf;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}r=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!==0&&P0(r,this._mask)}if(this._opcode>7)return this.controlMessage(r);if(this._compressed){this._state=M0,this.decompress(r,e);return}return r.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(r)),this.dataMessage()}decompress(e,r){this._extensions[af.extensionName].decompress(e,this._fin,(n,s)=>{if(n)return r(n);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return r(Be(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(s)}let o=this.dataMessage();if(o)return r(o);this.startLoop(r)})}dataMessage(){if(this._fin){let e=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let i;this._binaryType==="nodebuffer"?i=Ws(r,e):this._binaryType==="arraybuffer"?i=N0(Ws(r,e)):i=r,this.emit("message",i,!0)}else{let i=Ws(r,e);if(!this._skipUTF8Validation&&!cf(i))return this._loop=!1,Be(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");this.emit("message",i,!1)}}this._state=ti}controlMessage(e){if(this._opcode===8)if(this._loop=!1,e.length===0)this.emit("conclude",1005,lf),this.end();else{if(e.length===1)return Be(RangeError,"invalid payload length 1",!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");{let r=e.readUInt16BE(0);if(!F0(r))return Be(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");let i=e.slice(2);if(!this._skipUTF8Validation&&!cf(i))return Be(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");this.emit("conclude",r,i),this.end()}}else this._opcode===9?this.emit("ping",e):this.emit("pong",e);this._state=ti}};pf.exports=Ks;function Be(t,e,r,i,n){let s=new t(r?`Invalid WebSocket frame: ${e}`:e);return Error.captureStackTrace(s,Be),s.code=n,s[B0]=i,s}});var Xs=x((oy,gf)=>{"use strict";var ny=require("net"),sy=require("tls"),{randomFillSync:U0}=require("crypto"),df=Jr(),{EMPTY_BUFFER:D0}=St(),{isValidStatusCode:j0}=ei(),{mask:mf,toBuffer:_r}=Zr(),ot=Symbol("kByteLength"),q0=Buffer.alloc(4),ct=class{constructor(e,r,i){this._extensions=r||{},i&&(this._generateMask=i,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,r){let i,n=!1,s=2,o=!1;r.mask&&(i=r.maskBuffer||q0,r.generateMask?r.generateMask(i):U0(i,0,4),o=(i[0]|i[1]|i[2]|i[3])===0,s=6);let a;typeof e=="string"?(!r.mask||o)&&r[ot]!==void 0?a=r[ot]:(e=Buffer.from(e),a=e.length):(a=e.length,n=r.mask&&r.readOnly&&!o);let l=a;a>=65536?(s+=8,l=127):a>125&&(s+=2,l=126);let c=Buffer.allocUnsafe(n?a+s:s);return c[0]=r.fin?r.opcode|128:r.opcode,r.rsv1&&(c[0]|=64),c[1]=l,l===126?c.writeUInt16BE(a,2):l===127&&(c[2]=c[3]=0,c.writeUIntBE(a,4,6)),r.mask?(c[1]|=128,c[s-4]=i[0],c[s-3]=i[1],c[s-2]=i[2],c[s-1]=i[3],o?[c,e]:n?(mf(e,i,c,s,a),[c]):(mf(e,i,e,0,a),[c,e])):[c,e]}close(e,r,i,n){let s;if(e===void 0)s=D0;else{if(typeof e!="number"||!j0(e))throw new TypeError("First argument must be a valid error code number");if(r===void 0||!r.length)s=Buffer.allocUnsafe(2),s.writeUInt16BE(e,0);else{let a=Buffer.byteLength(r);if(a>123)throw new RangeError("The message must not be greater than 123 bytes");s=Buffer.allocUnsafe(2+a),s.writeUInt16BE(e,0),typeof r=="string"?s.write(r,2):s.set(r,2)}}let o={[ot]:s.length,fin:!0,generateMask:this._generateMask,mask:i,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,s,!1,o,n]):this.sendFrame(ct.frame(s,o),n)}ping(e,r,i){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=_r(e),n=e.length,s=_r.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[ot]:n,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:9,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,i]):this.sendFrame(ct.frame(e,o),i)}pong(e,r,i){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=_r(e),n=e.length,s=_r.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[ot]:n,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:10,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,i]):this.sendFrame(ct.frame(e,o),i)}send(e,r,i){let n=this._extensions[df.extensionName],s=r.binary?2:1,o=r.compress,a,l;if(typeof e=="string"?(a=Buffer.byteLength(e),l=!1):(e=_r(e),a=e.length,l=_r.readOnly),this._firstFragment?(this._firstFragment=!1,o&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(o=a>=n._threshold),this._compress=o):(o=!1,s=0),r.fin&&(this._firstFragment=!0),n){let c={[ot]:a,fin:r.fin,generateMask:this._generateMask,mask:r.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:l,rsv1:o};this._deflating?this.enqueue([this.dispatch,e,this._compress,c,i]):this.dispatch(e,this._compress,c,i)}else this.sendFrame(ct.frame(e,{[ot]:a,fin:r.fin,generateMask:this._generateMask,mask:r.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:l,rsv1:!1}),i)}dispatch(e,r,i,n){if(!r){this.sendFrame(ct.frame(e,i),n);return}let s=this._extensions[df.extensionName];this._bufferedBytes+=i[ot],this._deflating=!0,s.compress(e,i.fin,(o,a)=>{if(this._socket.destroyed){let l=new Error("The socket was closed while data was being compressed");typeof n=="function"&&n(l);for(let c=0;c<this._queue.length;c++){let u=this._queue[c],f=u[u.length-1];typeof f=="function"&&f(l)}return}this._bufferedBytes-=i[ot],this._deflating=!1,i.readOnly=!1,this.sendFrame(ct.frame(a,i),n),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][ot],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][ot],this._queue.push(e)}sendFrame(e,r){e.length===2?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],r),this._socket.uncork()):this._socket.write(e[0],r)}};gf.exports=ct});var Cf=x((ay,kf)=>{"use strict";var{kForOnEventAttribute:Qs,kListener:vf}=St(),_f=Symbol("kCode"),xf=Symbol("kData"),yf=Symbol("kError"),bf=Symbol("kMessage"),wf=Symbol("kReason"),xr=Symbol("kTarget"),Ef=Symbol("kType"),Sf=Symbol("kWasClean"),dt=class{constructor(e){this[xr]=null,this[Ef]=e}get target(){return this[xr]}get type(){return this[Ef]}};Object.defineProperty(dt.prototype,"target",{enumerable:!0});Object.defineProperty(dt.prototype,"type",{enumerable:!0});var Ht=class extends dt{constructor(e,r={}){super(e),this[_f]=r.code===void 0?0:r.code,this[wf]=r.reason===void 0?"":r.reason,this[Sf]=r.wasClean===void 0?!1:r.wasClean}get code(){return this[_f]}get reason(){return this[wf]}get wasClean(){return this[Sf]}};Object.defineProperty(Ht.prototype,"code",{enumerable:!0});Object.defineProperty(Ht.prototype,"reason",{enumerable:!0});Object.defineProperty(Ht.prototype,"wasClean",{enumerable:!0});var yr=class extends dt{constructor(e,r={}){super(e),this[yf]=r.error===void 0?null:r.error,this[bf]=r.message===void 0?"":r.message}get error(){return this[yf]}get message(){return this[bf]}};Object.defineProperty(yr.prototype,"error",{enumerable:!0});Object.defineProperty(yr.prototype,"message",{enumerable:!0});var ri=class extends dt{constructor(e,r={}){super(e),this[xf]=r.data===void 0?null:r.data}get data(){return this[xf]}};Object.defineProperty(ri.prototype,"data",{enumerable:!0});var H0={addEventListener(t,e,r={}){let i;if(t==="message")i=function(s,o){let a=new ri("message",{data:o?s:s.toString()});a[xr]=this,e.call(this,a)};else if(t==="close")i=function(s,o){let a=new Ht("close",{code:s,reason:o.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});a[xr]=this,e.call(this,a)};else if(t==="error")i=function(s){let o=new yr("error",{error:s,message:s.message});o[xr]=this,e.call(this,o)};else if(t==="open")i=function(){let s=new dt("open");s[xr]=this,e.call(this,s)};else return;i[Qs]=!!r[Qs],i[vf]=e,r.once?this.once(t,i):this.on(t,i)},removeEventListener(t,e){for(let r of this.listeners(t))if(r[vf]===e&&!r[Qs]){this.removeListener(t,r);break}}};kf.exports={CloseEvent:Ht,ErrorEvent:yr,Event:dt,EventTarget:H0,MessageEvent:ri}});var Js=x((ly,Of)=>{"use strict";var{tokenChars:ii}=ei();function ut(t,e,r){t[e]===void 0?t[e]=[r]:t[e].push(r)}function V0(t){let e=Object.create(null),r=Object.create(null),i=!1,n=!1,s=!1,o,a,l=-1,c=-1,u=-1,f=0;for(;f<t.length;f++)if(c=t.charCodeAt(f),o===void 0)if(u===-1&&ii[c]===1)l===-1&&(l=f);else if(f!==0&&(c===32||c===9))u===-1&&l!==-1&&(u=f);else if(c===59||c===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f);let p=t.slice(l,u);c===44?(ut(e,p,r),r=Object.create(null)):o=p,l=u=-1}else throw new SyntaxError(`Unexpected character at index ${f}`);else if(a===void 0)if(u===-1&&ii[c]===1)l===-1&&(l=f);else if(c===32||c===9)u===-1&&l!==-1&&(u=f);else if(c===59||c===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f),ut(r,t.slice(l,u),!0),c===44&&(ut(e,o,r),r=Object.create(null),o=void 0),l=u=-1}else if(c===61&&l!==-1&&u===-1)a=t.slice(l,f),l=u=-1;else throw new SyntaxError(`Unexpected character at index ${f}`);else if(n){if(ii[c]!==1)throw new SyntaxError(`Unexpected character at index ${f}`);l===-1?l=f:i||(i=!0),n=!1}else if(s)if(ii[c]===1)l===-1&&(l=f);else if(c===34&&l!==-1)s=!1,u=f;else if(c===92)n=!0;else throw new SyntaxError(`Unexpected character at index ${f}`);else if(c===34&&t.charCodeAt(f-1)===61)s=!0;else if(u===-1&&ii[c]===1)l===-1&&(l=f);else if(l!==-1&&(c===32||c===9))u===-1&&(u=f);else if(c===59||c===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f);let p=t.slice(l,u);i&&(p=p.replace(/\\/g,""),i=!1),ut(r,a,p),c===44&&(ut(e,o,r),r=Object.create(null),o=void 0),a=void 0,l=u=-1}else throw new SyntaxError(`Unexpected character at index ${f}`);if(l===-1||s||c===32||c===9)throw new SyntaxError("Unexpected end of input");u===-1&&(u=f);let h=t.slice(l,u);return o===void 0?ut(e,h,r):(a===void 0?ut(r,h,!0):i?ut(r,a,h.replace(/\\/g,"")):ut(r,a,h),ut(e,o,r)),e}function $0(t){return Object.keys(t).map(e=>{let r=t[e];return Array.isArray(r)||(r=[r]),r.map(i=>[e].concat(Object.keys(i).map(n=>{let s=i[n];return Array.isArray(s)||(s=[s]),s.map(o=>o===!0?n:`${n}=${o}`).join("; ")})).join("; ")).join(", ")}).join(", ")}Of.exports={format:$0,parse:V0}});var so=x((uy,Mf)=>{"use strict";var G0=require("events"),z0=require("https"),W0=require("http"),Af=require("net"),Y0=require("tls"),{randomBytes:K0,createHash:Z0}=require("crypto"),{Readable:cy}=require("stream"),{URL:eo}=require("url"),Ct=Jr(),X0=Zs(),Q0=Xs(),{BINARY_TYPES:If,EMPTY_BUFFER:Ui,GUID:J0,kForOnEventAttribute:to,kListener:ev,kStatusCode:tv,kWebSocket:He,NOOP:Rf}=St(),{EventTarget:{addEventListener:rv,removeEventListener:iv}}=Cf(),{format:nv,parse:sv}=Js(),{toBuffer:ov}=Zr(),mt=["CONNECTING","OPEN","CLOSING","CLOSED"],av=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,ro=[8,13],lv=30*1e3,X=class extends G0{constructor(e,r,i){super(),this._binaryType=If[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=Ui,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=X.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,e!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,r===void 0?r=[]:Array.isArray(r)||(typeof r=="object"&&r!==null?(i=r,r=[]):r=[r]),Bf(this,e,r,i)):this._isServer=!0}get binaryType(){return this._binaryType}set binaryType(e){!If.includes(e)||(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,r,i){let n=new X0({binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:i.maxPayload,skipUTF8Validation:i.skipUTF8Validation});this._sender=new Q0(e,this._extensions,i.generateMask),this._receiver=n,this._socket=e,n[He]=this,e[He]=this,n.on("conclude",fv),n.on("drain",hv),n.on("error",pv),n.on("message",dv),n.on("ping",mv),n.on("pong",gv),e.setTimeout(0),e.setNoDelay(),r.length>0&&e.unshift(r),e.on("close",Nf),e.on("data",Di),e.on("end",Pf),e.on("error",Ff),this._readyState=X.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=X.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[Ct.extensionName]&&this._extensions[Ct.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=X.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,r){if(this.readyState!==X.CLOSED){if(this.readyState===X.CONNECTING){let i="WebSocket was closed before the connection was established";return Je(this,this._req,i)}if(this.readyState===X.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=X.CLOSING,this._sender.close(e,r,!this._isServer,i=>{i||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),lv)}}pause(){this.readyState===X.CONNECTING||this.readyState===X.CLOSED||(this._paused=!0,this._socket.pause())}ping(e,r,i){if(this.readyState===X.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(i=e,e=r=void 0):typeof r=="function"&&(i=r,r=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==X.OPEN){no(this,e,i);return}r===void 0&&(r=!this._isServer),this._sender.ping(e||Ui,r,i)}pong(e,r,i){if(this.readyState===X.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(i=e,e=r=void 0):typeof r=="function"&&(i=r,r=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==X.OPEN){no(this,e,i);return}r===void 0&&(r=!this._isServer),this._sender.pong(e||Ui,r,i)}resume(){this.readyState===X.CONNECTING||this.readyState===X.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,r,i){if(this.readyState===X.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof r=="function"&&(i=r,r={}),typeof e=="number"&&(e=e.toString()),this.readyState!==X.OPEN){no(this,e,i);return}let n={binary:typeof e!="string",mask:!this._isServer,compress:!0,fin:!0,...r};this._extensions[Ct.extensionName]||(n.compress=!1),this._sender.send(e||Ui,n,i)}terminate(){if(this.readyState!==X.CLOSED){if(this.readyState===X.CONNECTING){let e="WebSocket was closed before the connection was established";return Je(this,this._req,e)}this._socket&&(this._readyState=X.CLOSING,this._socket.destroy())}}};Object.defineProperty(X,"CONNECTING",{enumerable:!0,value:mt.indexOf("CONNECTING")});Object.defineProperty(X.prototype,"CONNECTING",{enumerable:!0,value:mt.indexOf("CONNECTING")});Object.defineProperty(X,"OPEN",{enumerable:!0,value:mt.indexOf("OPEN")});Object.defineProperty(X.prototype,"OPEN",{enumerable:!0,value:mt.indexOf("OPEN")});Object.defineProperty(X,"CLOSING",{enumerable:!0,value:mt.indexOf("CLOSING")});Object.defineProperty(X.prototype,"CLOSING",{enumerable:!0,value:mt.indexOf("CLOSING")});Object.defineProperty(X,"CLOSED",{enumerable:!0,value:mt.indexOf("CLOSED")});Object.defineProperty(X.prototype,"CLOSED",{enumerable:!0,value:mt.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(t=>{Object.defineProperty(X.prototype,t,{enumerable:!0})});["open","error","close","message"].forEach(t=>{Object.defineProperty(X.prototype,`on${t}`,{enumerable:!0,get(){for(let e of this.listeners(t))if(e[to])return e[ev];return null},set(e){for(let r of this.listeners(t))if(r[to]){this.removeListener(t,r);break}typeof e=="function"&&this.addEventListener(t,e,{[to]:!0})}})});X.prototype.addEventListener=rv;X.prototype.removeEventListener=iv;Mf.exports=X;function Bf(t,e,r,i){let n={protocolVersion:ro[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...i,createConnection:void 0,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:void 0,host:void 0,path:void 0,port:void 0};if(!ro.includes(n.protocolVersion))throw new RangeError(`Unsupported protocol version: ${n.protocolVersion} (supported versions: ${ro.join(", ")})`);let s;if(e instanceof eo)s=e,t._url=e.href;else{try{s=new eo(e)}catch{throw new SyntaxError(`Invalid URL: ${e}`)}t._url=e}let o=s.protocol==="wss:",a=s.protocol==="ws+unix:",l;if(s.protocol!=="ws:"&&!o&&!a?l=`The URL's protocol must be one of "ws:", "wss:", or "ws+unix:"`:a&&!s.pathname?l="The URL's pathname is empty":s.hash&&(l="The URL contains a fragment identifier"),l){let m=new SyntaxError(l);if(t._redirects===0)throw m;io(t,m);return}let c=o?443:80,u=K0(16).toString("base64"),f=o?z0.get:W0.get,h=new Set,p;if(n.createConnection=o?uv:cv,n.defaultPort=n.defaultPort||c,n.port=s.port||c,n.host=s.hostname.startsWith("[")?s.hostname.slice(1,-1):s.hostname,n.headers={"Sec-WebSocket-Version":n.protocolVersion,"Sec-WebSocket-Key":u,Connection:"Upgrade",Upgrade:"websocket",...n.headers},n.path=s.pathname+s.search,n.timeout=n.handshakeTimeout,n.perMessageDeflate&&(p=new Ct(n.perMessageDeflate!==!0?n.perMessageDeflate:{},!1,n.maxPayload),n.headers["Sec-WebSocket-Extensions"]=nv({[Ct.extensionName]:p.offer()})),r.length){for(let m of r){if(typeof m!="string"||!av.test(m)||h.has(m))throw new SyntaxError("An invalid or duplicated subprotocol was specified");h.add(m)}n.headers["Sec-WebSocket-Protocol"]=r.join(",")}if(n.origin&&(n.protocolVersion<13?n.headers["Sec-WebSocket-Origin"]=n.origin:n.headers.Origin=n.origin),(s.username||s.password)&&(n.auth=`${s.username}:${s.password}`),a){let m=n.path.split(":");n.socketPath=m[0],n.path=m[1]}let d=t._req=f(n);n.timeout&&d.on("timeout",()=>{Je(t,d,"Opening handshake has timed out")}),d.on("error",m=>{d===null||d.aborted||(d=t._req=null,io(t,m))}),d.on("response",m=>{let g=m.headers.location,y=m.statusCode;if(g&&n.followRedirects&&y>=300&&y<400){if(++t._redirects>n.maxRedirects){Je(t,d,"Maximum redirects exceeded");return}d.abort();let I;try{I=new eo(g,e)}catch{let S=new SyntaxError(`Invalid URL: ${g}`);io(t,S);return}Bf(t,I,r,i)}else t.emit("unexpected-response",d,m)||Je(t,d,`Unexpected server response: ${m.statusCode}`)}),d.on("upgrade",(m,g,y)=>{if(t.emit("upgrade",m),t.readyState!==X.CONNECTING)return;d=t._req=null;let I=Z0("sha1").update(u+J0).digest("base64");if(m.headers["sec-websocket-accept"]!==I){Je(t,g,"Invalid Sec-WebSocket-Accept header");return}let w=m.headers["sec-websocket-protocol"],S;if(w!==void 0?h.size?h.has(w)||(S="Server sent an invalid subprotocol"):S="Server sent a subprotocol but none was requested":h.size&&(S="Server sent no subprotocol"),S){Je(t,g,S);return}w&&(t._protocol=w);let b=m.headers["sec-websocket-extensions"];if(b!==void 0){if(!p){Je(t,g,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let $;try{$=sv(b)}catch{Je(t,g,"Invalid Sec-WebSocket-Extensions header");return}let C=Object.keys($);if(C.length!==1||C[0]!==Ct.extensionName){Je(t,g,"Server indicated an extension that was not requested");return}try{p.accept($[Ct.extensionName])}catch{Je(t,g,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[Ct.extensionName]=p}t.setSocket(g,y,{generateMask:n.generateMask,maxPayload:n.maxPayload,skipUTF8Validation:n.skipUTF8Validation})})}function io(t,e){t._readyState=X.CLOSING,t.emit("error",e),t.emitClose()}function cv(t){return t.path=t.socketPath,Af.connect(t)}function uv(t){return t.path=void 0,!t.servername&&t.servername!==""&&(t.servername=Af.isIP(t.host)?"":t.host),Y0.connect(t)}function Je(t,e,r){t._readyState=X.CLOSING;let i=new Error(r);Error.captureStackTrace(i,Je),e.setHeader?(e.abort(),e.socket&&!e.socket.destroyed&&e.socket.destroy(),e.once("abort",t.emitClose.bind(t)),t.emit("error",i)):(e.destroy(i),e.once("error",t.emit.bind(t,"error")),e.once("close",t.emitClose.bind(t)))}function no(t,e,r){if(e){let i=ov(e).length;t._socket?t._sender._bufferedBytes+=i:t._bufferedAmount+=i}if(r){let i=new Error(`WebSocket is not open: readyState ${t.readyState} (${mt[t.readyState]})`);r(i)}}function fv(t,e){let r=this[He];r._closeFrameReceived=!0,r._closeMessage=e,r._closeCode=t,r._socket[He]!==void 0&&(r._socket.removeListener("data",Di),process.nextTick(Lf,r._socket),t===1005?r.close():r.close(t,e))}function hv(){let t=this[He];t.isPaused||t._socket.resume()}function pv(t){let e=this[He];e._socket[He]!==void 0&&(e._socket.removeListener("data",Di),process.nextTick(Lf,e._socket),e.close(t[tv])),e.emit("error",t)}function Tf(){this[He].emitClose()}function dv(t,e){this[He].emit("message",t,e)}function mv(t){let e=this[He];e.pong(t,!e._isServer,Rf),e.emit("ping",t)}function gv(t){this[He].emit("pong",t)}function Lf(t){t.resume()}function Nf(){let t=this[He];this.removeListener("close",Nf),this.removeListener("data",Di),this.removeListener("end",Pf),t._readyState=X.CLOSING;let e;!this._readableState.endEmitted&&!t._closeFrameReceived&&!t._receiver._writableState.errorEmitted&&(e=t._socket.read())!==null&&t._receiver.write(e),t._receiver.end(),this[He]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",Tf),t._receiver.on("finish",Tf))}function Di(t){this[He]._receiver.write(t)||this.pause()}function Pf(){let t=this[He];t._readyState=X.CLOSING,t._receiver.end(),this.end()}function Ff(){let t=this[He];this.removeListener("error",Ff),this.on("error",Rf),t&&(t._readyState=X.CLOSING,this.destroy())}});var Df=x((fy,Uf)=>{"use strict";var{tokenChars:vv}=ei();function _v(t){let e=new Set,r=-1,i=-1,n=0;for(n;n<t.length;n++){let o=t.charCodeAt(n);if(i===-1&&vv[o]===1)r===-1&&(r=n);else if(n!==0&&(o===32||o===9))i===-1&&r!==-1&&(i=n);else if(o===44){if(r===-1)throw new SyntaxError(`Unexpected character at index ${n}`);i===-1&&(i=n);let a=t.slice(r,i);if(e.has(a))throw new SyntaxError(`The "${a}" subprotocol is duplicated`);e.add(a),r=i=-1}else throw new SyntaxError(`Unexpected character at index ${n}`)}if(r===-1||i!==-1)throw new SyntaxError("Unexpected end of input");let s=t.slice(r,n);if(e.has(s))throw new SyntaxError(`The "${s}" subprotocol is duplicated`);return e.add(s),e}Uf.exports={parse:_v}});var Gf=x((my,$f)=>{"use strict";var xv=require("events"),ji=require("http"),hy=require("https"),py=require("net"),dy=require("tls"),{createHash:yv}=require("crypto"),jf=Js(),Vt=Jr(),bv=Df(),wv=so(),{GUID:Ev,kWebSocket:Sv}=St(),kv=/^[+/0-9A-Za-z]{22}==$/,qf=0,Hf=1,Vf=2,oo=class extends xv{constructor(e,r){if(super(),e={maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,...e},e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(e.port!=null?(this._server=ji.createServer((i,n)=>{let s=ji.STATUS_CODES[426];n.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),n.end(s)}),this._server.listen(e.port,e.host,e.backlog,r)):e.server&&(this._server=e.server),this._server){let i=this.emit.bind(this,"connection");this._removeListeners=Cv(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(n,s,o)=>{this.handleUpgrade(n,s,o,i)}})}e.perMessageDeflate===!0&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=qf}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(this._state===Vf){e&&this.once("close",()=>{e(new Error("The server is not running"))}),process.nextTick(ni,this);return}if(e&&this.once("close",e),this._state!==Hf)if(this._state=Hf,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(ni,this):process.nextTick(ni,this);else{let r=this._server;this._removeListeners(),this._removeListeners=this._server=null,r.close(()=>{ni(this)})}}shouldHandle(e){if(this.options.path){let r=e.url.indexOf("?");if((r!==-1?e.url.slice(0,r):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,r,i,n){r.on("error",ao);let s=e.headers["sec-websocket-key"]!==void 0?e.headers["sec-websocket-key"]:!1,o=+e.headers["sec-websocket-version"];if(e.method!=="GET"||e.headers.upgrade.toLowerCase()!=="websocket"||!s||!kv.test(s)||o!==8&&o!==13||!this.shouldHandle(e))return br(r,400);let a=e.headers["sec-websocket-protocol"],l=new Set;if(a!==void 0)try{l=bv.parse(a)}catch{return br(r,400)}let c=e.headers["sec-websocket-extensions"],u={};if(this.options.perMessageDeflate&&c!==void 0){let f=new Vt(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let h=jf.parse(c);h[Vt.extensionName]&&(f.accept(h[Vt.extensionName]),u[Vt.extensionName]=f)}catch{return br(r,400)}}if(this.options.verifyClient){let f={origin:e.headers[`${o===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(f,(h,p,d,m)=>{if(!h)return br(r,p||401,d,m);this.completeUpgrade(u,s,l,e,r,i,n)});return}if(!this.options.verifyClient(f))return br(r,401)}this.completeUpgrade(u,s,l,e,r,i,n)}completeUpgrade(e,r,i,n,s,o,a){if(!s.readable||!s.writable)return s.destroy();if(s[Sv])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>qf)return br(s,503);let l=yv("sha1").update(r+Ev).digest("base64"),c=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${l}`],u=new wv(null);if(i.size){let f=this.options.handleProtocols?this.options.handleProtocols(i,n):i.values().next().value;f&&(c.push(`Sec-WebSocket-Protocol: ${f}`),u._protocol=f)}if(e[Vt.extensionName]){let f=e[Vt.extensionName].params,h=jf.format({[Vt.extensionName]:[f]});c.push(`Sec-WebSocket-Extensions: ${h}`),u._extensions=e}this.emit("headers",c,n),s.write(c.concat(`\r
`).join(`\r
`)),s.removeListener("error",ao),u.setSocket(s,o,{maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(u),u.on("close",()=>{this.clients.delete(u),this._shouldEmitClose&&!this.clients.size&&process.nextTick(ni,this)})),a(u,n)}};$f.exports=oo;function Cv(t,e){for(let r of Object.keys(e))t.on(r,e[r]);return function(){for(let i of Object.keys(e))t.removeListener(i,e[i])}}function ni(t){t._state=Vf,t.emit("close")}function ao(){this.destroy()}function br(t,e,r,i){t.writable&&(r=r||ji.STATUS_CODES[e],i={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(r),...i},t.write(`HTTP/1.1 ${e} ${ji.STATUS_CODES[e]}\r
`+Object.keys(i).map(n=>`${n}: ${i[n]}`).join(`\r
`)+`\r
\r
`+r)),t.removeListener("error",ao),t.destroy()}});var Kf=x((vy,Yf)=>{var Ot=require("constants"),Iv=process.cwd,qi=null,Tv=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return qi||(qi=Iv.call(process)),qi};try{process.cwd()}catch{}typeof process.chdir=="function"&&(fo=process.chdir,process.chdir=function(t){qi=null,fo.call(process,t)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,fo));var fo;Yf.exports=Av;function Av(t){Ot.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&e(t),t.lutimes||r(t),t.chown=s(t.chown),t.fchown=s(t.fchown),t.lchown=s(t.lchown),t.chmod=i(t.chmod),t.fchmod=i(t.fchmod),t.lchmod=i(t.lchmod),t.chownSync=o(t.chownSync),t.fchownSync=o(t.fchownSync),t.lchownSync=o(t.lchownSync),t.chmodSync=n(t.chmodSync),t.fchmodSync=n(t.fchmodSync),t.lchmodSync=n(t.lchmodSync),t.stat=a(t.stat),t.fstat=a(t.fstat),t.lstat=a(t.lstat),t.statSync=l(t.statSync),t.fstatSync=l(t.fstatSync),t.lstatSync=l(t.lstatSync),t.chmod&&!t.lchmod&&(t.lchmod=function(u,f,h){h&&process.nextTick(h)},t.lchmodSync=function(){}),t.chown&&!t.lchown&&(t.lchown=function(u,f,h,p){p&&process.nextTick(p)},t.lchownSync=function(){}),Tv==="win32"&&(t.rename=typeof t.rename!="function"?t.rename:function(u){function f(h,p,d){var m=Date.now(),g=0;u(h,p,function y(I){if(I&&(I.code==="EACCES"||I.code==="EPERM")&&Date.now()-m<6e4){setTimeout(function(){t.stat(p,function(w,S){w&&w.code==="ENOENT"?u(h,p,y):d(I)})},g),g<100&&(g+=10);return}d&&d(I)})}return Object.setPrototypeOf&&Object.setPrototypeOf(f,u),f}(t.rename)),t.read=typeof t.read!="function"?t.read:function(u){function f(h,p,d,m,g,y){var I;if(y&&typeof y=="function"){var w=0;I=function(S,b,$){if(S&&S.code==="EAGAIN"&&w<10)return w++,u.call(t,h,p,d,m,g,I);y.apply(this,arguments)}}return u.call(t,h,p,d,m,g,I)}return Object.setPrototypeOf&&Object.setPrototypeOf(f,u),f}(t.read),t.readSync=typeof t.readSync!="function"?t.readSync:function(u){return function(f,h,p,d,m){for(var g=0;;)try{return u.call(t,f,h,p,d,m)}catch(y){if(y.code==="EAGAIN"&&g<10){g++;continue}throw y}}}(t.readSync);function e(u){u.lchmod=function(f,h,p){u.open(f,Ot.O_WRONLY|Ot.O_SYMLINK,h,function(d,m){if(d){p&&p(d);return}u.fchmod(m,h,function(g){u.close(m,function(y){p&&p(g||y)})})})},u.lchmodSync=function(f,h){var p=u.openSync(f,Ot.O_WRONLY|Ot.O_SYMLINK,h),d=!0,m;try{m=u.fchmodSync(p,h),d=!1}finally{if(d)try{u.closeSync(p)}catch{}else u.closeSync(p)}return m}}function r(u){Ot.hasOwnProperty("O_SYMLINK")&&u.futimes?(u.lutimes=function(f,h,p,d){u.open(f,Ot.O_SYMLINK,function(m,g){if(m){d&&d(m);return}u.futimes(g,h,p,function(y){u.close(g,function(I){d&&d(y||I)})})})},u.lutimesSync=function(f,h,p){var d=u.openSync(f,Ot.O_SYMLINK),m,g=!0;try{m=u.futimesSync(d,h,p),g=!1}finally{if(g)try{u.closeSync(d)}catch{}else u.closeSync(d)}return m}):u.futimes&&(u.lutimes=function(f,h,p,d){d&&process.nextTick(d)},u.lutimesSync=function(){})}function i(u){return u&&function(f,h,p){return u.call(t,f,h,function(d){c(d)&&(d=null),p&&p.apply(this,arguments)})}}function n(u){return u&&function(f,h){try{return u.call(t,f,h)}catch(p){if(!c(p))throw p}}}function s(u){return u&&function(f,h,p,d){return u.call(t,f,h,p,function(m){c(m)&&(m=null),d&&d.apply(this,arguments)})}}function o(u){return u&&function(f,h,p){try{return u.call(t,f,h,p)}catch(d){if(!c(d))throw d}}}function a(u){return u&&function(f,h,p){typeof h=="function"&&(p=h,h=null);function d(m,g){g&&(g.uid<0&&(g.uid+=4294967296),g.gid<0&&(g.gid+=4294967296)),p&&p.apply(this,arguments)}return h?u.call(t,f,h,d):u.call(t,f,d)}}function l(u){return u&&function(f,h){var p=h?u.call(t,f,h):u.call(t,f);return p&&(p.uid<0&&(p.uid+=4294967296),p.gid<0&&(p.gid+=4294967296)),p}}function c(u){if(!u||u.code==="ENOSYS")return!0;var f=!process.getuid||process.getuid()!==0;return!!(f&&(u.code==="EINVAL"||u.code==="EPERM"))}}});var Qf=x((_y,Xf)=>{var Zf=require("stream").Stream;Xf.exports=Rv;function Rv(t){return{ReadStream:e,WriteStream:r};function e(i,n){if(!(this instanceof e))return new e(i,n);Zf.call(this);var s=this;this.path=i,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=64*1024,n=n||{};for(var o=Object.keys(n),a=0,l=o.length;a<l;a++){var c=o[a];this[c]=n[c]}if(this.encoding&&this.setEncoding(this.encoding),this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){s._read()});return}t.open(this.path,this.flags,this.mode,function(u,f){if(u){s.emit("error",u),s.readable=!1;return}s.fd=f,s.emit("open",f),s._read()})}function r(i,n){if(!(this instanceof r))return new r(i,n);Zf.call(this),this.path=i,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,n=n||{};for(var s=Object.keys(n),o=0,a=s.length;o<a;o++){var l=s[o];this[l]=n[l]}if(this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],this.fd===null&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}});var eh=x((xy,Jf)=>{"use strict";Jf.exports=Lv;var Bv=Object.getPrototypeOf||function(t){return t.__proto__};function Lv(t){if(t===null||typeof t!="object")return t;if(t instanceof Object)var e={__proto__:Bv(t)};else var e=Object.create(null);return Object.getOwnPropertyNames(t).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}});var nh=x((yy,mo)=>{var Ie=require("fs"),Nv=Kf(),Pv=Qf(),Fv=eh(),Hi=require("util"),De,$i;typeof Symbol=="function"&&typeof Symbol.for=="function"?(De=Symbol.for("graceful-fs.queue"),$i=Symbol.for("graceful-fs.previous")):(De="___graceful-fs.queue",$i="___graceful-fs.previous");function Mv(){}function ih(t,e){Object.defineProperty(t,De,{get:function(){return e}})}var $t=Mv;Hi.debuglog?$t=Hi.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&($t=function(){var t=Hi.format.apply(Hi,arguments);t="GFS4: "+t.split(/\n/).join(`
GFS4: `),console.error(t)});Ie[De]||(th=global[De]||[],ih(Ie,th),Ie.close=function(t){function e(r,i){return t.call(Ie,r,function(n){n||rh(),typeof i=="function"&&i.apply(this,arguments)})}return Object.defineProperty(e,$i,{value:t}),e}(Ie.close),Ie.closeSync=function(t){function e(r){t.apply(Ie,arguments),rh()}return Object.defineProperty(e,$i,{value:t}),e}(Ie.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){$t(Ie[De]),require("assert").equal(Ie[De].length,0)}));var th;global[De]||ih(global,Ie[De]);mo.exports=ho(Fv(Ie));process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!Ie.__patched&&(mo.exports=ho(Ie),Ie.__patched=!0);function ho(t){Nv(t),t.gracefulify=ho,t.createReadStream=b,t.createWriteStream=$;var e=t.readFile;t.readFile=r;function r(E,T,B){return typeof T=="function"&&(B=T,T=null),G(E,T,B);function G(L,Q,R,F){return e(L,Q,function(j){j&&(j.code==="EMFILE"||j.code==="ENFILE")?wr([G,[L,Q,R],j,F||Date.now(),Date.now()]):typeof R=="function"&&R.apply(this,arguments)})}}var i=t.writeFile;t.writeFile=n;function n(E,T,B,G){return typeof B=="function"&&(G=B,B=null),L(E,T,B,G);function L(Q,R,F,j,z){return i(Q,R,F,function(N){N&&(N.code==="EMFILE"||N.code==="ENFILE")?wr([L,[Q,R,F,j],N,z||Date.now(),Date.now()]):typeof j=="function"&&j.apply(this,arguments)})}}var s=t.appendFile;s&&(t.appendFile=o);function o(E,T,B,G){return typeof B=="function"&&(G=B,B=null),L(E,T,B,G);function L(Q,R,F,j,z){return s(Q,R,F,function(N){N&&(N.code==="EMFILE"||N.code==="ENFILE")?wr([L,[Q,R,F,j],N,z||Date.now(),Date.now()]):typeof j=="function"&&j.apply(this,arguments)})}}var a=t.copyFile;a&&(t.copyFile=l);function l(E,T,B,G){return typeof B=="function"&&(G=B,B=0),L(E,T,B,G);function L(Q,R,F,j,z){return a(Q,R,F,function(N){N&&(N.code==="EMFILE"||N.code==="ENFILE")?wr([L,[Q,R,F,j],N,z||Date.now(),Date.now()]):typeof j=="function"&&j.apply(this,arguments)})}}var c=t.readdir;t.readdir=f;var u=/^v[0-5]\./;function f(E,T,B){typeof T=="function"&&(B=T,T=null);var G=u.test(process.version)?function(R,F,j,z){return c(R,L(R,F,j,z))}:function(R,F,j,z){return c(R,F,L(R,F,j,z))};return G(E,T,B);function L(Q,R,F,j){return function(z,N){z&&(z.code==="EMFILE"||z.code==="ENFILE")?wr([G,[Q,R,F],z,j||Date.now(),Date.now()]):(N&&N.sort&&N.sort(),typeof F=="function"&&F.call(this,z,N))}}}if(process.version.substr(0,4)==="v0.8"){var h=Pv(t);y=h.ReadStream,w=h.WriteStream}var p=t.ReadStream;p&&(y.prototype=Object.create(p.prototype),y.prototype.open=I);var d=t.WriteStream;d&&(w.prototype=Object.create(d.prototype),w.prototype.open=S),Object.defineProperty(t,"ReadStream",{get:function(){return y},set:function(E){y=E},enumerable:!0,configurable:!0}),Object.defineProperty(t,"WriteStream",{get:function(){return w},set:function(E){w=E},enumerable:!0,configurable:!0});var m=y;Object.defineProperty(t,"FileReadStream",{get:function(){return m},set:function(E){m=E},enumerable:!0,configurable:!0});var g=w;Object.defineProperty(t,"FileWriteStream",{get:function(){return g},set:function(E){g=E},enumerable:!0,configurable:!0});function y(E,T){return this instanceof y?(p.apply(this,arguments),this):y.apply(Object.create(y.prototype),arguments)}function I(){var E=this;U(E.path,E.flags,E.mode,function(T,B){T?(E.autoClose&&E.destroy(),E.emit("error",T)):(E.fd=B,E.emit("open",B),E.read())})}function w(E,T){return this instanceof w?(d.apply(this,arguments),this):w.apply(Object.create(w.prototype),arguments)}function S(){var E=this;U(E.path,E.flags,E.mode,function(T,B){T?(E.destroy(),E.emit("error",T)):(E.fd=B,E.emit("open",B))})}function b(E,T){return new t.ReadStream(E,T)}function $(E,T){return new t.WriteStream(E,T)}var C=t.open;t.open=U;function U(E,T,B,G){return typeof B=="function"&&(G=B,B=null),L(E,T,B,G);function L(Q,R,F,j,z){return C(Q,R,F,function(N,Se){N&&(N.code==="EMFILE"||N.code==="ENFILE")?wr([L,[Q,R,F,j],N,z||Date.now(),Date.now()]):typeof j=="function"&&j.apply(this,arguments)})}}return t}function wr(t){$t("ENQUEUE",t[0].name,t[1]),Ie[De].push(t),po()}var Vi;function rh(){for(var t=Date.now(),e=0;e<Ie[De].length;++e)Ie[De][e].length>2&&(Ie[De][e][3]=t,Ie[De][e][4]=t);po()}function po(){if(clearTimeout(Vi),Vi=void 0,Ie[De].length!==0){var t=Ie[De].shift(),e=t[0],r=t[1],i=t[2],n=t[3],s=t[4];if(n===void 0)$t("RETRY",e.name,r),e.apply(null,r);else if(Date.now()-n>=6e4){$t("TIMEOUT",e.name,r);var o=r.pop();typeof o=="function"&&o.call(null,i)}else{var a=Date.now()-s,l=Math.max(s-n,1),c=Math.min(l*1.2,100);a>=c?($t("RETRY",e.name,r),e.apply(null,r.concat([n]))):Ie[De].push(t)}Vi===void 0&&(Vi=setTimeout(po,0))}}});var oh=x((by,sh)=>{function et(t,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(t)),this._timeouts=t,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}sh.exports=et;et.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts};et.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timeouts=[],this._cachedTimeouts=null};et.prototype.retry=function(t){if(this._timeout&&clearTimeout(this._timeout),!t)return!1;var e=new Date().getTime();if(t&&e-this._operationStart>=this._maxRetryTime)return this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(t);var r=this._timeouts.shift();if(r===void 0)if(this._cachedTimeouts)this._errors.splice(this._errors.length-1,this._errors.length),this._timeouts=this._cachedTimeouts.slice(0),r=this._timeouts.shift();else return!1;var i=this,n=setTimeout(function(){i._attempts++,i._operationTimeoutCb&&(i._timeout=setTimeout(function(){i._operationTimeoutCb(i._attempts)},i._operationTimeout),i._options.unref&&i._timeout.unref()),i._fn(i._attempts)},r);return this._options.unref&&n.unref(),!0};et.prototype.attempt=function(t,e){this._fn=t,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var r=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){r._operationTimeoutCb()},r._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};et.prototype.try=function(t){console.log("Using RetryOperation.try() is deprecated"),this.attempt(t)};et.prototype.start=function(t){console.log("Using RetryOperation.start() is deprecated"),this.attempt(t)};et.prototype.start=et.prototype.try;et.prototype.errors=function(){return this._errors};et.prototype.attempts=function(){return this._attempts};et.prototype.mainError=function(){if(this._errors.length===0)return null;for(var t={},e=null,r=0,i=0;i<this._errors.length;i++){var n=this._errors[i],s=n.message,o=(t[s]||0)+1;t[s]=o,o>=r&&(e=n,r=o)}return e}});var ah=x(Gt=>{var Uv=oh();Gt.operation=function(t){var e=Gt.timeouts(t);return new Uv(e,{forever:t&&t.forever,unref:t&&t.unref,maxRetryTime:t&&t.maxRetryTime})};Gt.timeouts=function(t){if(t instanceof Array)return[].concat(t);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var r in t)e[r]=t[r];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var i=[],n=0;n<e.retries;n++)i.push(this.createTimeout(n,e));return t&&t.forever&&!i.length&&i.push(this.createTimeout(n,e)),i.sort(function(s,o){return s-o}),i};Gt.createTimeout=function(t,e){var r=e.randomize?Math.random()+1:1,i=Math.round(r*e.minTimeout*Math.pow(e.factor,t));return i=Math.min(i,e.maxTimeout),i};Gt.wrap=function(t,e,r){if(e instanceof Array&&(r=e,e=null),!r){r=[];for(var i in t)typeof t[i]=="function"&&r.push(i)}for(var n=0;n<r.length;n++){var s=r[n],o=t[s];t[s]=function(l){var c=Gt.operation(e),u=Array.prototype.slice.call(arguments,1),f=u.pop();u.push(function(h){c.retry(h)||(h&&(arguments[0]=c.mainError()),f.apply(this,arguments))}),c.attempt(function(){l.apply(t,u)})}.bind(t,o),t[s].options=e}}});var ch=x((Ey,lh)=>{lh.exports=ah()});var uh=x((Sy,Gi)=>{Gi.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];process.platform!=="win32"&&Gi.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&Gi.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});var mh=x((ky,kr)=>{var Ce=global.process,zt=function(t){return t&&typeof t=="object"&&typeof t.removeListener=="function"&&typeof t.emit=="function"&&typeof t.reallyExit=="function"&&typeof t.listeners=="function"&&typeof t.kill=="function"&&typeof t.pid=="number"&&typeof t.on=="function"};zt(Ce)?(fh=require("assert"),Er=uh(),hh=/^win/i.test(Ce.platform),si=require("events"),typeof si!="function"&&(si=si.EventEmitter),Ce.__signal_exit_emitter__?Pe=Ce.__signal_exit_emitter__:(Pe=Ce.__signal_exit_emitter__=new si,Pe.count=0,Pe.emitted={}),Pe.infinite||(Pe.setMaxListeners(1/0),Pe.infinite=!0),kr.exports=function(t,e){if(!zt(global.process))return function(){};fh.equal(typeof t,"function","a callback must be provided for exit handler"),Sr===!1&&go();var r="exit";e&&e.alwaysLast&&(r="afterexit");var i=function(){Pe.removeListener(r,t),Pe.listeners("exit").length===0&&Pe.listeners("afterexit").length===0&&zi()};return Pe.on(r,t),i},zi=function(){!Sr||!zt(global.process)||(Sr=!1,Er.forEach(function(e){try{Ce.removeListener(e,Wi[e])}catch{}}),Ce.emit=Yi,Ce.reallyExit=vo,Pe.count-=1)},kr.exports.unload=zi,Wt=function(e,r,i){Pe.emitted[e]||(Pe.emitted[e]=!0,Pe.emit(e,r,i))},Wi={},Er.forEach(function(t){Wi[t]=function(){if(!!zt(global.process)){var r=Ce.listeners(t);r.length===Pe.count&&(zi(),Wt("exit",null,t),Wt("afterexit",null,t),hh&&t==="SIGHUP"&&(t="SIGINT"),Ce.kill(Ce.pid,t))}}}),kr.exports.signals=function(){return Er},Sr=!1,go=function(){Sr||!zt(global.process)||(Sr=!0,Pe.count+=1,Er=Er.filter(function(e){try{return Ce.on(e,Wi[e]),!0}catch{return!1}}),Ce.emit=dh,Ce.reallyExit=ph)},kr.exports.load=go,vo=Ce.reallyExit,ph=function(e){!zt(global.process)||(Ce.exitCode=e||0,Wt("exit",Ce.exitCode,null),Wt("afterexit",Ce.exitCode,null),vo.call(Ce,Ce.exitCode))},Yi=Ce.emit,dh=function(e,r){if(e==="exit"&&zt(global.process)){r!==void 0&&(Ce.exitCode=r);var i=Yi.apply(this,arguments);return Wt("exit",Ce.exitCode,null),Wt("afterexit",Ce.exitCode,null),i}else return Yi.apply(this,arguments)}):kr.exports=function(){return function(){}};var fh,Er,hh,si,Pe,zi,Wt,Wi,Sr,go,vo,ph,Yi,dh});var Eh=x((Cy,wh)=>{"use strict";var Dv=require("path"),xh=nh(),jv=ch(),qv=mh(),It={},gh=Symbol();function Hv(t,e,r){let i=e[gh];if(i)return e.stat(t,(s,o)=>{if(s)return r(s);r(null,o.mtime,i)});let n=new Date(Math.ceil(Date.now()/1e3)*1e3+5);e.utimes(t,n,n,s=>{if(s)return r(s);e.stat(t,(o,a)=>{if(o)return r(o);let l=a.mtime.getTime()%1e3===0?"s":"ms";Object.defineProperty(e,gh,{value:l}),r(null,a.mtime,l)})})}function Vv(t){let e=Date.now();return t==="s"&&(e=Math.ceil(e/1e3)*1e3),new Date(e)}function Zi(t,e){return e.lockfilePath||`${t}.lock`}function yh(t,e,r){if(!e.realpath)return r(null,Dv.resolve(t));e.fs.realpath(t,r)}function xo(t,e,r){let i=Zi(t,e);e.fs.mkdir(i,n=>{if(!n)return Hv(i,e.fs,(s,o,a)=>{if(s)return e.fs.rmdir(i,()=>{}),r(s);r(null,o,a)});if(n.code!=="EEXIST")return r(n);if(e.stale<=0)return r(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:t}));e.fs.stat(i,(s,o)=>{if(s)return s.code==="ENOENT"?xo(t,{...e,stale:0},r):r(s);if(!$v(o,e))return r(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:t}));bh(t,e,a=>{if(a)return r(a);xo(t,{...e,stale:0},r)})})})}function $v(t,e){return t.mtime.getTime()<Date.now()-e.stale}function bh(t,e,r){e.fs.rmdir(Zi(t,e),i=>{if(i&&i.code!=="ENOENT")return r(i);r()})}function Ki(t,e){let r=It[t];r.updateTimeout||(r.updateDelay=r.updateDelay||e.update,r.updateTimeout=setTimeout(()=>{r.updateTimeout=null,e.fs.stat(r.lockfilePath,(i,n)=>{let s=r.lastUpdate+e.stale<Date.now();if(i)return i.code==="ENOENT"||s?_o(t,r,Object.assign(i,{code:"ECOMPROMISED"})):(r.updateDelay=1e3,Ki(t,e));if(!(r.mtime.getTime()===n.mtime.getTime()))return _o(t,r,Object.assign(new Error("Unable to update lock within the stale threshold"),{code:"ECOMPROMISED"}));let a=Vv(r.mtimePrecision);e.fs.utimes(r.lockfilePath,a,a,l=>{let c=r.lastUpdate+e.stale<Date.now();if(!r.released){if(l)return l.code==="ENOENT"||c?_o(t,r,Object.assign(l,{code:"ECOMPROMISED"})):(r.updateDelay=1e3,Ki(t,e));r.mtime=a,r.lastUpdate=Date.now(),r.updateDelay=null,Ki(t,e)}})})},r.updateDelay),r.updateTimeout.unref&&r.updateTimeout.unref())}function _o(t,e,r){e.released=!0,e.updateTimeout&&clearTimeout(e.updateTimeout),It[t]===e&&delete It[t],e.options.onCompromised(r)}function Gv(t,e,r){e={stale:1e4,update:null,realpath:!0,retries:0,fs:xh,onCompromised:i=>{throw i},...e},e.retries=e.retries||0,e.retries=typeof e.retries=="number"?{retries:e.retries}:e.retries,e.stale=Math.max(e.stale||0,2e3),e.update=e.update==null?e.stale/2:e.update||0,e.update=Math.max(Math.min(e.update,e.stale/2),1e3),yh(t,e,(i,n)=>{if(i)return r(i);let s=jv.operation(e.retries);s.attempt(()=>{xo(n,e,(o,a,l)=>{if(s.retry(o))return;if(o)return r(s.mainError());let c=It[n]={lockfilePath:Zi(n,e),mtime:a,mtimePrecision:l,options:e,lastUpdate:Date.now()};Ki(n,e),r(null,u=>{if(c.released)return u&&u(Object.assign(new Error("Lock is already released"),{code:"ERELEASED"}));zv(n,{...e,realpath:!1},u)})})})})}function zv(t,e,r){e={fs:xh,realpath:!0,...e},yh(t,e,(i,n)=>{if(i)return r(i);let s=It[n];if(!s)return r(Object.assign(new Error("Lock is not acquired/owned by you"),{code:"ENOTACQUIRED"}));s.updateTimeout&&clearTimeout(s.updateTimeout),s.released=!0,delete It[n],bh(n,e,r)})}function vh(t){return(...e)=>new Promise((r,i)=>{e.push((n,s)=>{n?i(n):r(s)}),t(...e)})}var _h=!1;function Wv(){_h||(_h=!0,qv(()=>{for(let t in It){let e=It[t].options;try{e.fs.rmdirSync(Zi(t,e))}catch{}}}))}wh.exports.lock=async(t,e)=>{Wv();let r=await vh(Gv)(t,e);return vh(r)}});var c_={};$h(c_,{HttpsProxyAgent:()=>Nh.HttpsProxyAgent,PNG:()=>Ph.PNG,SocksProxyAgent:()=>Mh.SocksProxyAgent,StackUtils:()=>n_,colors:()=>Yv,debug:()=>Kv,getProxyForUrl:()=>Lh.getProxyForUrl,jpegjs:()=>Zv,lockfile:()=>Qv,mime:()=>Jv,minimatch:()=>e_,open:()=>t_,program:()=>Fh.program,progress:()=>r_,rimraf:()=>i_,ws:()=>s_,wsReceiver:()=>a_,wsSender:()=>l_,wsServer:()=>o_});module.exports=Gh(c_);var Sh=Te(aa()),kh=Te(tr()),Lh=Te(xa()),Nh=Te(Ia()),Ch=Te(Pa()),Oh=Te(Va()),Ih=Te(Ur()),Th=Te(El()),Ph=Te(_c()),Fh=Te(Rc()),Ah=Te(Fc()),Rh=Te(mu()),Mh=Te(Nu()),Bh=Te(ju());var Ov=Te($u(),1),lo=Te(Zs(),1),co=Te(Xs(),1),zf=Te(so(),1),uo=Te(Gf(),1);var Wf=zf.default;var Yv=Sh.default,Kv=kh.default,Zv=Ch.default,Xv=Eh(),Qv=Xv,Jv=Oh.default,e_=Ih.default,t_=Th.default,r_=Ah.default,i_=Rh.default,n_=Bh.default,s_=Wf,o_=uo.default,a_=lo.default,l_=co.default;0&&(module.exports={HttpsProxyAgent,PNG,SocksProxyAgent,StackUtils,colors,debug,getProxyForUrl,jpegjs,lockfile,mime,minimatch,open,program,progress,rimraf,ws,wsReceiver,wsSender,wsServer});
/*!
 * node-progress
 * Copyright(c) 2011 TJ Holowaychuk <<EMAIL>>
 * MIT Licensed
 */
