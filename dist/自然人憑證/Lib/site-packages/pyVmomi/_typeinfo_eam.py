# ******* WARNING - AUTO GENERATED CODE - DO NOT EDIT *******
from .VmomiSupport import CreateDataType, CreateManagedType
from .VmomiSupport import CreateEnumType
from .VmomiSupport import AddVersion, AddVersionParent
from .VmomiSupport import AddBreakingChangesInfo
from .VmomiSupport import F_LINK, F_LINKABLE
from .VmomiSupport import F_OPTIONAL, F_SECRET
from .VmomiSupport import newestVersions
from .VmomiSupport import ltsVersions, dottedVersions
from .VmomiSupport import oldestVersions

AddVersion("vmodl.query.version.version4", "", "", 0, "vim25")
AddVersion("vmodl.query.version.version3", "", "", 0, "vim25")
AddVersion("vmodl.query.version.version2", "", "", 0, "vim25")
AddVersion("vmodl.query.version.version1", "", "", 0, "vim25")
AddVersion("vim.version.version8", "vim25", "5.1", 0, "vim25")
AddVersion("vim.version.version9", "vim25", "5.5", 0, "vim25")
AddVersion("vim.version.version6", "vim25", "4.1", 0, "vim25")
AddVersion("vim.version.version7", "vim25", "5.0", 0, "vim25")
AddVersion("vim.version.version1", "vim2", "2.0", 0, "vim25")
AddVersion("vim.version.version4", "vim25", "2.5u2server", 0, "vim25")
AddVersion("vim.version.version5", "vim25", "4.0", 0, "vim25")
AddVersion("vim.version.version2", "vim25", "2.5", 0, "vim25")
AddVersion("vim.version.version3", "vim25", "2.5u2", 0, "vim25")
AddVersion("vmodl.version.version0", "", "", 0, "vim25")
AddVersion("vmodl.version.version1", "", "", 0, "vim25")
AddVersion("vmodl.version.version2", "", "", 0, "vim25")
AddVersion("eam.version.version2_5", "eam", "2_5", 0, "eam")
AddVersion("eam.version.version6_5", "eam", "6.5", 0, "eam")
AddVersion("eam.version.version6_8", "eam", "6.8", 0, "eam")
AddVersion("eam.version.version6_9", "eam", "6.9", 0, "eam")
AddVersion("eam.version.version6_7", "eam", "6.7", 0, "eam")
AddVersion("vmodl.reflect.version.version1", "reflect", "1.0", 0, "reflect")
AddVersion("vmodl.reflect.version.version2", "reflect", "2.0", 0, "reflect")
AddVersion("vim.version.version13", "vim25", "6.7.1", 0, "vim25")
AddVersion("eam.version.version7_3", "eam", "7.3", 0, "eam")
AddVersion("eam.version.version1", "eam", "1.0", 0, "eam")
AddVersion("eam.version.version2", "eam", "2.0", 0, "eam")
AddVersion("eam.version.version7_4", "eam", "7.4", 0, "eam")
AddVersion("eam.version.version3", "eam", "3.0", 0, "eam")
AddVersion("eam.version.version7_1", "eam", "7.1", 0, "eam")
AddVersion("eam.version.version7_2", "eam", "7.2", 0, "eam")
AddVersion("eam.version.version7_5", "eam", "7.5", 0, "eam")
AddVersion("eam.version.version7_6", "eam", "7.6", 0, "eam")
AddVersion("eam.version.version6", "eam", "6.0", 0, "eam")
AddVersion("eam.version.version7", "eam", "7.0", 0, "eam")
AddVersion("vim.version.version10", "vim25", "6.0", 0, "vim25")
AddVersion("vim.version.version11", "vim25", "6.5", 0, "vim25")
AddVersion("vim.version.version12", "vim25", "6.7", 0, "vim25")
AddVersion("eam.version.version8_2", "eam", "8.2", 0, "eam")
AddVersionParent("vmodl.query.version.version4", "vmodl.query.version.version4")
AddVersionParent("vmodl.query.version.version4", "vmodl.query.version.version3")
AddVersionParent("vmodl.query.version.version4", "vmodl.query.version.version2")
AddVersionParent("vmodl.query.version.version4", "vmodl.query.version.version1")
AddVersionParent("vmodl.query.version.version4", "vmodl.version.version0")
AddVersionParent("vmodl.query.version.version4", "vmodl.version.version1")
AddVersionParent("vmodl.query.version.version4", "vmodl.version.version2")
AddVersionParent("vmodl.query.version.version3", "vmodl.query.version.version3")
AddVersionParent("vmodl.query.version.version3", "vmodl.query.version.version2")
AddVersionParent("vmodl.query.version.version3", "vmodl.query.version.version1")
AddVersionParent("vmodl.query.version.version3", "vmodl.version.version0")
AddVersionParent("vmodl.query.version.version3", "vmodl.version.version1")
AddVersionParent("vmodl.query.version.version2", "vmodl.query.version.version2")
AddVersionParent("vmodl.query.version.version2", "vmodl.query.version.version1")
AddVersionParent("vmodl.query.version.version2", "vmodl.version.version0")
AddVersionParent("vmodl.query.version.version2", "vmodl.version.version1")
AddVersionParent("vmodl.query.version.version1", "vmodl.query.version.version1")
AddVersionParent("vmodl.query.version.version1", "vmodl.version.version0")
AddVersionParent("vim.version.version8", "vmodl.query.version.version4")
AddVersionParent("vim.version.version8", "vmodl.query.version.version3")
AddVersionParent("vim.version.version8", "vmodl.query.version.version2")
AddVersionParent("vim.version.version8", "vmodl.query.version.version1")
AddVersionParent("vim.version.version8", "vim.version.version8")
AddVersionParent("vim.version.version8", "vim.version.version6")
AddVersionParent("vim.version.version8", "vim.version.version7")
AddVersionParent("vim.version.version8", "vim.version.version1")
AddVersionParent("vim.version.version8", "vim.version.version4")
AddVersionParent("vim.version.version8", "vim.version.version5")
AddVersionParent("vim.version.version8", "vim.version.version2")
AddVersionParent("vim.version.version8", "vim.version.version3")
AddVersionParent("vim.version.version8", "vmodl.version.version0")
AddVersionParent("vim.version.version8", "vmodl.version.version1")
AddVersionParent("vim.version.version8", "vmodl.version.version2")
AddVersionParent("vim.version.version8", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version9", "vmodl.query.version.version4")
AddVersionParent("vim.version.version9", "vmodl.query.version.version3")
AddVersionParent("vim.version.version9", "vmodl.query.version.version2")
AddVersionParent("vim.version.version9", "vmodl.query.version.version1")
AddVersionParent("vim.version.version9", "vim.version.version8")
AddVersionParent("vim.version.version9", "vim.version.version9")
AddVersionParent("vim.version.version9", "vim.version.version6")
AddVersionParent("vim.version.version9", "vim.version.version7")
AddVersionParent("vim.version.version9", "vim.version.version1")
AddVersionParent("vim.version.version9", "vim.version.version4")
AddVersionParent("vim.version.version9", "vim.version.version5")
AddVersionParent("vim.version.version9", "vim.version.version2")
AddVersionParent("vim.version.version9", "vim.version.version3")
AddVersionParent("vim.version.version9", "vmodl.version.version0")
AddVersionParent("vim.version.version9", "vmodl.version.version1")
AddVersionParent("vim.version.version9", "vmodl.version.version2")
AddVersionParent("vim.version.version9", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version6", "vmodl.query.version.version3")
AddVersionParent("vim.version.version6", "vmodl.query.version.version2")
AddVersionParent("vim.version.version6", "vmodl.query.version.version1")
AddVersionParent("vim.version.version6", "vim.version.version6")
AddVersionParent("vim.version.version6", "vim.version.version1")
AddVersionParent("vim.version.version6", "vim.version.version4")
AddVersionParent("vim.version.version6", "vim.version.version5")
AddVersionParent("vim.version.version6", "vim.version.version2")
AddVersionParent("vim.version.version6", "vim.version.version3")
AddVersionParent("vim.version.version6", "vmodl.version.version0")
AddVersionParent("vim.version.version6", "vmodl.version.version1")
AddVersionParent("vim.version.version7", "vmodl.query.version.version4")
AddVersionParent("vim.version.version7", "vmodl.query.version.version3")
AddVersionParent("vim.version.version7", "vmodl.query.version.version2")
AddVersionParent("vim.version.version7", "vmodl.query.version.version1")
AddVersionParent("vim.version.version7", "vim.version.version6")
AddVersionParent("vim.version.version7", "vim.version.version7")
AddVersionParent("vim.version.version7", "vim.version.version1")
AddVersionParent("vim.version.version7", "vim.version.version4")
AddVersionParent("vim.version.version7", "vim.version.version5")
AddVersionParent("vim.version.version7", "vim.version.version2")
AddVersionParent("vim.version.version7", "vim.version.version3")
AddVersionParent("vim.version.version7", "vmodl.version.version0")
AddVersionParent("vim.version.version7", "vmodl.version.version1")
AddVersionParent("vim.version.version7", "vmodl.version.version2")
AddVersionParent("vim.version.version7", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version1", "vmodl.query.version.version1")
AddVersionParent("vim.version.version1", "vim.version.version1")
AddVersionParent("vim.version.version1", "vmodl.version.version0")
AddVersionParent("vim.version.version4", "vmodl.query.version.version1")
AddVersionParent("vim.version.version4", "vim.version.version1")
AddVersionParent("vim.version.version4", "vim.version.version4")
AddVersionParent("vim.version.version4", "vim.version.version2")
AddVersionParent("vim.version.version4", "vim.version.version3")
AddVersionParent("vim.version.version4", "vmodl.version.version0")
AddVersionParent("vim.version.version5", "vmodl.query.version.version2")
AddVersionParent("vim.version.version5", "vmodl.query.version.version1")
AddVersionParent("vim.version.version5", "vim.version.version1")
AddVersionParent("vim.version.version5", "vim.version.version4")
AddVersionParent("vim.version.version5", "vim.version.version5")
AddVersionParent("vim.version.version5", "vim.version.version2")
AddVersionParent("vim.version.version5", "vim.version.version3")
AddVersionParent("vim.version.version5", "vmodl.version.version0")
AddVersionParent("vim.version.version5", "vmodl.version.version1")
AddVersionParent("vim.version.version2", "vmodl.query.version.version1")
AddVersionParent("vim.version.version2", "vim.version.version1")
AddVersionParent("vim.version.version2", "vim.version.version2")
AddVersionParent("vim.version.version2", "vmodl.version.version0")
AddVersionParent("vim.version.version3", "vmodl.query.version.version1")
AddVersionParent("vim.version.version3", "vim.version.version1")
AddVersionParent("vim.version.version3", "vim.version.version2")
AddVersionParent("vim.version.version3", "vim.version.version3")
AddVersionParent("vim.version.version3", "vmodl.version.version0")
AddVersionParent("vmodl.version.version0", "vmodl.version.version0")
AddVersionParent("vmodl.version.version1", "vmodl.version.version0")
AddVersionParent("vmodl.version.version1", "vmodl.version.version1")
AddVersionParent("vmodl.version.version2", "vmodl.version.version0")
AddVersionParent("vmodl.version.version2", "vmodl.version.version1")
AddVersionParent("vmodl.version.version2", "vmodl.version.version2")
AddVersionParent("eam.version.version2_5", "vmodl.query.version.version4")
AddVersionParent("eam.version.version2_5", "vmodl.query.version.version3")
AddVersionParent("eam.version.version2_5", "vmodl.query.version.version2")
AddVersionParent("eam.version.version2_5", "vmodl.query.version.version1")
AddVersionParent("eam.version.version2_5", "vim.version.version8")
AddVersionParent("eam.version.version2_5", "vim.version.version6")
AddVersionParent("eam.version.version2_5", "vim.version.version7")
AddVersionParent("eam.version.version2_5", "vim.version.version1")
AddVersionParent("eam.version.version2_5", "vim.version.version4")
AddVersionParent("eam.version.version2_5", "vim.version.version5")
AddVersionParent("eam.version.version2_5", "vim.version.version2")
AddVersionParent("eam.version.version2_5", "vim.version.version3")
AddVersionParent("eam.version.version2_5", "vmodl.version.version0")
AddVersionParent("eam.version.version2_5", "vmodl.version.version1")
AddVersionParent("eam.version.version2_5", "vmodl.version.version2")
AddVersionParent("eam.version.version2_5", "eam.version.version2_5")
AddVersionParent("eam.version.version2_5", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version2_5", "eam.version.version1")
AddVersionParent("eam.version.version2_5", "eam.version.version2")
AddVersionParent("eam.version.version6_5", "vmodl.query.version.version4")
AddVersionParent("eam.version.version6_5", "vmodl.query.version.version3")
AddVersionParent("eam.version.version6_5", "vmodl.query.version.version2")
AddVersionParent("eam.version.version6_5", "vmodl.query.version.version1")
AddVersionParent("eam.version.version6_5", "vim.version.version8")
AddVersionParent("eam.version.version6_5", "vim.version.version6")
AddVersionParent("eam.version.version6_5", "vim.version.version7")
AddVersionParent("eam.version.version6_5", "vim.version.version1")
AddVersionParent("eam.version.version6_5", "vim.version.version4")
AddVersionParent("eam.version.version6_5", "vim.version.version5")
AddVersionParent("eam.version.version6_5", "vim.version.version2")
AddVersionParent("eam.version.version6_5", "vim.version.version3")
AddVersionParent("eam.version.version6_5", "vmodl.version.version0")
AddVersionParent("eam.version.version6_5", "vmodl.version.version1")
AddVersionParent("eam.version.version6_5", "vmodl.version.version2")
AddVersionParent("eam.version.version6_5", "eam.version.version2_5")
AddVersionParent("eam.version.version6_5", "eam.version.version6_5")
AddVersionParent("eam.version.version6_5", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version6_5", "eam.version.version1")
AddVersionParent("eam.version.version6_5", "eam.version.version2")
AddVersionParent("eam.version.version6_5", "eam.version.version3")
AddVersionParent("eam.version.version6_5", "eam.version.version6")
AddVersionParent("eam.version.version6_8", "vmodl.query.version.version4")
AddVersionParent("eam.version.version6_8", "vmodl.query.version.version3")
AddVersionParent("eam.version.version6_8", "vmodl.query.version.version2")
AddVersionParent("eam.version.version6_8", "vmodl.query.version.version1")
AddVersionParent("eam.version.version6_8", "vim.version.version8")
AddVersionParent("eam.version.version6_8", "vim.version.version6")
AddVersionParent("eam.version.version6_8", "vim.version.version7")
AddVersionParent("eam.version.version6_8", "vim.version.version1")
AddVersionParent("eam.version.version6_8", "vim.version.version4")
AddVersionParent("eam.version.version6_8", "vim.version.version5")
AddVersionParent("eam.version.version6_8", "vim.version.version2")
AddVersionParent("eam.version.version6_8", "vim.version.version3")
AddVersionParent("eam.version.version6_8", "vmodl.version.version0")
AddVersionParent("eam.version.version6_8", "vmodl.version.version1")
AddVersionParent("eam.version.version6_8", "vmodl.version.version2")
AddVersionParent("eam.version.version6_8", "eam.version.version2_5")
AddVersionParent("eam.version.version6_8", "eam.version.version6_5")
AddVersionParent("eam.version.version6_8", "eam.version.version6_8")
AddVersionParent("eam.version.version6_8", "eam.version.version6_7")
AddVersionParent("eam.version.version6_8", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version6_8", "eam.version.version1")
AddVersionParent("eam.version.version6_8", "eam.version.version2")
AddVersionParent("eam.version.version6_8", "eam.version.version3")
AddVersionParent("eam.version.version6_8", "eam.version.version6")
AddVersionParent("eam.version.version6_9", "vmodl.query.version.version4")
AddVersionParent("eam.version.version6_9", "vmodl.query.version.version3")
AddVersionParent("eam.version.version6_9", "vmodl.query.version.version2")
AddVersionParent("eam.version.version6_9", "vmodl.query.version.version1")
AddVersionParent("eam.version.version6_9", "vim.version.version8")
AddVersionParent("eam.version.version6_9", "vim.version.version6")
AddVersionParent("eam.version.version6_9", "vim.version.version7")
AddVersionParent("eam.version.version6_9", "vim.version.version1")
AddVersionParent("eam.version.version6_9", "vim.version.version4")
AddVersionParent("eam.version.version6_9", "vim.version.version5")
AddVersionParent("eam.version.version6_9", "vim.version.version2")
AddVersionParent("eam.version.version6_9", "vim.version.version3")
AddVersionParent("eam.version.version6_9", "vmodl.version.version0")
AddVersionParent("eam.version.version6_9", "vmodl.version.version1")
AddVersionParent("eam.version.version6_9", "vmodl.version.version2")
AddVersionParent("eam.version.version6_9", "eam.version.version2_5")
AddVersionParent("eam.version.version6_9", "eam.version.version6_5")
AddVersionParent("eam.version.version6_9", "eam.version.version6_8")
AddVersionParent("eam.version.version6_9", "eam.version.version6_9")
AddVersionParent("eam.version.version6_9", "eam.version.version6_7")
AddVersionParent("eam.version.version6_9", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version6_9", "eam.version.version1")
AddVersionParent("eam.version.version6_9", "eam.version.version2")
AddVersionParent("eam.version.version6_9", "eam.version.version3")
AddVersionParent("eam.version.version6_9", "eam.version.version6")
AddVersionParent("eam.version.version6_7", "vmodl.query.version.version4")
AddVersionParent("eam.version.version6_7", "vmodl.query.version.version3")
AddVersionParent("eam.version.version6_7", "vmodl.query.version.version2")
AddVersionParent("eam.version.version6_7", "vmodl.query.version.version1")
AddVersionParent("eam.version.version6_7", "vim.version.version8")
AddVersionParent("eam.version.version6_7", "vim.version.version6")
AddVersionParent("eam.version.version6_7", "vim.version.version7")
AddVersionParent("eam.version.version6_7", "vim.version.version1")
AddVersionParent("eam.version.version6_7", "vim.version.version4")
AddVersionParent("eam.version.version6_7", "vim.version.version5")
AddVersionParent("eam.version.version6_7", "vim.version.version2")
AddVersionParent("eam.version.version6_7", "vim.version.version3")
AddVersionParent("eam.version.version6_7", "vmodl.version.version0")
AddVersionParent("eam.version.version6_7", "vmodl.version.version1")
AddVersionParent("eam.version.version6_7", "vmodl.version.version2")
AddVersionParent("eam.version.version6_7", "eam.version.version2_5")
AddVersionParent("eam.version.version6_7", "eam.version.version6_5")
AddVersionParent("eam.version.version6_7", "eam.version.version6_7")
AddVersionParent("eam.version.version6_7", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version6_7", "eam.version.version1")
AddVersionParent("eam.version.version6_7", "eam.version.version2")
AddVersionParent("eam.version.version6_7", "eam.version.version3")
AddVersionParent("eam.version.version6_7", "eam.version.version6")
AddVersionParent("vmodl.reflect.version.version1", "vmodl.version.version0")
AddVersionParent("vmodl.reflect.version.version1", "vmodl.version.version1")
AddVersionParent("vmodl.reflect.version.version1", "vmodl.version.version2")
AddVersionParent("vmodl.reflect.version.version1", "vmodl.reflect.version.version1")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.version.version0")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.version.version1")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.version.version2")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.reflect.version.version1")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version13", "vmodl.query.version.version4")
AddVersionParent("vim.version.version13", "vmodl.query.version.version3")
AddVersionParent("vim.version.version13", "vmodl.query.version.version2")
AddVersionParent("vim.version.version13", "vmodl.query.version.version1")
AddVersionParent("vim.version.version13", "vim.version.version8")
AddVersionParent("vim.version.version13", "vim.version.version9")
AddVersionParent("vim.version.version13", "vim.version.version6")
AddVersionParent("vim.version.version13", "vim.version.version7")
AddVersionParent("vim.version.version13", "vim.version.version1")
AddVersionParent("vim.version.version13", "vim.version.version4")
AddVersionParent("vim.version.version13", "vim.version.version5")
AddVersionParent("vim.version.version13", "vim.version.version2")
AddVersionParent("vim.version.version13", "vim.version.version3")
AddVersionParent("vim.version.version13", "vmodl.version.version0")
AddVersionParent("vim.version.version13", "vmodl.version.version1")
AddVersionParent("vim.version.version13", "vmodl.version.version2")
AddVersionParent("vim.version.version13", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version13", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version13", "vim.version.version13")
AddVersionParent("vim.version.version13", "vim.version.version10")
AddVersionParent("vim.version.version13", "vim.version.version11")
AddVersionParent("vim.version.version13", "vim.version.version12")
AddVersionParent("eam.version.version7_3", "vmodl.query.version.version4")
AddVersionParent("eam.version.version7_3", "vmodl.query.version.version3")
AddVersionParent("eam.version.version7_3", "vmodl.query.version.version2")
AddVersionParent("eam.version.version7_3", "vmodl.query.version.version1")
AddVersionParent("eam.version.version7_3", "vim.version.version8")
AddVersionParent("eam.version.version7_3", "vim.version.version9")
AddVersionParent("eam.version.version7_3", "vim.version.version6")
AddVersionParent("eam.version.version7_3", "vim.version.version7")
AddVersionParent("eam.version.version7_3", "vim.version.version1")
AddVersionParent("eam.version.version7_3", "vim.version.version4")
AddVersionParent("eam.version.version7_3", "vim.version.version5")
AddVersionParent("eam.version.version7_3", "vim.version.version2")
AddVersionParent("eam.version.version7_3", "vim.version.version3")
AddVersionParent("eam.version.version7_3", "vmodl.version.version0")
AddVersionParent("eam.version.version7_3", "vmodl.version.version1")
AddVersionParent("eam.version.version7_3", "vmodl.version.version2")
AddVersionParent("eam.version.version7_3", "eam.version.version2_5")
AddVersionParent("eam.version.version7_3", "eam.version.version6_5")
AddVersionParent("eam.version.version7_3", "eam.version.version6_8")
AddVersionParent("eam.version.version7_3", "eam.version.version6_9")
AddVersionParent("eam.version.version7_3", "eam.version.version6_7")
AddVersionParent("eam.version.version7_3", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version7_3", "vmodl.reflect.version.version2")
AddVersionParent("eam.version.version7_3", "vim.version.version13")
AddVersionParent("eam.version.version7_3", "eam.version.version7_3")
AddVersionParent("eam.version.version7_3", "eam.version.version1")
AddVersionParent("eam.version.version7_3", "eam.version.version2")
AddVersionParent("eam.version.version7_3", "eam.version.version3")
AddVersionParent("eam.version.version7_3", "eam.version.version7_1")
AddVersionParent("eam.version.version7_3", "eam.version.version7_2")
AddVersionParent("eam.version.version7_3", "eam.version.version6")
AddVersionParent("eam.version.version7_3", "eam.version.version7")
AddVersionParent("eam.version.version7_3", "vim.version.version10")
AddVersionParent("eam.version.version7_3", "vim.version.version11")
AddVersionParent("eam.version.version7_3", "vim.version.version12")
AddVersionParent("eam.version.version1", "vmodl.query.version.version4")
AddVersionParent("eam.version.version1", "vmodl.query.version.version3")
AddVersionParent("eam.version.version1", "vmodl.query.version.version2")
AddVersionParent("eam.version.version1", "vmodl.query.version.version1")
AddVersionParent("eam.version.version1", "vim.version.version8")
AddVersionParent("eam.version.version1", "vim.version.version6")
AddVersionParent("eam.version.version1", "vim.version.version7")
AddVersionParent("eam.version.version1", "vim.version.version1")
AddVersionParent("eam.version.version1", "vim.version.version4")
AddVersionParent("eam.version.version1", "vim.version.version5")
AddVersionParent("eam.version.version1", "vim.version.version2")
AddVersionParent("eam.version.version1", "vim.version.version3")
AddVersionParent("eam.version.version1", "vmodl.version.version0")
AddVersionParent("eam.version.version1", "vmodl.version.version1")
AddVersionParent("eam.version.version1", "vmodl.version.version2")
AddVersionParent("eam.version.version1", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version1", "eam.version.version1")
AddVersionParent("eam.version.version2", "vmodl.query.version.version4")
AddVersionParent("eam.version.version2", "vmodl.query.version.version3")
AddVersionParent("eam.version.version2", "vmodl.query.version.version2")
AddVersionParent("eam.version.version2", "vmodl.query.version.version1")
AddVersionParent("eam.version.version2", "vim.version.version8")
AddVersionParent("eam.version.version2", "vim.version.version6")
AddVersionParent("eam.version.version2", "vim.version.version7")
AddVersionParent("eam.version.version2", "vim.version.version1")
AddVersionParent("eam.version.version2", "vim.version.version4")
AddVersionParent("eam.version.version2", "vim.version.version5")
AddVersionParent("eam.version.version2", "vim.version.version2")
AddVersionParent("eam.version.version2", "vim.version.version3")
AddVersionParent("eam.version.version2", "vmodl.version.version0")
AddVersionParent("eam.version.version2", "vmodl.version.version1")
AddVersionParent("eam.version.version2", "vmodl.version.version2")
AddVersionParent("eam.version.version2", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version2", "eam.version.version1")
AddVersionParent("eam.version.version2", "eam.version.version2")
AddVersionParent("eam.version.version7_4", "vmodl.query.version.version4")
AddVersionParent("eam.version.version7_4", "vmodl.query.version.version3")
AddVersionParent("eam.version.version7_4", "vmodl.query.version.version2")
AddVersionParent("eam.version.version7_4", "vmodl.query.version.version1")
AddVersionParent("eam.version.version7_4", "vim.version.version8")
AddVersionParent("eam.version.version7_4", "vim.version.version9")
AddVersionParent("eam.version.version7_4", "vim.version.version6")
AddVersionParent("eam.version.version7_4", "vim.version.version7")
AddVersionParent("eam.version.version7_4", "vim.version.version1")
AddVersionParent("eam.version.version7_4", "vim.version.version4")
AddVersionParent("eam.version.version7_4", "vim.version.version5")
AddVersionParent("eam.version.version7_4", "vim.version.version2")
AddVersionParent("eam.version.version7_4", "vim.version.version3")
AddVersionParent("eam.version.version7_4", "vmodl.version.version0")
AddVersionParent("eam.version.version7_4", "vmodl.version.version1")
AddVersionParent("eam.version.version7_4", "vmodl.version.version2")
AddVersionParent("eam.version.version7_4", "eam.version.version2_5")
AddVersionParent("eam.version.version7_4", "eam.version.version6_5")
AddVersionParent("eam.version.version7_4", "eam.version.version6_8")
AddVersionParent("eam.version.version7_4", "eam.version.version6_9")
AddVersionParent("eam.version.version7_4", "eam.version.version6_7")
AddVersionParent("eam.version.version7_4", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version7_4", "vmodl.reflect.version.version2")
AddVersionParent("eam.version.version7_4", "vim.version.version13")
AddVersionParent("eam.version.version7_4", "eam.version.version7_3")
AddVersionParent("eam.version.version7_4", "eam.version.version1")
AddVersionParent("eam.version.version7_4", "eam.version.version2")
AddVersionParent("eam.version.version7_4", "eam.version.version7_4")
AddVersionParent("eam.version.version7_4", "eam.version.version3")
AddVersionParent("eam.version.version7_4", "eam.version.version7_1")
AddVersionParent("eam.version.version7_4", "eam.version.version7_2")
AddVersionParent("eam.version.version7_4", "eam.version.version6")
AddVersionParent("eam.version.version7_4", "eam.version.version7")
AddVersionParent("eam.version.version7_4", "vim.version.version10")
AddVersionParent("eam.version.version7_4", "vim.version.version11")
AddVersionParent("eam.version.version7_4", "vim.version.version12")
AddVersionParent("eam.version.version3", "vmodl.query.version.version4")
AddVersionParent("eam.version.version3", "vmodl.query.version.version3")
AddVersionParent("eam.version.version3", "vmodl.query.version.version2")
AddVersionParent("eam.version.version3", "vmodl.query.version.version1")
AddVersionParent("eam.version.version3", "vim.version.version8")
AddVersionParent("eam.version.version3", "vim.version.version6")
AddVersionParent("eam.version.version3", "vim.version.version7")
AddVersionParent("eam.version.version3", "vim.version.version1")
AddVersionParent("eam.version.version3", "vim.version.version4")
AddVersionParent("eam.version.version3", "vim.version.version5")
AddVersionParent("eam.version.version3", "vim.version.version2")
AddVersionParent("eam.version.version3", "vim.version.version3")
AddVersionParent("eam.version.version3", "vmodl.version.version0")
AddVersionParent("eam.version.version3", "vmodl.version.version1")
AddVersionParent("eam.version.version3", "vmodl.version.version2")
AddVersionParent("eam.version.version3", "eam.version.version2_5")
AddVersionParent("eam.version.version3", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version3", "eam.version.version1")
AddVersionParent("eam.version.version3", "eam.version.version2")
AddVersionParent("eam.version.version3", "eam.version.version3")
AddVersionParent("eam.version.version7_1", "vmodl.query.version.version4")
AddVersionParent("eam.version.version7_1", "vmodl.query.version.version3")
AddVersionParent("eam.version.version7_1", "vmodl.query.version.version2")
AddVersionParent("eam.version.version7_1", "vmodl.query.version.version1")
AddVersionParent("eam.version.version7_1", "vim.version.version8")
AddVersionParent("eam.version.version7_1", "vim.version.version9")
AddVersionParent("eam.version.version7_1", "vim.version.version6")
AddVersionParent("eam.version.version7_1", "vim.version.version7")
AddVersionParent("eam.version.version7_1", "vim.version.version1")
AddVersionParent("eam.version.version7_1", "vim.version.version4")
AddVersionParent("eam.version.version7_1", "vim.version.version5")
AddVersionParent("eam.version.version7_1", "vim.version.version2")
AddVersionParent("eam.version.version7_1", "vim.version.version3")
AddVersionParent("eam.version.version7_1", "vmodl.version.version0")
AddVersionParent("eam.version.version7_1", "vmodl.version.version1")
AddVersionParent("eam.version.version7_1", "vmodl.version.version2")
AddVersionParent("eam.version.version7_1", "eam.version.version2_5")
AddVersionParent("eam.version.version7_1", "eam.version.version6_5")
AddVersionParent("eam.version.version7_1", "eam.version.version6_8")
AddVersionParent("eam.version.version7_1", "eam.version.version6_9")
AddVersionParent("eam.version.version7_1", "eam.version.version6_7")
AddVersionParent("eam.version.version7_1", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version7_1", "vmodl.reflect.version.version2")
AddVersionParent("eam.version.version7_1", "vim.version.version13")
AddVersionParent("eam.version.version7_1", "eam.version.version1")
AddVersionParent("eam.version.version7_1", "eam.version.version2")
AddVersionParent("eam.version.version7_1", "eam.version.version3")
AddVersionParent("eam.version.version7_1", "eam.version.version7_1")
AddVersionParent("eam.version.version7_1", "eam.version.version6")
AddVersionParent("eam.version.version7_1", "eam.version.version7")
AddVersionParent("eam.version.version7_1", "vim.version.version10")
AddVersionParent("eam.version.version7_1", "vim.version.version11")
AddVersionParent("eam.version.version7_1", "vim.version.version12")
AddVersionParent("eam.version.version7_2", "vmodl.query.version.version4")
AddVersionParent("eam.version.version7_2", "vmodl.query.version.version3")
AddVersionParent("eam.version.version7_2", "vmodl.query.version.version2")
AddVersionParent("eam.version.version7_2", "vmodl.query.version.version1")
AddVersionParent("eam.version.version7_2", "vim.version.version8")
AddVersionParent("eam.version.version7_2", "vim.version.version9")
AddVersionParent("eam.version.version7_2", "vim.version.version6")
AddVersionParent("eam.version.version7_2", "vim.version.version7")
AddVersionParent("eam.version.version7_2", "vim.version.version1")
AddVersionParent("eam.version.version7_2", "vim.version.version4")
AddVersionParent("eam.version.version7_2", "vim.version.version5")
AddVersionParent("eam.version.version7_2", "vim.version.version2")
AddVersionParent("eam.version.version7_2", "vim.version.version3")
AddVersionParent("eam.version.version7_2", "vmodl.version.version0")
AddVersionParent("eam.version.version7_2", "vmodl.version.version1")
AddVersionParent("eam.version.version7_2", "vmodl.version.version2")
AddVersionParent("eam.version.version7_2", "eam.version.version2_5")
AddVersionParent("eam.version.version7_2", "eam.version.version6_5")
AddVersionParent("eam.version.version7_2", "eam.version.version6_8")
AddVersionParent("eam.version.version7_2", "eam.version.version6_9")
AddVersionParent("eam.version.version7_2", "eam.version.version6_7")
AddVersionParent("eam.version.version7_2", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version7_2", "vmodl.reflect.version.version2")
AddVersionParent("eam.version.version7_2", "vim.version.version13")
AddVersionParent("eam.version.version7_2", "eam.version.version1")
AddVersionParent("eam.version.version7_2", "eam.version.version2")
AddVersionParent("eam.version.version7_2", "eam.version.version3")
AddVersionParent("eam.version.version7_2", "eam.version.version7_1")
AddVersionParent("eam.version.version7_2", "eam.version.version7_2")
AddVersionParent("eam.version.version7_2", "eam.version.version6")
AddVersionParent("eam.version.version7_2", "eam.version.version7")
AddVersionParent("eam.version.version7_2", "vim.version.version10")
AddVersionParent("eam.version.version7_2", "vim.version.version11")
AddVersionParent("eam.version.version7_2", "vim.version.version12")
AddVersionParent("eam.version.version7_5", "vmodl.query.version.version4")
AddVersionParent("eam.version.version7_5", "vmodl.query.version.version3")
AddVersionParent("eam.version.version7_5", "vmodl.query.version.version2")
AddVersionParent("eam.version.version7_5", "vmodl.query.version.version1")
AddVersionParent("eam.version.version7_5", "vim.version.version8")
AddVersionParent("eam.version.version7_5", "vim.version.version9")
AddVersionParent("eam.version.version7_5", "vim.version.version6")
AddVersionParent("eam.version.version7_5", "vim.version.version7")
AddVersionParent("eam.version.version7_5", "vim.version.version1")
AddVersionParent("eam.version.version7_5", "vim.version.version4")
AddVersionParent("eam.version.version7_5", "vim.version.version5")
AddVersionParent("eam.version.version7_5", "vim.version.version2")
AddVersionParent("eam.version.version7_5", "vim.version.version3")
AddVersionParent("eam.version.version7_5", "vmodl.version.version0")
AddVersionParent("eam.version.version7_5", "vmodl.version.version1")
AddVersionParent("eam.version.version7_5", "vmodl.version.version2")
AddVersionParent("eam.version.version7_5", "eam.version.version2_5")
AddVersionParent("eam.version.version7_5", "eam.version.version6_5")
AddVersionParent("eam.version.version7_5", "eam.version.version6_8")
AddVersionParent("eam.version.version7_5", "eam.version.version6_9")
AddVersionParent("eam.version.version7_5", "eam.version.version6_7")
AddVersionParent("eam.version.version7_5", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version7_5", "vmodl.reflect.version.version2")
AddVersionParent("eam.version.version7_5", "vim.version.version13")
AddVersionParent("eam.version.version7_5", "eam.version.version7_3")
AddVersionParent("eam.version.version7_5", "eam.version.version1")
AddVersionParent("eam.version.version7_5", "eam.version.version2")
AddVersionParent("eam.version.version7_5", "eam.version.version7_4")
AddVersionParent("eam.version.version7_5", "eam.version.version3")
AddVersionParent("eam.version.version7_5", "eam.version.version7_1")
AddVersionParent("eam.version.version7_5", "eam.version.version7_2")
AddVersionParent("eam.version.version7_5", "eam.version.version7_5")
AddVersionParent("eam.version.version7_5", "eam.version.version6")
AddVersionParent("eam.version.version7_5", "eam.version.version7")
AddVersionParent("eam.version.version7_5", "vim.version.version10")
AddVersionParent("eam.version.version7_5", "vim.version.version11")
AddVersionParent("eam.version.version7_5", "vim.version.version12")
AddVersionParent("eam.version.version7_6", "vmodl.query.version.version4")
AddVersionParent("eam.version.version7_6", "vmodl.query.version.version3")
AddVersionParent("eam.version.version7_6", "vmodl.query.version.version2")
AddVersionParent("eam.version.version7_6", "vmodl.query.version.version1")
AddVersionParent("eam.version.version7_6", "vim.version.version8")
AddVersionParent("eam.version.version7_6", "vim.version.version9")
AddVersionParent("eam.version.version7_6", "vim.version.version6")
AddVersionParent("eam.version.version7_6", "vim.version.version7")
AddVersionParent("eam.version.version7_6", "vim.version.version1")
AddVersionParent("eam.version.version7_6", "vim.version.version4")
AddVersionParent("eam.version.version7_6", "vim.version.version5")
AddVersionParent("eam.version.version7_6", "vim.version.version2")
AddVersionParent("eam.version.version7_6", "vim.version.version3")
AddVersionParent("eam.version.version7_6", "vmodl.version.version0")
AddVersionParent("eam.version.version7_6", "vmodl.version.version1")
AddVersionParent("eam.version.version7_6", "vmodl.version.version2")
AddVersionParent("eam.version.version7_6", "eam.version.version2_5")
AddVersionParent("eam.version.version7_6", "eam.version.version6_5")
AddVersionParent("eam.version.version7_6", "eam.version.version6_8")
AddVersionParent("eam.version.version7_6", "eam.version.version6_9")
AddVersionParent("eam.version.version7_6", "eam.version.version6_7")
AddVersionParent("eam.version.version7_6", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version7_6", "vmodl.reflect.version.version2")
AddVersionParent("eam.version.version7_6", "vim.version.version13")
AddVersionParent("eam.version.version7_6", "eam.version.version7_3")
AddVersionParent("eam.version.version7_6", "eam.version.version1")
AddVersionParent("eam.version.version7_6", "eam.version.version2")
AddVersionParent("eam.version.version7_6", "eam.version.version7_4")
AddVersionParent("eam.version.version7_6", "eam.version.version3")
AddVersionParent("eam.version.version7_6", "eam.version.version7_1")
AddVersionParent("eam.version.version7_6", "eam.version.version7_2")
AddVersionParent("eam.version.version7_6", "eam.version.version7_5")
AddVersionParent("eam.version.version7_6", "eam.version.version7_6")
AddVersionParent("eam.version.version7_6", "eam.version.version6")
AddVersionParent("eam.version.version7_6", "eam.version.version7")
AddVersionParent("eam.version.version7_6", "vim.version.version10")
AddVersionParent("eam.version.version7_6", "vim.version.version11")
AddVersionParent("eam.version.version7_6", "vim.version.version12")
AddVersionParent("eam.version.version6", "vmodl.query.version.version4")
AddVersionParent("eam.version.version6", "vmodl.query.version.version3")
AddVersionParent("eam.version.version6", "vmodl.query.version.version2")
AddVersionParent("eam.version.version6", "vmodl.query.version.version1")
AddVersionParent("eam.version.version6", "vim.version.version8")
AddVersionParent("eam.version.version6", "vim.version.version6")
AddVersionParent("eam.version.version6", "vim.version.version7")
AddVersionParent("eam.version.version6", "vim.version.version1")
AddVersionParent("eam.version.version6", "vim.version.version4")
AddVersionParent("eam.version.version6", "vim.version.version5")
AddVersionParent("eam.version.version6", "vim.version.version2")
AddVersionParent("eam.version.version6", "vim.version.version3")
AddVersionParent("eam.version.version6", "vmodl.version.version0")
AddVersionParent("eam.version.version6", "vmodl.version.version1")
AddVersionParent("eam.version.version6", "vmodl.version.version2")
AddVersionParent("eam.version.version6", "eam.version.version2_5")
AddVersionParent("eam.version.version6", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version6", "eam.version.version1")
AddVersionParent("eam.version.version6", "eam.version.version2")
AddVersionParent("eam.version.version6", "eam.version.version3")
AddVersionParent("eam.version.version6", "eam.version.version6")
AddVersionParent("eam.version.version7", "vmodl.query.version.version4")
AddVersionParent("eam.version.version7", "vmodl.query.version.version3")
AddVersionParent("eam.version.version7", "vmodl.query.version.version2")
AddVersionParent("eam.version.version7", "vmodl.query.version.version1")
AddVersionParent("eam.version.version7", "vim.version.version8")
AddVersionParent("eam.version.version7", "vim.version.version9")
AddVersionParent("eam.version.version7", "vim.version.version6")
AddVersionParent("eam.version.version7", "vim.version.version7")
AddVersionParent("eam.version.version7", "vim.version.version1")
AddVersionParent("eam.version.version7", "vim.version.version4")
AddVersionParent("eam.version.version7", "vim.version.version5")
AddVersionParent("eam.version.version7", "vim.version.version2")
AddVersionParent("eam.version.version7", "vim.version.version3")
AddVersionParent("eam.version.version7", "vmodl.version.version0")
AddVersionParent("eam.version.version7", "vmodl.version.version1")
AddVersionParent("eam.version.version7", "vmodl.version.version2")
AddVersionParent("eam.version.version7", "eam.version.version2_5")
AddVersionParent("eam.version.version7", "eam.version.version6_5")
AddVersionParent("eam.version.version7", "eam.version.version6_8")
AddVersionParent("eam.version.version7", "eam.version.version6_9")
AddVersionParent("eam.version.version7", "eam.version.version6_7")
AddVersionParent("eam.version.version7", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version7", "vmodl.reflect.version.version2")
AddVersionParent("eam.version.version7", "vim.version.version13")
AddVersionParent("eam.version.version7", "eam.version.version1")
AddVersionParent("eam.version.version7", "eam.version.version2")
AddVersionParent("eam.version.version7", "eam.version.version3")
AddVersionParent("eam.version.version7", "eam.version.version6")
AddVersionParent("eam.version.version7", "eam.version.version7")
AddVersionParent("eam.version.version7", "vim.version.version10")
AddVersionParent("eam.version.version7", "vim.version.version11")
AddVersionParent("eam.version.version7", "vim.version.version12")
AddVersionParent("vim.version.version10", "vmodl.query.version.version4")
AddVersionParent("vim.version.version10", "vmodl.query.version.version3")
AddVersionParent("vim.version.version10", "vmodl.query.version.version2")
AddVersionParent("vim.version.version10", "vmodl.query.version.version1")
AddVersionParent("vim.version.version10", "vim.version.version8")
AddVersionParent("vim.version.version10", "vim.version.version9")
AddVersionParent("vim.version.version10", "vim.version.version6")
AddVersionParent("vim.version.version10", "vim.version.version7")
AddVersionParent("vim.version.version10", "vim.version.version1")
AddVersionParent("vim.version.version10", "vim.version.version4")
AddVersionParent("vim.version.version10", "vim.version.version5")
AddVersionParent("vim.version.version10", "vim.version.version2")
AddVersionParent("vim.version.version10", "vim.version.version3")
AddVersionParent("vim.version.version10", "vmodl.version.version0")
AddVersionParent("vim.version.version10", "vmodl.version.version1")
AddVersionParent("vim.version.version10", "vmodl.version.version2")
AddVersionParent("vim.version.version10", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version10", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version10", "vim.version.version10")
AddVersionParent("vim.version.version11", "vmodl.query.version.version4")
AddVersionParent("vim.version.version11", "vmodl.query.version.version3")
AddVersionParent("vim.version.version11", "vmodl.query.version.version2")
AddVersionParent("vim.version.version11", "vmodl.query.version.version1")
AddVersionParent("vim.version.version11", "vim.version.version8")
AddVersionParent("vim.version.version11", "vim.version.version9")
AddVersionParent("vim.version.version11", "vim.version.version6")
AddVersionParent("vim.version.version11", "vim.version.version7")
AddVersionParent("vim.version.version11", "vim.version.version1")
AddVersionParent("vim.version.version11", "vim.version.version4")
AddVersionParent("vim.version.version11", "vim.version.version5")
AddVersionParent("vim.version.version11", "vim.version.version2")
AddVersionParent("vim.version.version11", "vim.version.version3")
AddVersionParent("vim.version.version11", "vmodl.version.version0")
AddVersionParent("vim.version.version11", "vmodl.version.version1")
AddVersionParent("vim.version.version11", "vmodl.version.version2")
AddVersionParent("vim.version.version11", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version11", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version11", "vim.version.version10")
AddVersionParent("vim.version.version11", "vim.version.version11")
AddVersionParent("vim.version.version12", "vmodl.query.version.version4")
AddVersionParent("vim.version.version12", "vmodl.query.version.version3")
AddVersionParent("vim.version.version12", "vmodl.query.version.version2")
AddVersionParent("vim.version.version12", "vmodl.query.version.version1")
AddVersionParent("vim.version.version12", "vim.version.version8")
AddVersionParent("vim.version.version12", "vim.version.version9")
AddVersionParent("vim.version.version12", "vim.version.version6")
AddVersionParent("vim.version.version12", "vim.version.version7")
AddVersionParent("vim.version.version12", "vim.version.version1")
AddVersionParent("vim.version.version12", "vim.version.version4")
AddVersionParent("vim.version.version12", "vim.version.version5")
AddVersionParent("vim.version.version12", "vim.version.version2")
AddVersionParent("vim.version.version12", "vim.version.version3")
AddVersionParent("vim.version.version12", "vmodl.version.version0")
AddVersionParent("vim.version.version12", "vmodl.version.version1")
AddVersionParent("vim.version.version12", "vmodl.version.version2")
AddVersionParent("vim.version.version12", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version12", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version12", "vim.version.version10")
AddVersionParent("vim.version.version12", "vim.version.version11")
AddVersionParent("vim.version.version12", "vim.version.version12")
AddVersionParent("eam.version.version8_2", "vmodl.query.version.version4")
AddVersionParent("eam.version.version8_2", "vmodl.query.version.version3")
AddVersionParent("eam.version.version8_2", "vmodl.query.version.version2")
AddVersionParent("eam.version.version8_2", "vmodl.query.version.version1")
AddVersionParent("eam.version.version8_2", "vim.version.version8")
AddVersionParent("eam.version.version8_2", "vim.version.version9")
AddVersionParent("eam.version.version8_2", "vim.version.version6")
AddVersionParent("eam.version.version8_2", "vim.version.version7")
AddVersionParent("eam.version.version8_2", "vim.version.version1")
AddVersionParent("eam.version.version8_2", "vim.version.version4")
AddVersionParent("eam.version.version8_2", "vim.version.version5")
AddVersionParent("eam.version.version8_2", "vim.version.version2")
AddVersionParent("eam.version.version8_2", "vim.version.version3")
AddVersionParent("eam.version.version8_2", "vmodl.version.version0")
AddVersionParent("eam.version.version8_2", "vmodl.version.version1")
AddVersionParent("eam.version.version8_2", "vmodl.version.version2")
AddVersionParent("eam.version.version8_2", "eam.version.version2_5")
AddVersionParent("eam.version.version8_2", "eam.version.version6_5")
AddVersionParent("eam.version.version8_2", "eam.version.version6_8")
AddVersionParent("eam.version.version8_2", "eam.version.version6_9")
AddVersionParent("eam.version.version8_2", "eam.version.version6_7")
AddVersionParent("eam.version.version8_2", "vmodl.reflect.version.version1")
AddVersionParent("eam.version.version8_2", "vmodl.reflect.version.version2")
AddVersionParent("eam.version.version8_2", "vim.version.version13")
AddVersionParent("eam.version.version8_2", "eam.version.version7_3")
AddVersionParent("eam.version.version8_2", "eam.version.version1")
AddVersionParent("eam.version.version8_2", "eam.version.version2")
AddVersionParent("eam.version.version8_2", "eam.version.version7_4")
AddVersionParent("eam.version.version8_2", "eam.version.version3")
AddVersionParent("eam.version.version8_2", "eam.version.version7_1")
AddVersionParent("eam.version.version8_2", "eam.version.version7_2")
AddVersionParent("eam.version.version8_2", "eam.version.version7_5")
AddVersionParent("eam.version.version8_2", "eam.version.version7_6")
AddVersionParent("eam.version.version8_2", "eam.version.version6")
AddVersionParent("eam.version.version8_2", "eam.version.version7")
AddVersionParent("eam.version.version8_2", "vim.version.version10")
AddVersionParent("eam.version.version8_2", "vim.version.version11")
AddVersionParent("eam.version.version8_2", "vim.version.version12")
AddVersionParent("eam.version.version8_2", "eam.version.version8_2")

newestVersions.Add("eam.version.version8_2")
ltsVersions.Add("eam.version.version8_2")
dottedVersions.Add("eam.version.version8_2")
oldestVersions.Add("eam.version.version1")

CreateManagedType("eam.EamObject", "EamObject", "vmodl.ManagedObject", "eam.version.version1", None, [("resolve", "Resolve", "eam.version.version1", (("issueKey", "int[]", "eam.version.version1", 0, None),), (F_OPTIONAL, "int[]", "int[]"), None, None), ("resolveAll", "ResolveAll", "eam.version.version1", (), (0, "void", "void"), None, None), ("queryIssue", "QueryIssue", "eam.version.version1", (("issueKey", "int[]", "eam.version.version1", F_OPTIONAL, None),), (F_OPTIONAL, "eam.issue.Issue[]", "eam.issue.Issue[]"), None, None)])
CreateDataType("eam.EamObject.RuntimeInfo", "EamObjectRuntimeInfo", "vmodl.DynamicData", "eam.version.version1", [("status", "string", "eam.version.version1", 0), ("issue", "eam.issue.Issue[]", "eam.version.version1", F_OPTIONAL), ("goalState", "string", "eam.version.version1", 0), ("entity", "eam.EamObject", "eam.version.version1", 0)])
CreateEnumType("eam.EamObject.RuntimeInfo.Status", "EamObjectRuntimeInfoStatus", "eam.version.version1", ["green", "yellow", "red"])
CreateEnumType("eam.EamObject.RuntimeInfo.GoalState", "EamObjectRuntimeInfoGoalState", "eam.version.version1", ["enabled", "disabled", "uninstalled"])
CreateManagedType("eam.Task", "EamTask", "vmodl.ManagedObject", "eam.version.version1", None, None)
CreateDataType("eam.fault.EamFault", "EamFault", "vmodl.MethodFault", "eam.version.version1", None)
CreateDataType("eam.fault.EamRuntimeFault", "EamRuntimeFault", "vmodl.RuntimeFault", "eam.version.version1", None)
CreateDataType("eam.fault.EamServiceNotInitialized", "EamServiceNotInitialized", "eam.fault.EamRuntimeFault", "eam.version.version6_5", None)
CreateDataType("eam.fault.EamSystemFault", "EamSystemFault", "eam.fault.EamRuntimeFault", "eam.version.version6_5", None)
CreateDataType("eam.fault.InvalidAgencyScope", "InvalidAgencyScope", "eam.fault.EamFault", "eam.version.version1", [("unknownComputeResource", "vim.ComputeResource[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.fault.InvalidLogin", "EamInvalidLogin", "eam.fault.EamRuntimeFault", "eam.version.version1", None)
CreateDataType("eam.fault.InvalidUrl", "InvalidUrl", "eam.fault.EamFault", "eam.version.version1", [("url", "string", "eam.version.version1", 0), ("malformedUrl", "boolean", "eam.version.version1", 0), ("unknownHost", "boolean", "eam.version.version1", 0), ("connectionRefused", "boolean", "eam.version.version1", 0), ("responseCode", "int", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.fault.InvalidVibPackage", "EamInvalidVibPackage", "eam.fault.EamRuntimeFault", "eam.version.version1", None)
CreateDataType("eam.fault.NoConnectionToVCenter", "NoConnectionToVCenter", "eam.fault.EamRuntimeFault", "eam.version.version1", None)
CreateDataType("eam.fault.NotAuthorized", "NotAuthorized", "eam.fault.EamRuntimeFault", "eam.version.version1", None)
CreateDataType("eam.issue.Issue", "Issue", "vmodl.DynamicData", "eam.version.version1", [("key", "int", "eam.version.version1", 0), ("description", "string", "eam.version.version1", 0), ("time", "vmodl.DateTime", "eam.version.version1", 0)])
CreateEnumType("eam.lccm.Hooks.HookType", "HooksHookType", "eam.version.version1", ["POST_PROVISIONING", "POST_POWER_ON"])
CreateDataType("eam.lccm.Hooks.MarkAsProcessedSpec", "HooksMarkAsProcessedSpec", "vmodl.DynamicData", "eam.version.version1", [("vm", "vim.VirtualMachine", "eam.version.version1", 0), ("hookType", "string", "eam.version.version1", 0), ("success", "boolean", "eam.version.version1", 0)])
CreateDataType("eam.lccm.Hooks.HookListSpec", "HooksHookListSpec", "vmodl.DynamicData", "eam.version.version1", [("solutions", "string[]", "eam.version.version1", F_OPTIONAL), ("hosts", "vim.HostSystem[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.lccm.Hooks.HookInfo", "HooksHookInfo", "vmodl.DynamicData", "eam.version.version1", [("vm", "vim.VirtualMachine", "eam.version.version1", 0), ("solution", "string", "eam.version.version1", 0), ("hookType", "string", "eam.version.version1", 0), ("raisedAt", "vmodl.DateTime", "eam.version.version1", 0)])
CreateDataType("eam.lccm.Solutions.VMSource", "SolutionsVMSource", "vmodl.DynamicData", "eam.version.version1", None)
CreateDataType("eam.lccm.Solutions.UrlVMSource", "SolutionsUrlVMSource", "eam.lccm.Solutions.VMSource", "eam.version.version1", [("ovfUrl", "string", "eam.version.version1", 0), ("certificatePEM", "string", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.lccm.Solutions.OvfProperty", "SolutionsOvfProperty", "vmodl.DynamicData", "eam.version.version1", [("key", "string", "eam.version.version1", 0), ("value", "string", "eam.version.version1", 0)])
CreateDataType("eam.lccm.Solutions.StoragePolicy", "SolutionsStoragePolicy", "vmodl.DynamicData", "eam.version.version1", None)
CreateDataType("eam.lccm.Solutions.ProfileIdStoragePolicy", "SolutionsProfileIdStoragePolicy", "eam.lccm.Solutions.StoragePolicy", "eam.version.version1", [("profileId", "string", "eam.version.version1", 0)])
CreateEnumType("eam.lccm.Solutions.VMDiskProvisioning", "SolutionsVMDiskProvisioning", "eam.version.version1", ["THIN", "THICK"])
CreateEnumType("eam.lccm.Solutions.VMDeploymentOptimization", "SolutionsVMDeploymentOptimization", "eam.version.version1", ["ALL_CLONES", "FULL_CLONES_ONLY", "NO_CLONES"])
CreateDataType("eam.lccm.Solutions.TypeSpecificSolutionConfig", "SolutionsTypeSpecificSolutionConfig", "vmodl.DynamicData", "eam.version.version1", None)
CreateDataType("eam.lccm.Solutions.HostBoundSolutionConfig", "SolutionsHostBoundSolutionConfig", "eam.lccm.Solutions.TypeSpecificSolutionConfig", "eam.version.version1", [("preferHostConfiguration", "boolean", "eam.version.version1", F_OPTIONAL), ("networks", "vim.Network[]", "eam.version.version1", F_OPTIONAL), ("datastores", "vim.Datastore[]", "eam.version.version1", F_OPTIONAL), ("vmci", "string[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.lccm.Solutions.HookAcknowledgeConfig", "SolutionsHookAcknowledgeConfig", "vmodl.DynamicData", "eam.version.version1", None)
CreateDataType("eam.lccm.Solutions.InteractiveHookAcknowledgeConfig", "SolutionsInteractiveHookAcknowledgeConfig", "eam.lccm.Solutions.HookAcknowledgeConfig", "eam.version.version1", None)
CreateDataType("eam.lccm.Solutions.HookConfig", "SolutionsHookConfig", "vmodl.DynamicData", "eam.version.version1", [("type", "string", "eam.version.version1", 0), ("acknowledgement", "eam.lccm.Solutions.HookAcknowledgeConfig", "eam.version.version1", 0)])
CreateDataType("eam.lccm.Solutions.SolutionConfig", "SolutionsSolutionConfig", "vmodl.DynamicData", "eam.version.version1", [("solution", "string", "eam.version.version1", 0), ("name", "string", "eam.version.version1", 0), ("version", "string", "eam.version.version1", 0), ("vmSource", "eam.lccm.Solutions.VMSource", "eam.version.version1", 0), ("uuidVmName", "boolean", "eam.version.version1", 0), ("resourcePool", "vim.ResourcePool", "eam.version.version1", F_OPTIONAL), ("folder", "vim.Folder", "eam.version.version1", F_OPTIONAL), ("ovfProperties", "eam.lccm.Solutions.OvfProperty[]", "eam.version.version1", F_OPTIONAL), ("storagePolicies", "eam.lccm.Solutions.StoragePolicy[]", "eam.version.version1", F_OPTIONAL), ("vmDiskProvisioning", "string", "eam.version.version1", F_OPTIONAL), ("vmDeploymentOptimization", "string", "eam.version.version1", F_OPTIONAL), ("typeSpecificConfig", "eam.lccm.Solutions.TypeSpecificSolutionConfig", "eam.version.version1", 0), ("hooks", "eam.lccm.Solutions.HookConfig[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.lccm.Solutions.TransitionSpec", "SolutionsTransitionSpec", "vmodl.DynamicData", "eam.version.version1", [("solution", "string", "eam.version.version1", 0), ("agencyId", "string", "eam.version.version1", 0)])
CreateDataType("eam.lccm.Solutions.ApplySpec", "SolutionsApplySpec", "vmodl.DynamicData", "eam.version.version1", [("desiredState", "eam.lccm.Solutions.SolutionConfig[]", "eam.version.version1", F_OPTIONAL), ("solutions", "string[]", "eam.version.version1", F_OPTIONAL), ("hosts", "vim.HostSystem[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.lccm.Solutions.ValidateSpec", "SolutionsValidateSpec", "vmodl.DynamicData", "eam.version.version1", [("desiredState", "eam.lccm.Solutions.SolutionConfig[]", "eam.version.version1", 0)])
CreateEnumType("eam.lccm.Solutions.InvalidReason", "SolutionsInvalidReason", "eam.version.version1", ["INVALID_OVF_DESCRIPTOR", "INACCESSBLE_VM_SOURCE", "INVALID_NETWORKS", "INVALID_DATASTORES", "INVALID_RESOURCE_POOL", "INVALID_FOLDER", "INVALID_PROPERTIES", "INVALID_TRANSITION"])
CreateDataType("eam.lccm.Solutions.ComplianceSpec", "SolutionsComplianceSpec", "vmodl.DynamicData", "eam.version.version1", [("desiredState", "eam.lccm.Solutions.SolutionConfig[]", "eam.version.version1", F_OPTIONAL), ("solutions", "string[]", "eam.version.version1", F_OPTIONAL), ("hosts", "vim.HostSystem[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.lccm.Solutions.SolutionValidationResult", "SolutionsSolutionValidationResult", "vmodl.DynamicData", "eam.version.version1", [("solution", "string", "eam.version.version1", 0), ("valid", "boolean", "eam.version.version1", 0), ("invalidReason", "string", "eam.version.version1", F_OPTIONAL)])
CreateEnumType("eam.lccm.Solutions.NonComplianceReason", "SolutionsNonComplianceReason", "eam.version.version1", ["WORKING", "ISSUE", "IN_HOOK", "OBSOLETE_SPEC", "NO_SPEC"])
CreateDataType("eam.lccm.Solutions.SolutionComplianceResult", "SolutionsSolutionComplianceResult", "vmodl.DynamicData", "eam.version.version1", [("solution", "string", "eam.version.version1", 0), ("compliant", "boolean", "eam.version.version1", 0), ("nonComplianceReason", "string", "eam.version.version1", F_OPTIONAL), ("vm", "vim.VirtualMachine", "eam.version.version1", F_OPTIONAL), ("upgradingVm", "vim.VirtualMachine", "eam.version.version1", F_OPTIONAL), ("hook", "eam.lccm.Hooks.HookInfo", "eam.version.version1", F_OPTIONAL), ("issues", "eam.issue.Issue[]", "eam.version.version1", F_OPTIONAL), ("solutionConfig", "eam.lccm.Solutions.SolutionConfig", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.lccm.Solutions.HostComplianceResult", "SolutionsHostComplianceResult", "vmodl.DynamicData", "eam.version.version1", [("host", "vim.HostSystem", "eam.version.version1", 0), ("compliant", "boolean", "eam.version.version1", 0), ("solutions", "eam.lccm.Solutions.SolutionComplianceResult[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.lccm.Solutions.ValidationResult", "SolutionsValidationResult", "vmodl.DynamicData", "eam.version.version1", [("valid", "boolean", "eam.version.version1", 0), ("solutionResult", "eam.lccm.Solutions.SolutionValidationResult[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.lccm.Solutions.ComplianceResult", "SolutionsComplianceResult", "vmodl.DynamicData", "eam.version.version1", [("compliant", "boolean", "eam.version.version1", 0), ("hosts", "eam.lccm.Solutions.HostComplianceResult[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.vib.VibInfo", "VibVibInfo", "vmodl.DynamicData", "eam.version.version6", [("id", "string", "eam.version.version6", 0), ("name", "string", "eam.version.version6", 0), ("version", "string", "eam.version.version6", 0), ("vendor", "string", "eam.version.version6", 0), ("summary", "string", "eam.version.version6", 0), ("softwareTags", "eam.vib.VibInfo.SoftwareTags", "eam.version.version6_5", F_OPTIONAL), ("releaseDate", "vmodl.DateTime", "eam.version.version6", 0)])
CreateDataType("eam.vib.VibInfo.SoftwareTags", "VibVibInfoSoftwareTags", "vmodl.DynamicData", "eam.version.version6_5", [("tags", "string[]", "eam.version.version6_5", F_OPTIONAL)])
CreateDataType("eam.vib.VibServices.SslTrust", "VibVibServicesSslTrust", "vmodl.DynamicData", "eam.version.version8_2", None)
CreateDataType("eam.vib.VibServices.PinnedPemCertificate", "VibVibServicesPinnedPemCertificate", "eam.vib.VibServices.SslTrust", "eam.version.version8_2", [("sslCertificate", "string", "eam.version.version8_2", 0)])
CreateDataType("eam.vib.VibServices.AnyCertificate", "VibVibServicesAnyCertificate", "eam.vib.VibServices.SslTrust", "eam.version.version8_2", None)
CreateManagedType("eam.Agent", "Agent", "eam.EamObject", "eam.version.version1", [("runtime", "eam.Agent.RuntimeInfo", "eam.version.version1", 0, None), ("config", "eam.Agent.ConfigInfo", "eam.version.version1", 0, None)], [("queryRuntime", "AgentQueryRuntime", "eam.version.version1", (), (0, "eam.Agent.RuntimeInfo", "eam.Agent.RuntimeInfo"), None, None), ("markAsAvailable", "MarkAsAvailable", "eam.version.version1", (), (0, "void", "void"), None, None), ("queryConfig", "AgentQueryConfig", "eam.version.version1", (), (0, "eam.Agent.ConfigInfo", "eam.Agent.ConfigInfo"), None, None)])
CreateDataType("eam.Agent.RuntimeInfo", "AgentRuntimeInfo", "eam.EamObject.RuntimeInfo", "eam.version.version1", [("vmPowerState", "vim.VirtualMachine.PowerState", "eam.version.version1", 0), ("receivingHeartBeat", "boolean", "eam.version.version1", 0), ("host", "vim.HostSystem", "eam.version.version1", F_OPTIONAL), ("vm", "vim.VirtualMachine", "eam.version.version1", F_OPTIONAL), ("vmIp", "string", "eam.version.version1", F_OPTIONAL), ("vmName", "string", "eam.version.version1", 0), ("esxAgentResourcePool", "vim.ResourcePool", "eam.version.version1", F_OPTIONAL), ("esxAgentFolder", "vim.Folder", "eam.version.version1", F_OPTIONAL), ("installedBulletin", "string[]", "eam.version.version1", F_OPTIONAL), ("installedVibs", "eam.vib.VibInfo[]", "eam.version.version6", F_OPTIONAL), ("agency", "eam.Agency", "eam.version.version2", F_OPTIONAL), ("vmHook", "eam.Agent.VmHook", "eam.version.version6_7", F_OPTIONAL)])
CreateDataType("eam.Agent.VmHook", "AgentVmHook", "vmodl.DynamicData", "eam.version.version6_7", [("vm", "vim.VirtualMachine", "eam.version.version6_7", 0), ("vmState", "string", "eam.version.version6_7", 0)])
CreateEnumType("eam.Agent.VmHook.VmState", "AgentVmHookVmState", "eam.version.version1", ["provisioned", "poweredOn", "prePowerOn"])
CreateDataType("eam.Agent.StoragePolicy", "AgentStoragePolicy", "vmodl.DynamicData", "eam.version.version7", None)
CreateDataType("eam.Agent.VsanStoragePolicy", "AgentVsanStoragePolicy", "eam.Agent.StoragePolicy", "eam.version.version7", [("profileId", "string", "eam.version.version7", 0)])
CreateDataType("eam.Agent.SslTrust", "AgentSslTrust", "vmodl.DynamicData", "eam.version.version8_2", None)
CreateDataType("eam.Agent.PinnedPemCertificate", "AgentPinnedPemCertificate", "eam.Agent.SslTrust", "eam.version.version8_2", [("sslCertificate", "string", "eam.version.version8_2", 0)])
CreateDataType("eam.Agent.AnyCertificate", "AgentAnyCertificate", "eam.Agent.SslTrust", "eam.version.version8_2", None)
CreateDataType("eam.Agent.ConfigInfo", "AgentConfigInfo", "vmodl.DynamicData", "eam.version.version1", [("productLineId", "string", "eam.version.version1", F_OPTIONAL), ("hostVersion", "string", "eam.version.version1", F_OPTIONAL), ("ovfPackageUrl", "string", "eam.version.version1", F_OPTIONAL), ("ovfSslTrust", "eam.Agent.SslTrust", "eam.version.version8_2", F_OPTIONAL), ("ovfEnvironment", "eam.Agent.OvfEnvironmentInfo", "eam.version.version1", F_OPTIONAL), ("vibUrl", "string", "eam.version.version1", F_OPTIONAL), ("vibSslTrust", "eam.Agent.SslTrust", "eam.version.version8_2", F_OPTIONAL), ("vibMatchingRules", "eam.Agent.VibMatchingRule[]", "eam.version.version2_5", F_OPTIONAL), ("vibName", "string", "eam.version.version2", F_OPTIONAL), ("dvFilterEnabled", "boolean", "eam.version.version1", F_OPTIONAL), ("rebootHostAfterVibUninstall", "boolean", "eam.version.version1", F_OPTIONAL), ("vmciService", "string[]", "eam.version.version1", F_OPTIONAL), ("ovfDiskProvisioning", "string", "eam.version.version6_9", F_OPTIONAL), ("vmStoragePolicies", "eam.Agent.StoragePolicy[]", "eam.version.version7", F_OPTIONAL)])
CreateEnumType("eam.Agent.ConfigInfo.OvfDiskProvisioning", "AgentConfigInfoOvfDiskProvisioning", "eam.version.version6_9", ["none", "thin", "thick"])
CreateDataType("eam.Agent.OvfEnvironmentInfo", "AgentOvfEnvironmentInfo", "vmodl.DynamicData", "eam.version.version1", [("ovfProperty", "eam.Agent.OvfEnvironmentInfo.OvfProperty[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.Agent.OvfEnvironmentInfo.OvfProperty", "AgentOvfEnvironmentInfoOvfProperty", "vmodl.DynamicData", "eam.version.version1", [("key", "string", "eam.version.version1", 0), ("value", "string", "eam.version.version1", 0)])
CreateDataType("eam.Agent.VibMatchingRule", "AgentVibMatchingRule", "vmodl.DynamicData", "eam.version.version1", [("vibNameRegex", "string", "eam.version.version1", 0), ("vibVersionRegex", "string", "eam.version.version1", 0)])
CreateDataType("eam.fault.EamAppFault", "EamAppFault", "eam.fault.EamRuntimeFault", "eam.version.version6", None)
CreateDataType("eam.fault.EamIOFault", "EamIOFault", "eam.fault.EamRuntimeFault", "eam.version.version6", None)
CreateDataType("eam.fault.InvalidAgentConfiguration", "InvalidAgentConfiguration", "eam.fault.EamFault", "eam.version.version1", [("invalidAgentConfiguration", "eam.Agent.ConfigInfo", "eam.version.version1", F_OPTIONAL), ("invalidField", "string", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.fault.InvalidState", "EamInvalidState", "eam.fault.EamAppFault", "eam.version.version7_1", None)
CreateDataType("eam.issue.AgencyIssue", "AgencyIssue", "eam.issue.Issue", "eam.version.version1", [("agency", "eam.Agency", "eam.version.version1", 0), ("agencyName", "string", "eam.version.version1", 0), ("solutionId", "string", "eam.version.version1", 0), ("solutionName", "string", "eam.version.version1", 0)])
CreateDataType("eam.issue.AgentIssue", "AgentIssue", "eam.issue.AgencyIssue", "eam.version.version1", [("agent", "eam.Agent", "eam.version.version1", 0), ("agentName", "string", "eam.version.version1", 0), ("host", "vim.HostSystem", "eam.version.version1", 0), ("hostName", "string", "eam.version.version1", 0)])
CreateDataType("eam.issue.CertificateNotTrusted", "CertificateNotTrusted", "eam.issue.AgentIssue", "eam.version.version8_2", [("url", "string", "eam.version.version8_2", 0)])
CreateDataType("eam.issue.ExtensibleIssue", "ExtensibleIssue", "eam.issue.Issue", "eam.version.version2", [("typeId", "string", "eam.version.version2", 0), ("argument", "vmodl.KeyAnyValue[]", "eam.version.version2", F_OPTIONAL), ("target", "vim.ManagedEntity", "eam.version.version2", F_OPTIONAL), ("agent", "eam.Agent", "eam.version.version2", F_OPTIONAL), ("agency", "eam.Agency", "eam.version.version2", F_OPTIONAL)])
CreateDataType("eam.issue.HostIssue", "HostIssue", "eam.issue.Issue", "eam.version.version1", [("host", "vim.HostSystem", "eam.version.version1", 0)])
CreateDataType("eam.issue.HostNotReachable", "ManagedHostNotReachable", "eam.issue.AgentIssue", "eam.version.version6_8", None)
CreateDataType("eam.issue.MissingDvFilterSwitch", "MissingDvFilterSwitch", "eam.issue.AgentIssue", "eam.version.version1", None)
CreateDataType("eam.issue.OrphanedAgency", "OrphanedAgency", "eam.issue.AgencyIssue", "eam.version.version1", None)
CreateDataType("eam.issue.OrphanedDvFilterSwitch", "OrphanedDvFilterSwitch", "eam.issue.HostIssue", "eam.version.version1", None)
CreateDataType("eam.issue.OvfInvalidProperty", "OvfInvalidProperty", "eam.issue.AgentIssue", "eam.version.version1", [("error", "vmodl.MethodFault[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.issue.UnknownAgentVm", "UnknownAgentVm", "eam.issue.HostIssue", "eam.version.version1", [("vm", "vim.VirtualMachine", "eam.version.version1", 0)])
CreateDataType("eam.issue.VibIssue", "VibIssue", "eam.issue.AgentIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VibNotInstalled", "VibNotInstalled", "eam.issue.VibIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VibRequirementsNotMetByHost", "VibRequirementsNotMetByHost", "eam.issue.VibNotInstalled", "eam.version.version6_8", None)
CreateDataType("eam.issue.VibRequiresHostInMaintenanceMode", "VibRequiresHostInMaintenanceMode", "eam.issue.VibIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VibRequiresHostReboot", "VibRequiresHostReboot", "eam.issue.VibIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VibRequiresManualInstallation", "VibRequiresManualInstallation", "eam.issue.VibIssue", "eam.version.version1", [("bulletin", "string[]", "eam.version.version1", 0)])
CreateDataType("eam.issue.VibRequiresManualUninstallation", "VibRequiresManualUninstallation", "eam.issue.VibIssue", "eam.version.version1", [("bulletin", "string[]", "eam.version.version1", 0)])
CreateDataType("eam.issue.VmIssue", "VmIssue", "eam.issue.AgentIssue", "eam.version.version1", [("vm", "vim.VirtualMachine", "eam.version.version1", 0)])
CreateDataType("eam.issue.VmMarkedAsTemplate", "VmMarkedAsTemplate", "eam.issue.VmIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VmNotDeployed", "VmNotDeployed", "eam.issue.AgentIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VmOrphaned", "VmOrphaned", "eam.issue.VmIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VmPoweredOff", "VmPoweredOff", "eam.issue.VmIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VmPoweredOn", "VmPoweredOn", "eam.issue.VmIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VmRequiresHostOutOfMaintenanceMode", "VmRequiresHostOutOfMaintenanceMode", "eam.issue.VmNotDeployed", "eam.version.version7_2", None)
CreateDataType("eam.issue.VmSuspended", "VmSuspended", "eam.issue.VmIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VmWrongFolder", "VmWrongFolder", "eam.issue.VmIssue", "eam.version.version1", [("currentFolder", "vim.Folder", "eam.version.version1", 0), ("requiredFolder", "vim.Folder", "eam.version.version1", 0)])
CreateDataType("eam.issue.VmWrongResourcePool", "VmWrongResourcePool", "eam.issue.VmIssue", "eam.version.version1", [("currentResourcePool", "vim.ResourcePool", "eam.version.version1", 0), ("requiredResourcePool", "vim.ResourcePool", "eam.version.version1", 0)])
CreateDataType("eam.issue.cluster.agent.AgentIssue", "ClusterAgentAgentIssue", "eam.issue.AgencyIssue", "eam.version.version6_9", [("agent", "eam.Agent", "eam.version.version6_9", 0), ("cluster", "vim.ComputeResource", "eam.version.version6_9", F_OPTIONAL)])
CreateDataType("eam.issue.cluster.agent.OvfInvalidProperty", "ClusterAgentOvfInvalidProperty", "eam.issue.cluster.agent.AgentIssue", "eam.version.version6_9", [("error", "vmodl.MethodFault[]", "eam.version.version6_9", F_OPTIONAL)])
CreateDataType("eam.issue.cluster.agent.VmIssue", "ClusterAgentVmIssue", "eam.issue.cluster.agent.AgentIssue", "eam.version.version6_9", [("vm", "vim.VirtualMachine", "eam.version.version6_9", 0)])
CreateDataType("eam.issue.cluster.agent.VmNotDeployed", "ClusterAgentVmNotDeployed", "eam.issue.cluster.agent.AgentIssue", "eam.version.version6_9", None)
CreateDataType("eam.issue.cluster.agent.VmNotRemoved", "ClusterAgentVmNotRemoved", "eam.issue.cluster.agent.VmIssue", "eam.version.version6_9", None)
CreateDataType("eam.issue.cluster.agent.VmPoweredOff", "ClusterAgentVmPoweredOff", "eam.issue.cluster.agent.VmIssue", "eam.version.version6_9", None)
CreateDataType("eam.issue.cluster.agent.VmPoweredOn", "ClusterAgentVmPoweredOn", "eam.issue.cluster.agent.VmIssue", "eam.version.version6_9", None)
CreateDataType("eam.issue.cluster.agent.VmSuspended", "ClusterAgentVmSuspended", "eam.issue.cluster.agent.VmIssue", "eam.version.version6_9", None)
CreateDataType("eam.issue.integrity.agency.VUMIssue", "IntegrityAgencyVUMIssue", "eam.issue.AgencyIssue", "eam.version.version6_7", None)
CreateDataType("eam.issue.integrity.agency.VUMUnavailable", "IntegrityAgencyVUMUnavailable", "eam.issue.integrity.agency.VUMIssue", "eam.version.version6_7", None)
CreateDataType("eam.issue.personality.agency.PMIssue", "PersonalityAgencyPMIssue", "eam.issue.AgencyIssue", "eam.version.version7_1", None)
CreateDataType("eam.issue.personality.agency.PMUnavailable", "PersonalityAgencyPMUnavailable", "eam.issue.personality.agency.PMIssue", "eam.version.version7_1", None)
CreateDataType("eam.issue.personality.agent.PMIssue", "PersonalityAgentPMIssue", "eam.issue.AgentIssue", "eam.version.version7_1", None)
CreateManagedType("eam.Agency", "Agency", "eam.EamObject", "eam.version.version1", [("solutionId", "string", "eam.version.version1", 0, None), ("owner", "string", "eam.version.version6", F_OPTIONAL, None), ("config", "eam.Agency.ConfigInfo", "eam.version.version1", 0, None), ("runtime", "eam.EamObject.RuntimeInfo", "eam.version.version1", 0, None), ("agent", "eam.Agent[]", "eam.version.version1", F_OPTIONAL, None)], [("querySolutionId", "QuerySolutionId", "eam.version.version1", (), (0, "string", "string"), None, None), ("queryConfig", "QueryConfig", "eam.version.version1", (), (0, "eam.Agency.ConfigInfo", "eam.Agency.ConfigInfo"), None, None), ("update", "Update", "eam.version.version1", (("config", "eam.Agency.ConfigInfo", "eam.version.version1", 0, None),), (0, "void", "void"), None, ["eam.fault.InvalidAgentConfiguration", "eam.fault.InvalidAgencyScope", "eam.fault.InvalidUrl", ]), ("queryRuntime", "AgencyQueryRuntime", "eam.version.version1", (), (0, "eam.EamObject.RuntimeInfo", "eam.EamObject.RuntimeInfo"), None, None), ("queryAgent", "QueryAgent", "eam.version.version1", (), (F_OPTIONAL, "eam.Agent[]", "eam.Agent[]"), None, None), ("registerAgentVm", "RegisterAgentVm", "eam.version.version2", (("agentVm", "vim.VirtualMachine", "eam.version.version2", 0, None),), (0, "eam.Agent", "eam.Agent"), None, ["vmodl.fault.ManagedObjectNotFound", ]), ("unregisterAgentVm", "UnregisterAgentVm", "eam.version.version2", (("agentVm", "vim.VirtualMachine", "eam.version.version2", 0, None),), (0, "void", "void"), None, None), ("enable", "Agency_Enable", "eam.version.version1", (), (0, "void", "void"), None, None), ("disable", "Agency_Disable", "eam.version.version1", (), (0, "void", "void"), None, None), ("uninstall", "Uninstall", "eam.version.version1", (), (0, "void", "void"), None, None), ("destroyAgency", "DestroyAgency", "eam.version.version1", (), (0, "void", "void"), None, None), ("addIssue", "AddIssue", "eam.version.version2", (("issue", "eam.issue.Issue", "eam.version.version2", 0, None),), (0, "eam.issue.Issue", "eam.issue.Issue"), None, ["vmodl.fault.InvalidArgument", ])])
CreateDataType("eam.Agency.VMResourcePool", "AgencyVMResourcePool", "vmodl.DynamicData", "eam.version.version6_9", [("resourcePoolId", "vim.ResourcePool", "eam.version.version6_9", 0), ("computeResourceId", "vim.ComputeResource", "eam.version.version6_9", 0)])
CreateDataType("eam.Agency.VMFolder", "AgencyVMFolder", "vmodl.DynamicData", "eam.version.version6_9", [("folderId", "vim.Folder", "eam.version.version6_9", 0), ("datacenterId", "vim.Datacenter", "eam.version.version6_9", 0)])
CreateEnumType("eam.Agency.VMPlacementPolicy.VMDataAffinity", "AgencyVMPlacementPolicyVMDataAffinity", "eam.version.version1", ["none", "soft"])
CreateEnumType("eam.Agency.VMPlacementPolicy.VMAntiAffinity", "AgencyVMPlacementPolicyVMAntiAffinity", "eam.version.version1", ["none", "soft"])
CreateDataType("eam.Agency.ConfigInfo", "AgencyConfigInfo", "vmodl.DynamicData", "eam.version.version1", [("agentConfig", "eam.Agent.ConfigInfo[]", "eam.version.version1", F_OPTIONAL), ("scope", "eam.Agency.Scope", "eam.version.version1", F_OPTIONAL), ("manuallyMarkAgentVmAvailableAfterProvisioning", "boolean", "eam.version.version1", F_OPTIONAL), ("manuallyMarkAgentVmAvailableAfterPowerOn", "boolean", "eam.version.version1", F_OPTIONAL), ("optimizedDeploymentEnabled", "boolean", "eam.version.version1", F_OPTIONAL), ("agentName", "string", "eam.version.version1", F_OPTIONAL), ("agencyName", "string", "eam.version.version1", F_OPTIONAL), ("useUuidVmName", "boolean", "eam.version.version7_5", F_OPTIONAL), ("manuallyProvisioned", "boolean", "eam.version.version2", F_OPTIONAL), ("manuallyMonitored", "boolean", "eam.version.version2", F_OPTIONAL), ("bypassVumEnabled", "boolean", "eam.version.version2", F_OPTIONAL), ("agentVmNetwork", "vim.Network[]", "eam.version.version2", F_OPTIONAL), ("agentVmDatastore", "vim.Datastore[]", "eam.version.version2_5", F_OPTIONAL), ("preferHostConfiguration", "boolean", "eam.version.version2_5", F_OPTIONAL), ("ipPool", "vim.vApp.IpPool", "eam.version.version3", F_OPTIONAL), ("resourcePools", "eam.Agency.VMResourcePool[]", "eam.version.version6_9", F_OPTIONAL), ("folders", "eam.Agency.VMFolder[]", "eam.version.version6_9", F_OPTIONAL)])
CreateDataType("eam.Agency.Scope", "AgencyScope", "vmodl.DynamicData", "eam.version.version1", None)
CreateDataType("eam.Agency.ComputeResourceScope", "AgencyComputeResourceScope", "eam.Agency.Scope", "eam.version.version1", [("computeResource", "vim.ComputeResource[]", "eam.version.version1", F_OPTIONAL)])
CreateManagedType("eam.EsxAgentManager", "EsxAgentManager", "eam.EamObject", "eam.version.version1", [("agency", "eam.Agency[]", "eam.version.version1", F_OPTIONAL, None), ("issue", "eam.issue.Issue[]", "eam.version.version1", F_OPTIONAL, None)], [("queryAgency", "QueryAgency", "eam.version.version1", (), (F_OPTIONAL, "eam.Agency[]", "eam.Agency[]"), None, None), ("createAgency", "CreateAgency", "eam.version.version1", (("agencyConfigInfo", "eam.Agency.ConfigInfo", "eam.version.version1", 0, None),("initialGoalState", "string", "eam.version.version1", 0, None),), (0, "eam.Agency", "eam.Agency"), None, ["eam.fault.InvalidAgentConfiguration", "eam.fault.InvalidAgencyScope", "eam.fault.InvalidUrl", ]), ("scanForUnknownAgentVm", "ScanForUnknownAgentVm", "eam.version.version1", (), (0, "void", "void"), None, None), ("setMaintenanceModePolicy", "SetMaintenanceModePolicy", "eam.version.version7_4", (("policy", "string", "eam.version.version7_4", 0, None),), (0, "void", "void"), None, None), ("getMaintenanceModePolicy", "GetMaintenanceModePolicy", "eam.version.version7_4", (), (0, "string", "string"), None, None)])
CreateEnumType("eam.EsxAgentManager.MaintenanceModePolicy", "EsxAgentManagerMaintenanceModePolicy", "eam.version.version7_4", ["singleHost", "multipleHosts"])
CreateDataType("eam.fault.CertificateNotTrustedFault", "CertificateNotTrustedFault", "eam.fault.EamAppFault", "eam.version.version8_2", [("url", "string", "eam.version.version8_2", F_OPTIONAL)])
CreateDataType("eam.fault.DisabledClusterFault", "DisabledClusterFault", "eam.fault.EamAppFault", "eam.version.version7_6", [("disabledComputeResource", "vim.ComputeResource[]", "eam.version.version7_6", F_OPTIONAL)])
CreateDataType("eam.issue.AgencyDisabled", "AgencyDisabled", "eam.issue.AgencyIssue", "eam.version.version7_6", None)
CreateDataType("eam.issue.CannotAccessAgentOVF", "CannotAccessAgentOVF", "eam.issue.VmNotDeployed", "eam.version.version1", [("downloadUrl", "string", "eam.version.version1", 0)])
CreateDataType("eam.issue.CannotAccessAgentVib", "CannotAccessAgentVib", "eam.issue.VibNotInstalled", "eam.version.version1", [("downloadUrl", "string", "eam.version.version1", 0)])
CreateDataType("eam.issue.ImmediateHostRebootRequired", "ImmediateHostRebootRequired", "eam.issue.VibIssue", "eam.version.version6_8", None)
CreateDataType("eam.issue.IncompatibleHostVersion", "IncompatibleHostVersion", "eam.issue.VmNotDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.InsufficientIpAddresses", "InsufficientIpAddresses", "eam.issue.VmPoweredOff", "eam.version.version1", [("network", "vim.Network", "eam.version.version1", 0)])
CreateDataType("eam.issue.InsufficientResources", "InsufficientResources", "eam.issue.VmNotDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.InsufficientSpace", "InsufficientSpace", "eam.issue.VmNotDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.InvalidConfig", "InvalidConfig", "eam.issue.VmIssue", "eam.version.version6_9", [("error", "anyType", "eam.version.version6_9", 0)])
CreateDataType("eam.issue.MissingAgentIpPool", "MissingAgentIpPool", "eam.issue.VmPoweredOff", "eam.version.version1", [("network", "vim.Network", "eam.version.version1", 0)])
CreateDataType("eam.issue.NoAgentVmDatastore", "NoAgentVmDatastore", "eam.issue.VmNotDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.NoAgentVmNetwork", "NoAgentVmNetwork", "eam.issue.VmNotDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.NoCustomAgentVmDatastore", "NoCustomAgentVmDatastore", "eam.issue.NoAgentVmDatastore", "eam.version.version1", [("customAgentVmDatastore", "vim.Datastore[]", "eam.version.version1", 0), ("customAgentVmDatastoreName", "string[]", "eam.version.version1", 0)])
CreateDataType("eam.issue.NoCustomAgentVmNetwork", "NoCustomAgentVmNetwork", "eam.issue.NoAgentVmNetwork", "eam.version.version1", [("customAgentVmNetwork", "vim.Network[]", "eam.version.version1", 0), ("customAgentVmNetworkName", "string[]", "eam.version.version1", 0)])
CreateDataType("eam.issue.NoDiscoverableAgentVmDatastore", "NoDiscoverableAgentVmDatastore", "eam.issue.VmNotDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.NoDiscoverableAgentVmNetwork", "NoDiscoverableAgentVmNetwork", "eam.issue.VmNotDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.OvfInvalidFormat", "OvfInvalidFormat", "eam.issue.VmNotDeployed", "eam.version.version1", [("error", "vmodl.MethodFault[]", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.issue.VibCannotPutHostInMaintenanceMode", "VibCannotPutHostInMaintenanceMode", "eam.issue.VibIssue", "eam.version.version1", None)
CreateDataType("eam.issue.VibCannotPutHostOutOfMaintenanceMode", "VibCannotPutHostOutOfMaintenanceMode", "eam.issue.VibIssue", "eam.version.version6_5", None)
CreateDataType("eam.issue.VibDependenciesNotMetByHost", "VibDependenciesNotMetByHost", "eam.issue.VibNotInstalled", "eam.version.version6_8", None)
CreateDataType("eam.issue.VibInvalidFormat", "VibInvalidFormat", "eam.issue.VibNotInstalled", "eam.version.version1", None)
CreateDataType("eam.issue.VmCorrupted", "VmCorrupted", "eam.issue.VmIssue", "eam.version.version1", [("missingFile", "string", "eam.version.version1", F_OPTIONAL)])
CreateDataType("eam.issue.VmDeployed", "VmDeployed", "eam.issue.VmIssue", "eam.version.version1", None)
CreateDataType("eam.issue.cluster.agent.CertificateNotTrusted", "ClusterAgentCertificateNotTrusted", "eam.issue.cluster.agent.VmNotDeployed", "eam.version.version8_2", [("url", "string", "eam.version.version8_2", 0)])
CreateDataType("eam.issue.cluster.agent.InsufficientClusterResources", "ClusterAgentInsufficientClusterResources", "eam.issue.cluster.agent.VmPoweredOff", "eam.version.version6_9", None)
CreateDataType("eam.issue.cluster.agent.InsufficientClusterSpace", "ClusterAgentInsufficientClusterSpace", "eam.issue.cluster.agent.VmNotDeployed", "eam.version.version6_9", None)
CreateDataType("eam.issue.cluster.agent.InvalidConfig", "ClusterAgentInvalidConfig", "eam.issue.cluster.agent.VmIssue", "eam.version.version6_9", [("error", "anyType", "eam.version.version6_9", 0)])
CreateDataType("eam.issue.cluster.agent.MissingClusterVmDatastore", "ClusterAgentMissingClusterVmDatastore", "eam.issue.cluster.agent.VmNotDeployed", "eam.version.version6_9", [("missingDatastores", "vim.Datastore[]", "eam.version.version6_9", F_OPTIONAL)])
CreateDataType("eam.issue.cluster.agent.MissingClusterVmNetwork", "ClusterAgentMissingClusterVmNetwork", "eam.issue.cluster.agent.VmNotDeployed", "eam.version.version6_9", [("missingNetworks", "vim.Network[]", "eam.version.version6_9", F_OPTIONAL), ("networkNames", "string[]", "eam.version.version6_9", F_OPTIONAL)])
CreateDataType("eam.issue.integrity.agency.CannotDeleteSoftware", "IntegrityAgencyCannotDeleteSoftware", "eam.issue.integrity.agency.VUMIssue", "eam.version.version6_7", None)
CreateDataType("eam.issue.integrity.agency.CannotStageSoftware", "IntegrityAgencyCannotStageSoftware", "eam.issue.integrity.agency.VUMIssue", "eam.version.version6_7", None)
CreateDataType("eam.issue.personality.agency.CannotConfigureSolutions", "PersonalityAgencyCannotConfigureSolutions", "eam.issue.personality.agency.PMIssue", "eam.version.version7_1", [("cr", "vim.ComputeResource", "eam.version.version7_1", 0), ("solutionsToModify", "string[]", "eam.version.version7_1", F_OPTIONAL), ("solutionsToRemove", "string[]", "eam.version.version7_1", F_OPTIONAL)])
CreateDataType("eam.issue.personality.agency.DepotIssue", "PersonalityAgencyDepotIssue", "eam.issue.personality.agency.PMIssue", "eam.version.version7_1", [("remoteDepotUrl", "string", "eam.version.version7_1", 0)])
CreateDataType("eam.issue.personality.agency.InaccessibleDepot", "PersonalityAgencyInaccessibleDepot", "eam.issue.personality.agency.DepotIssue", "eam.version.version7_1", None)
CreateDataType("eam.issue.personality.agency.InvalidDepot", "PersonalityAgencyInvalidDepot", "eam.issue.personality.agency.DepotIssue", "eam.version.version7_1", None)
CreateDataType("eam.issue.personality.agent.AwaitingPMRemediation", "PersonalityAgentAwaitingPMRemediation", "eam.issue.personality.agent.PMIssue", "eam.version.version7_1", None)
CreateDataType("eam.issue.personality.agent.BlockedByAgencyOperation", "PersonalityAgentBlockedByAgencyOperation", "eam.issue.personality.agent.PMIssue", "eam.version.version7_1", None)
CreateDataType("eam.issue.HostInMaintenanceMode", "HostInMaintenanceMode", "eam.issue.VmDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.HostInStandbyMode", "HostInStandbyMode", "eam.issue.VmDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.HostPoweredOff", "HostPoweredOff", "eam.issue.VmDeployed", "eam.version.version1", None)
CreateDataType("eam.issue.personality.agency.CannotUploadDepot", "PersonalityAgencyCannotUploadDepot", "eam.issue.personality.agency.DepotIssue", "eam.version.version7_1", [("localDepotUrl", "string", "eam.version.version7_1", 0)])
