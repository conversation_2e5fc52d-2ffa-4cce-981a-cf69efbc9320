# ******* WARNING - AUTO GENERATED CODE - DO NOT EDIT *******

from pyVmomi.VmomiSupport import CreateDataType, CreateManagedType, CreateEnumType
from pyVmomi.VmomiSupport import AddVersion, AddVersionParent
from pyVmomi.VmomiSupport import F_OPTIONAL, F_SECRET
from pyVmomi.VmomiSupport import newestVersions, ltsVersions, oldestVersions, dottedVersions

AddVersion("vim.version.v8_0_2_0", "vim25", "8.0.2.0", 0, "vim25")
AddVersion("vmodl.query.version.version4", "", "", 0, "vim25")
AddVersion("vmodl.query.version.version3", "", "", 0, "vim25")
AddVersion("vmodl.query.version.version2", "", "", 0, "vim25")
AddVersion("vmodl.query.version.version1", "", "", 0, "vim25")
AddVersion("sms.version.v8_0_0_0", "sms", "8.0.0.0", 0, "sms")
AddVersion("vim.version.v7_0_1_1", "vim25", "7.0.1.1", 0, "vim25")
AddVersion("vmodl.version.version0", "", "", 0, "vim25")
AddVersion("vmodl.version.version1", "", "", 0, "vim25")
AddVersion("vmodl.version.version2", "", "", 0, "vim25")
AddVersion("vim.version.v6_9_1", "vim25", "6.9.1", 0, "vim25")
AddVersion("vim.version.v8_0_1_0", "vim25", "8.0.1.0", 0, "vim25")
AddVersion("vim.version.v7_0_0_2", "vim25", "7.0.0.2", 0, "vim25")
AddVersion("vim.version.v6_8_7", "vim25", "6.8.7", 0, "vim25")
AddVersion("vmodl.reflect.version.version1", "reflect", "1.0", 0, "reflect")
AddVersion("vmodl.reflect.version.version2", "reflect", "2.0", 0, "reflect")
AddVersion("vim.version.v8_0_0_0", "vim25", "8.0.0.0", 0, "vim25")
AddVersion("sms.version.v8_0_2_0", "sms", "8.0.2.0", 0, "sms")
AddVersion("vim.version.v7_0_3_1", "vim25", "7.0.3.1", 0, "vim25")
AddVersion("vim.version.v7_0_3_2", "vim25", "7.0.3.2", 0, "vim25")
AddVersion("vim.version.v7_0_3_0", "vim25", "7.0.3.0", 0, "vim25")
AddVersion("vim.version.version13", "vim25", "6.7.1", 0, "vim25")
AddVersion("vim.version.version14", "vim25", "6.7.2", 0, "vim25")
AddVersion("vim.version.version15", "vim25", "6.7.3", 0, "vim25")
AddVersion("sms.version.v7_0_0_1", "sms", "7.0.0.1", 0, "sms")
AddVersion("sms.version.v6_8_7", "sms", "6.8.7", 0, "sms")
AddVersion("vim.version.version10", "vim25", "6.0", 0, "vim25")
AddVersion("vim.version.version11", "vim25", "6.5", 0, "vim25")
AddVersion("vim.version.version12", "vim25", "6.7", 0, "vim25")
AddVersion("sms.version.v8_0_1_0", "sms", "8.0.1.0", 0, "sms")
AddVersion("vim.version.v7_0_2_0", "vim25", "7.0.2.0", 0, "vim25")
AddVersion("vim.version.v7_0_2_1", "vim25", "7.0.2.1", 0, "vim25")
AddVersion("sms.version.version5", "sms", "5.0", 0, "sms")
AddVersion("sms.version.version13", "sms", "6.7.1", 0, "sms")
AddVersion("vim.version.v7_0_1_0", "vim25", "7.0.1.0", 0, "vim25")
AddVersion("sms.version.version12", "sms", "6.7", 0, "sms")
AddVersion("sms.version.version11", "sms", "6.5", 0, "sms")
AddVersion("sms.version.version2", "sms", "2.0", 0, "sms")
AddVersion("sms.version.version1", "sms", "1.0", 0, "sms")
AddVersion("sms.version.version4", "sms", "4.0", 0, "sms")
AddVersion("sms.version.version3", "sms", "3.0", 0, "sms")
AddVersion("vim.version.v7_0", "vim25", "7.0.0.0", 0, "vim25")
AddVersion("sms.version.version14", "sms", "6.7.2", 0, "sms")
AddVersion("vim.version.version8", "vim25", "5.1", 0, "vim25")
AddVersion("vim.version.version9", "vim25", "5.5", 0, "vim25")
AddVersion("vim.version.version6", "vim25", "4.1", 0, "vim25")
AddVersion("vim.version.version7", "vim25", "5.0", 0, "vim25")
AddVersion("vim.version.version1", "vim2", "2.0", 0, "vim25")
AddVersion("vim.version.version4", "vim25", "2.5u2server", 0, "vim25")
AddVersion("vim.version.version5", "vim25", "4.0", 0, "vim25")
AddVersion("vim.version.version2", "vim25", "2.5", 0, "vim25")
AddVersion("vim.version.version3", "vim25", "2.5u2", 0, "vim25")
AddVersion("sms.version.v7_0_3_2", "sms", "7.0.3.2", 0, "sms")
AddVersion("vim.version.v8_0_0_1", "vim25", "8.0.0.1", 0, "vim25")
AddVersion("vim.version.v8_0_0_2", "vim25", "8.0.0.2", 0, "vim25")
AddVersion("sms.version.v7_0", "sms", "7.0.0.0", 0, "sms")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v8_0_2_0")
AddVersionParent("vim.version.v8_0_2_0", "vmodl.query.version.version4")
AddVersionParent("vim.version.v8_0_2_0", "vmodl.query.version.version3")
AddVersionParent("vim.version.v8_0_2_0", "vmodl.query.version.version2")
AddVersionParent("vim.version.v8_0_2_0", "vmodl.query.version.version1")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v8_0_2_0", "vmodl.version.version0")
AddVersionParent("vim.version.v8_0_2_0", "vmodl.version.version1")
AddVersionParent("vim.version.v8_0_2_0", "vmodl.version.version2")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v6_9_1")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v8_0_1_0")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v6_8_7")
AddVersionParent("vim.version.v8_0_2_0", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v8_0_2_0", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v8_0_0_0")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v7_0_3_1")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v7_0_3_2")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v7_0_3_0")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version13")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version14")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version15")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version10")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version11")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version12")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v7_0_2_1")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v7_0")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version8")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version9")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version6")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version7")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version1")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version4")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version5")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version2")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.version3")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v8_0_0_1")
AddVersionParent("vim.version.v8_0_2_0", "vim.version.v8_0_0_2")
AddVersionParent("vmodl.query.version.version4", "vmodl.query.version.version4")
AddVersionParent("vmodl.query.version.version4", "vmodl.query.version.version3")
AddVersionParent("vmodl.query.version.version4", "vmodl.query.version.version2")
AddVersionParent("vmodl.query.version.version4", "vmodl.query.version.version1")
AddVersionParent("vmodl.query.version.version4", "vmodl.version.version0")
AddVersionParent("vmodl.query.version.version4", "vmodl.version.version1")
AddVersionParent("vmodl.query.version.version4", "vmodl.version.version2")
AddVersionParent("vmodl.query.version.version3", "vmodl.query.version.version3")
AddVersionParent("vmodl.query.version.version3", "vmodl.query.version.version2")
AddVersionParent("vmodl.query.version.version3", "vmodl.query.version.version1")
AddVersionParent("vmodl.query.version.version3", "vmodl.version.version0")
AddVersionParent("vmodl.query.version.version3", "vmodl.version.version1")
AddVersionParent("vmodl.query.version.version2", "vmodl.query.version.version2")
AddVersionParent("vmodl.query.version.version2", "vmodl.query.version.version1")
AddVersionParent("vmodl.query.version.version2", "vmodl.version.version0")
AddVersionParent("vmodl.query.version.version2", "vmodl.version.version1")
AddVersionParent("vmodl.query.version.version1", "vmodl.query.version.version1")
AddVersionParent("vmodl.query.version.version1", "vmodl.version.version0")
AddVersionParent("sms.version.v8_0_0_0", "vmodl.query.version.version4")
AddVersionParent("sms.version.v8_0_0_0", "vmodl.query.version.version3")
AddVersionParent("sms.version.v8_0_0_0", "vmodl.query.version.version2")
AddVersionParent("sms.version.v8_0_0_0", "vmodl.query.version.version1")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.v8_0_0_0")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v7_0_1_1")
AddVersionParent("sms.version.v8_0_0_0", "vmodl.version.version0")
AddVersionParent("sms.version.v8_0_0_0", "vmodl.version.version1")
AddVersionParent("sms.version.v8_0_0_0", "vmodl.version.version2")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v6_9_1")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v7_0_0_2")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v6_8_7")
AddVersionParent("sms.version.v8_0_0_0", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.v8_0_0_0", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v8_0_0_0")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v7_0_3_1")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v7_0_3_2")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v7_0_3_0")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version13")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version14")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version15")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.v7_0_0_1")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.v6_8_7")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version10")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version11")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version12")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v7_0_2_0")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v7_0_2_1")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.version5")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.version13")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v7_0_1_0")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.version12")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.version11")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.version2")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.version1")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.version4")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.version3")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.v7_0")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.version14")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version8")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version9")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version6")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version7")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version1")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version4")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version5")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version2")
AddVersionParent("sms.version.v8_0_0_0", "vim.version.version3")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.v7_0_3_2")
AddVersionParent("sms.version.v8_0_0_0", "sms.version.v7_0")
AddVersionParent("vim.version.v7_0_1_1", "vmodl.query.version.version4")
AddVersionParent("vim.version.v7_0_1_1", "vmodl.query.version.version3")
AddVersionParent("vim.version.v7_0_1_1", "vmodl.query.version.version2")
AddVersionParent("vim.version.v7_0_1_1", "vmodl.query.version.version1")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v7_0_1_1", "vmodl.version.version0")
AddVersionParent("vim.version.v7_0_1_1", "vmodl.version.version1")
AddVersionParent("vim.version.v7_0_1_1", "vmodl.version.version2")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.v6_9_1")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.v6_8_7")
AddVersionParent("vim.version.v7_0_1_1", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v7_0_1_1", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version13")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version14")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version15")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version10")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version11")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version12")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.v7_0")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version8")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version9")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version6")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version7")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version1")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version4")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version5")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version2")
AddVersionParent("vim.version.v7_0_1_1", "vim.version.version3")
AddVersionParent("vmodl.version.version0", "vmodl.version.version0")
AddVersionParent("vmodl.version.version1", "vmodl.version.version0")
AddVersionParent("vmodl.version.version1", "vmodl.version.version1")
AddVersionParent("vmodl.version.version2", "vmodl.version.version0")
AddVersionParent("vmodl.version.version2", "vmodl.version.version1")
AddVersionParent("vmodl.version.version2", "vmodl.version.version2")
AddVersionParent("vim.version.v6_9_1", "vmodl.query.version.version4")
AddVersionParent("vim.version.v6_9_1", "vmodl.query.version.version3")
AddVersionParent("vim.version.v6_9_1", "vmodl.query.version.version2")
AddVersionParent("vim.version.v6_9_1", "vmodl.query.version.version1")
AddVersionParent("vim.version.v6_9_1", "vmodl.version.version0")
AddVersionParent("vim.version.v6_9_1", "vmodl.version.version1")
AddVersionParent("vim.version.v6_9_1", "vmodl.version.version2")
AddVersionParent("vim.version.v6_9_1", "vim.version.v6_9_1")
AddVersionParent("vim.version.v6_9_1", "vim.version.v6_8_7")
AddVersionParent("vim.version.v6_9_1", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v6_9_1", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v6_9_1", "vim.version.version13")
AddVersionParent("vim.version.v6_9_1", "vim.version.version14")
AddVersionParent("vim.version.v6_9_1", "vim.version.version15")
AddVersionParent("vim.version.v6_9_1", "vim.version.version10")
AddVersionParent("vim.version.v6_9_1", "vim.version.version11")
AddVersionParent("vim.version.v6_9_1", "vim.version.version12")
AddVersionParent("vim.version.v6_9_1", "vim.version.version8")
AddVersionParent("vim.version.v6_9_1", "vim.version.version9")
AddVersionParent("vim.version.v6_9_1", "vim.version.version6")
AddVersionParent("vim.version.v6_9_1", "vim.version.version7")
AddVersionParent("vim.version.v6_9_1", "vim.version.version1")
AddVersionParent("vim.version.v6_9_1", "vim.version.version4")
AddVersionParent("vim.version.v6_9_1", "vim.version.version5")
AddVersionParent("vim.version.v6_9_1", "vim.version.version2")
AddVersionParent("vim.version.v6_9_1", "vim.version.version3")
AddVersionParent("vim.version.v8_0_1_0", "vmodl.query.version.version4")
AddVersionParent("vim.version.v8_0_1_0", "vmodl.query.version.version3")
AddVersionParent("vim.version.v8_0_1_0", "vmodl.query.version.version2")
AddVersionParent("vim.version.v8_0_1_0", "vmodl.query.version.version1")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v8_0_1_0", "vmodl.version.version0")
AddVersionParent("vim.version.v8_0_1_0", "vmodl.version.version1")
AddVersionParent("vim.version.v8_0_1_0", "vmodl.version.version2")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v6_9_1")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v8_0_1_0")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v6_8_7")
AddVersionParent("vim.version.v8_0_1_0", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v8_0_1_0", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v8_0_0_0")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v7_0_3_1")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v7_0_3_2")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v7_0_3_0")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version13")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version14")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version15")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version10")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version11")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version12")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v7_0_2_1")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v7_0")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version8")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version9")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version6")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version7")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version1")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version4")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version5")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version2")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.version3")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v8_0_0_1")
AddVersionParent("vim.version.v8_0_1_0", "vim.version.v8_0_0_2")
AddVersionParent("vim.version.v7_0_0_2", "vmodl.query.version.version4")
AddVersionParent("vim.version.v7_0_0_2", "vmodl.query.version.version3")
AddVersionParent("vim.version.v7_0_0_2", "vmodl.query.version.version2")
AddVersionParent("vim.version.v7_0_0_2", "vmodl.query.version.version1")
AddVersionParent("vim.version.v7_0_0_2", "vmodl.version.version0")
AddVersionParent("vim.version.v7_0_0_2", "vmodl.version.version1")
AddVersionParent("vim.version.v7_0_0_2", "vmodl.version.version2")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.v6_9_1")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.v6_8_7")
AddVersionParent("vim.version.v7_0_0_2", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v7_0_0_2", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version13")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version14")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version15")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version10")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version11")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version12")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.v7_0")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version8")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version9")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version6")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version7")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version1")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version4")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version5")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version2")
AddVersionParent("vim.version.v7_0_0_2", "vim.version.version3")
AddVersionParent("vim.version.v6_8_7", "vmodl.query.version.version4")
AddVersionParent("vim.version.v6_8_7", "vmodl.query.version.version3")
AddVersionParent("vim.version.v6_8_7", "vmodl.query.version.version2")
AddVersionParent("vim.version.v6_8_7", "vmodl.query.version.version1")
AddVersionParent("vim.version.v6_8_7", "vmodl.version.version0")
AddVersionParent("vim.version.v6_8_7", "vmodl.version.version1")
AddVersionParent("vim.version.v6_8_7", "vmodl.version.version2")
AddVersionParent("vim.version.v6_8_7", "vim.version.v6_8_7")
AddVersionParent("vim.version.v6_8_7", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v6_8_7", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v6_8_7", "vim.version.version13")
AddVersionParent("vim.version.v6_8_7", "vim.version.version14")
AddVersionParent("vim.version.v6_8_7", "vim.version.version15")
AddVersionParent("vim.version.v6_8_7", "vim.version.version10")
AddVersionParent("vim.version.v6_8_7", "vim.version.version11")
AddVersionParent("vim.version.v6_8_7", "vim.version.version12")
AddVersionParent("vim.version.v6_8_7", "vim.version.version8")
AddVersionParent("vim.version.v6_8_7", "vim.version.version9")
AddVersionParent("vim.version.v6_8_7", "vim.version.version6")
AddVersionParent("vim.version.v6_8_7", "vim.version.version7")
AddVersionParent("vim.version.v6_8_7", "vim.version.version1")
AddVersionParent("vim.version.v6_8_7", "vim.version.version4")
AddVersionParent("vim.version.v6_8_7", "vim.version.version5")
AddVersionParent("vim.version.v6_8_7", "vim.version.version2")
AddVersionParent("vim.version.v6_8_7", "vim.version.version3")
AddVersionParent("vmodl.reflect.version.version1", "vmodl.version.version0")
AddVersionParent("vmodl.reflect.version.version1", "vmodl.version.version1")
AddVersionParent("vmodl.reflect.version.version1", "vmodl.version.version2")
AddVersionParent("vmodl.reflect.version.version1", "vmodl.reflect.version.version1")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.version.version0")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.version.version1")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.version.version2")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.reflect.version.version1")
AddVersionParent("vmodl.reflect.version.version2", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v8_0_0_0", "vmodl.query.version.version4")
AddVersionParent("vim.version.v8_0_0_0", "vmodl.query.version.version3")
AddVersionParent("vim.version.v8_0_0_0", "vmodl.query.version.version2")
AddVersionParent("vim.version.v8_0_0_0", "vmodl.query.version.version1")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v8_0_0_0", "vmodl.version.version0")
AddVersionParent("vim.version.v8_0_0_0", "vmodl.version.version1")
AddVersionParent("vim.version.v8_0_0_0", "vmodl.version.version2")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v6_9_1")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v6_8_7")
AddVersionParent("vim.version.v8_0_0_0", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v8_0_0_0", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v8_0_0_0")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v7_0_3_1")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v7_0_3_2")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v7_0_3_0")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version13")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version14")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version15")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version10")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version11")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version12")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v7_0_2_1")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.v7_0")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version8")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version9")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version6")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version7")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version1")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version4")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version5")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version2")
AddVersionParent("vim.version.v8_0_0_0", "vim.version.version3")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v8_0_2_0")
AddVersionParent("sms.version.v8_0_2_0", "vmodl.query.version.version4")
AddVersionParent("sms.version.v8_0_2_0", "vmodl.query.version.version3")
AddVersionParent("sms.version.v8_0_2_0", "vmodl.query.version.version2")
AddVersionParent("sms.version.v8_0_2_0", "vmodl.query.version.version1")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.v8_0_0_0")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v7_0_1_1")
AddVersionParent("sms.version.v8_0_2_0", "vmodl.version.version0")
AddVersionParent("sms.version.v8_0_2_0", "vmodl.version.version1")
AddVersionParent("sms.version.v8_0_2_0", "vmodl.version.version2")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v6_9_1")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v8_0_1_0")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v7_0_0_2")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v6_8_7")
AddVersionParent("sms.version.v8_0_2_0", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.v8_0_2_0", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v8_0_0_0")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.v8_0_2_0")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v7_0_3_1")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v7_0_3_2")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v7_0_3_0")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version13")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version14")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version15")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.v7_0_0_1")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.v6_8_7")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version10")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version11")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version12")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.v8_0_1_0")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v7_0_2_0")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v7_0_2_1")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.version5")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.version13")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v7_0_1_0")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.version12")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.version11")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.version2")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.version1")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.version4")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.version3")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v7_0")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.version14")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version8")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version9")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version6")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version7")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version1")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version4")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version5")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version2")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.version3")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.v7_0_3_2")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v8_0_0_1")
AddVersionParent("sms.version.v8_0_2_0", "vim.version.v8_0_0_2")
AddVersionParent("sms.version.v8_0_2_0", "sms.version.v7_0")
AddVersionParent("vim.version.v7_0_3_1", "vmodl.query.version.version4")
AddVersionParent("vim.version.v7_0_3_1", "vmodl.query.version.version3")
AddVersionParent("vim.version.v7_0_3_1", "vmodl.query.version.version2")
AddVersionParent("vim.version.v7_0_3_1", "vmodl.query.version.version1")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v7_0_3_1", "vmodl.version.version0")
AddVersionParent("vim.version.v7_0_3_1", "vmodl.version.version1")
AddVersionParent("vim.version.v7_0_3_1", "vmodl.version.version2")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v6_9_1")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v6_8_7")
AddVersionParent("vim.version.v7_0_3_1", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v7_0_3_1", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v7_0_3_1")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v7_0_3_0")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version13")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version14")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version15")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version10")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version11")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version12")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v7_0_2_1")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.v7_0")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version8")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version9")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version6")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version7")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version1")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version4")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version5")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version2")
AddVersionParent("vim.version.v7_0_3_1", "vim.version.version3")
AddVersionParent("vim.version.v7_0_3_2", "vmodl.query.version.version4")
AddVersionParent("vim.version.v7_0_3_2", "vmodl.query.version.version3")
AddVersionParent("vim.version.v7_0_3_2", "vmodl.query.version.version2")
AddVersionParent("vim.version.v7_0_3_2", "vmodl.query.version.version1")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v7_0_3_2", "vmodl.version.version0")
AddVersionParent("vim.version.v7_0_3_2", "vmodl.version.version1")
AddVersionParent("vim.version.v7_0_3_2", "vmodl.version.version2")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v6_9_1")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v6_8_7")
AddVersionParent("vim.version.v7_0_3_2", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v7_0_3_2", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v7_0_3_1")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v7_0_3_2")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v7_0_3_0")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version13")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version14")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version15")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version10")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version11")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version12")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v7_0_2_1")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.v7_0")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version8")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version9")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version6")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version7")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version1")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version4")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version5")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version2")
AddVersionParent("vim.version.v7_0_3_2", "vim.version.version3")
AddVersionParent("vim.version.v7_0_3_0", "vmodl.query.version.version4")
AddVersionParent("vim.version.v7_0_3_0", "vmodl.query.version.version3")
AddVersionParent("vim.version.v7_0_3_0", "vmodl.query.version.version2")
AddVersionParent("vim.version.v7_0_3_0", "vmodl.query.version.version1")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v7_0_3_0", "vmodl.version.version0")
AddVersionParent("vim.version.v7_0_3_0", "vmodl.version.version1")
AddVersionParent("vim.version.v7_0_3_0", "vmodl.version.version2")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.v6_9_1")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.v6_8_7")
AddVersionParent("vim.version.v7_0_3_0", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v7_0_3_0", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.v7_0_3_0")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version13")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version14")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version15")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version10")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version11")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version12")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.v7_0_2_1")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.v7_0")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version8")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version9")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version6")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version7")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version1")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version4")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version5")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version2")
AddVersionParent("vim.version.v7_0_3_0", "vim.version.version3")
AddVersionParent("vim.version.version13", "vmodl.query.version.version4")
AddVersionParent("vim.version.version13", "vmodl.query.version.version3")
AddVersionParent("vim.version.version13", "vmodl.query.version.version2")
AddVersionParent("vim.version.version13", "vmodl.query.version.version1")
AddVersionParent("vim.version.version13", "vmodl.version.version0")
AddVersionParent("vim.version.version13", "vmodl.version.version1")
AddVersionParent("vim.version.version13", "vmodl.version.version2")
AddVersionParent("vim.version.version13", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version13", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version13", "vim.version.version13")
AddVersionParent("vim.version.version13", "vim.version.version10")
AddVersionParent("vim.version.version13", "vim.version.version11")
AddVersionParent("vim.version.version13", "vim.version.version12")
AddVersionParent("vim.version.version13", "vim.version.version8")
AddVersionParent("vim.version.version13", "vim.version.version9")
AddVersionParent("vim.version.version13", "vim.version.version6")
AddVersionParent("vim.version.version13", "vim.version.version7")
AddVersionParent("vim.version.version13", "vim.version.version1")
AddVersionParent("vim.version.version13", "vim.version.version4")
AddVersionParent("vim.version.version13", "vim.version.version5")
AddVersionParent("vim.version.version13", "vim.version.version2")
AddVersionParent("vim.version.version13", "vim.version.version3")
AddVersionParent("vim.version.version14", "vmodl.query.version.version4")
AddVersionParent("vim.version.version14", "vmodl.query.version.version3")
AddVersionParent("vim.version.version14", "vmodl.query.version.version2")
AddVersionParent("vim.version.version14", "vmodl.query.version.version1")
AddVersionParent("vim.version.version14", "vmodl.version.version0")
AddVersionParent("vim.version.version14", "vmodl.version.version1")
AddVersionParent("vim.version.version14", "vmodl.version.version2")
AddVersionParent("vim.version.version14", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version14", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version14", "vim.version.version13")
AddVersionParent("vim.version.version14", "vim.version.version14")
AddVersionParent("vim.version.version14", "vim.version.version10")
AddVersionParent("vim.version.version14", "vim.version.version11")
AddVersionParent("vim.version.version14", "vim.version.version12")
AddVersionParent("vim.version.version14", "vim.version.version8")
AddVersionParent("vim.version.version14", "vim.version.version9")
AddVersionParent("vim.version.version14", "vim.version.version6")
AddVersionParent("vim.version.version14", "vim.version.version7")
AddVersionParent("vim.version.version14", "vim.version.version1")
AddVersionParent("vim.version.version14", "vim.version.version4")
AddVersionParent("vim.version.version14", "vim.version.version5")
AddVersionParent("vim.version.version14", "vim.version.version2")
AddVersionParent("vim.version.version14", "vim.version.version3")
AddVersionParent("vim.version.version15", "vmodl.query.version.version4")
AddVersionParent("vim.version.version15", "vmodl.query.version.version3")
AddVersionParent("vim.version.version15", "vmodl.query.version.version2")
AddVersionParent("vim.version.version15", "vmodl.query.version.version1")
AddVersionParent("vim.version.version15", "vmodl.version.version0")
AddVersionParent("vim.version.version15", "vmodl.version.version1")
AddVersionParent("vim.version.version15", "vmodl.version.version2")
AddVersionParent("vim.version.version15", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version15", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version15", "vim.version.version13")
AddVersionParent("vim.version.version15", "vim.version.version14")
AddVersionParent("vim.version.version15", "vim.version.version15")
AddVersionParent("vim.version.version15", "vim.version.version10")
AddVersionParent("vim.version.version15", "vim.version.version11")
AddVersionParent("vim.version.version15", "vim.version.version12")
AddVersionParent("vim.version.version15", "vim.version.version8")
AddVersionParent("vim.version.version15", "vim.version.version9")
AddVersionParent("vim.version.version15", "vim.version.version6")
AddVersionParent("vim.version.version15", "vim.version.version7")
AddVersionParent("vim.version.version15", "vim.version.version1")
AddVersionParent("vim.version.version15", "vim.version.version4")
AddVersionParent("vim.version.version15", "vim.version.version5")
AddVersionParent("vim.version.version15", "vim.version.version2")
AddVersionParent("vim.version.version15", "vim.version.version3")
AddVersionParent("sms.version.v7_0_0_1", "vmodl.query.version.version4")
AddVersionParent("sms.version.v7_0_0_1", "vmodl.query.version.version3")
AddVersionParent("sms.version.v7_0_0_1", "vmodl.query.version.version2")
AddVersionParent("sms.version.v7_0_0_1", "vmodl.query.version.version1")
AddVersionParent("sms.version.v7_0_0_1", "vmodl.version.version0")
AddVersionParent("sms.version.v7_0_0_1", "vmodl.version.version1")
AddVersionParent("sms.version.v7_0_0_1", "vmodl.version.version2")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.v6_9_1")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.v6_8_7")
AddVersionParent("sms.version.v7_0_0_1", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.v7_0_0_1", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version13")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version14")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version15")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.v7_0_0_1")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.v6_8_7")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version10")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version11")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version12")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.version5")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.version13")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.version12")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.version11")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.version2")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.version1")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.version4")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.version3")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.v7_0")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.version14")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version8")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version9")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version6")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version7")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version1")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version4")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version5")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version2")
AddVersionParent("sms.version.v7_0_0_1", "vim.version.version3")
AddVersionParent("sms.version.v7_0_0_1", "sms.version.v7_0")
AddVersionParent("sms.version.v6_8_7", "vmodl.query.version.version4")
AddVersionParent("sms.version.v6_8_7", "vmodl.query.version.version3")
AddVersionParent("sms.version.v6_8_7", "vmodl.query.version.version2")
AddVersionParent("sms.version.v6_8_7", "vmodl.query.version.version1")
AddVersionParent("sms.version.v6_8_7", "vmodl.version.version0")
AddVersionParent("sms.version.v6_8_7", "vmodl.version.version1")
AddVersionParent("sms.version.v6_8_7", "vmodl.version.version2")
AddVersionParent("sms.version.v6_8_7", "vim.version.v6_8_7")
AddVersionParent("sms.version.v6_8_7", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.v6_8_7", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.v6_8_7", "vim.version.version13")
AddVersionParent("sms.version.v6_8_7", "vim.version.version14")
AddVersionParent("sms.version.v6_8_7", "vim.version.version15")
AddVersionParent("sms.version.v6_8_7", "sms.version.v6_8_7")
AddVersionParent("sms.version.v6_8_7", "vim.version.version10")
AddVersionParent("sms.version.v6_8_7", "vim.version.version11")
AddVersionParent("sms.version.v6_8_7", "vim.version.version12")
AddVersionParent("sms.version.v6_8_7", "sms.version.version5")
AddVersionParent("sms.version.v6_8_7", "sms.version.version13")
AddVersionParent("sms.version.v6_8_7", "sms.version.version12")
AddVersionParent("sms.version.v6_8_7", "sms.version.version11")
AddVersionParent("sms.version.v6_8_7", "sms.version.version2")
AddVersionParent("sms.version.v6_8_7", "sms.version.version1")
AddVersionParent("sms.version.v6_8_7", "sms.version.version4")
AddVersionParent("sms.version.v6_8_7", "sms.version.version3")
AddVersionParent("sms.version.v6_8_7", "sms.version.version14")
AddVersionParent("sms.version.v6_8_7", "vim.version.version8")
AddVersionParent("sms.version.v6_8_7", "vim.version.version9")
AddVersionParent("sms.version.v6_8_7", "vim.version.version6")
AddVersionParent("sms.version.v6_8_7", "vim.version.version7")
AddVersionParent("sms.version.v6_8_7", "vim.version.version1")
AddVersionParent("sms.version.v6_8_7", "vim.version.version4")
AddVersionParent("sms.version.v6_8_7", "vim.version.version5")
AddVersionParent("sms.version.v6_8_7", "vim.version.version2")
AddVersionParent("sms.version.v6_8_7", "vim.version.version3")
AddVersionParent("vim.version.version10", "vmodl.query.version.version4")
AddVersionParent("vim.version.version10", "vmodl.query.version.version3")
AddVersionParent("vim.version.version10", "vmodl.query.version.version2")
AddVersionParent("vim.version.version10", "vmodl.query.version.version1")
AddVersionParent("vim.version.version10", "vmodl.version.version0")
AddVersionParent("vim.version.version10", "vmodl.version.version1")
AddVersionParent("vim.version.version10", "vmodl.version.version2")
AddVersionParent("vim.version.version10", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version10", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version10", "vim.version.version10")
AddVersionParent("vim.version.version10", "vim.version.version8")
AddVersionParent("vim.version.version10", "vim.version.version9")
AddVersionParent("vim.version.version10", "vim.version.version6")
AddVersionParent("vim.version.version10", "vim.version.version7")
AddVersionParent("vim.version.version10", "vim.version.version1")
AddVersionParent("vim.version.version10", "vim.version.version4")
AddVersionParent("vim.version.version10", "vim.version.version5")
AddVersionParent("vim.version.version10", "vim.version.version2")
AddVersionParent("vim.version.version10", "vim.version.version3")
AddVersionParent("vim.version.version11", "vmodl.query.version.version4")
AddVersionParent("vim.version.version11", "vmodl.query.version.version3")
AddVersionParent("vim.version.version11", "vmodl.query.version.version2")
AddVersionParent("vim.version.version11", "vmodl.query.version.version1")
AddVersionParent("vim.version.version11", "vmodl.version.version0")
AddVersionParent("vim.version.version11", "vmodl.version.version1")
AddVersionParent("vim.version.version11", "vmodl.version.version2")
AddVersionParent("vim.version.version11", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version11", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version11", "vim.version.version10")
AddVersionParent("vim.version.version11", "vim.version.version11")
AddVersionParent("vim.version.version11", "vim.version.version8")
AddVersionParent("vim.version.version11", "vim.version.version9")
AddVersionParent("vim.version.version11", "vim.version.version6")
AddVersionParent("vim.version.version11", "vim.version.version7")
AddVersionParent("vim.version.version11", "vim.version.version1")
AddVersionParent("vim.version.version11", "vim.version.version4")
AddVersionParent("vim.version.version11", "vim.version.version5")
AddVersionParent("vim.version.version11", "vim.version.version2")
AddVersionParent("vim.version.version11", "vim.version.version3")
AddVersionParent("vim.version.version12", "vmodl.query.version.version4")
AddVersionParent("vim.version.version12", "vmodl.query.version.version3")
AddVersionParent("vim.version.version12", "vmodl.query.version.version2")
AddVersionParent("vim.version.version12", "vmodl.query.version.version1")
AddVersionParent("vim.version.version12", "vmodl.version.version0")
AddVersionParent("vim.version.version12", "vmodl.version.version1")
AddVersionParent("vim.version.version12", "vmodl.version.version2")
AddVersionParent("vim.version.version12", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version12", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.version12", "vim.version.version10")
AddVersionParent("vim.version.version12", "vim.version.version11")
AddVersionParent("vim.version.version12", "vim.version.version12")
AddVersionParent("vim.version.version12", "vim.version.version8")
AddVersionParent("vim.version.version12", "vim.version.version9")
AddVersionParent("vim.version.version12", "vim.version.version6")
AddVersionParent("vim.version.version12", "vim.version.version7")
AddVersionParent("vim.version.version12", "vim.version.version1")
AddVersionParent("vim.version.version12", "vim.version.version4")
AddVersionParent("vim.version.version12", "vim.version.version5")
AddVersionParent("vim.version.version12", "vim.version.version2")
AddVersionParent("vim.version.version12", "vim.version.version3")
AddVersionParent("sms.version.v8_0_1_0", "vmodl.query.version.version4")
AddVersionParent("sms.version.v8_0_1_0", "vmodl.query.version.version3")
AddVersionParent("sms.version.v8_0_1_0", "vmodl.query.version.version2")
AddVersionParent("sms.version.v8_0_1_0", "vmodl.query.version.version1")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.v8_0_0_0")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v7_0_1_1")
AddVersionParent("sms.version.v8_0_1_0", "vmodl.version.version0")
AddVersionParent("sms.version.v8_0_1_0", "vmodl.version.version1")
AddVersionParent("sms.version.v8_0_1_0", "vmodl.version.version2")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v6_9_1")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v8_0_1_0")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v7_0_0_2")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v6_8_7")
AddVersionParent("sms.version.v8_0_1_0", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.v8_0_1_0", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v8_0_0_0")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v7_0_3_1")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v7_0_3_2")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v7_0_3_0")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version13")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version14")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version15")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.v7_0_0_1")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.v6_8_7")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version10")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version11")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version12")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.v8_0_1_0")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v7_0_2_0")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v7_0_2_1")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.version5")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.version13")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v7_0_1_0")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.version12")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.version11")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.version2")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.version1")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.version4")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.version3")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v7_0")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.version14")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version8")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version9")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version6")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version7")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version1")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version4")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version5")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version2")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.version3")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.v7_0_3_2")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v8_0_0_1")
AddVersionParent("sms.version.v8_0_1_0", "vim.version.v8_0_0_2")
AddVersionParent("sms.version.v8_0_1_0", "sms.version.v7_0")
AddVersionParent("vim.version.v7_0_2_0", "vmodl.query.version.version4")
AddVersionParent("vim.version.v7_0_2_0", "vmodl.query.version.version3")
AddVersionParent("vim.version.v7_0_2_0", "vmodl.query.version.version2")
AddVersionParent("vim.version.v7_0_2_0", "vmodl.query.version.version1")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v7_0_2_0", "vmodl.version.version0")
AddVersionParent("vim.version.v7_0_2_0", "vmodl.version.version1")
AddVersionParent("vim.version.v7_0_2_0", "vmodl.version.version2")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.v6_9_1")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.v6_8_7")
AddVersionParent("vim.version.v7_0_2_0", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v7_0_2_0", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version13")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version14")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version15")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version10")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version11")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version12")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.v7_0")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version8")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version9")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version6")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version7")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version1")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version4")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version5")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version2")
AddVersionParent("vim.version.v7_0_2_0", "vim.version.version3")
AddVersionParent("vim.version.v7_0_2_1", "vmodl.query.version.version4")
AddVersionParent("vim.version.v7_0_2_1", "vmodl.query.version.version3")
AddVersionParent("vim.version.v7_0_2_1", "vmodl.query.version.version2")
AddVersionParent("vim.version.v7_0_2_1", "vmodl.query.version.version1")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v7_0_2_1", "vmodl.version.version0")
AddVersionParent("vim.version.v7_0_2_1", "vmodl.version.version1")
AddVersionParent("vim.version.v7_0_2_1", "vmodl.version.version2")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.v6_9_1")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.v6_8_7")
AddVersionParent("vim.version.v7_0_2_1", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v7_0_2_1", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version13")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version14")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version15")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version10")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version11")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version12")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.v7_0_2_1")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.v7_0")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version8")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version9")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version6")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version7")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version1")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version4")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version5")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version2")
AddVersionParent("vim.version.v7_0_2_1", "vim.version.version3")
AddVersionParent("sms.version.version5", "vmodl.query.version.version3")
AddVersionParent("sms.version.version5", "vmodl.query.version.version2")
AddVersionParent("sms.version.version5", "vmodl.query.version.version1")
AddVersionParent("sms.version.version5", "vmodl.version.version0")
AddVersionParent("sms.version.version5", "vmodl.version.version1")
AddVersionParent("sms.version.version5", "sms.version.version5")
AddVersionParent("sms.version.version5", "sms.version.version2")
AddVersionParent("sms.version.version5", "sms.version.version1")
AddVersionParent("sms.version.version5", "sms.version.version4")
AddVersionParent("sms.version.version5", "sms.version.version3")
AddVersionParent("sms.version.version5", "vim.version.version6")
AddVersionParent("sms.version.version5", "vim.version.version1")
AddVersionParent("sms.version.version5", "vim.version.version4")
AddVersionParent("sms.version.version5", "vim.version.version5")
AddVersionParent("sms.version.version5", "vim.version.version2")
AddVersionParent("sms.version.version5", "vim.version.version3")
AddVersionParent("sms.version.version13", "vmodl.query.version.version4")
AddVersionParent("sms.version.version13", "vmodl.query.version.version3")
AddVersionParent("sms.version.version13", "vmodl.query.version.version2")
AddVersionParent("sms.version.version13", "vmodl.query.version.version1")
AddVersionParent("sms.version.version13", "vmodl.version.version0")
AddVersionParent("sms.version.version13", "vmodl.version.version1")
AddVersionParent("sms.version.version13", "vmodl.version.version2")
AddVersionParent("sms.version.version13", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.version13", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.version13", "vim.version.version13")
AddVersionParent("sms.version.version13", "vim.version.version10")
AddVersionParent("sms.version.version13", "vim.version.version11")
AddVersionParent("sms.version.version13", "vim.version.version12")
AddVersionParent("sms.version.version13", "sms.version.version5")
AddVersionParent("sms.version.version13", "sms.version.version13")
AddVersionParent("sms.version.version13", "sms.version.version12")
AddVersionParent("sms.version.version13", "sms.version.version11")
AddVersionParent("sms.version.version13", "sms.version.version2")
AddVersionParent("sms.version.version13", "sms.version.version1")
AddVersionParent("sms.version.version13", "sms.version.version4")
AddVersionParent("sms.version.version13", "sms.version.version3")
AddVersionParent("sms.version.version13", "vim.version.version8")
AddVersionParent("sms.version.version13", "vim.version.version9")
AddVersionParent("sms.version.version13", "vim.version.version6")
AddVersionParent("sms.version.version13", "vim.version.version7")
AddVersionParent("sms.version.version13", "vim.version.version1")
AddVersionParent("sms.version.version13", "vim.version.version4")
AddVersionParent("sms.version.version13", "vim.version.version5")
AddVersionParent("sms.version.version13", "vim.version.version2")
AddVersionParent("sms.version.version13", "vim.version.version3")
AddVersionParent("vim.version.v7_0_1_0", "vmodl.query.version.version4")
AddVersionParent("vim.version.v7_0_1_0", "vmodl.query.version.version3")
AddVersionParent("vim.version.v7_0_1_0", "vmodl.query.version.version2")
AddVersionParent("vim.version.v7_0_1_0", "vmodl.query.version.version1")
AddVersionParent("vim.version.v7_0_1_0", "vmodl.version.version0")
AddVersionParent("vim.version.v7_0_1_0", "vmodl.version.version1")
AddVersionParent("vim.version.v7_0_1_0", "vmodl.version.version2")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.v6_9_1")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.v6_8_7")
AddVersionParent("vim.version.v7_0_1_0", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v7_0_1_0", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version13")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version14")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version15")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version10")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version11")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version12")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.v7_0")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version8")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version9")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version6")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version7")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version1")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version4")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version5")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version2")
AddVersionParent("vim.version.v7_0_1_0", "vim.version.version3")
AddVersionParent("sms.version.version12", "vmodl.query.version.version4")
AddVersionParent("sms.version.version12", "vmodl.query.version.version3")
AddVersionParent("sms.version.version12", "vmodl.query.version.version2")
AddVersionParent("sms.version.version12", "vmodl.query.version.version1")
AddVersionParent("sms.version.version12", "vmodl.version.version0")
AddVersionParent("sms.version.version12", "vmodl.version.version1")
AddVersionParent("sms.version.version12", "vmodl.version.version2")
AddVersionParent("sms.version.version12", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.version12", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.version12", "vim.version.version10")
AddVersionParent("sms.version.version12", "vim.version.version11")
AddVersionParent("sms.version.version12", "vim.version.version12")
AddVersionParent("sms.version.version12", "sms.version.version5")
AddVersionParent("sms.version.version12", "sms.version.version12")
AddVersionParent("sms.version.version12", "sms.version.version11")
AddVersionParent("sms.version.version12", "sms.version.version2")
AddVersionParent("sms.version.version12", "sms.version.version1")
AddVersionParent("sms.version.version12", "sms.version.version4")
AddVersionParent("sms.version.version12", "sms.version.version3")
AddVersionParent("sms.version.version12", "vim.version.version8")
AddVersionParent("sms.version.version12", "vim.version.version9")
AddVersionParent("sms.version.version12", "vim.version.version6")
AddVersionParent("sms.version.version12", "vim.version.version7")
AddVersionParent("sms.version.version12", "vim.version.version1")
AddVersionParent("sms.version.version12", "vim.version.version4")
AddVersionParent("sms.version.version12", "vim.version.version5")
AddVersionParent("sms.version.version12", "vim.version.version2")
AddVersionParent("sms.version.version12", "vim.version.version3")
AddVersionParent("sms.version.version11", "vmodl.query.version.version4")
AddVersionParent("sms.version.version11", "vmodl.query.version.version3")
AddVersionParent("sms.version.version11", "vmodl.query.version.version2")
AddVersionParent("sms.version.version11", "vmodl.query.version.version1")
AddVersionParent("sms.version.version11", "vmodl.version.version0")
AddVersionParent("sms.version.version11", "vmodl.version.version1")
AddVersionParent("sms.version.version11", "vmodl.version.version2")
AddVersionParent("sms.version.version11", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.version11", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.version11", "vim.version.version10")
AddVersionParent("sms.version.version11", "vim.version.version11")
AddVersionParent("sms.version.version11", "sms.version.version5")
AddVersionParent("sms.version.version11", "sms.version.version11")
AddVersionParent("sms.version.version11", "sms.version.version2")
AddVersionParent("sms.version.version11", "sms.version.version1")
AddVersionParent("sms.version.version11", "sms.version.version4")
AddVersionParent("sms.version.version11", "sms.version.version3")
AddVersionParent("sms.version.version11", "vim.version.version8")
AddVersionParent("sms.version.version11", "vim.version.version9")
AddVersionParent("sms.version.version11", "vim.version.version6")
AddVersionParent("sms.version.version11", "vim.version.version7")
AddVersionParent("sms.version.version11", "vim.version.version1")
AddVersionParent("sms.version.version11", "vim.version.version4")
AddVersionParent("sms.version.version11", "vim.version.version5")
AddVersionParent("sms.version.version11", "vim.version.version2")
AddVersionParent("sms.version.version11", "vim.version.version3")
AddVersionParent("sms.version.version2", "vmodl.query.version.version3")
AddVersionParent("sms.version.version2", "vmodl.query.version.version2")
AddVersionParent("sms.version.version2", "vmodl.query.version.version1")
AddVersionParent("sms.version.version2", "vmodl.version.version0")
AddVersionParent("sms.version.version2", "vmodl.version.version1")
AddVersionParent("sms.version.version2", "sms.version.version2")
AddVersionParent("sms.version.version2", "sms.version.version1")
AddVersionParent("sms.version.version2", "vim.version.version6")
AddVersionParent("sms.version.version2", "vim.version.version1")
AddVersionParent("sms.version.version2", "vim.version.version4")
AddVersionParent("sms.version.version2", "vim.version.version5")
AddVersionParent("sms.version.version2", "vim.version.version2")
AddVersionParent("sms.version.version2", "vim.version.version3")
AddVersionParent("sms.version.version1", "vmodl.version.version0")
AddVersionParent("sms.version.version1", "vmodl.version.version1")
AddVersionParent("sms.version.version1", "sms.version.version1")
AddVersionParent("sms.version.version4", "vmodl.query.version.version3")
AddVersionParent("sms.version.version4", "vmodl.query.version.version2")
AddVersionParent("sms.version.version4", "vmodl.query.version.version1")
AddVersionParent("sms.version.version4", "vmodl.version.version0")
AddVersionParent("sms.version.version4", "vmodl.version.version1")
AddVersionParent("sms.version.version4", "sms.version.version2")
AddVersionParent("sms.version.version4", "sms.version.version1")
AddVersionParent("sms.version.version4", "sms.version.version4")
AddVersionParent("sms.version.version4", "sms.version.version3")
AddVersionParent("sms.version.version4", "vim.version.version6")
AddVersionParent("sms.version.version4", "vim.version.version1")
AddVersionParent("sms.version.version4", "vim.version.version4")
AddVersionParent("sms.version.version4", "vim.version.version5")
AddVersionParent("sms.version.version4", "vim.version.version2")
AddVersionParent("sms.version.version4", "vim.version.version3")
AddVersionParent("sms.version.version3", "vmodl.query.version.version3")
AddVersionParent("sms.version.version3", "vmodl.query.version.version2")
AddVersionParent("sms.version.version3", "vmodl.query.version.version1")
AddVersionParent("sms.version.version3", "vmodl.version.version0")
AddVersionParent("sms.version.version3", "vmodl.version.version1")
AddVersionParent("sms.version.version3", "sms.version.version2")
AddVersionParent("sms.version.version3", "sms.version.version1")
AddVersionParent("sms.version.version3", "sms.version.version3")
AddVersionParent("sms.version.version3", "vim.version.version6")
AddVersionParent("sms.version.version3", "vim.version.version1")
AddVersionParent("sms.version.version3", "vim.version.version4")
AddVersionParent("sms.version.version3", "vim.version.version5")
AddVersionParent("sms.version.version3", "vim.version.version2")
AddVersionParent("sms.version.version3", "vim.version.version3")
AddVersionParent("vim.version.v7_0", "vmodl.query.version.version4")
AddVersionParent("vim.version.v7_0", "vmodl.query.version.version3")
AddVersionParent("vim.version.v7_0", "vmodl.query.version.version2")
AddVersionParent("vim.version.v7_0", "vmodl.query.version.version1")
AddVersionParent("vim.version.v7_0", "vmodl.version.version0")
AddVersionParent("vim.version.v7_0", "vmodl.version.version1")
AddVersionParent("vim.version.v7_0", "vmodl.version.version2")
AddVersionParent("vim.version.v7_0", "vim.version.v6_9_1")
AddVersionParent("vim.version.v7_0", "vim.version.v6_8_7")
AddVersionParent("vim.version.v7_0", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v7_0", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v7_0", "vim.version.version13")
AddVersionParent("vim.version.v7_0", "vim.version.version14")
AddVersionParent("vim.version.v7_0", "vim.version.version15")
AddVersionParent("vim.version.v7_0", "vim.version.version10")
AddVersionParent("vim.version.v7_0", "vim.version.version11")
AddVersionParent("vim.version.v7_0", "vim.version.version12")
AddVersionParent("vim.version.v7_0", "vim.version.v7_0")
AddVersionParent("vim.version.v7_0", "vim.version.version8")
AddVersionParent("vim.version.v7_0", "vim.version.version9")
AddVersionParent("vim.version.v7_0", "vim.version.version6")
AddVersionParent("vim.version.v7_0", "vim.version.version7")
AddVersionParent("vim.version.v7_0", "vim.version.version1")
AddVersionParent("vim.version.v7_0", "vim.version.version4")
AddVersionParent("vim.version.v7_0", "vim.version.version5")
AddVersionParent("vim.version.v7_0", "vim.version.version2")
AddVersionParent("vim.version.v7_0", "vim.version.version3")
AddVersionParent("sms.version.version14", "vmodl.query.version.version4")
AddVersionParent("sms.version.version14", "vmodl.query.version.version3")
AddVersionParent("sms.version.version14", "vmodl.query.version.version2")
AddVersionParent("sms.version.version14", "vmodl.query.version.version1")
AddVersionParent("sms.version.version14", "vmodl.version.version0")
AddVersionParent("sms.version.version14", "vmodl.version.version1")
AddVersionParent("sms.version.version14", "vmodl.version.version2")
AddVersionParent("sms.version.version14", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.version14", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.version14", "vim.version.version13")
AddVersionParent("sms.version.version14", "vim.version.version14")
AddVersionParent("sms.version.version14", "vim.version.version10")
AddVersionParent("sms.version.version14", "vim.version.version11")
AddVersionParent("sms.version.version14", "vim.version.version12")
AddVersionParent("sms.version.version14", "sms.version.version5")
AddVersionParent("sms.version.version14", "sms.version.version13")
AddVersionParent("sms.version.version14", "sms.version.version12")
AddVersionParent("sms.version.version14", "sms.version.version11")
AddVersionParent("sms.version.version14", "sms.version.version2")
AddVersionParent("sms.version.version14", "sms.version.version1")
AddVersionParent("sms.version.version14", "sms.version.version4")
AddVersionParent("sms.version.version14", "sms.version.version3")
AddVersionParent("sms.version.version14", "sms.version.version14")
AddVersionParent("sms.version.version14", "vim.version.version8")
AddVersionParent("sms.version.version14", "vim.version.version9")
AddVersionParent("sms.version.version14", "vim.version.version6")
AddVersionParent("sms.version.version14", "vim.version.version7")
AddVersionParent("sms.version.version14", "vim.version.version1")
AddVersionParent("sms.version.version14", "vim.version.version4")
AddVersionParent("sms.version.version14", "vim.version.version5")
AddVersionParent("sms.version.version14", "vim.version.version2")
AddVersionParent("sms.version.version14", "vim.version.version3")
AddVersionParent("vim.version.version8", "vmodl.query.version.version4")
AddVersionParent("vim.version.version8", "vmodl.query.version.version3")
AddVersionParent("vim.version.version8", "vmodl.query.version.version2")
AddVersionParent("vim.version.version8", "vmodl.query.version.version1")
AddVersionParent("vim.version.version8", "vmodl.version.version0")
AddVersionParent("vim.version.version8", "vmodl.version.version1")
AddVersionParent("vim.version.version8", "vmodl.version.version2")
AddVersionParent("vim.version.version8", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version8", "vim.version.version8")
AddVersionParent("vim.version.version8", "vim.version.version6")
AddVersionParent("vim.version.version8", "vim.version.version7")
AddVersionParent("vim.version.version8", "vim.version.version1")
AddVersionParent("vim.version.version8", "vim.version.version4")
AddVersionParent("vim.version.version8", "vim.version.version5")
AddVersionParent("vim.version.version8", "vim.version.version2")
AddVersionParent("vim.version.version8", "vim.version.version3")
AddVersionParent("vim.version.version9", "vmodl.query.version.version4")
AddVersionParent("vim.version.version9", "vmodl.query.version.version3")
AddVersionParent("vim.version.version9", "vmodl.query.version.version2")
AddVersionParent("vim.version.version9", "vmodl.query.version.version1")
AddVersionParent("vim.version.version9", "vmodl.version.version0")
AddVersionParent("vim.version.version9", "vmodl.version.version1")
AddVersionParent("vim.version.version9", "vmodl.version.version2")
AddVersionParent("vim.version.version9", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version9", "vim.version.version8")
AddVersionParent("vim.version.version9", "vim.version.version9")
AddVersionParent("vim.version.version9", "vim.version.version6")
AddVersionParent("vim.version.version9", "vim.version.version7")
AddVersionParent("vim.version.version9", "vim.version.version1")
AddVersionParent("vim.version.version9", "vim.version.version4")
AddVersionParent("vim.version.version9", "vim.version.version5")
AddVersionParent("vim.version.version9", "vim.version.version2")
AddVersionParent("vim.version.version9", "vim.version.version3")
AddVersionParent("vim.version.version6", "vmodl.query.version.version3")
AddVersionParent("vim.version.version6", "vmodl.query.version.version2")
AddVersionParent("vim.version.version6", "vmodl.query.version.version1")
AddVersionParent("vim.version.version6", "vmodl.version.version0")
AddVersionParent("vim.version.version6", "vmodl.version.version1")
AddVersionParent("vim.version.version6", "vim.version.version6")
AddVersionParent("vim.version.version6", "vim.version.version1")
AddVersionParent("vim.version.version6", "vim.version.version4")
AddVersionParent("vim.version.version6", "vim.version.version5")
AddVersionParent("vim.version.version6", "vim.version.version2")
AddVersionParent("vim.version.version6", "vim.version.version3")
AddVersionParent("vim.version.version7", "vmodl.query.version.version4")
AddVersionParent("vim.version.version7", "vmodl.query.version.version3")
AddVersionParent("vim.version.version7", "vmodl.query.version.version2")
AddVersionParent("vim.version.version7", "vmodl.query.version.version1")
AddVersionParent("vim.version.version7", "vmodl.version.version0")
AddVersionParent("vim.version.version7", "vmodl.version.version1")
AddVersionParent("vim.version.version7", "vmodl.version.version2")
AddVersionParent("vim.version.version7", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.version7", "vim.version.version6")
AddVersionParent("vim.version.version7", "vim.version.version7")
AddVersionParent("vim.version.version7", "vim.version.version1")
AddVersionParent("vim.version.version7", "vim.version.version4")
AddVersionParent("vim.version.version7", "vim.version.version5")
AddVersionParent("vim.version.version7", "vim.version.version2")
AddVersionParent("vim.version.version7", "vim.version.version3")
AddVersionParent("vim.version.version1", "vmodl.query.version.version1")
AddVersionParent("vim.version.version1", "vmodl.version.version0")
AddVersionParent("vim.version.version1", "vim.version.version1")
AddVersionParent("vim.version.version4", "vmodl.query.version.version1")
AddVersionParent("vim.version.version4", "vmodl.version.version0")
AddVersionParent("vim.version.version4", "vim.version.version1")
AddVersionParent("vim.version.version4", "vim.version.version4")
AddVersionParent("vim.version.version4", "vim.version.version2")
AddVersionParent("vim.version.version4", "vim.version.version3")
AddVersionParent("vim.version.version5", "vmodl.query.version.version2")
AddVersionParent("vim.version.version5", "vmodl.query.version.version1")
AddVersionParent("vim.version.version5", "vmodl.version.version0")
AddVersionParent("vim.version.version5", "vmodl.version.version1")
AddVersionParent("vim.version.version5", "vim.version.version1")
AddVersionParent("vim.version.version5", "vim.version.version4")
AddVersionParent("vim.version.version5", "vim.version.version5")
AddVersionParent("vim.version.version5", "vim.version.version2")
AddVersionParent("vim.version.version5", "vim.version.version3")
AddVersionParent("vim.version.version2", "vmodl.query.version.version1")
AddVersionParent("vim.version.version2", "vmodl.version.version0")
AddVersionParent("vim.version.version2", "vim.version.version1")
AddVersionParent("vim.version.version2", "vim.version.version2")
AddVersionParent("vim.version.version3", "vmodl.query.version.version1")
AddVersionParent("vim.version.version3", "vmodl.version.version0")
AddVersionParent("vim.version.version3", "vim.version.version1")
AddVersionParent("vim.version.version3", "vim.version.version2")
AddVersionParent("vim.version.version3", "vim.version.version3")
AddVersionParent("sms.version.v7_0_3_2", "vmodl.query.version.version4")
AddVersionParent("sms.version.v7_0_3_2", "vmodl.query.version.version3")
AddVersionParent("sms.version.v7_0_3_2", "vmodl.query.version.version2")
AddVersionParent("sms.version.v7_0_3_2", "vmodl.query.version.version1")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v7_0_1_1")
AddVersionParent("sms.version.v7_0_3_2", "vmodl.version.version0")
AddVersionParent("sms.version.v7_0_3_2", "vmodl.version.version1")
AddVersionParent("sms.version.v7_0_3_2", "vmodl.version.version2")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v6_9_1")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v7_0_0_2")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v6_8_7")
AddVersionParent("sms.version.v7_0_3_2", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.v7_0_3_2", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v7_0_3_1")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v7_0_3_2")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v7_0_3_0")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version13")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version14")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version15")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.v7_0_0_1")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.v6_8_7")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version10")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version11")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version12")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v7_0_2_0")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v7_0_2_1")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.version5")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.version13")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v7_0_1_0")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.version12")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.version11")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.version2")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.version1")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.version4")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.version3")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.v7_0")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.version14")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version8")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version9")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version6")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version7")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version1")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version4")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version5")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version2")
AddVersionParent("sms.version.v7_0_3_2", "vim.version.version3")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.v7_0_3_2")
AddVersionParent("sms.version.v7_0_3_2", "sms.version.v7_0")
AddVersionParent("vim.version.v8_0_0_1", "vmodl.query.version.version4")
AddVersionParent("vim.version.v8_0_0_1", "vmodl.query.version.version3")
AddVersionParent("vim.version.v8_0_0_1", "vmodl.query.version.version2")
AddVersionParent("vim.version.v8_0_0_1", "vmodl.query.version.version1")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v8_0_0_1", "vmodl.version.version0")
AddVersionParent("vim.version.v8_0_0_1", "vmodl.version.version1")
AddVersionParent("vim.version.v8_0_0_1", "vmodl.version.version2")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v6_9_1")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v6_8_7")
AddVersionParent("vim.version.v8_0_0_1", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v8_0_0_1", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v8_0_0_0")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v7_0_3_1")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v7_0_3_2")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v7_0_3_0")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version13")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version14")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version15")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version10")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version11")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version12")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v7_0_2_1")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v7_0")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version8")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version9")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version6")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version7")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version1")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version4")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version5")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version2")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.version3")
AddVersionParent("vim.version.v8_0_0_1", "vim.version.v8_0_0_1")
AddVersionParent("vim.version.v8_0_0_2", "vmodl.query.version.version4")
AddVersionParent("vim.version.v8_0_0_2", "vmodl.query.version.version3")
AddVersionParent("vim.version.v8_0_0_2", "vmodl.query.version.version2")
AddVersionParent("vim.version.v8_0_0_2", "vmodl.query.version.version1")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v7_0_1_1")
AddVersionParent("vim.version.v8_0_0_2", "vmodl.version.version0")
AddVersionParent("vim.version.v8_0_0_2", "vmodl.version.version1")
AddVersionParent("vim.version.v8_0_0_2", "vmodl.version.version2")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v6_9_1")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v7_0_0_2")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v6_8_7")
AddVersionParent("vim.version.v8_0_0_2", "vmodl.reflect.version.version1")
AddVersionParent("vim.version.v8_0_0_2", "vmodl.reflect.version.version2")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v8_0_0_0")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v7_0_3_1")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v7_0_3_2")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v7_0_3_0")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version13")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version14")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version15")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version10")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version11")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version12")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v7_0_2_0")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v7_0_2_1")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v7_0_1_0")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v7_0")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version8")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version9")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version6")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version7")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version1")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version4")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version5")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version2")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.version3")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v8_0_0_1")
AddVersionParent("vim.version.v8_0_0_2", "vim.version.v8_0_0_2")
AddVersionParent("sms.version.v7_0", "vmodl.query.version.version4")
AddVersionParent("sms.version.v7_0", "vmodl.query.version.version3")
AddVersionParent("sms.version.v7_0", "vmodl.query.version.version2")
AddVersionParent("sms.version.v7_0", "vmodl.query.version.version1")
AddVersionParent("sms.version.v7_0", "vmodl.version.version0")
AddVersionParent("sms.version.v7_0", "vmodl.version.version1")
AddVersionParent("sms.version.v7_0", "vmodl.version.version2")
AddVersionParent("sms.version.v7_0", "vim.version.v6_9_1")
AddVersionParent("sms.version.v7_0", "vim.version.v6_8_7")
AddVersionParent("sms.version.v7_0", "vmodl.reflect.version.version1")
AddVersionParent("sms.version.v7_0", "vmodl.reflect.version.version2")
AddVersionParent("sms.version.v7_0", "vim.version.version13")
AddVersionParent("sms.version.v7_0", "vim.version.version14")
AddVersionParent("sms.version.v7_0", "vim.version.version15")
AddVersionParent("sms.version.v7_0", "sms.version.v6_8_7")
AddVersionParent("sms.version.v7_0", "vim.version.version10")
AddVersionParent("sms.version.v7_0", "vim.version.version11")
AddVersionParent("sms.version.v7_0", "vim.version.version12")
AddVersionParent("sms.version.v7_0", "sms.version.version5")
AddVersionParent("sms.version.v7_0", "sms.version.version13")
AddVersionParent("sms.version.v7_0", "sms.version.version12")
AddVersionParent("sms.version.v7_0", "sms.version.version11")
AddVersionParent("sms.version.v7_0", "sms.version.version2")
AddVersionParent("sms.version.v7_0", "sms.version.version1")
AddVersionParent("sms.version.v7_0", "sms.version.version4")
AddVersionParent("sms.version.v7_0", "sms.version.version3")
AddVersionParent("sms.version.v7_0", "vim.version.v7_0")
AddVersionParent("sms.version.v7_0", "sms.version.version14")
AddVersionParent("sms.version.v7_0", "vim.version.version8")
AddVersionParent("sms.version.v7_0", "vim.version.version9")
AddVersionParent("sms.version.v7_0", "vim.version.version6")
AddVersionParent("sms.version.v7_0", "vim.version.version7")
AddVersionParent("sms.version.v7_0", "vim.version.version1")
AddVersionParent("sms.version.v7_0", "vim.version.version4")
AddVersionParent("sms.version.v7_0", "vim.version.version5")
AddVersionParent("sms.version.v7_0", "vim.version.version2")
AddVersionParent("sms.version.v7_0", "vim.version.version3")
AddVersionParent("sms.version.v7_0", "sms.version.v7_0")

newestVersions.Add("sms.version.v8_0_2_0")
ltsVersions.Add("sms.version.v8_0_2_0")
dottedVersions.Add("sms.version.v8_0_2_0")
oldestVersions.Add("sms.version.version1")

CreateDataType("sms.AboutInfo", "SmsAboutInfo", "vmodl.DynamicData", "sms.version.version2", [("name", "string", "sms.version.version2", 0), ("fullName", "string", "sms.version.version2", 0), ("vendor", "string", "sms.version.version2", 0), ("apiVersion", "string", "sms.version.version2", 0), ("instanceUuid", "string", "sms.version.version2", 0), ("vasaApiVersion", "string", "sms.version.version4", F_OPTIONAL)])
CreateDataType("sms.EntityReference", "EntityReference", "vmodl.DynamicData", "sms.version.version1", [("id", "string", "sms.version.version1", 0), ("type", "sms.EntityReference.EntityType", "sms.version.version1", F_OPTIONAL)])
CreateEnumType("sms.EntityReference.EntityType", "EntityReferenceEntityType", "sms.version.version1", ["datacenter", "resourcePool", "storagePod", "cluster", "vm", "datastore", "host", "vmFile", "scsiPath", "scsiTarget", "scsiVolume", "scsiAdapter", "nasMount"])
CreateDataType("sms.FaultDomainFilter", "FaultDomainFilter", "vmodl.DynamicData", "sms.version.version11", [("providerId", "string", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.ReplicationGroupFilter", "ReplicationGroupFilter", "vmodl.DynamicData", "sms.version.version11", [("groupId", "vim.vm.replication.ReplicationGroupId[]", "sms.version.version11", F_OPTIONAL)])
CreateManagedType("sms.ServiceInstance", "SmsServiceInstance", "vmodl.ManagedObject", "sms.version.version1", None, [("queryStorageManager", "QueryStorageManager", "sms.version.version2", (), (0, "sms.StorageManager", "sms.StorageManager"), "StorageViews.View", None), ("querySessionManager", "QuerySessionManager", "sms.version.version5", (), (0, "sms.auth.SessionManager", "sms.auth.SessionManager"), "System.Anonymous", None), ("queryAboutInfo", "QueryAboutInfo", "sms.version.version2", (), (0, "sms.AboutInfo", "sms.AboutInfo"), "StorageViews.View", None)])
CreateManagedType("sms.StorageManager", "SmsStorageManager", "vmodl.ManagedObject", "sms.version.version2", None, [("registerProvider", "RegisterProvider_Task", "sms.version.version2", (("providerSpec", "sms.provider.ProviderSpec", "sms.version.version2", 0, None),), (0, "sms.Task", "sms.provider.Provider"), "StorageViews.ConfigureService", ["vim.fault.AlreadyExists", "sms.fault.ProviderRegistrationFault", ]), ("unregisterProvider", "UnregisterProvider_Task", "sms.version.version2", (("providerId", "string", "sms.version.version2", 0, None),), (0, "sms.Task", "void"), "StorageViews.ConfigureService", ["vim.fault.NotFound", "sms.fault.ProviderUnregistrationFault", ]), ("queryProvider", "QueryProvider", "sms.version.version2", (), (F_OPTIONAL, "sms.provider.Provider[]", "sms.provider.Provider[]"), "StorageViews.View", ["sms.fault.QueryExecutionFault", ]), ("queryArray", "QueryArray", "sms.version.version2", (("providerId", "string[]", "sms.version.version2", F_OPTIONAL, None),), (F_OPTIONAL, "sms.storage.StorageArray[]", "sms.storage.StorageArray[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryProcessorAssociatedWithArray", "QueryProcessorAssociatedWithArray", "sms.version.version2", (("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "sms.storage.StorageProcessor[]", "sms.storage.StorageProcessor[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryPortAssociatedWithArray", "QueryPortAssociatedWithArray", "sms.version.version2", (("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "sms.storage.StoragePort[]", "sms.storage.StoragePort[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryPortAssociatedWithLun", "QueryPortAssociatedWithLun", "sms.version.version2", (("scsi3Id", "string", "sms.version.version2", 0, None),("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "sms.storage.StoragePort", "sms.storage.StoragePort"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryLunAssociatedWithPort", "QueryLunAssociatedWithPort", "sms.version.version2", (("portId", "string", "sms.version.version2", 0, None),("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "sms.storage.StorageLun[]", "sms.storage.StorageLun[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryArrayAssociatedWithLun", "QueryArrayAssociatedWithLun", "sms.version.version2", (("canonicalName", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "sms.storage.StorageArray", "sms.storage.StorageArray"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryPortAssociatedWithProcessor", "QueryPortAssociatedWithProcessor", "sms.version.version2", (("processorId", "string", "sms.version.version2", 0, None),("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "sms.storage.StoragePort[]", "sms.storage.StoragePort[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryLunAssociatedWithArray", "QueryLunAssociatedWithArray", "sms.version.version2", (("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "sms.storage.StorageLun[]", "sms.storage.StorageLun[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryFileSystemAssociatedWithArray", "QueryFileSystemAssociatedWithArray", "sms.version.version2", (("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "sms.storage.StorageFileSystem[]", "sms.storage.StorageFileSystem[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryDatastoreCapability", "QueryDatastoreCapability", "sms.version.version2", (("datastore", "vim.Datastore", "sms.version.version2", 0, None),), (F_OPTIONAL, "sms.storage.StorageCapability", "sms.storage.StorageCapability"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryHostAssociatedWithLun", "QueryHostAssociatedWithLun", "sms.version.version2", (("scsi3Id", "string", "sms.version.version2", 0, None),("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "vim.HostSystem[]", "vim.HostSystem[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryVmfsDatastoreAssociatedWithLun", "QueryVmfsDatastoreAssociatedWithLun", "sms.version.version2", (("scsi3Id", "string", "sms.version.version2", 0, None),("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "vim.Datastore", "vim.Datastore"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryNfsDatastoreAssociatedWithFileSystem", "QueryNfsDatastoreAssociatedWithFileSystem", "sms.version.version2", (("fileSystemId", "string", "sms.version.version2", 0, None),("arrayId", "string", "sms.version.version2", 0, None),), (F_OPTIONAL, "vim.Datastore", "vim.Datastore"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryDrsMigrationCapabilityForPerformance", "QueryDrsMigrationCapabilityForPerformance", "sms.version.version2", (("srcDatastore", "vim.Datastore", "sms.version.version2", 0, None),("dstDatastore", "vim.Datastore", "sms.version.version2", 0, None),), (0, "boolean", "boolean"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryDrsMigrationCapabilityForPerformanceEx", "QueryDrsMigrationCapabilityForPerformanceEx", "sms.version.version3", (("datastore", "vim.Datastore[]", "sms.version.version3", 0, None),), (0, "sms.storage.DrsMigrationCapabilityResult", "sms.storage.DrsMigrationCapabilityResult"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryStorageContainer", "QueryStorageContainer", "sms.version.version5", (("containerSpec", "sms.storage.StorageContainerSpec", "sms.version.version5", F_OPTIONAL, None),), (F_OPTIONAL, "sms.storage.StorageContainerResult", "sms.storage.StorageContainerResult"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryAssociatedBackingStoragePool", "QueryAssociatedBackingStoragePool", "sms.version.version5", (("entityId", "string", "sms.version.version5", F_OPTIONAL, None),("entityType", "string", "sms.version.version5", F_OPTIONAL, None),), (F_OPTIONAL, "sms.storage.BackingStoragePool[]", "sms.storage.BackingStoragePool[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryDatastoreBackingPoolMapping", "QueryDatastoreBackingPoolMapping", "sms.version.version5", (("datastore", "vim.Datastore[]", "sms.version.version5", 0, None),), (0, "sms.storage.DatastoreBackingPoolMapping[]", "sms.storage.DatastoreBackingPoolMapping[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("refreshCACertificatesAndCRLs", "SmsRefreshCACertificatesAndCRLs_Task", "sms.version.version5", (("providerId", "string[]", "sms.version.version5", F_OPTIONAL, None),), (0, "sms.Task", "void"), "StorageViews.ConfigureService", ["vim.fault.NotFound", "sms.fault.CertificateRefreshFailed", ]), ("queryFaultDomain", "QueryFaultDomain", "sms.version.version11", (("filter", "sms.FaultDomainFilter", "sms.version.version11", F_OPTIONAL, None),), (F_OPTIONAL, "vim.vm.replication.FaultDomainId[]", "vim.vm.replication.FaultDomainId[]"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.QueryExecutionFault", ]), ("queryReplicationGroupInfo", "QueryReplicationGroupInfo", "sms.version.version11", (("rgFilter", "sms.ReplicationGroupFilter", "sms.version.version11", 0, None),), (F_OPTIONAL, "sms.storage.replication.GroupOperationResult[]", "sms.storage.replication.GroupOperationResult[]"), "StorageViews.View", ["sms.fault.QueryExecutionFault", ])])
CreateManagedType("sms.Task", "SmsTask", "vmodl.ManagedObject", "sms.version.version2", None, [("queryResult", "QuerySmsTaskResult", "sms.version.version2", (), (F_OPTIONAL, "anyType", "anyType"), "StorageViews.View", None), ("queryInfo", "QuerySmsTaskInfo", "sms.version.version2", (), (0, "sms.TaskInfo", "sms.TaskInfo"), "StorageViews.View", None)])
CreateDataType("sms.TaskInfo", "SmsTaskInfo", "vmodl.DynamicData", "sms.version.version2", [("key", "string", "sms.version.version2", 0), ("task", "sms.Task", "sms.version.version2", 0), ("object", "vmodl.ManagedObject", "sms.version.version2", F_OPTIONAL), ("error", "vmodl.MethodFault", "sms.version.version2", F_OPTIONAL), ("result", "anyType", "sms.version.version2", F_OPTIONAL), ("startTime", "vmodl.DateTime", "sms.version.version2", F_OPTIONAL), ("completionTime", "vmodl.DateTime", "sms.version.version2", F_OPTIONAL), ("state", "string", "sms.version.version2", 0), ("progress", "int", "sms.version.version2", F_OPTIONAL)])
CreateEnumType("sms.TaskInfo.State", "SmsTaskState", "sms.version.version1", ["queued", "running", "success", "error"])
CreateManagedType("sms.auth.SessionManager", "SmsSessionManager", "vmodl.ManagedObject", "sms.version.version5", None, None)
CreateDataType("sms.fault.AuthConnectionFailed", "AuthConnectionFailed", "vim.fault.NoPermission", "sms.version.version2", None)
CreateDataType("sms.fault.CertificateRefreshFailed", "CertificateRefreshFailed", "vmodl.MethodFault", "sms.version.version5", [("providerId", "string[]", "sms.version.version5", F_OPTIONAL)])
CreateDataType("sms.fault.CertificateRevocationFailed", "CertificateRevocationFailed", "vmodl.MethodFault", "sms.version.version5", None)
CreateDataType("sms.fault.DuplicateEntry", "DuplicateEntry", "vmodl.MethodFault", "sms.version.version11", None)
CreateDataType("sms.fault.InactiveProvider", "InactiveProvider", "vmodl.MethodFault", "sms.version.version11", [("mapping", "sms.storage.FaultDomainProviderMapping[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.fault.InvalidLogin", "SmsInvalidLogin", "vmodl.MethodFault", "sms.version.version5", None)
CreateDataType("sms.fault.InvalidProfile", "InvalidProfile", "vmodl.MethodFault", "sms.version.version11", None)
CreateDataType("sms.fault.InvalidSession", "InvalidSession", "vim.fault.NoPermission", "sms.version.version2", [("sessionCookie", "string", "sms.version.version2", 0)])
CreateDataType("sms.fault.MultipleSortSpecsNotSupported", "MultipleSortSpecsNotSupported", "vmodl.fault.InvalidArgument", "sms.version.version1", None)
CreateDataType("sms.fault.NotSupportedByProvider", "NotSupportedByProvider", "vmodl.MethodFault", "sms.version.version11", None)
CreateDataType("sms.fault.ProviderBusy", "ProviderBusy", "vmodl.MethodFault", "sms.version.version11", None)
CreateDataType("sms.fault.ProviderConnectionFailed", "ProviderConnectionFailed", "vmodl.RuntimeFault", "sms.version.version2", None)
CreateDataType("sms.fault.ProviderOutOfProvisioningResource", "ProviderOutOfProvisioningResource", "vmodl.MethodFault", "sms.version.version11", [("provisioningResourceId", "string", "sms.version.version11", 0), ("availableBefore", "long", "sms.version.version11", F_OPTIONAL), ("availableAfter", "long", "sms.version.version11", F_OPTIONAL), ("total", "long", "sms.version.version11", F_OPTIONAL), ("isTransient", "boolean", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.fault.ProviderOutOfResource", "ProviderOutOfResource", "vmodl.MethodFault", "sms.version.version11", None)
CreateDataType("sms.fault.ProviderRegistrationFault", "ProviderRegistrationFault", "vmodl.MethodFault", "sms.version.version2", None)
CreateDataType("sms.fault.ProviderSyncFailed", "ProviderSyncFailed", "vmodl.MethodFault", "sms.version.version1", None)
CreateDataType("sms.fault.ProviderUnavailable", "ProviderUnavailable", "vmodl.MethodFault", "sms.version.version11", None)
CreateDataType("sms.fault.ProviderUnregistrationFault", "ProviderUnregistrationFault", "vmodl.MethodFault", "sms.version.version2", None)
CreateDataType("sms.fault.ProxyRegistrationFailed", "ProxyRegistrationFailed", "vmodl.RuntimeFault", "sms.version.version1", None)
CreateDataType("sms.fault.QueryExecutionFault", "QueryExecutionFault", "vmodl.MethodFault", "sms.version.version1", None)
CreateDataType("sms.fault.QueryNotSupported", "QueryNotSupported", "vmodl.fault.InvalidArgument", "sms.version.version1", [("entityType", "sms.EntityReference.EntityType", "sms.version.version1", F_OPTIONAL), ("relatedEntityType", "sms.EntityReference.EntityType", "sms.version.version1", 0)])
CreateDataType("sms.fault.ResourceInUse", "SmsResourceInUse", "vim.fault.ResourceInUse", "sms.version.version11", [("deviceIds", "sms.storage.replication.DeviceId[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.fault.ServiceNotInitialized", "ServiceNotInitialized", "vmodl.RuntimeFault", "sms.version.version1", None)
CreateDataType("sms.fault.SyncInProgress", "SyncInProgress", "sms.fault.ProviderSyncFailed", "sms.version.version1", None)
CreateDataType("sms.fault.TooMany", "TooMany", "vmodl.MethodFault", "sms.version.version11", [("maxBatchSize", "long", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.fault.replication.ReplicationFault", "SmsReplicationFault", "vmodl.MethodFault", "sms.version.version11", None)
CreateDataType("sms.fault.replication.SyncOngoing", "SyncOngoing", "sms.fault.replication.ReplicationFault", "sms.version.version11", [("task", "sms.Task", "sms.version.version11", 0)])
CreateDataType("sms.provider.AlarmFilter", "AlarmFilter", "vmodl.DynamicData", "sms.version.version11", [("alarmStatus", "string", "sms.version.version11", F_OPTIONAL), ("alarmType", "string", "sms.version.version11", F_OPTIONAL), ("entityType", "string", "sms.version.version11", F_OPTIONAL), ("entityId", "anyType[]", "sms.version.version11", F_OPTIONAL), ("pageMarker", "string", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.provider.AlarmResult", "AlarmResult", "vmodl.DynamicData", "sms.version.version11", [("storageAlarm", "sms.storage.StorageAlarm[]", "sms.version.version11", F_OPTIONAL), ("pageMarker", "string", "sms.version.version11", F_OPTIONAL)])
CreateManagedType("sms.provider.Provider", "SmsProvider", "vmodl.ManagedObject", "sms.version.version2", None, [("queryProviderInfo", "QueryProviderInfo", "sms.version.version2", (), (0, "sms.provider.ProviderInfo", "sms.provider.ProviderInfo"), "StorageViews.View", None)])
CreateDataType("sms.provider.ProviderInfo", "SmsProviderInfo", "vmodl.DynamicData", "sms.version.version2", [("uid", "string", "sms.version.version2", 0), ("name", "string", "sms.version.version2", 0), ("description", "string", "sms.version.version2", F_OPTIONAL), ("version", "string", "sms.version.version2", F_OPTIONAL)])
CreateDataType("sms.provider.ProviderSpec", "SmsProviderSpec", "vmodl.DynamicData", "sms.version.version2", [("name", "string", "sms.version.version2", 0), ("description", "string", "sms.version.version2", F_OPTIONAL)])
CreateManagedType("sms.provider.VasaProvider", "VasaProvider", "sms.provider.Provider", "sms.version.version2", None, [("sync", "VasaProviderSync_Task", "sms.version.version2", (("arrayId", "string", "sms.version.version2", F_OPTIONAL, None),), (0, "sms.Task", "void"), "StorageViews.View", ["sms.fault.ProviderSyncFailed", ]), ("refreshCertificate", "VasaProviderRefreshCertificate_Task", "sms.version.version5", (), (0, "sms.Task", "void"), "StorageViews.ConfigureService", ["sms.fault.CertificateRefreshFailed", ]), ("revokeCertificate", "VasaProviderRevokeCertificate_Task", "sms.version.version5", (), (0, "sms.Task", "void"), "StorageViews.ConfigureService", ["sms.fault.CertificateRevocationFailed", ]), ("reconnect", "VasaProviderReconnect_Task", "sms.version.version5", (), (0, "sms.Task", "void"), "StorageViews.ConfigureService", ["sms.fault.InvalidCertificate", "sms.fault.ProviderConnectionFailed", ]), ("queryReplicationPeer", "QueryReplicationPeer", "sms.version.version11", (("faultDomainId", "vim.vm.replication.FaultDomainId[]", "sms.version.version11", F_OPTIONAL, None),), (F_OPTIONAL, "sms.storage.replication.QueryReplicationPeerResult[]", "sms.storage.replication.QueryReplicationPeerResult[]"), "StorageViews.View", ["sms.fault.ProviderUnavailable", "sms.fault.InactiveProvider", "sms.fault.ProviderBusy", "sms.fault.QueryExecutionFault", ]), ("queryReplicationGroup", "QueryReplicationGroup", "sms.version.version11", (("groupId", "vim.vm.replication.ReplicationGroupId[]", "sms.version.version11", F_OPTIONAL, None),), (F_OPTIONAL, "sms.storage.replication.GroupOperationResult[]", "sms.storage.replication.GroupOperationResult[]"), "StorageViews.View", ["sms.fault.ProviderUnavailable", "sms.fault.InactiveProvider", "sms.fault.ProviderBusy", "sms.fault.QueryExecutionFault", ]), ("queryPointInTimeReplica", "QueryPointInTimeReplica", "sms.version.version11", (("groupId", "vim.vm.replication.ReplicationGroupId[]", "sms.version.version11", F_OPTIONAL, None),("queryParam", "sms.storage.replication.QueryPointInTimeReplicaParam", "sms.version.version11", F_OPTIONAL, None),), (F_OPTIONAL, "sms.storage.replication.GroupOperationResult[]", "sms.storage.replication.GroupOperationResult[]"), "StorageViews.View", ["sms.fault.ProviderUnavailable", "sms.fault.InactiveProvider", "sms.fault.ProviderBusy", "sms.fault.QueryExecutionFault", ]), ("testFailoverReplicationGroupStart", "TestFailoverReplicationGroupStart_Task", "sms.version.version11", (("testFailoverParam", "sms.storage.replication.TestFailoverParam", "sms.version.version11", 0, None),), (0, "sms.Task", "void"), "StorageViews.ConfigureService", ["sms.fault.ProviderUnavailable", "sms.fault.ProviderOutOfResource", "sms.fault.InactiveProvider", "sms.fault.TooMany", "sms.fault.ProviderBusy", "sms.fault.replication.ReplicationFault", ]), ("testFailoverReplicationGroupStop", "TestFailoverReplicationGroupStop_Task", "sms.version.version11", (("groupId", "vim.vm.replication.ReplicationGroupId[]", "sms.version.version11", F_OPTIONAL, None),("force", "boolean", "sms.version.version11", 0, None),), (0, "sms.Task", "void"), "StorageViews.ConfigureService", ["sms.fault.ProviderUnavailable", "sms.fault.ProviderOutOfResource", "sms.fault.InactiveProvider", "sms.fault.TooMany", "sms.fault.ProviderBusy", "sms.fault.replication.ReplicationFault", "sms.fault.NotSupportedByProvider", ]), ("promoteReplicationGroup", "PromoteReplicationGroup_Task", "sms.version.version11", (("promoteParam", "sms.storage.replication.PromoteParam", "sms.version.version11", 0, None),), (F_OPTIONAL, "sms.Task", "sms.storage.replication.GroupOperationResult[]"), "StorageViews.ConfigureService", ["sms.fault.ProviderUnavailable", "sms.fault.ProviderOutOfResource", "sms.fault.InactiveProvider", "sms.fault.TooMany", "sms.fault.ProviderBusy", "sms.fault.replication.ReplicationFault", ]), ("syncReplicationGroup", "SyncReplicationGroup_Task", "sms.version.version11", (("groupId", "vim.vm.replication.ReplicationGroupId[]", "sms.version.version11", F_OPTIONAL, None),("pitName", "string", "sms.version.version11", 0, None),), (F_OPTIONAL, "sms.Task", "sms.storage.replication.GroupOperationResult[]"), "StorageViews.ConfigureService", ["sms.fault.ProviderUnavailable", "sms.fault.ProviderOutOfResource", "sms.fault.InactiveProvider", "sms.fault.ProviderBusy", "sms.fault.replication.ReplicationFault", "sms.fault.TooMany", ]), ("prepareFailoverReplicationGroup", "PrepareFailoverReplicationGroup_Task", "sms.version.version11", (("groupId", "vim.vm.replication.ReplicationGroupId[]", "sms.version.version11", F_OPTIONAL, None),), (F_OPTIONAL, "sms.Task", "sms.storage.replication.GroupOperationResult[]"), "StorageViews.ConfigureService", ["sms.fault.ProviderUnavailable", "sms.fault.ProviderOutOfResource", "sms.fault.InactiveProvider", "sms.fault.TooMany", "sms.fault.ProviderBusy", "sms.fault.replication.ReplicationFault", ]), ("failoverReplicationGroup", "FailoverReplicationGroup_Task", "sms.version.version11", (("failoverParam", "sms.storage.replication.FailoverParam", "sms.version.version11", 0, None),), (F_OPTIONAL, "sms.Task", "sms.storage.replication.GroupOperationResult[]"), "StorageViews.ConfigureService", ["sms.fault.ProviderUnavailable", "sms.fault.ProviderOutOfResource", "sms.fault.InactiveProvider", "sms.fault.TooMany", "sms.fault.ProviderBusy", "sms.fault.replication.ReplicationFault", ]), ("reverseReplicateGroup", "ReverseReplicateGroup_Task", "sms.version.version11", (("groupId", "vim.vm.replication.ReplicationGroupId[]", "sms.version.version11", F_OPTIONAL, None),), (F_OPTIONAL, "sms.Task", "sms.storage.replication.GroupOperationResult[]"), "StorageViews.ConfigureService", ["sms.fault.ProviderUnavailable", "sms.fault.ProviderOutOfResource", "sms.fault.InactiveProvider", "sms.fault.TooMany", "sms.fault.ProviderBusy", "sms.fault.replication.ReplicationFault", ]), ("queryActiveAlarm", "QueryActiveAlarm", "sms.version.version11", (("alarmFilter", "sms.provider.AlarmFilter", "sms.version.version11", F_OPTIONAL, None),), (F_OPTIONAL, "sms.provider.AlarmResult", "sms.provider.AlarmResult"), "StorageViews.View", ["vim.fault.NotFound", "sms.fault.ProviderBusy", "sms.fault.InactiveProvider", "sms.fault.ProviderUnavailable", "sms.fault.QueryExecutionFault", ])])
CreateDataType("sms.provider.VasaProviderInfo", "VasaProviderInfo", "sms.provider.ProviderInfo", "sms.version.version2", [("url", "string", "sms.version.version2", 0), ("certificate", "string", "sms.version.version2", F_OPTIONAL), ("status", "string", "sms.version.version2", F_OPTIONAL), ("statusFault", "vmodl.MethodFault", "sms.version.version11", F_OPTIONAL), ("vasaVersion", "string", "sms.version.version2", F_OPTIONAL), ("namespace", "string", "sms.version.version2", F_OPTIONAL), ("lastSyncTime", "string", "sms.version.version2", F_OPTIONAL), ("supportedVendorModelMapping", "sms.provider.VasaProviderInfo.SupportedVendorModelMapping[]", "sms.version.version2", F_OPTIONAL), ("supportedProfile", "string[]", "sms.version.version2", F_OPTIONAL), ("supportedProviderProfile", "string[]", "sms.version.version4", F_OPTIONAL), ("relatedStorageArray", "sms.provider.VasaProviderInfo.RelatedStorageArray[]", "sms.version.version4", F_OPTIONAL), ("providerId", "string", "sms.version.version4", F_OPTIONAL), ("certificateExpiryDate", "string", "sms.version.version5", F_OPTIONAL), ("certificateStatus", "string", "sms.version.version5", F_OPTIONAL), ("serviceLocation", "string", "sms.version.version5", F_OPTIONAL), ("needsExplicitActivation", "boolean", "sms.version.version5", F_OPTIONAL), ("maxBatchSize", "long", "sms.version.version11", F_OPTIONAL), ("retainVasaProviderCertificate", "boolean", "sms.version.version5", F_OPTIONAL), ("arrayIndependentProvider", "boolean", "sms.version.version11", F_OPTIONAL), ("type", "string", "sms.version.version11", F_OPTIONAL), ("category", "string", "sms.version.version12", F_OPTIONAL), ("priority", "int", "sms.version.version11", F_OPTIONAL), ("failoverGroupId", "string", "sms.version.version11", F_OPTIONAL)])
CreateEnumType("sms.provider.VasaProviderInfo.CertificateStatus", "VasaProviderCertificateStatus", "sms.version.version5", ["valid", "expirySoftLimitReached", "expiryHardLimitReached", "expired", "invalid"])
CreateDataType("sms.provider.VasaProviderInfo.RelatedStorageArray", "RelatedStorageArray", "vmodl.DynamicData", "sms.version.version4", [("arrayId", "string", "sms.version.version4", 0), ("active", "boolean", "sms.version.version4", 0), ("manageable", "boolean", "sms.version.version4", 0), ("priority", "int", "sms.version.version4", 0)])
CreateDataType("sms.provider.VasaProviderInfo.SupportedVendorModelMapping", "SupportedVendorModelMapping", "vmodl.DynamicData", "sms.version.version1", [("vendorId", "string", "sms.version.version1", F_OPTIONAL), ("modelId", "string", "sms.version.version1", F_OPTIONAL)])
CreateEnumType("sms.provider.VasaProviderInfo.VasaProviderStatus", "VasaProviderStatus", "sms.version.version1", ["online", "offline", "syncError", "unknown", "connected", "disconnected"])
CreateEnumType("sms.provider.VasaProviderInfo.VasaProviderProfile", "VasaProviderProfile", "sms.version.version1", ["blockDevice", "fileSystem", "capability"])
CreateEnumType("sms.provider.VasaProviderInfo.ProviderProfile", "ProviderProfile", "sms.version.version11", ["ProfileBasedManagement", "Replication"])
CreateEnumType("sms.provider.VasaProviderInfo.Type", "VpType", "sms.version.version11", ["PERSISTENCE", "DATASERVICE", "UNKNOWN"])
CreateEnumType("sms.provider.VasaProviderInfo.Category", "VpCategory", "sms.version.version12", ["internal", "external"])
CreateDataType("sms.provider.VasaProviderSpec", "VasaProviderSpec", "sms.provider.ProviderSpec", "sms.version.version2", [("username", "string", "sms.version.version2", 0), ("password", "string", "sms.version.version2", F_SECRET), ("url", "string", "sms.version.version2", 0), ("certificate", "string", "sms.version.version2", F_OPTIONAL)])
CreateEnumType("sms.provider.VmodlVasaProviderSpec.AuthenticationType", "VasaAuthenticationType", "sms.version.version12", ["LoginByToken", "UseSessionId"])
CreateEnumType("sms.storage.AlarmStatus", "SmsAlarmStatus", "sms.version.version11", ["Red", "Green", "Yellow"])
CreateEnumType("sms.storage.AlarmType", "AlarmType", "sms.version.version11", ["SpaceCapacityAlarm", "CapabilityAlarm", "StorageObjectAlarm", "ObjectAlarm", "ComplianceAlarm", "ManageabilityAlarm", "ReplicationAlarm", "CertificateAlarm"])
CreateDataType("sms.storage.BackingConfig", "BackingConfig", "vmodl.DynamicData", "sms.version.version5", [("thinProvisionBackingIdentifier", "string", "sms.version.version5", F_OPTIONAL), ("deduplicationBackingIdentifier", "string", "sms.version.version5", F_OPTIONAL), ("autoTieringEnabled", "boolean", "sms.version.version5", F_OPTIONAL), ("deduplicationEfficiency", "long", "sms.version.version5", F_OPTIONAL), ("performanceOptimizationInterval", "long", "sms.version.version5", F_OPTIONAL)])
CreateDataType("sms.storage.BackingStoragePool", "BackingStoragePool", "vmodl.DynamicData", "sms.version.version5", [("uuid", "string", "sms.version.version5", 0), ("type", "string", "sms.version.version5", 0), ("capacityInMB", "long", "sms.version.version5", 0), ("usedSpaceInMB", "long", "sms.version.version5", 0)])
CreateEnumType("sms.storage.BackingStoragePool.BackingStoragePoolType", "BackingStoragePoolType", "sms.version.version1", ["thinProvisioningPool", "deduplicationPool", "thinAndDeduplicationCombinedPool"])
CreateDataType("sms.storage.DatastoreBackingPoolMapping", "DatastoreBackingPoolMapping", "vmodl.DynamicData", "sms.version.version5", [("datastore", "vim.Datastore[]", "sms.version.version5", 0), ("backingStoragePool", "sms.storage.BackingStoragePool[]", "sms.version.version5", F_OPTIONAL)])
CreateDataType("sms.storage.DatastorePair", "DatastorePair", "vmodl.DynamicData", "sms.version.version3", [("datastore1", "vim.Datastore", "sms.version.version3", 0), ("datastore2", "vim.Datastore", "sms.version.version3", 0)])
CreateDataType("sms.storage.DrsMigrationCapabilityResult", "DrsMigrationCapabilityResult", "vmodl.DynamicData", "sms.version.version3", [("recommendedDatastorePair", "sms.storage.DatastorePair[]", "sms.version.version3", F_OPTIONAL), ("nonRecommendedDatastorePair", "sms.storage.DatastorePair[]", "sms.version.version3", F_OPTIONAL)])
CreateEnumType("sms.storage.EntityType", "SmsEntityType", "sms.version.version11", ["StorageArrayEntity", "StorageProcessorEntity", "StoragePortEntity", "StorageLunEntity", "StorageFileSystemEntity", "StorageCapabilityEntity", "CapabilitySchemaEntity", "CapabilityProfileEntity", "DefaultProfileEntity", "ResourceAssociationEntity", "StorageContainerEntity", "StorageObjectEntity", "MessageCatalogEntity", "ProtocolEndpointEntity", "VirtualVolumeInfoEntity", "BackingStoragePoolEntity", "FaultDomainEntity", "ReplicationGroupEntity"])
CreateDataType("sms.storage.FaultDomainProviderMapping", "FaultDomainProviderMapping", "vmodl.DynamicData", "sms.version.version11", [("activeProvider", "sms.provider.Provider", "sms.version.version11", 0), ("faultDomainId", "vim.vm.replication.FaultDomainId[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.FileSystemInfo", "StorageFileSystemInfo", "vmodl.DynamicData", "sms.version.version2", [("fileServerName", "string", "sms.version.version2", 0), ("fileSystemPath", "string", "sms.version.version2", 0), ("ipAddress", "string", "sms.version.version2", F_OPTIONAL)])
CreateDataType("sms.storage.LunHbaAssociation", "LunHbaAssociation", "vmodl.DynamicData", "sms.version.version2", [("canonicalName", "string", "sms.version.version2", 0), ("hba", "vim.host.HostBusAdapter[]", "sms.version.version2", 0)])
CreateDataType("sms.storage.NameValuePair", "NameValuePair", "vmodl.DynamicData", "sms.version.version11", [("parameterName", "string", "sms.version.version11", 0), ("parameterValue", "string", "sms.version.version11", 0)])
CreateDataType("sms.storage.StorageAlarm", "StorageAlarm", "vmodl.DynamicData", "sms.version.version11", [("alarmId", "long", "sms.version.version11", 0), ("alarmType", "string", "sms.version.version11", 0), ("containerId", "string", "sms.version.version11", F_OPTIONAL), ("objectId", "string", "sms.version.version11", F_OPTIONAL), ("objectType", "string", "sms.version.version11", 0), ("status", "string", "sms.version.version11", 0), ("alarmTimeStamp", "vmodl.DateTime", "sms.version.version11", 0), ("messageId", "string", "sms.version.version11", 0), ("parameterList", "sms.storage.NameValuePair[]", "sms.version.version11", F_OPTIONAL), ("alarmObject", "anyType", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.StorageArray", "StorageArray", "vmodl.DynamicData", "sms.version.version2", [("name", "string", "sms.version.version2", 0), ("uuid", "string", "sms.version.version2", 0), ("vendorId", "string", "sms.version.version2", 0), ("modelId", "string", "sms.version.version2", 0), ("firmware", "string", "sms.version.version2", F_OPTIONAL), ("alternateName", "string[]", "sms.version.version2", F_OPTIONAL), ("supportedBlockInterface", "string[]", "sms.version.version2", F_OPTIONAL), ("supportedFileSystemInterface", "string[]", "sms.version.version2", F_OPTIONAL), ("supportedProfile", "string[]", "sms.version.version3", F_OPTIONAL), ("priority", "int", "sms.version.version4", F_OPTIONAL), ("discoverySvc", "vim.VasaStorageArray.DiscoverySvcInfo[]", "sms.version.v8_0_0_0", F_OPTIONAL)])
CreateEnumType("sms.storage.StorageArray.BlockDeviceInterface", "BlockDeviceInterface", "sms.version.version1", ["fc", "iscsi", "fcoe", "otherBlock"])
CreateEnumType("sms.storage.StorageArray.FileSystemInterface", "FileSystemInterface", "sms.version.version1", ["nfs", "otherFileSystem"])
CreateEnumType("sms.storage.StorageArray.VasaProfile", "VasaProfile", "sms.version.version3", ["blockDevice", "fileSystem", "capability", "policy", "object", "statistics", "storageDrsBlockDevice", "storageDrsFileSystem"])
CreateDataType("sms.storage.StorageCapability", "StorageCapability", "vmodl.DynamicData", "sms.version.version2", [("uuid", "string", "sms.version.version2", 0), ("name", "string", "sms.version.version2", 0), ("description", "string", "sms.version.version2", 0)])
CreateDataType("sms.storage.StorageContainer", "StorageContainer", "vmodl.DynamicData", "sms.version.version5", [("uuid", "string", "sms.version.version5", 0), ("name", "string", "sms.version.version5", 0), ("maxVvolSizeInMB", "long", "sms.version.version5", 0), ("providerId", "string[]", "sms.version.version5", 0), ("arrayId", "string[]", "sms.version.version5", 0), ("vvolContainerType", "string", "sms.version.v8_0_0_0", F_OPTIONAL)])
CreateEnumType("sms.storage.StorageContainer.VvolContainerTypeEnum", "StorageContainerVvolContainerTypeEnum", "sms.version.v8_0_0_0", ["NFS", "NFS4x", "SCSI", "NVMe"])
CreateDataType("sms.storage.StorageContainerResult", "StorageContainerResult", "vmodl.DynamicData", "sms.version.version5", [("storageContainer", "sms.storage.StorageContainer[]", "sms.version.version5", F_OPTIONAL), ("providerInfo", "sms.provider.ProviderInfo[]", "sms.version.version5", F_OPTIONAL)])
CreateDataType("sms.storage.StorageContainerSpec", "StorageContainerSpec", "vmodl.DynamicData", "sms.version.version5", [("containerId", "string[]", "sms.version.version5", F_OPTIONAL)])
CreateDataType("sms.storage.StorageFileSystem", "StorageFileSystem", "vmodl.DynamicData", "sms.version.version2", [("uuid", "string", "sms.version.version2", 0), ("info", "sms.storage.FileSystemInfo[]", "sms.version.version2", 0), ("nativeSnapshotSupported", "boolean", "sms.version.version2", 0), ("thinProvisioningStatus", "string", "sms.version.version2", 0), ("type", "string", "sms.version.version2", 0), ("version", "string", "sms.version.version2", 0), ("backingConfig", "sms.storage.BackingConfig", "sms.version.version5", F_OPTIONAL)])
CreateEnumType("sms.storage.StorageFileSystem.FileSystemInterfaceVersion", "FileSystemInterfaceVersion", "sms.version.version1", ["NFSV3_0"])
CreateDataType("sms.storage.StorageLun", "StorageLun", "vmodl.DynamicData", "sms.version.version2", [("uuid", "string", "sms.version.version2", 0), ("vSphereLunIdentifier", "string", "sms.version.version2", 0), ("vendorDisplayName", "string", "sms.version.version2", 0), ("capacityInMB", "long", "sms.version.version2", 0), ("usedSpaceInMB", "long", "sms.version.version2", 0), ("lunThinProvisioned", "boolean", "sms.version.version2", 0), ("alternateIdentifier", "string[]", "sms.version.version2", F_OPTIONAL), ("drsManagementPermitted", "boolean", "sms.version.version2", 0), ("thinProvisioningStatus", "string", "sms.version.version2", 0), ("backingConfig", "sms.storage.BackingConfig", "sms.version.version5", F_OPTIONAL)])
CreateDataType("sms.storage.StoragePort", "StoragePort", "vmodl.DynamicData", "sms.version.version2", [("uuid", "string", "sms.version.version2", 0), ("type", "string", "sms.version.version2", 0), ("alternateName", "string[]", "sms.version.version2", F_OPTIONAL)])
CreateDataType("sms.storage.StorageProcessor", "StorageProcessor", "vmodl.DynamicData", "sms.version.version2", [("uuid", "string", "sms.version.version2", 0), ("alternateIdentifer", "string[]", "sms.version.version2", F_OPTIONAL)])
CreateEnumType("sms.storage.ThinProvisioningStatus", "ThinProvisioningStatus", "sms.version.version2", ["RED", "YELLOW", "GREEN"])
CreateDataType("sms.storage.replication.DeviceId", "DeviceId", "vmodl.DynamicData", "sms.version.version11", None)
CreateDataType("sms.storage.replication.FailoverParam", "FailoverParam", "vmodl.DynamicData", "sms.version.version11", [("isPlanned", "boolean", "sms.version.version11", 0), ("checkOnly", "boolean", "sms.version.version11", 0), ("replicationGroupsToFailover", "sms.storage.replication.FailoverParam.ReplicationGroupData[]", "sms.version.version11", F_OPTIONAL), ("policyAssociations", "sms.storage.replication.FailoverParam.PolicyAssociation[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.FailoverParam.ReplicationGroupData", "ReplicationGroupData", "vmodl.DynamicData", "sms.version.version11", [("groupId", "vim.vm.replication.ReplicationGroupId", "sms.version.version11", 0), ("pitId", "sms.storage.replication.PointInTimeReplicaId", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.FailoverParam.PolicyAssociation", "PolicyAssociation", "vmodl.DynamicData", "sms.version.version11", [("id", "sms.storage.replication.DeviceId", "sms.version.version11", 0), ("policyId", "string", "sms.version.version11", 0), ("datastore", "vim.Datastore", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.FaultDomainInfo", "FaultDomainInfo", "vim.vm.replication.FaultDomainId", "sms.version.version11", [("name", "string", "sms.version.version11", F_OPTIONAL), ("description", "string", "sms.version.version11", F_OPTIONAL), ("storageArrayId", "string", "sms.version.version11", F_OPTIONAL), ("children", "vim.vm.replication.FaultDomainId[]", "sms.version.version11", F_OPTIONAL), ("provider", "sms.provider.Provider", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.GroupInfo", "GroupInfo", "vmodl.DynamicData", "sms.version.version11", [("groupId", "vim.vm.replication.ReplicationGroupId", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.GroupOperationResult", "GroupOperationResult", "vmodl.DynamicData", "sms.version.version11", [("groupId", "vim.vm.replication.ReplicationGroupId", "sms.version.version11", 0), ("warning", "vmodl.MethodFault[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.PointInTimeReplicaId", "PointInTimeReplicaId", "vmodl.DynamicData", "sms.version.version11", [("id", "string", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.PromoteParam", "PromoteParam", "vmodl.DynamicData", "sms.version.version11", [("isPlanned", "boolean", "sms.version.version11", 0), ("replicationGroupsToPromote", "vim.vm.replication.ReplicationGroupId[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.QueryPointInTimeReplicaParam", "QueryPointInTimeReplicaParam", "vmodl.DynamicData", "sms.version.version11", [("replicaTimeQueryParam", "sms.storage.replication.QueryPointInTimeReplicaParam.ReplicaQueryIntervalParam", "sms.version.version11", F_OPTIONAL), ("pitName", "string", "sms.version.version11", F_OPTIONAL), ("tags", "string[]", "sms.version.version11", F_OPTIONAL), ("preferDetails", "boolean", "sms.version.v7_0_0_1", F_OPTIONAL)])
CreateDataType("sms.storage.replication.QueryPointInTimeReplicaParam.ReplicaQueryIntervalParam", "ReplicaQueryIntervalParam", "vmodl.DynamicData", "sms.version.version11", [("fromDate", "vmodl.DateTime", "sms.version.version11", F_OPTIONAL), ("toDate", "vmodl.DateTime", "sms.version.version11", F_OPTIONAL), ("number", "int", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.QueryPointInTimeReplicaSuccessResult", "QueryPointInTimeReplicaSuccessResult", "sms.storage.replication.GroupOperationResult", "sms.version.version11", [("replicaInfo", "sms.storage.replication.QueryPointInTimeReplicaSuccessResult.PointInTimeReplicaInfo[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.QueryPointInTimeReplicaSuccessResult.PointInTimeReplicaInfo", "PointInTimeReplicaInfo", "vmodl.DynamicData", "sms.version.version11", [("id", "sms.storage.replication.PointInTimeReplicaId", "sms.version.version11", 0), ("pitName", "string", "sms.version.version11", 0), ("timeStamp", "vmodl.DateTime", "sms.version.version11", 0), ("tags", "string[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.QueryPointInTimeReplicaSummaryResult", "QueryPointInTimeReplicaSummaryResult", "sms.storage.replication.GroupOperationResult", "sms.version.version11", [("intervalResults", "sms.storage.replication.QueryPointInTimeReplicaSummaryResult.ReplicaIntervalQueryResult[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.QueryPointInTimeReplicaSummaryResult.ReplicaIntervalQueryResult", "ReplicaIntervalQueryResult", "vmodl.DynamicData", "sms.version.version11", [("fromDate", "vmodl.DateTime", "sms.version.version11", 0), ("toDate", "vmodl.DateTime", "sms.version.version11", 0), ("number", "int", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.QueryReplicationGroupSuccessResult", "QueryReplicationGroupSuccessResult", "sms.storage.replication.GroupOperationResult", "sms.version.version11", [("rgInfo", "sms.storage.replication.GroupInfo", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.QueryReplicationPeerResult", "QueryReplicationPeerResult", "vmodl.DynamicData", "sms.version.version11", [("sourceDomain", "vim.vm.replication.FaultDomainId", "sms.version.version11", 0), ("targetDomain", "vim.vm.replication.FaultDomainId[]", "sms.version.version11", F_OPTIONAL), ("error", "vmodl.MethodFault[]", "sms.version.version11", F_OPTIONAL), ("warning", "vmodl.MethodFault[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.ReplicaId", "ReplicaId", "vmodl.DynamicData", "sms.version.version11", [("id", "string", "sms.version.version11", 0)])
CreateEnumType("sms.storage.replication.ReplicationState", "ReplicationReplicationState", "sms.version.version11", ["SOURCE", "TARGET", "FAILEDOVER", "INTEST", "REMOTE_FAILEDOVER"])
CreateDataType("sms.storage.replication.ReverseReplicationSuccessResult", "ReverseReplicationSuccessResult", "sms.storage.replication.GroupOperationResult", "sms.version.version11", [("newGroupId", "vim.vm.replication.DeviceGroupId", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.SourceGroupInfo", "SourceGroupInfo", "sms.storage.replication.GroupInfo", "sms.version.version11", [("name", "string", "sms.version.version11", F_OPTIONAL), ("description", "string", "sms.version.version11", F_OPTIONAL), ("state", "string", "sms.version.version11", 0), ("replica", "sms.storage.replication.SourceGroupInfo.ReplicationTargetInfo[]", "sms.version.version11", F_OPTIONAL), ("memberInfo", "sms.storage.replication.SourceGroupMemberInfo[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.SourceGroupInfo.ReplicationTargetInfo", "ReplicationTargetInfo", "vmodl.DynamicData", "sms.version.version11", [("targetGroupId", "vim.vm.replication.ReplicationGroupId", "sms.version.version11", 0), ("replicationAgreementDescription", "string", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.SourceGroupMemberInfo", "SourceGroupMemberInfo", "vmodl.DynamicData", "sms.version.version11", [("deviceId", "sms.storage.replication.DeviceId", "sms.version.version11", 0), ("targetId", "sms.storage.replication.SourceGroupMemberInfo.TargetDeviceId[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.SourceGroupMemberInfo.TargetDeviceId", "TargetDeviceId", "vmodl.DynamicData", "sms.version.version11", [("domainId", "vim.vm.replication.FaultDomainId", "sms.version.version11", 0), ("deviceId", "sms.storage.replication.ReplicaId", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.SyncReplicationGroupSuccessResult", "SyncReplicationGroupSuccessResult", "sms.storage.replication.GroupOperationResult", "sms.version.version11", [("timeStamp", "vmodl.DateTime", "sms.version.version11", 0), ("pitId", "sms.storage.replication.PointInTimeReplicaId", "sms.version.version11", F_OPTIONAL), ("pitName", "string", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.TargetGroupInfo", "TargetGroupInfo", "sms.storage.replication.GroupInfo", "sms.version.version11", [("sourceInfo", "sms.storage.replication.TargetGroupInfo.TargetToSourceInfo", "sms.version.version11", 0), ("state", "string", "sms.version.version11", 0), ("devices", "sms.storage.replication.TargetGroupMemberInfo[]", "sms.version.version11", F_OPTIONAL), ("isPromoteCapable", "boolean", "sms.version.version11", F_OPTIONAL), ("name", "string", "sms.version.v8_0_2_0", F_OPTIONAL)])
CreateDataType("sms.storage.replication.TargetGroupInfo.TargetToSourceInfo", "TargetToSourceInfo", "vmodl.DynamicData", "sms.version.version11", [("sourceGroupId", "vim.vm.replication.ReplicationGroupId", "sms.version.version11", 0), ("replicationAgreementDescription", "string", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.TargetGroupMemberInfo", "TargetGroupMemberInfo", "vmodl.DynamicData", "sms.version.version11", [("replicaId", "sms.storage.replication.ReplicaId", "sms.version.version11", 0), ("sourceId", "sms.storage.replication.DeviceId", "sms.version.version11", 0), ("targetDatastore", "vim.Datastore", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.TestFailoverParam", "TestFailoverParam", "sms.storage.replication.FailoverParam", "sms.version.version11", None)
CreateDataType("sms.storage.replication.VVolId", "VVolId", "sms.storage.replication.DeviceId", "sms.version.version11", [("id", "string", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.VirtualDiskId", "VasaVirtualDiskId", "sms.storage.replication.DeviceId", "sms.version.version11", [("diskId", "string", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.VirtualDiskKey", "VirtualDiskKey", "sms.storage.replication.DeviceId", "sms.version.version11", [("vmInstanceUUID", "string", "sms.version.version11", 0), ("deviceKey", "int", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.VirtualDiskMoId", "VirtualDiskMoId", "sms.storage.replication.DeviceId", "sms.version.version12", [("vcUuid", "string", "sms.version.version12", F_OPTIONAL), ("vmMoid", "string", "sms.version.version12", 0), ("diskKey", "string", "sms.version.version12", 0)])
CreateDataType("sms.storage.replication.VirtualMachineId", "VirtualMachineId", "sms.storage.replication.DeviceId", "sms.version.version11", None)
CreateDataType("sms.storage.replication.VirtualMachineMoId", "VirtualMachineMoId", "sms.storage.replication.VirtualMachineId", "sms.version.version12", [("vcUuid", "string", "sms.version.version12", F_OPTIONAL), ("vmMoid", "string", "sms.version.version12", 0)])
CreateDataType("sms.storage.replication.VirtualMachineUUID", "VirtualMachineUUID", "sms.storage.replication.VirtualMachineId", "sms.version.version11", [("vmInstanceUUID", "string", "sms.version.version11", 0)])
CreateDataType("sms.fault.CertificateAuthorityFault", "CertificateAuthorityFault", "sms.fault.ProviderRegistrationFault", "sms.version.version5", [("faultCode", "int", "sms.version.version5", 0)])
CreateDataType("sms.fault.CertificateNotImported", "CertificateNotImported", "sms.fault.ProviderRegistrationFault", "sms.version.version5", None)
CreateDataType("sms.fault.CertificateNotTrusted", "CertificateNotTrusted", "sms.fault.ProviderRegistrationFault", "sms.version.version2", [("certificate", "string", "sms.version.version2", 0)])
CreateDataType("sms.fault.IncorrectUsernamePassword", "IncorrectUsernamePassword", "sms.fault.ProviderRegistrationFault", "sms.version.version2", None)
CreateDataType("sms.fault.InvalidCertificate", "InvalidCertificate", "sms.fault.ProviderRegistrationFault", "sms.version.version4", [("certificate", "string", "sms.version.version4", 0)])
CreateDataType("sms.fault.InvalidUrl", "InvalidUrl", "sms.fault.ProviderRegistrationFault", "sms.version.version4", [("url", "string", "sms.version.version4", 0)])
CreateDataType("sms.fault.NoCommonProviderForAllBackings", "NoCommonProviderForAllBackings", "sms.fault.QueryExecutionFault", "sms.version.version2", None)
CreateDataType("sms.fault.ProviderNotFound", "ProviderNotFound", "sms.fault.QueryExecutionFault", "sms.version.version2", None)
CreateDataType("sms.fault.replication.AlreadyDone", "AlreadyDone", "sms.fault.replication.ReplicationFault", "sms.version.version11", None)
CreateDataType("sms.fault.replication.InvalidFunctionTarget", "InvalidFunctionTarget", "sms.fault.replication.ReplicationFault", "sms.version.version11", None)
CreateDataType("sms.fault.replication.InvalidReplicationState", "InvalidReplicationState", "sms.fault.replication.ReplicationFault", "sms.version.version11", [("desiredState", "string[]", "sms.version.version11", F_OPTIONAL), ("currentState", "string", "sms.version.version11", 0)])
CreateDataType("sms.fault.replication.NoReplicationTarget", "NoReplicationTarget", "sms.fault.replication.ReplicationFault", "sms.version.version11", None)
CreateDataType("sms.fault.replication.NoValidReplica", "NoValidReplica", "sms.fault.replication.ReplicationFault", "sms.version.version11", [("deviceId", "sms.storage.replication.DeviceId", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.fault.replication.PeerNotReachable", "PeerNotReachable", "sms.fault.replication.ReplicationFault", "sms.version.version11", None)
CreateDataType("sms.storage.FcStoragePort", "FcStoragePort", "sms.storage.StoragePort", "sms.version.version2", [("portWwn", "string", "sms.version.version2", 0), ("nodeWwn", "string", "sms.version.version2", 0)])
CreateDataType("sms.storage.FcoeStoragePort", "FcoeStoragePort", "sms.storage.StoragePort", "sms.version.version2", [("portWwn", "string", "sms.version.version2", 0), ("nodeWwn", "string", "sms.version.version2", 0)])
CreateDataType("sms.storage.IscsiStoragePort", "IscsiStoragePort", "sms.storage.StoragePort", "sms.version.version2", [("identifier", "string", "sms.version.version2", 0)])
CreateDataType("sms.storage.replication.FailoverSuccessResult", "FailoverSuccessResult", "sms.storage.replication.GroupOperationResult", "sms.version.version11", [("newState", "string", "sms.version.version11", 0), ("pitId", "sms.storage.replication.PointInTimeReplicaId", "sms.version.version11", F_OPTIONAL), ("pitIdBeforeFailover", "sms.storage.replication.PointInTimeReplicaId", "sms.version.version11", F_OPTIONAL), ("recoveredDeviceInfo", "sms.storage.replication.FailoverSuccessResult.RecoveredDevice[]", "sms.version.version11", F_OPTIONAL), ("timeStamp", "vmodl.DateTime", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.FailoverSuccessResult.RecoveredDiskInfo", "RecoveredDiskInfo", "vmodl.DynamicData", "sms.version.version11", [("deviceKey", "int", "sms.version.version11", 0), ("dsUrl", "string", "sms.version.version11", 0), ("diskPath", "string", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.FailoverSuccessResult.RecoveredDevice", "RecoveredDevice", "vmodl.DynamicData", "sms.version.version11", [("targetDeviceId", "sms.storage.replication.ReplicaId", "sms.version.version11", F_OPTIONAL), ("recoveredDeviceId", "sms.storage.replication.DeviceId", "sms.version.version11", F_OPTIONAL), ("sourceDeviceId", "sms.storage.replication.DeviceId", "sms.version.version11", 0), ("info", "string[]", "sms.version.version11", F_OPTIONAL), ("datastore", "vim.Datastore", "sms.version.version11", 0), ("recoveredDiskInfo", "sms.storage.replication.FailoverSuccessResult.RecoveredDiskInfo[]", "sms.version.version11", F_OPTIONAL), ("error", "vmodl.MethodFault", "sms.version.version11", F_OPTIONAL), ("warnings", "vmodl.MethodFault[]", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.GroupErrorResult", "GroupErrorResult", "sms.storage.replication.GroupOperationResult", "sms.version.version11", [("error", "vmodl.MethodFault[]", "sms.version.version11", 0)])
CreateDataType("sms.storage.replication.RecoveredTargetGroupMemberInfo", "RecoveredTargetGroupMemberInfo", "sms.storage.replication.TargetGroupMemberInfo", "sms.version.version11", [("recoveredDeviceId", "sms.storage.replication.DeviceId", "sms.version.version11", F_OPTIONAL)])
CreateDataType("sms.storage.replication.VirtualMachineFilePath", "VirtualMachineFilePath", "sms.storage.replication.VirtualMachineId", "sms.version.version11", [("vcUuid", "string", "sms.version.version11", F_OPTIONAL), ("dsUrl", "string", "sms.version.version11", 0), ("vmxPath", "string", "sms.version.version11", 0)])
