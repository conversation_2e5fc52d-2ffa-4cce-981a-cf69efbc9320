{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://discoveryengine.googleapis.com/", "batchPath": "batch", "canonicalName": "Discovery Engine", "description": "Discovery Engine API.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/discovery-engine/docs", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "discoveryengine:v1alpha", "kind": "discovery#restDescription", "mtlsRootUrl": "https://discoveryengine.mtls.googleapis.com/", "name": "discoveryengine", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"locations": {"methods": {"lookupWidgetConfig": {"description": "Gets the Widget Config using the uuid.", "flatPath": "v1alpha/locations/{locationsId}/lookupWidgetConfig", "httpMethod": "POST", "id": "discoveryengine.locations.lookupWidgetConfig", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location resource where lookup widget will be performed. Format: `locations/{location}`", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+location}/lookupWidgetConfig", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaLookupWidgetConfigRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaLookupWidgetConfigResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "widgetCompleteQuery": {"description": "Performs a user input completion with keyword suggestion. Similar to the CompletionService.CompleteQuery method, but a widget version that allows CompleteQuery without API Key. It supports CompleteQuery with or without JWT token.", "flatPath": "v1alpha/locations/{locationsId}/widgetCompleteQuery", "httpMethod": "POST", "id": "discoveryengine.locations.widgetCompleteQuery", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location resource where widget complete query will be performed. Format: `locations/{location}`", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+location}/widgetCompleteQuery", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetCompleteQueryRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetCompleteQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "widgetConverseConversation": {"description": "Converse a conversation with <PERSON><PERSON><PERSON>.", "flatPath": "v1alpha/locations/{locationsId}/widgetConverseConversation", "httpMethod": "POST", "id": "discoveryengine.locations.widgetConverseConversation", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location resource where widget converse conversation will be performed. Format: `locations/{location}`", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+location}/widgetConverseConversation", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetConverseConversationRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetConverseConversationResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "widgetSearch": {"description": "Performs a search. Similar to the SearchService.Search method, but a widget version that allows search without API Key. It supports search with or without JWT token.", "flatPath": "v1alpha/locations/{locationsId}/widgetSearch", "httpMethod": "POST", "id": "discoveryengine.locations.widgetSearch", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location resource where widget search will be performed. Format: `locations/{location}`", "location": "path", "pattern": "^locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+location}/widgetSearch", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetSearchRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetSearchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "projects": {"resources": {"locations": {"methods": {"estimateDataSize": {"description": "Estimates the data size to be used by a customer.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}:estimateDataSize", "httpMethod": "POST", "id": "discoveryengine.projects.locations.estimateDataSize", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. Full resource name of the Location, such as `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+location}:estimateDataSize", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"collections": {"resources": {"dataConnector": {"resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataConnector/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataConnector.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataConnector/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataConnector/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataConnector.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataConnector$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "dataStores": {"methods": {"completeQuery": {"description": "Completes the specified user input with keyword suggestions.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}:completeQuery", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.completeQuery", "parameterOrder": ["dataStore"], "parameters": {"dataStore": {"description": "Required. The parent data store resource name for which the completion is performed, such as `projects/*/locations/global/collections/default_collection/dataStores/default_data_store`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "includeTailSuggestions": {"description": "Indicates if tail suggestions should be returned if there are no suggestions that match the full query. Even if set to true, if there are suggestions that match the full query, those are returned and no tail suggestions are returned.", "location": "query", "type": "boolean"}, "query": {"description": "Required. The typeahead input used to fetch suggestions. Maximum length is 128 characters.", "location": "query", "type": "string"}, "queryModel": {"description": "Selects data model of query suggestions for serving. Currently supported values: * `document` - Using suggestions generated from user-imported documents. * `search-history` - Using suggestions generated from the past history of SearchService.Search API calls. Do not use it when there is no traffic for Search API. * `user-event` - Using suggestions generated from user-imported search events. * `document-completable` - Using suggestions taken directly from user-imported document fields marked as completable. Default values: * `document` is the default model for regular dataStores. * `search-history` is the default model for site search dataStores.", "location": "query", "type": "string"}, "userPseudoId": {"description": "A unique identifier for tracking visitors. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor logs in or out of the website. This field should NOT have a fixed value such as `unknown_visitor`. This should be the same identifier as UserEvent.user_pseudo_id and SearchRequest.user_pseudo_id. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an `INVALID_ARGUMENT` error is returned.", "location": "query", "type": "string"}}, "path": "v1alpha/{+dataStore}:completeQuery", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaCompleteQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a DataStore. DataStore is for storing Documents. To serve these documents for Search, or Recommendation use case, an Engine needs to be created separately.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.create", "parameterOrder": ["parent"], "parameters": {"createAdvancedSiteSearch": {"description": "A boolean flag indicating whether user want to directly create an advanced data store for site search. If the data store is not configured as site search (GENERIC vertical and PUBLIC_WEBSITE content_config), this flag will be ignored.", "location": "query", "type": "boolean"}, "dataStoreId": {"description": "Required. The ID to use for the DataStore, which will become the final component of the DataStore's resource name. This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length limit of 63 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, such as `projects/{project}/locations/{location}/collections/{collection}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/dataStores", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDataStore"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.collections.dataStores.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of DataStore, such as `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`. If the caller does not have permission to delete the DataStore, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the DataStore to delete does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of DataStore, such as `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`. If the caller does not have permission to access the DataStore, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the requested DataStore does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDataStore"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getDocumentProcessingConfig": {"description": "Gets a DocumentProcessingConfig.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/documentProcessingConfig", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.getDocumentProcessingConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full DocumentProcessingConfig resource name. Format: `projects/{project_number}/locations/{location_id}/collections/{collection_id}/dataStores/{data_store_id}/documentProcessingConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/documentProcessingConfig$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocumentProcessingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSiteSearchEngine": {"description": "Gets the SiteSearchEngine.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.getSiteSearchEngine", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of SiteSearchEngine, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`. If the caller does not have permission to access the [SiteSearchEngine], regardless of whether or not it exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaSiteSearchEngine"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the DataStores associated with the project.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter by solution type. For example: filter = 'solution_type:SOLUTION_TYPE_SEARCH'", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of DataStores to return. If unspecified, defaults to 10. The maximum allowed value is 50. Values above 50 will be coerced to 50. If this field is negative, an INVALID_ARGUMENT is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token ListDataStoresResponse.next_page_token, received from a previous DataStoreService.ListDataStores call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to DataStoreService.ListDataStores must match the call that provided the page token. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent branch resource name, such as `projects/{project}/locations/{location}/collections/{collection_id}`. If the caller does not have permission to list DataStores under this location, regardless of whether or not this data store exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/dataStores", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListDataStoresResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a DataStore", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.collections.dataStores.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The full resource name of the data store. Format: `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided DataStore to update. If an unsupported or unknown field is provided, an INVALID_ARGUMENT error is returned.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDataStore"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDataStore"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "trainCustomModel": {"description": "Trains a custom model.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}:trainCustomModel", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.trainCustomModel", "parameterOrder": ["dataStore"], "parameters": {"dataStore": {"description": "Required. The resource name of the Data Store, such as `projects/*/locations/global/collections/default_collection/dataStores/default_data_store`. This field is used to identify the data store where to train the models.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+dataStore}:trainCustomModel", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaTrainCustomModelRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateDocumentProcessingConfig": {"description": "Updates the DocumentProcessingConfig. DocumentProcessingConfig is a singleon resource of DataStore. It's empty when DataStore is created. The first call to this method will set up DocumentProcessingConfig.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/documentProcessingConfig", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.collections.dataStores.updateDocumentProcessingConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The full resource name of the Document Processing Config. Format: `projects/*/locations/*/collections/*/dataStores/*/documentProcessingConfig`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/documentProcessingConfig$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided DocumentProcessingConfig to update. The following are the only supported fields: * DocumentProcessingConfig.orc_config If not set, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocumentProcessingConfig"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocumentProcessingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"branches": {"resources": {"documents": {"methods": {"create": {"description": "Creates a Document.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.branches.documents.create", "parameterOrder": ["parent"], "parameters": {"documentId": {"description": "Required. The ID to use for the Document, which will become the final component of the Document.name. If the caller does not have permission to create the Document, regardless of whether or not it exists, a `PERMISSION_DENIED` error is returned. This field must be unique among all Documents with the same parent. Otherwise, an `ALREADY_EXISTS` error is returned. This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length limit of 63 characters. Otherwise, an `INVALID_ARGUMENT` error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/documents", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Document.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents/{documentsId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.collections.dataStores.branches.documents.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of Document, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document}`. If the caller does not have permission to delete the Document, regardless of whether or not it exists, a `PERMISSION_DENIED` error is returned. If the Document to delete does not exist, a `NOT_FOUND` error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/branches/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Document.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents/{documentsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.branches.documents.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of Document, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document}`. If the caller does not have permission to access the Document, regardless of whether or not it exists, a `PERMISSION_DENIED` error is returned. If the requested Document does not exist, a `NOT_FOUND` error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/branches/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Bulk import of multiple Documents. Request processing may be synchronous. Non-existing items will be created. Note: It is possible for a subset of the Documents to be successfully updated.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents:import", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.branches.documents.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent branch resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`. Requires create/update permission.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/documents:import", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportDocumentsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets a list of Documents.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.branches.documents.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of Documents to return. If unspecified, defaults to 100. The maximum allowed value is 1000. Values above 1000 will be coerced to 1000. If this field is negative, an `INVALID_ARGUMENT` error is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token ListDocumentsResponse.next_page_token, received from a previous DocumentService.ListDocuments call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to DocumentService.ListDocuments must match the call that provided the page token. Otherwise, an `INVALID_ARGUMENT` error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent branch resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`. Use `default_branch` as the branch ID, to list documents under the default branch. If the caller does not have permission to list Documents under this branch, regardless of whether or not this branch exists, a `PERMISSION_DENIED` error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/documents", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListDocumentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Document.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents/{documentsId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.collections.dataStores.branches.documents.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the Document is not found, a new Document will be created.", "location": "query", "type": "boolean"}, "name": {"description": "Immutable. The full resource name of the document. Format: `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document_id}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/branches/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "purge": {"description": "Permanently deletes all selected Documents in a branch. This process is asynchronous. Depending on the number of Documents to be deleted, this operation can take hours to complete. Before the delete operation completes, some Documents might still be returned by DocumentService.GetDocument or DocumentService.ListDocuments. To get a list of the Documents to be deleted, set PurgeDocumentsRequest.force to false.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents:purge", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.branches.documents.purge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/documents:purge", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaPurgeDocumentsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/branches/{branchesId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.branches.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/branches/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/branches/{branchesId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.branches.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "conversations": {"methods": {"converse": {"description": "Converses a conversation.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/conversations/{conversationsId}:converse", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.conversations.converse", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Conversation to get. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`. Use `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/-` to activate auto session mode, which automatically creates a new conversation inside a ConverseConversation session.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:converse", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaConverseConversationRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConverseConversationResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a Conversation. If the Conversation to create already exists, an ALREADY_EXISTS error is returned.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/conversations", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.conversations.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Full resource name of parent data store. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/conversations", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Conversation. If the Conversation to delete does not exist, a NOT_FOUND error is returned.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/conversations/{conversationsId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.collections.dataStores.conversations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Conversation to delete. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Conversation.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/conversations/{conversationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.conversations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Conversation to get. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all Conversations by their parent DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/conversations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.conversations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to apply on the list results. The supported features are: user_pseudo_id, state. Example: \"user_pseudo_id = some_id\"", "location": "query", "type": "string"}, "orderBy": {"description": "A comma-separated list of fields to order by, sorted in ascending order. Use \"desc\" after a field name for descending. Supported fields: * `update_time` * `create_time` * `conversation_name` Example: \"update_time desc\" \"create_time\"", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. If unspecified, defaults to 50. Max allowed value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListConversations` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The data store resource name. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/conversations", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListConversationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Conversation. Conversation action type cannot be changed. If the Conversation to update does not exist, a NOT_FOUND error is returned.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/conversations/{conversationsId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.collections.dataStores.conversations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Fully qualified name `project/*/locations/global/collections/{collection}/dataStore/*/conversations/*` or `project/*/locations/global/collections/{collection}/engines/*/conversations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided Conversation to update. The following are NOT supported: * Conversation.name If not set or empty, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "models": {"resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/models/{modelsId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.models.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/models/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/models/{modelsId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.models.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/models/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "schemas": {"methods": {"create": {"description": "Creates a Schema.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/schemas", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.schemas.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent data store resource name, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "schemaId": {"description": "Required. The ID to use for the Schema, which will become the final component of the Schema.name. This field should conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length limit of 63 characters.", "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/schemas", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaSchema"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Schema.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/schemas/{schemasId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.collections.dataStores.schemas.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full resource name of the schema, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/schemas/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Schema.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/schemas/{schemasId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.schemas.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full resource name of the schema, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/schemas/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaSchema"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets a list of Schemas.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/schemas", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.schemas.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of Schemas to return. The service may return fewer than this value. If unspecified, at most 100 Schemas will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous SchemaService.ListSchemas call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to SchemaService.ListSchemas must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent data store resource name, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/schemas", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListSchemasResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Schema.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/schemas/{schemasId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.collections.dataStores.schemas.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the Schema is not found, a new Schema will be created. In this situation, `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Immutable. The full resource name of the schema, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/schemas/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaSchema"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/schemas/{schemasId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.schemas.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/schemas/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/schemas/{schemasId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.schemas.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/schemas/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "servingConfigs": {"methods": {"recommend": {"description": "Makes a recommendation, which requires a contextual user event.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/servingConfigs/{servingConfigsId}:recommend", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.servingConfigs.recommend", "parameterOrder": ["servingConfig"], "parameters": {"servingConfig": {"description": "Required. Full resource name of a ServingConfig: `projects/*/locations/global/collections/*/engines/*/servingConfigs/*`, or `projects/*/locations/global/collections/*/dataStores/*/servingConfigs/*` One default serving config is created along with your recommendation engine creation. The engine ID will be used as the ID of the default serving config. For example, for Engine `projects/*/locations/global/collections/*/engines/my-engine`, you can use `projects/*/locations/global/collections/*/engines/my-engine/servingConfigs/my-engine` for your RecommendationService.Recommend requests.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+servingConfig}:recommend", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecommendRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecommendResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "Performs a search.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/servingConfigs/{servingConfigsId}:search", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.servingConfigs.search", "parameterOrder": ["servingConfig"], "parameters": {"servingConfig": {"description": "Required. The resource name of the Search serving config, such as `projects/*/locations/global/collections/default_collection/engines/*/servingConfigs/default_serving_config`, or `projects/*/locations/global/collections/default_collection/dataStores/default_data_store/servingConfigs/default_serving_config`. This field is used to identify the serving configuration name, set of models used to make the search.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+servingConfig}:search", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "siteSearchEngine": {"methods": {"batchVerifyTargetSites": {"description": "Verify target sites' ownership and validity. This API sends all the target sites under site search engine for verification.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine:batchVerifyTargetSites", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.batchVerifyTargetSites", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource shared by all TargetSites being verified. `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}:batchVerifyTargetSites", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaBatchVerifyTargetSitesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "disableAdvancedSiteSearch": {"description": "Downgrade from advanced site search to basic site search.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine:disableAdvancedSiteSearch", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.disableAdvancedSiteSearch", "parameterOrder": ["siteSearchEngine"], "parameters": {"siteSearchEngine": {"description": "Required. Full resource name of the SiteSearchEngine, such as `projects/{project}/locations/{location}/dataStores/{data_store_id}/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+siteSearchEngine}:disableAdvancedSiteSearch", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDisableAdvancedSiteSearchRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enableAdvancedSiteSearch": {"description": "Upgrade from basic site search to advanced site search.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine:enableAdvancedSiteSearch", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.enableAdvancedSiteSearch", "parameterOrder": ["siteSearchEngine"], "parameters": {"siteSearchEngine": {"description": "Required. Full resource name of the SiteSearchEngine, such as `projects/{project}/locations/{location}/dataStores/{data_store_id}/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+siteSearchEngine}:enableAdvancedSiteSearch", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaEnableAdvancedSiteSearchRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "fetchDomainVerificationStatus": {"description": "Returns list of target sites with its domain verification status. This method can only be called under data store with BASIC_SITE_SEARCH state at the moment.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine:fetchDomainVerificationStatus", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.fetchDomainVerificationStatus", "parameterOrder": ["siteSearchEngine"], "parameters": {"pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. The maximum value is 1000; values above 1000 will be coerced to 1000. If this field is negative, an INVALID_ARGUMENT error is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `FetchDomainVerificationStatus` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `FetchDomainVerificationStatus` must match the call that provided the page token.", "location": "query", "type": "string"}, "siteSearchEngine": {"description": "Required. The site search engine resource under which we fetch all the domain verification status. `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+siteSearchEngine}:fetchDomainVerificationStatus", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaFetchDomainVerificationStatusResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "recrawlUris": {"description": "Request on-demand recrawl for a list of URIs.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine:recrawlUris", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.recrawlUris", "parameterOrder": ["siteSearchEngine"], "parameters": {"siteSearchEngine": {"description": "Required. Full resource name of the SiteSearchEngine, such as `projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+siteSearchEngine}:recrawlUris", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecrawlUrisRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "targetSites": {"methods": {"batchCreate": {"description": "Creates TargetSite in a batch.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites:batchCreate", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.targetSites.batchCreate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource shared by all TargetSites being created. `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`. The parent field in the CreateBookRequest messages must either be empty or match this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/targetSites:batchCreate", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaBatchCreateTargetSitesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a TargetSite.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.targetSites.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent resource name of TargetSite, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/targetSites", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a TargetSite.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites/{targetSitesId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.targetSites.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of TargetSite, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}`. If the caller does not have permission to access the TargetSite, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the requested TargetSite does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine/targetSites/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a TargetSite.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites/{targetSitesId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.targetSites.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of TargetSite, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}`. If the caller does not have permission to access the TargetSite, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the requested TargetSite does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine/targetSites/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets a list of TargetSites.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.targetSites.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. The maximum value is 1000; values above 1000 will be coerced to 1000. If this field is negative, an INVALID_ARGUMENT error is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListTargetSites` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListTargetSites` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent site search engine resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`. If the caller does not have permission to list TargetSites under this site search engine, regardless of whether or not this branch exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/targetSites", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListTargetSitesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a TargetSite.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites/{targetSitesId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.targetSites.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The fully qualified resource name of the target site. `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}` The `target_site_id` is system-generated.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine/targetSites/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.targetSites.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine/targetSites/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.siteSearchEngine.targetSites.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+/siteSearchEngine/targetSites$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "suggestionDenyListEntries": {"methods": {"import": {"description": "Imports all SuggestionDenyListEntry for a DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/suggestionDenyListEntries:import", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.suggestionDenyListEntries.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent data store resource name for which to import denylist entries. Follows pattern projects/*/locations/*/collections/*/dataStores/*.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/suggestionDenyListEntries:import", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "purge": {"description": "Permanently deletes all SuggestionDenyListEntry for a DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/suggestionDenyListEntries:purge", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.suggestionDenyListEntries.purge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent data store resource name for which to import denylist entries. Follows pattern projects/*/locations/*/collections/*/dataStores/*.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/suggestionDenyListEntries:purge", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaPurgeSuggestionDenyListEntriesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "userEvents": {"methods": {"collect": {"description": "Writes a single user event from the browser. This uses a GET request to due to browser restriction of POST-ing to a third-party domain. This method is used only by the Discovery Engine API JavaScript pixel and Google Tag Manager. Users should not call this method directly.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/userEvents:collect", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.dataStores.userEvents.collect", "parameterOrder": ["parent"], "parameters": {"ets": {"description": "The event timestamp in milliseconds. This prevents browser caching of otherwise identical get requests. The name is abbreviated to reduce the payload bytes.", "format": "int64", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent DataStore resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "uri": {"description": "The URL including cgi-parameters but excluding the hash fragment with a length limit of 5,000 characters. This is often more useful than the referer URL, because many browsers only send the domain for third-party requests.", "location": "query", "type": "string"}, "userEvent": {"description": "Required. URL encoded UserEvent proto with a length limit of 2,000,000 characters.", "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/userEvents:collect", "response": {"$ref": "GoogleApiHttpBody"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Bulk import of User events. Request processing might be synchronous. Events that already exist are skipped. Use this method for backfilling historical user events. Operation.response is of type ImportResponse. Note that it is possible for a subset of the items to be successfully inserted. Operation.metadata is of type ImportMetadata.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/userEvents:import", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.userEvents.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent DataStore resource name, of the form `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/userEvents:import", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "purge": {"description": "Deletes permanently all user events specified by the filter provided. Depending on the number of events specified by the filter, this operation could take hours or days to complete. To test a filter, use the list command first.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/userEvents:purge", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.userEvents.purge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the catalog under which the events are created. The format is `projects/${projectId}/locations/global/collections/{$collectionId}/dataStores/${dataStoreId}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/userEvents:purge", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaPurgeUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "write": {"description": "Writes a single user event.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/dataStores/{dataStoresId}/userEvents:write", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.dataStores.userEvents.write", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent DataStore resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/userEvents:write", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaUserEvent"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaUserEvent"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "engines": {"methods": {"create": {"description": "Creates a Engine.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.engines.create", "parameterOrder": ["parent"], "parameters": {"engineId": {"description": "Required. The ID to use for the Engine, which will become the final component of the Engine's resource name. This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length limit of 63 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, such as `projects/{project}/locations/{location}/collections/{collection}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/engines", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngine"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Engine.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.collections.engines.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of Engine, such as `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`. If the caller does not have permission to delete the Engine, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the Engine to delete does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Engine.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.engines.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of Engine, such as `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngine"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the Engines associated with the project.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.engines.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter by solution type. For example: solution_type=SOLUTION_TYPE_SEARCH", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Not supported.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Not supported.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, such as `projects/{project}/locations/{location}/collections/{collection_id}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/engines", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListEnginesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an Engine", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.collections.engines.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The fully qualified resource name of the engine. This field must be a UTF-8 encoded string with a length limit of 1024 characters. Format: `projects/{project_number}/locations/{location}/collections/{collection}/engines/{engine}` engine should be 1-63 characters, and valid characters are /a-z0-9*/. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided Engine to update. If an unsupported or unknown field is provided, an INVALID_ARGUMENT error is returned.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngine"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngine"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "pause": {"description": "Pauses the training of an existing engine. Only applicable if SolutionType is SOLUTION_TYPE_RECOMMENDATION.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}:pause", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.engines.pause", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the engine to pause. Format: `projects/{project_number}/locations/{location_id}/collections/{collection_id}/engines/{engine_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:pause", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaPauseEngineRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngine"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resume": {"description": "Resumes the training of an existing engine. Only applicable if SolutionType is SOLUTION_TYPE_RECOMMENDATION.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}:resume", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.engines.resume", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the engine to resume. Format: `projects/{project_number}/locations/{location_id}/collections/{collection_id}/engines/{engine_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:resume", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaResumeEngineRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngine"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "tune": {"description": "Tunes an existing engine. Only applicable if SolutionType is SOLUTION_TYPE_RECOMMENDATION.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}:tune", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.engines.tune", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the engine to tune. Format: `projects/{project_number}/locations/{location_id}/collections/{collection_id}/engines/{engine_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:tune", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaTuneEngineRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"conversations": {"methods": {"converse": {"description": "Converses a conversation.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/conversations/{conversationsId}:converse", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.engines.conversations.converse", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Conversation to get. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`. Use `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/-` to activate auto session mode, which automatically creates a new conversation inside a ConverseConversation session.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:converse", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaConverseConversationRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConverseConversationResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a Conversation. If the Conversation to create already exists, an ALREADY_EXISTS error is returned.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/conversations", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.engines.conversations.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Full resource name of parent data store. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/conversations", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Conversation. If the Conversation to delete does not exist, a NOT_FOUND error is returned.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/conversations/{conversationsId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.collections.engines.conversations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Conversation to delete. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Conversation.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/conversations/{conversationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.engines.conversations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Conversation to get. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all Conversations by their parent DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/conversations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.engines.conversations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to apply on the list results. The supported features are: user_pseudo_id, state. Example: \"user_pseudo_id = some_id\"", "location": "query", "type": "string"}, "orderBy": {"description": "A comma-separated list of fields to order by, sorted in ascending order. Use \"desc\" after a field name for descending. Supported fields: * `update_time` * `create_time` * `conversation_name` Example: \"update_time desc\" \"create_time\"", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. If unspecified, defaults to 50. Max allowed value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListConversations` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The data store resource name. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/conversations", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListConversationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Conversation. Conversation action type cannot be changed. If the Conversation to update does not exist, a NOT_FOUND error is returned.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/conversations/{conversationsId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.collections.engines.conversations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Fully qualified name `project/*/locations/global/collections/{collection}/dataStore/*/conversations/*` or `project/*/locations/global/collections/{collection}/engines/*/conversations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided Conversation to update. The following are NOT supported: * Conversation.name If not set or empty, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.engines.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.engines.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "servingConfigs": {"methods": {"recommend": {"description": "Makes a recommendation, which requires a contextual user event.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/servingConfigs/{servingConfigsId}:recommend", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.engines.servingConfigs.recommend", "parameterOrder": ["servingConfig"], "parameters": {"servingConfig": {"description": "Required. Full resource name of a ServingConfig: `projects/*/locations/global/collections/*/engines/*/servingConfigs/*`, or `projects/*/locations/global/collections/*/dataStores/*/servingConfigs/*` One default serving config is created along with your recommendation engine creation. The engine ID will be used as the ID of the default serving config. For example, for Engine `projects/*/locations/global/collections/*/engines/my-engine`, you can use `projects/*/locations/global/collections/*/engines/my-engine/servingConfigs/my-engine` for your RecommendationService.Recommend requests.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+servingConfig}:recommend", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecommendRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecommendResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "Performs a search.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/engines/{enginesId}/servingConfigs/{servingConfigsId}:search", "httpMethod": "POST", "id": "discoveryengine.projects.locations.collections.engines.servingConfigs.search", "parameterOrder": ["servingConfig"], "parameters": {"servingConfig": {"description": "Required. The resource name of the Search serving config, such as `projects/*/locations/global/collections/default_collection/engines/*/servingConfigs/default_serving_config`, or `projects/*/locations/global/collections/default_collection/dataStores/default_data_store/servingConfigs/default_serving_config`. This field is used to identify the serving configuration name, set of models used to make the search.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/engines/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+servingConfig}:search", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/collections/{collectionsId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.collections.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/collections/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "dataStores": {"methods": {"completeQuery": {"description": "Completes the specified user input with keyword suggestions.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}:completeQuery", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.completeQuery", "parameterOrder": ["dataStore"], "parameters": {"dataStore": {"description": "Required. The parent data store resource name for which the completion is performed, such as `projects/*/locations/global/collections/default_collection/dataStores/default_data_store`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "includeTailSuggestions": {"description": "Indicates if tail suggestions should be returned if there are no suggestions that match the full query. Even if set to true, if there are suggestions that match the full query, those are returned and no tail suggestions are returned.", "location": "query", "type": "boolean"}, "query": {"description": "Required. The typeahead input used to fetch suggestions. Maximum length is 128 characters.", "location": "query", "type": "string"}, "queryModel": {"description": "Selects data model of query suggestions for serving. Currently supported values: * `document` - Using suggestions generated from user-imported documents. * `search-history` - Using suggestions generated from the past history of SearchService.Search API calls. Do not use it when there is no traffic for Search API. * `user-event` - Using suggestions generated from user-imported search events. * `document-completable` - Using suggestions taken directly from user-imported document fields marked as completable. Default values: * `document` is the default model for regular dataStores. * `search-history` is the default model for site search dataStores.", "location": "query", "type": "string"}, "userPseudoId": {"description": "A unique identifier for tracking visitors. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor logs in or out of the website. This field should NOT have a fixed value such as `unknown_visitor`. This should be the same identifier as UserEvent.user_pseudo_id and SearchRequest.user_pseudo_id. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an `INVALID_ARGUMENT` error is returned.", "location": "query", "type": "string"}}, "path": "v1alpha/{+dataStore}:completeQuery", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaCompleteQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a DataStore. DataStore is for storing Documents. To serve these documents for Search, or Recommendation use case, an Engine needs to be created separately.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.create", "parameterOrder": ["parent"], "parameters": {"createAdvancedSiteSearch": {"description": "A boolean flag indicating whether user want to directly create an advanced data store for site search. If the data store is not configured as site search (GENERIC vertical and PUBLIC_WEBSITE content_config), this flag will be ignored.", "location": "query", "type": "boolean"}, "dataStoreId": {"description": "Required. The ID to use for the DataStore, which will become the final component of the DataStore's resource name. This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length limit of 63 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, such as `projects/{project}/locations/{location}/collections/{collection}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/dataStores", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDataStore"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.dataStores.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of DataStore, such as `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`. If the caller does not have permission to delete the DataStore, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the DataStore to delete does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of DataStore, such as `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`. If the caller does not have permission to access the DataStore, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the requested DataStore does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDataStore"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getDocumentProcessingConfig": {"description": "Gets a DocumentProcessingConfig.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/documentProcessingConfig", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.getDocumentProcessingConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full DocumentProcessingConfig resource name. Format: `projects/{project_number}/locations/{location_id}/collections/{collection_id}/dataStores/{data_store_id}/documentProcessingConfig`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/documentProcessingConfig$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocumentProcessingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSiteSearchEngine": {"description": "Gets the SiteSearchEngine.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.getSiteSearchEngine", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of SiteSearchEngine, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`. If the caller does not have permission to access the [SiteSearchEngine], regardless of whether or not it exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaSiteSearchEngine"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the DataStores associated with the project.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter by solution type. For example: filter = 'solution_type:SOLUTION_TYPE_SEARCH'", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of DataStores to return. If unspecified, defaults to 10. The maximum allowed value is 50. Values above 50 will be coerced to 50. If this field is negative, an INVALID_ARGUMENT is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token ListDataStoresResponse.next_page_token, received from a previous DataStoreService.ListDataStores call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to DataStoreService.ListDataStores must match the call that provided the page token. Otherwise, an INVALID_ARGUMENT error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent branch resource name, such as `projects/{project}/locations/{location}/collections/{collection_id}`. If the caller does not have permission to list DataStores under this location, regardless of whether or not this data store exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/dataStores", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListDataStoresResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a DataStore", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.dataStores.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The full resource name of the data store. Format: `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided DataStore to update. If an unsupported or unknown field is provided, an INVALID_ARGUMENT error is returned.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDataStore"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDataStore"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateDocumentProcessingConfig": {"description": "Updates the DocumentProcessingConfig. DocumentProcessingConfig is a singleon resource of DataStore. It's empty when DataStore is created. The first call to this method will set up DocumentProcessingConfig.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/documentProcessingConfig", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.dataStores.updateDocumentProcessingConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The full resource name of the Document Processing Config. Format: `projects/*/locations/*/collections/*/dataStores/*/documentProcessingConfig`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/documentProcessingConfig$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided DocumentProcessingConfig to update. The following are the only supported fields: * DocumentProcessingConfig.orc_config If not set, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocumentProcessingConfig"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocumentProcessingConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"branches": {"resources": {"documents": {"methods": {"create": {"description": "Creates a Document.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.branches.documents.create", "parameterOrder": ["parent"], "parameters": {"documentId": {"description": "Required. The ID to use for the Document, which will become the final component of the Document.name. If the caller does not have permission to create the Document, regardless of whether or not it exists, a `PERMISSION_DENIED` error is returned. This field must be unique among all Documents with the same parent. Otherwise, an `ALREADY_EXISTS` error is returned. This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length limit of 63 characters. Otherwise, an `INVALID_ARGUMENT` error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/documents", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Document.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents/{documentsId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.dataStores.branches.documents.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of Document, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document}`. If the caller does not have permission to delete the Document, regardless of whether or not it exists, a `PERMISSION_DENIED` error is returned. If the Document to delete does not exist, a `NOT_FOUND` error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/branches/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Document.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents/{documentsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.branches.documents.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of Document, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document}`. If the caller does not have permission to access the Document, regardless of whether or not it exists, a `PERMISSION_DENIED` error is returned. If the requested Document does not exist, a `NOT_FOUND` error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/branches/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Bulk import of multiple Documents. Request processing may be synchronous. Non-existing items will be created. Note: It is possible for a subset of the Documents to be successfully updated.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents:import", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.branches.documents.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent branch resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`. Requires create/update permission.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/documents:import", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportDocumentsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets a list of Documents.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.branches.documents.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of Documents to return. If unspecified, defaults to 100. The maximum allowed value is 1000. Values above 1000 will be coerced to 1000. If this field is negative, an `INVALID_ARGUMENT` error is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token ListDocumentsResponse.next_page_token, received from a previous DocumentService.ListDocuments call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to DocumentService.ListDocuments must match the call that provided the page token. Otherwise, an `INVALID_ARGUMENT` error is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent branch resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`. Use `default_branch` as the branch ID, to list documents under the default branch. If the caller does not have permission to list Documents under this branch, regardless of whether or not this branch exists, a `PERMISSION_DENIED` error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/documents", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListDocumentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Document.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents/{documentsId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.dataStores.branches.documents.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the Document is not found, a new Document will be created.", "location": "query", "type": "boolean"}, "name": {"description": "Immutable. The full resource name of the document. Format: `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document_id}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/branches/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "purge": {"description": "Permanently deletes all selected Documents in a branch. This process is asynchronous. Depending on the number of Documents to be deleted, this operation can take hours to complete. Before the delete operation completes, some Documents might still be returned by DocumentService.GetDocument or DocumentService.ListDocuments. To get a list of the Documents to be deleted, set PurgeDocumentsRequest.force to false.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/branches/{branchesId}/documents:purge", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.branches.documents.purge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/documents:purge", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaPurgeDocumentsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/branches/{branchesId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.branches.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/branches/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/branches/{branchesId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.branches.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/branches/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "conversations": {"methods": {"converse": {"description": "Converses a conversation.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/conversations/{conversationsId}:converse", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.conversations.converse", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Conversation to get. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`. Use `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/-` to activate auto session mode, which automatically creates a new conversation inside a ConverseConversation session.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:converse", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaConverseConversationRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConverseConversationResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a Conversation. If the Conversation to create already exists, an ALREADY_EXISTS error is returned.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/conversations", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.conversations.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Full resource name of parent data store. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/conversations", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Conversation. If the Conversation to delete does not exist, a NOT_FOUND error is returned.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/conversations/{conversationsId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.dataStores.conversations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Conversation to delete. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Conversation.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/conversations/{conversationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.conversations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Conversation to get. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all Conversations by their parent DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/conversations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.conversations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to apply on the list results. The supported features are: user_pseudo_id, state. Example: \"user_pseudo_id = some_id\"", "location": "query", "type": "string"}, "orderBy": {"description": "A comma-separated list of fields to order by, sorted in ascending order. Use \"desc\" after a field name for descending. Supported fields: * `update_time` * `create_time` * `conversation_name` Example: \"update_time desc\" \"create_time\"", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. If unspecified, defaults to 50. Max allowed value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListConversations` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The data store resource name. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/conversations", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListConversationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Conversation. Conversation action type cannot be changed. If the Conversation to update does not exist, a NOT_FOUND error is returned.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/conversations/{conversationsId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.dataStores.conversations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Fully qualified name `project/*/locations/global/collections/{collection}/dataStore/*/conversations/*` or `project/*/locations/global/collections/{collection}/engines/*/conversations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Indicates which fields in the provided Conversation to update. The following are NOT supported: * Conversation.name If not set or empty, all supported fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "models": {"resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/models/{modelsId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.models.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/models/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/models/{modelsId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.models.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/models/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "schemas": {"methods": {"create": {"description": "Creates a Schema.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/schemas", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.schemas.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent data store resource name, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "schemaId": {"description": "Required. The ID to use for the Schema, which will become the final component of the Schema.name. This field should conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length limit of 63 characters.", "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/schemas", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaSchema"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Schema.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/schemas/{schemasId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.dataStores.schemas.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full resource name of the schema, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/schemas/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Schema.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/schemas/{schemasId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.schemas.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The full resource name of the schema, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/schemas/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaSchema"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets a list of Schemas.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/schemas", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.schemas.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of Schemas to return. The service may return fewer than this value. If unspecified, at most 100 Schemas will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous SchemaService.ListSchemas call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to SchemaService.ListSchemas must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent data store resource name, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/schemas", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListSchemasResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Schema.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/schemas/{schemasId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.dataStores.schemas.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the Schema is not found, a new Schema will be created. In this situation, `update_mask` is ignored.", "location": "query", "type": "boolean"}, "name": {"description": "Immutable. The full resource name of the schema, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/schemas/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaSchema"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "servingConfigs": {"methods": {"recommend": {"description": "Makes a recommendation, which requires a contextual user event.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/servingConfigs/{servingConfigsId}:recommend", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.servingConfigs.recommend", "parameterOrder": ["servingConfig"], "parameters": {"servingConfig": {"description": "Required. Full resource name of a ServingConfig: `projects/*/locations/global/collections/*/engines/*/servingConfigs/*`, or `projects/*/locations/global/collections/*/dataStores/*/servingConfigs/*` One default serving config is created along with your recommendation engine creation. The engine ID will be used as the ID of the default serving config. For example, for Engine `projects/*/locations/global/collections/*/engines/my-engine`, you can use `projects/*/locations/global/collections/*/engines/my-engine/servingConfigs/my-engine` for your RecommendationService.Recommend requests.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+servingConfig}:recommend", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecommendRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecommendResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "Performs a search.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/servingConfigs/{servingConfigsId}:search", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.servingConfigs.search", "parameterOrder": ["servingConfig"], "parameters": {"servingConfig": {"description": "Required. The resource name of the Search serving config, such as `projects/*/locations/global/collections/default_collection/engines/*/servingConfigs/default_serving_config`, or `projects/*/locations/global/collections/default_collection/dataStores/default_data_store/servingConfigs/default_serving_config`. This field is used to identify the serving configuration name, set of models used to make the search.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/servingConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+servingConfig}:search", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequest"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "siteSearchEngine": {"methods": {"disableAdvancedSiteSearch": {"description": "Downgrade from advanced site search to basic site search.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine:disableAdvancedSiteSearch", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.siteSearchEngine.disableAdvancedSiteSearch", "parameterOrder": ["siteSearchEngine"], "parameters": {"siteSearchEngine": {"description": "Required. Full resource name of the SiteSearchEngine, such as `projects/{project}/locations/{location}/dataStores/{data_store_id}/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+siteSearchEngine}:disableAdvancedSiteSearch", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaDisableAdvancedSiteSearchRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enableAdvancedSiteSearch": {"description": "Upgrade from basic site search to advanced site search.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine:enableAdvancedSiteSearch", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.siteSearchEngine.enableAdvancedSiteSearch", "parameterOrder": ["siteSearchEngine"], "parameters": {"siteSearchEngine": {"description": "Required. Full resource name of the SiteSearchEngine, such as `projects/{project}/locations/{location}/dataStores/{data_store_id}/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+siteSearchEngine}:enableAdvancedSiteSearch", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaEnableAdvancedSiteSearchRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "recrawlUris": {"description": "Request on-demand recrawl for a list of URIs.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine:recrawlUris", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.siteSearchEngine.recrawlUris", "parameterOrder": ["siteSearchEngine"], "parameters": {"siteSearchEngine": {"description": "Required. Full resource name of the SiteSearchEngine, such as `projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+siteSearchEngine}:recrawlUris", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecrawlUrisRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"targetSites": {"methods": {"batchCreate": {"description": "Creates TargetSite in a batch.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites:batchCreate", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.siteSearchEngine.targetSites.batchCreate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource shared by all TargetSites being created. `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`. The parent field in the CreateBookRequest messages must either be empty or match this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/targetSites:batchCreate", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaBatchCreateTargetSitesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a TargetSite.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.siteSearchEngine.targetSites.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent resource name of TargetSite, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/targetSites", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a TargetSite.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites/{targetSitesId}", "httpMethod": "DELETE", "id": "discoveryengine.projects.locations.dataStores.siteSearchEngine.targetSites.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of TargetSite, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}`. If the caller does not have permission to access the TargetSite, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the requested TargetSite does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine/targetSites/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a TargetSite.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites/{targetSitesId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.siteSearchEngine.targetSites.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Full resource name of TargetSite, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}`. If the caller does not have permission to access the TargetSite, regardless of whether or not it exists, a PERMISSION_DENIED error is returned. If the requested TargetSite does not exist, a NOT_FOUND error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine/targetSites/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets a list of TargetSites.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.siteSearchEngine.targetSites.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. The maximum value is 1000; values above 1000 will be coerced to 1000. If this field is negative, an INVALID_ARGUMENT error is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListTargetSites` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListTargetSites` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent site search engine resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`. If the caller does not have permission to list TargetSites under this site search engine, regardless of whether or not this branch exists, a PERMISSION_DENIED error is returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/targetSites", "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaListTargetSitesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a TargetSite.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/siteSearchEngine/targetSites/{targetSitesId}", "httpMethod": "PATCH", "id": "discoveryengine.projects.locations.dataStores.siteSearchEngine.targetSites.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The fully qualified resource name of the target site. `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}` The `target_site_id` is system-generated.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+/siteSearchEngine/targetSites/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "suggestionDenyListEntries": {"methods": {"import": {"description": "Imports all SuggestionDenyListEntry for a DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/suggestionDenyListEntries:import", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.suggestionDenyListEntries.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent data store resource name for which to import denylist entries. Follows pattern projects/*/locations/*/collections/*/dataStores/*.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/suggestionDenyListEntries:import", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "purge": {"description": "Permanently deletes all SuggestionDenyListEntry for a DataStore.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/suggestionDenyListEntries:purge", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.suggestionDenyListEntries.purge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent data store resource name for which to import denylist entries. Follows pattern projects/*/locations/*/collections/*/dataStores/*.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/.*$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/suggestionDenyListEntries:purge", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaPurgeSuggestionDenyListEntriesRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "userEvents": {"methods": {"collect": {"description": "Writes a single user event from the browser. This uses a GET request to due to browser restriction of POST-ing to a third-party domain. This method is used only by the Discovery Engine API JavaScript pixel and Google Tag Manager. Users should not call this method directly.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/userEvents:collect", "httpMethod": "GET", "id": "discoveryengine.projects.locations.dataStores.userEvents.collect", "parameterOrder": ["parent"], "parameters": {"ets": {"description": "The event timestamp in milliseconds. This prevents browser caching of otherwise identical get requests. The name is abbreviated to reduce the payload bytes.", "format": "int64", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent DataStore resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}, "uri": {"description": "The URL including cgi-parameters but excluding the hash fragment with a length limit of 5,000 characters. This is often more useful than the referer URL, because many browsers only send the domain for third-party requests.", "location": "query", "type": "string"}, "userEvent": {"description": "Required. URL encoded UserEvent proto with a length limit of 2,000,000 characters.", "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/userEvents:collect", "response": {"$ref": "GoogleApiHttpBody"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Bulk import of User events. Request processing might be synchronous. Events that already exist are skipped. Use this method for backfilling historical user events. Operation.response is of type ImportResponse. Note that it is possible for a subset of the items to be successfully inserted. Operation.metadata is of type ImportMetadata.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/userEvents:import", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.userEvents.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent DataStore resource name, of the form `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/userEvents:import", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "purge": {"description": "Deletes permanently all user events specified by the filter provided. Depending on the number of events specified by the filter, this operation could take hours or days to complete. To test a filter, use the list command first.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/userEvents:purge", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.userEvents.purge", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the catalog under which the events are created. The format is `projects/${projectId}/locations/global/collections/{$collectionId}/dataStores/${dataStoreId}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/userEvents:purge", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaPurgeUserEventsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "write": {"description": "Writes a single user event.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/dataStores/{dataStoresId}/userEvents:write", "httpMethod": "POST", "id": "discoveryengine.projects.locations.dataStores.userEvents.write", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent DataStore resource name, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/dataStores/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/userEvents:write", "request": {"$ref": "GoogleCloudDiscoveryengineV1alphaUserEvent"}, "response": {"$ref": "GoogleCloudDiscoveryengineV1alphaUserEvent"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/operations/{operationsId}", "httpMethod": "GET", "id": "discoveryengine.projects.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/operations", "httpMethod": "GET", "id": "discoveryengine.projects.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}, "revision": "20240108", "rootUrl": "https://discoveryengine.googleapis.com/", "schemas": {"GoogleApiHttpBody": {"description": "Message that represents an arbitrary HTTP body. It should only be used for payload formats that can't be represented as JSON, such as raw binary or an HTML page. This message can be used both in streaming and non-streaming API methods in the request as well as the response. It can be used as a top-level request field, which is convenient if one wants to extract parameters from either the URL or HTTP template into the request fields and also want access to the raw HTTP body. Example: message GetResourceRequest { // A unique request id. string request_id = 1; // The raw HTTP body is bound to this field. google.api.HttpBody http_body = 2; } service ResourceService { rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); } Example with streaming methods: service CaldavService { rpc GetCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); } Use of this type only changes how the request and response bodies are handled, all other features will continue to work unchanged.", "id": "GoogleApiHttpBody", "properties": {"contentType": {"description": "The HTTP Content-Type header value specifying the content type of the body.", "type": "string"}, "data": {"description": "The HTTP request/response body as raw binary.", "format": "byte", "type": "string"}, "extensions": {"description": "Application specific response metadata. Must be set in the first response for streaming APIs.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineLoggingErrorContext": {"description": "A description of the context in which an error occurred.", "id": "GoogleCloudDiscoveryengineLoggingErrorContext", "properties": {"httpRequest": {"$ref": "GoogleCloudDiscoveryengineLoggingHttpRequestContext", "description": "The HTTP request which was processed when the error was triggered."}, "reportLocation": {"$ref": "GoogleCloudDiscoveryengineLoggingSourceLocation", "description": "The location in the source code where the decision was made to report the error, usually the place where it was logged."}}, "type": "object"}, "GoogleCloudDiscoveryengineLoggingErrorLog": {"description": "An error log which is reported to the Error Reporting system.", "id": "GoogleCloudDiscoveryengineLoggingErrorLog", "properties": {"context": {"$ref": "GoogleCloudDiscoveryengineLoggingErrorContext", "description": "A description of the context in which the error occurred."}, "importPayload": {"$ref": "GoogleCloudDiscoveryengineLoggingImportErrorContext", "description": "The error payload that is populated on LRO import APIs."}, "message": {"description": "A message describing the error.", "type": "string"}, "requestPayload": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The API request payload, represented as a protocol buffer. Most API request types are supported—for example: * `type.googleapis.com/google.cloud.discoveryengine.v1alpha.DocumentService.CreateDocumentRequest` * `type.googleapis.com/google.cloud.discoveryengine.v1alpha.UserEventService.WriteUserEventRequest`", "type": "object"}, "responsePayload": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The API response payload, represented as a protocol buffer. This is used to log some \"soft errors\", where the response is valid but we consider there are some quality issues like unjoined events. The following API responses are supported, and no PII is included: * `google.cloud.discoveryengine.v1alpha.RecommendationService.Recommend` * `google.cloud.discoveryengine.v1alpha.UserEventService.WriteUserEvent` * `google.cloud.discoveryengine.v1alpha.UserEventService.CollectUserEvent`", "type": "object"}, "serviceContext": {"$ref": "GoogleCloudDiscoveryengineLoggingServiceContext", "description": "The service context in which this error has occurred."}, "status": {"$ref": "GoogleRpcStatus", "description": "The RPC status associated with the error log."}}, "type": "object"}, "GoogleCloudDiscoveryengineLoggingHttpRequestContext": {"description": "HTTP request data that is related to a reported error.", "id": "GoogleCloudDiscoveryengineLoggingHttpRequestContext", "properties": {"responseStatusCode": {"description": "The HTTP response status code for the request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDiscoveryengineLoggingImportErrorContext": {"description": "The error payload that is populated on LRO import APIs, including the following: * `google.cloud.discoveryengine.v1alpha.DocumentService.ImportDocuments` * `google.cloud.discoveryengine.v1alpha.UserEventService.ImportUserEvents`", "id": "GoogleCloudDiscoveryengineLoggingImportErrorContext", "properties": {"document": {"description": "The detailed content which caused the error on importing a document.", "type": "string"}, "gcsPath": {"description": "Google Cloud Storage file path of the import source. Can be set for batch operation error.", "type": "string"}, "lineNumber": {"description": "Line number of the content in file. Should be empty for permission or batch operation error.", "type": "string"}, "operation": {"description": "The operation resource name of the LRO.", "type": "string"}, "userEvent": {"description": "The detailed content which caused the error on importing a user event.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineLoggingServiceContext": {"description": "Describes a running service that sends errors.", "id": "GoogleCloudDiscoveryengineLoggingServiceContext", "properties": {"service": {"description": "An identifier of the service—for example, `discoveryengine.googleapis.com`.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineLoggingSourceLocation": {"description": "Indicates a location in the source code of the service for which errors are reported.", "id": "GoogleCloudDiscoveryengineLoggingSourceLocation", "properties": {"functionName": {"description": "Human-readable name of a function or method—for example, `google.cloud.discoveryengine.v1alpha.RecommendationService.Recommend`.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1CreateSchemaMetadata": {"description": "Metadata for Create Schema LRO.", "id": "GoogleCloudDiscoveryengineV1CreateSchemaMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1DeleteSchemaMetadata": {"description": "Metadata for DeleteSchema LRO.", "id": "GoogleCloudDiscoveryengineV1DeleteSchemaMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1ImportDocumentsMetadata": {"description": "Metadata related to the progress of the ImportDocuments operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1ImportDocumentsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1ImportDocumentsResponse": {"description": "Response of the ImportDocumentsRequest. If the long running operation is done, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudDiscoveryengineV1ImportDocumentsResponse", "properties": {"errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1ImportErrorConfig", "description": "Echoes the destination for the complete errors in the request if set."}, "errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1ImportErrorConfig": {"description": "Configuration of destination for Import related errors.", "id": "GoogleCloudDiscoveryengineV1ImportErrorConfig", "properties": {"gcsPrefix": {"description": "Cloud Storage prefix for import errors. This must be an empty, existing Cloud Storage directory. Import errors are written to sharded files in this directory, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1ImportSuggestionDenyListEntriesMetadata": {"description": "Metadata related to the progress of the ImportSuggestionDenyListEntries operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1ImportSuggestionDenyListEntriesMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1ImportSuggestionDenyListEntriesResponse": {"description": "Response message for CompletionService.ImportSuggestionDenyListEntries method.", "id": "GoogleCloudDiscoveryengineV1ImportSuggestionDenyListEntriesResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "failedEntriesCount": {"description": "Count of deny list entries that failed to be imported.", "format": "int64", "type": "string"}, "importedEntriesCount": {"description": "Count of deny list entries successfully imported.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1ImportUserEventsMetadata": {"description": "Metadata related to the progress of the Import operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1ImportUserEventsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1ImportUserEventsResponse": {"description": "Response of the ImportUserEventsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudDiscoveryengineV1ImportUserEventsResponse", "properties": {"errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1ImportErrorConfig", "description": "Echoes the destination for the complete errors if this field was set in the request."}, "errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "joinedEventsCount": {"description": "Count of user events imported with complete existing Documents.", "format": "int64", "type": "string"}, "unjoinedEventsCount": {"description": "Count of user events imported, but with Document information not found in the existing Branch.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1PurgeDocumentsMetadata": {"description": "Metadata related to the progress of the PurgeDocuments operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1PurgeDocumentsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were deleted successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1PurgeDocumentsResponse": {"description": "Response message for DocumentService.PurgeDocuments method. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudDiscoveryengineV1PurgeDocumentsResponse", "properties": {"purgeCount": {"description": "The total count of documents purged as a result of the operation.", "format": "int64", "type": "string"}, "purgeSample": {"description": "A sample of document names that will be deleted. Only populated if `force` is set to false. A max of 100 names will be returned and the names are chosen at random.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1PurgeSuggestionDenyListEntriesMetadata": {"description": "Metadata related to the progress of the PurgeSuggestionDenyListEntries operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1PurgeSuggestionDenyListEntriesMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1PurgeSuggestionDenyListEntriesResponse": {"description": "Response message for CompletionService.PurgeSuggestionDenyListEntries method.", "id": "GoogleCloudDiscoveryengineV1PurgeSuggestionDenyListEntriesResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "purgeCount": {"description": "Number of suggestion deny list entries purged.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1Schema": {"description": "Defines the structure and layout of a type of document data.", "id": "GoogleCloudDiscoveryengineV1Schema", "properties": {"jsonSchema": {"description": "The JSON representation of the schema.", "type": "string"}, "name": {"description": "Immutable. The full resource name of the schema, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "type": "string"}, "structSchema": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The structured representation of the schema.", "type": "object"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1UpdateSchemaMetadata": {"description": "Metadata for UpdateSchema LRO.", "id": "GoogleCloudDiscoveryengineV1UpdateSchemaMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaAdditionalParams": {"description": "AdditionalParams message for WidgetService methods for security and privacy enhancement.", "id": "GoogleCloudDiscoveryengineV1alphaAdditionalParams", "properties": {"token": {"description": "Token that used for non-human user check.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaBatchCreateTargetSiteMetadata": {"description": "Metadata related to the progress of the SiteSearchEngineService.BatchCreateTargetSites operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaBatchCreateTargetSiteMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaBatchCreateTargetSitesRequest": {"description": "Request message for SiteSearchEngineService.BatchCreateTargetSites method.", "id": "GoogleCloudDiscoveryengineV1alphaBatchCreateTargetSitesRequest", "properties": {"requests": {"description": "Required. The request message specifying the resources to create. A maximum of 20 TargetSites can be created in a batch.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaCreateTargetSiteRequest"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaBatchCreateTargetSitesResponse": {"description": "Response message for SiteSearchEngineService.BatchCreateTargetSites method.", "id": "GoogleCloudDiscoveryengineV1alphaBatchCreateTargetSitesResponse", "properties": {"targetSites": {"description": "TargetSites created.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaBatchVerifyTargetSitesRequest": {"description": "Request message for SiteSearchEngineService.BatchVerifyTargetSites method.", "id": "GoogleCloudDiscoveryengineV1alphaBatchVerifyTargetSitesRequest", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaBigQuerySource": {"description": "BigQuery source import data from.", "id": "GoogleCloudDiscoveryengineV1alphaBigQuerySource", "properties": {"dataSchema": {"description": "The schema to use when parsing the data from the source. Supported values for user event imports: * `user_event` (default): One UserEvent per row. Supported values for document imports: * `document` (default): One Document format per row. Each document must have a valid Document.id and one of Document.json_data or Document.struct_data. * `custom`: One custom data per row in arbitrary format that conforms to the defined Schema of the data store. This can only be used by Gen App Builder.", "type": "string"}, "datasetId": {"description": "Required. The BigQuery data set to copy the data from with a length limit of 1,024 characters.", "type": "string"}, "gcsStagingDir": {"description": "Intermediate Cloud Storage directory used for the import with a length limit of 2,000 characters. Can be specified if one wants to have the BigQuery export to a specific Cloud Storage directory.", "type": "string"}, "partitionDate": {"$ref": "GoogleTypeDate", "description": "BigQuery time partitioned table's _PARTITIONDATE in YYYY-MM-DD format."}, "projectId": {"description": "The project ID (can be project # or ID) that the BigQuery source is in with a length limit of 128 characters. If not specified, inherits the project ID from the parent request.", "type": "string"}, "tableId": {"description": "Required. The BigQuery table to copy the data from with a length limit of 1,024 characters.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCompleteQueryRequest": {"description": "Request message for CompletionService.CompleteQuery method.", "id": "GoogleCloudDiscoveryengineV1alphaCompleteQueryRequest", "properties": {"dataStore": {"description": "Required. The parent data store resource name for which the completion is performed, such as `projects/*/locations/global/collections/default_collection/dataStores/default_data_store`.", "type": "string"}, "includeTailSuggestions": {"description": "Indicates if tail suggestions should be returned if there are no suggestions that match the full query. Even if set to true, if there are suggestions that match the full query, those are returned and no tail suggestions are returned.", "type": "boolean"}, "query": {"description": "Required. The typeahead input used to fetch suggestions. Maximum length is 128 characters.", "type": "string"}, "queryModel": {"description": "Selects data model of query suggestions for serving. Currently supported values: * `document` - Using suggestions generated from user-imported documents. * `search-history` - Using suggestions generated from the past history of SearchService.Search API calls. Do not use it when there is no traffic for Search API. * `user-event` - Using suggestions generated from user-imported search events. * `document-completable` - Using suggestions taken directly from user-imported document fields marked as completable. Default values: * `document` is the default model for regular dataStores. * `search-history` is the default model for site search dataStores.", "type": "string"}, "userPseudoId": {"description": "A unique identifier for tracking visitors. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor logs in or out of the website. This field should NOT have a fixed value such as `unknown_visitor`. This should be the same identifier as UserEvent.user_pseudo_id and SearchRequest.user_pseudo_id. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an `INVALID_ARGUMENT` error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCompleteQueryResponse": {"description": "Response message for CompletionService.CompleteQuery method.", "id": "GoogleCloudDiscoveryengineV1alphaCompleteQueryResponse", "properties": {"querySuggestions": {"description": "Results of the matched query suggestions. The result list is ordered and the first result is a top suggestion.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaCompleteQueryResponseQuerySuggestion"}, "type": "array"}, "tailMatchTriggered": {"description": "True if the returned suggestions are all tail suggestions. For tail matching to be triggered, include_tail_suggestions in the request must be true and there must be no suggestions that match the full query.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCompleteQueryResponseQuerySuggestion": {"description": "Suggestions as search queries.", "id": "GoogleCloudDiscoveryengineV1alphaCompleteQueryResponseQuerySuggestion", "properties": {"completableFieldPaths": {"description": "The unique document field paths that serve as the source of this suggestion if it was generated from completable fields. This field is only populated for the document-completable model.", "items": {"type": "string"}, "type": "array"}, "suggestion": {"description": "The suggestion for the query.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCompletionInfo": {"description": "Detailed completion information including completion attribution token and clicked completion info.", "id": "GoogleCloudDiscoveryengineV1alphaCompletionInfo", "properties": {"selectedPosition": {"description": "End user selected CompleteQueryResponse.QuerySuggestion.suggestion position, starting from 0.", "format": "int32", "type": "integer"}, "selectedSuggestion": {"description": "End user selected CompleteQueryResponse.QuerySuggestion.suggestion.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaConversation": {"description": "External conversation proto definition.", "id": "GoogleCloudDiscoveryengineV1alphaConversation", "properties": {"endTime": {"description": "Output only. The time the conversation finished.", "format": "google-datetime", "readOnly": true, "type": "string"}, "messages": {"description": "Conversation messages.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversationMessage"}, "type": "array"}, "name": {"description": "Immutable. Fully qualified name `project/*/locations/global/collections/{collection}/dataStore/*/conversations/*` or `project/*/locations/global/collections/{collection}/engines/*/conversations/*`.", "type": "string"}, "startTime": {"description": "Output only. The time the conversation started.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "The state of the Conversation.", "enum": ["STATE_UNSPECIFIED", "IN_PROGRESS", "COMPLETED"], "enumDescriptions": ["Unknown.", "Conversation is currently open.", "Conversation has been completed."], "type": "string"}, "userPseudoId": {"description": "A unique identifier for tracking users.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaConversationContext": {"description": "Defines context of the conversation", "id": "GoogleCloudDiscoveryengineV1alphaConversationContext", "properties": {"activeDocument": {"description": "The current active document the user opened. It contains the document resource reference.", "type": "string"}, "contextDocuments": {"description": "The current list of documents the user is seeing. It contains the document resource references.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaConversationMessage": {"description": "Defines a conversation message.", "id": "GoogleCloudDiscoveryengineV1alphaConversationMessage", "properties": {"createTime": {"description": "Output only. Message creation timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "reply": {"$ref": "GoogleCloudDiscoveryengineV1alphaReply", "description": "Search reply."}, "userInput": {"$ref": "GoogleCloudDiscoveryengineV1alphaTextInput", "description": "User text input."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaConverseConversationRequest": {"description": "Request message for ConversationalSearchService.ConverseConversation method.", "id": "GoogleCloudDiscoveryengineV1alphaConverseConversationRequest", "properties": {"conversation": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation", "description": "The conversation to be used by auto session only. The name field will be ignored as we automatically assign new name for the conversation in auto session."}, "filter": {"description": "The filter syntax consists of an expression language for constructing a predicate from one or more fields of the documents being filtered. Filter expression is case-sensitive. This will be used to filter search results which may affect the summary response. If this field is unrecognizable, an `INVALID_ARGUMENT` is returned. Filtering in Vertex AI Search is done by mapping the LHS filter key to a key property defined in the Vertex AI Search backend -- this mapping is defined by the customer in their schema. For example a media customer might have a field 'name' in their schema. In this case the filter would look like this: filter --> name:'AN<PERSON>(\"king kong\")' For more information about filtering including syntax and filter operators, see [Filter](https://cloud.google.com/generative-ai-app-builder/docs/filter-search-metadata)", "type": "string"}, "name": {"description": "Required. The resource name of the Conversation to get. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/{conversation_id}`. Use `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/conversations/-` to activate auto session mode, which automatically creates a new conversation inside a ConverseConversation session.", "type": "string"}, "query": {"$ref": "GoogleCloudDiscoveryengineV1alphaTextInput", "description": "Required. Current user input."}, "safeSearch": {"description": "Whether to turn on safe search.", "type": "boolean"}, "servingConfig": {"description": "The resource name of the Serving Config to use. Format: `projects/{project_number}/locations/{location_id}/collections/{collection}/dataStores/{data_store_id}/servingConfigs/{serving_config_id}` If this is not set, the default serving config will be used.", "type": "string"}, "summarySpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpec", "description": "A specification for configuring the summary returned in the response."}, "userLabels": {"additionalProperties": {"type": "string"}, "description": "The user labels applied to a resource must meet the following requirements: * Each resource can have multiple labels, up to a maximum of 64. * Each label must be a key-value pair. * Keys have a minimum length of 1 character and a maximum length of 63 characters and cannot be empty. Values can be empty and have a maximum length of 63 characters. * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. All characters must use UTF-8 encoding, and international characters are allowed. * The key portion of a label must be unique. However, you can use the same key with multiple resources. * Keys must start with a lowercase letter or international character. See [Google Cloud Document](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements) for more details.", "type": "object"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaConverseConversationResponse": {"description": "Response message for ConversationalSearchService.ConverseConversation method.", "id": "GoogleCloudDiscoveryengineV1alphaConverseConversationResponse", "properties": {"conversation": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation", "description": "Updated conversation including the answer."}, "relatedQuestions": {"description": "Suggested related questions.", "items": {"type": "string"}, "type": "array"}, "reply": {"$ref": "GoogleCloudDiscoveryengineV1alphaReply", "description": "Answer to the current query."}, "searchResults": {"description": "Search Results.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSearchResult"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCreateDataStoreMetadata": {"description": "Metadata related to the progress of the DataStoreService.CreateDataStore operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaCreateDataStoreMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCreateEngineMetadata": {"description": "Metadata related to the progress of the EngineService.CreateEngine operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaCreateEngineMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCreateSchemaMetadata": {"description": "Metadata for Create Schema LRO.", "id": "GoogleCloudDiscoveryengineV1alphaCreateSchemaMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCreateTargetSiteMetadata": {"description": "Metadata related to the progress of the SiteSearchEngineService.CreateTargetSite operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaCreateTargetSiteMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCreateTargetSiteRequest": {"description": "Request message for SiteSearchEngineService.CreateTargetSite method.", "id": "GoogleCloudDiscoveryengineV1alphaCreateTargetSiteRequest", "properties": {"parent": {"description": "Required. Parent resource name of TargetSite, such as `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.", "type": "string"}, "targetSite": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite", "description": "Required. The TargetSite to create."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaCustomAttribute": {"description": "A custom attribute that is not explicitly modeled in a resource, e.g. UserEvent.", "id": "GoogleCloudDiscoveryengineV1alphaCustomAttribute", "properties": {"numbers": {"description": "The numerical values of this custom attribute. For example, `[2.3, 15.4]` when the key is \"lengths_cm\". Exactly one of CustomAttribute.text or CustomAttribute.numbers should be set. Otherwise, an `INVALID_ARGUMENT` error is returned.", "items": {"format": "double", "type": "number"}, "type": "array"}, "text": {"description": "The textual values of this custom attribute. For example, `[\"yellow\", \"green\"]` when the key is \"color\". Empty string is not allowed. Otherwise, an `INVALID_ARGUMENT` error is returned. Exactly one of CustomAttribute.text or CustomAttribute.numbers should be set. Otherwise, an `INVALID_ARGUMENT` error is returned.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDataStore": {"description": "DataStore captures global settings and configs at the DataStore level.", "id": "GoogleCloudDiscoveryengineV1alphaDataStore", "properties": {"contentConfig": {"description": "Immutable. The content config of the data store. If this field is unset, the server behavior defaults to ContentConfig.NO_CONTENT.", "enum": ["CONTENT_CONFIG_UNSPECIFIED", "NO_CONTENT", "CONTENT_REQUIRED", "PUBLIC_WEBSITE"], "enumDescriptions": ["Default value.", "Only contains documents without any Document.content.", "Only contains documents with Document.content.", "The data store is used for public website search."], "type": "string"}, "createTime": {"description": "Output only. Timestamp the DataStore was created at.", "format": "google-datetime", "readOnly": true, "type": "string"}, "defaultSchemaId": {"description": "Output only. The id of the default Schema asscociated to this data store.", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The data store display name. This field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}, "industryVertical": {"description": "Immutable. The industry vertical that the data store registers.", "enum": ["INDUSTRY_VERTICAL_UNSPECIFIED", "GENERIC", "MEDIA"], "enumDescriptions": ["Value used when unset.", "The generic vertical for documents that are not specific to any industry vertical.", "The media industry vertical."], "type": "string"}, "name": {"description": "Immutable. The full resource name of the data store. Format: `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "type": "string"}, "solutionTypes": {"description": "The solutions that the data store enrolls. Available solutions for each industry_vertical: * `MEDIA`: `SOLUTION_TYPE_RECOMMENDATION` and `SOLUTION_TYPE_SEARCH`. * `SITE_SEARCH`: `SOLUTION_TYPE_SEARCH` is automatically enrolled. Other solutions cannot be enrolled.", "items": {"enum": ["SOLUTION_TYPE_UNSPECIFIED", "SOLUTION_TYPE_RECOMMENDATION", "SOLUTION_TYPE_SEARCH", "SOLUTION_TYPE_CHAT"], "enumDescriptions": ["Default value.", "Used for Recommendations AI.", "Used for Discovery Search.", "Used for use cases related to the Generative AI agent."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDeleteDataStoreMetadata": {"description": "Metadata related to the progress of the DataStoreService.DeleteDataStore operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaDeleteDataStoreMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDeleteEngineMetadata": {"description": "Metadata related to the progress of the EngineService.DeleteEngine operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaDeleteEngineMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDeleteSchemaMetadata": {"description": "Metadata for DeleteSchema LRO.", "id": "GoogleCloudDiscoveryengineV1alphaDeleteSchemaMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDeleteTargetSiteMetadata": {"description": "Metadata related to the progress of the SiteSearchEngineService.DeleteTargetSite operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaDeleteTargetSiteMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDisableAdvancedSiteSearchMetadata": {"description": "Metadata related to the progress of the SiteSearchEngineService.DisableAdvancedSiteSearch operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaDisableAdvancedSiteSearchMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDisableAdvancedSiteSearchRequest": {"description": "Request message for SiteSearchEngineService.DisableAdvancedSiteSearch method.", "id": "GoogleCloudDiscoveryengineV1alphaDisableAdvancedSiteSearchRequest", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDisableAdvancedSiteSearchResponse": {"description": "Response message for SiteSearchEngineService.DisableAdvancedSiteSearch method.", "id": "GoogleCloudDiscoveryengineV1alphaDisableAdvancedSiteSearchResponse", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDocument": {"description": "Document captures all raw metadata information of items to be recommended or searched.", "id": "GoogleCloudDiscoveryengineV1alphaDocument", "properties": {"content": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocumentContent", "description": "The unstructured data linked to this document. Content must be set if this document is under a `CONTENT_REQUIRED` data store."}, "derivedStructData": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Output only. This field is OUTPUT_ONLY. It contains derived data that are not in the original input document.", "readOnly": true, "type": "object"}, "id": {"description": "Immutable. The identifier of the document. Id should conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length limit of 63 characters.", "type": "string"}, "jsonData": {"description": "The JSON string representation of the document. It should conform to the registered Schema or an `INVALID_ARGUMENT` error is thrown.", "type": "string"}, "name": {"description": "Immutable. The full resource name of the document. Format: `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document_id}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "type": "string"}, "parentDocumentId": {"description": "The identifier of the parent document. Currently supports at most two level document hierarchy. Id should conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length limit of 63 characters.", "type": "string"}, "schemaId": {"description": "The identifier of the schema located in the same data store.", "type": "string"}, "structData": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The structured JSON data for the document. It should conform to the registered Schema or an `INVALID_ARGUMENT` error is thrown.", "type": "object"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDocumentContent": {"description": "Unstructured data linked to this document.", "id": "GoogleCloudDiscoveryengineV1alphaDocumentContent", "properties": {"mimeType": {"description": "The MIME type of the content. Supported types: * `application/pdf` (PDF, only native PDFs are supported for now) * `text/html` (HTML) * `application/vnd.openxmlformats-officedocument.wordprocessingml.document` (DOCX) * `application/vnd.openxmlformats-officedocument.presentationml.presentation` (PPTX) * `text/plain` (TXT) See https://www.iana.org/assignments/media-types/media-types.xhtml.", "type": "string"}, "rawBytes": {"description": "The content represented as a stream of bytes. The maximum length is 1,000,000 bytes (1 MB / ~0.95 MiB). Note: As with all `bytes` fields, this field is represented as pure binary in Protocol Buffers and base64-encoded string in JSON. For example, `abc123!?$*&()'-=@~` should be represented as `YWJjMTIzIT8kKiYoKSctPUB+` in JSON. See https://developers.google.com/protocol-buffers/docs/proto3#json.", "format": "byte", "type": "string"}, "uri": {"description": "The URI of the content. Only Cloud Storage URIs (e.g. `gs://bucket-name/path/to/file`) are supported. The maximum file size is 2.5 MB for text-based formats, 100 MB for other formats.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDocumentInfo": {"description": "Detailed document information associated with a user event.", "id": "GoogleCloudDiscoveryengineV1alphaDocumentInfo", "properties": {"id": {"description": "The Document resource ID.", "type": "string"}, "name": {"description": "The Document resource full name, of the form: `projects/{project_id}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}/branches/{branch_id}/documents/{document_id}`", "type": "string"}, "promotionIds": {"description": "The promotion IDs associated with this Document. Currently, this field is restricted to at most one ID.", "items": {"type": "string"}, "type": "array"}, "quantity": {"description": "Quantity of the Document associated with the user event. Defaults to 1. For example, this field will be 2 if two quantities of the same Document are involved in a `add-to-cart` event. Required for events of the following event types: * `add-to-cart` * `purchase`", "format": "int32", "type": "integer"}, "uri": {"description": "The Document URI - only allowed for website data stores.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDocumentProcessingConfig": {"description": "A singleton resource of DataStore. It's empty when DataStore is created, which defaults to digital parser. The first call to DataStoreService.UpdateDocumentProcessingConfig method will initialize the config.", "id": "GoogleCloudDiscoveryengineV1alphaDocumentProcessingConfig", "properties": {"name": {"description": "Output only. The full resource name of the Document Processing Config. Format: `projects/*/locations/*/collections/*/dataStores/*/documentProcessingConfig`.", "readOnly": true, "type": "string"}, "ocrConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaOcrConfig", "description": "The OCR config. Currently it only applies to PDFs."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaDoubleList": {"description": "Double list.", "id": "GoogleCloudDiscoveryengineV1alphaDoubleList", "properties": {"values": {"description": "Double values.", "items": {"format": "double", "type": "number"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEnableAdvancedSiteSearchMetadata": {"description": "Metadata related to the progress of the SiteSearchEngineService.EnableAdvancedSiteSearch operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaEnableAdvancedSiteSearchMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEnableAdvancedSiteSearchRequest": {"description": "Request message for SiteSearchEngineService.EnableAdvancedSiteSearch method.", "id": "GoogleCloudDiscoveryengineV1alphaEnableAdvancedSiteSearchRequest", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEnableAdvancedSiteSearchResponse": {"description": "Response message for SiteSearchEngineService.EnableAdvancedSiteSearch method.", "id": "GoogleCloudDiscoveryengineV1alphaEnableAdvancedSiteSearchResponse", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngine": {"description": "Metadata that describes the training and serving parameters of an Engine.", "id": "GoogleCloudDiscoveryengineV1alphaEngine", "properties": {"chatEngineConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngineChatEngineConfig", "description": "Configurations for the Chat Engine. Only applicable if solution_type is SOLUTION_TYPE_CHAT."}, "chatEngineMetadata": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngineChatEngineMetadata", "description": "Output only. Additional information of the Chat Engine. Only applicable if solution_type is SOLUTION_TYPE_CHAT.", "readOnly": true}, "commonConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngineCommonConfig", "description": "Common config spec that specifies the metadata of the engine."}, "createTime": {"description": "Output only. Timestamp the Recommendation Engine was created at.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataStoreIds": {"description": "The data stores associated with this engine. For SOLUTION_TYPE_SEARCH and SOLUTION_TYPE_RECOMMENDATION type of engines, they can only associate with at most one data store. If solution_type is SOLUTION_TYPE_CHAT, multiple DataStores in the same Collection can be associated here. Note that when used in CreateEngineRequest, one DataStore id must be provided as the system will use it for necessary intializations.", "items": {"type": "string"}, "type": "array"}, "displayName": {"description": "Required. The display name of the engine. Should be human readable. UTF-8 encoded string with limit of 1024 characters.", "type": "string"}, "industryVertical": {"description": "The industry vertical that the engine registers. The restriction of the Engine industry vertical is based on DataStore: If unspecified, default to `GENERIC`. Vertical on Engine has to match vertical of the DataStore liniked to the engine.", "enum": ["INDUSTRY_VERTICAL_UNSPECIFIED", "GENERIC", "MEDIA"], "enumDescriptions": ["Value used when unset.", "The generic vertical for documents that are not specific to any industry vertical.", "The media industry vertical."], "type": "string"}, "mediaRecommendationEngineConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngineMediaRecommendationEngineConfig", "description": "Configurations for the Media Engine. Only applicable on the data stores with solution_type SOLUTION_TYPE_RECOMMENDATION and IndustryVertical.MEDIA vertical."}, "name": {"description": "Immutable. The fully qualified resource name of the engine. This field must be a UTF-8 encoded string with a length limit of 1024 characters. Format: `projects/{project_number}/locations/{location}/collections/{collection}/engines/{engine}` engine should be 1-63 characters, and valid characters are /a-z0-9*/. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}, "recommendationMetadata": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngineRecommendationMetadata", "description": "Output only. Additional information of a recommendation engine. Only applicable if solution_type is SOLUTION_TYPE_RECOMMENDATION.", "readOnly": true}, "searchEngineConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngineSearchEngineConfig", "description": "Configurations for the Search Engine. Only applicable if solution_type is SOLUTION_TYPE_SEARCH."}, "similarDocumentsConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngineSimilarDocumentsEngineConfig", "description": "Additional config specs for a `similar-items` engine."}, "solutionType": {"description": "Required. The solutions of the engine.", "enum": ["SOLUTION_TYPE_UNSPECIFIED", "SOLUTION_TYPE_RECOMMENDATION", "SOLUTION_TYPE_SEARCH", "SOLUTION_TYPE_CHAT"], "enumDescriptions": ["Default value.", "Used for Recommendations AI.", "Used for Discovery Search.", "Used for use cases related to the Generative AI agent."], "type": "string"}, "updateTime": {"description": "Output only. Timestamp the Recommendation Engine was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngineChatEngineConfig": {"description": "Configurations for a Chat Engine.", "id": "GoogleCloudDiscoveryengineV1alphaEngineChatEngineConfig", "properties": {"agentCreationConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngineChatEngineConfigAgentCreationConfig", "description": "The configurationt generate the Dialogflow agent that is associated to this Engine. Note that these configurations are one-time consumed by and passed to Dialogflow service. It means they cannot be retrieved using EngineService.GetEngine or EngineService.ListEngines API after engine creation."}, "dialogflowAgentToLink": {"description": "The resource name of an exist Dialogflow agent to link to this Chat Engine. Customers can either provide `agent_creation_config` to create agent or provide an agent name that links the agent with the Chat engine. Format: `projects//locations//agents/`. Note that the `dialogflow_agent_to_link` are one-time consumed by and passed to Dialogflow service. It means they cannot be retrieved using EngineService.GetEngine or EngineService.ListEngines API after engine creation. Please use ChatEngineMetadata.dialogflow_agent for actual agent association after Engine is created.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngineChatEngineConfigAgentCreationConfig": {"description": "Configurations for generating a Dialogflow agent. Note that these configurations are one-time consumed by and passed to Dialogflow service. It means they cannot be retrieved using EngineService.GetEngine or EngineService.ListEngines API after engine creation.", "id": "GoogleCloudDiscoveryengineV1alphaEngineChatEngineConfigAgentCreationConfig", "properties": {"business": {"description": "Name of the company, organization or other entity that the agent represents. Used for knowledge connector LLM prompt and for knowledge search.", "type": "string"}, "defaultLanguageCode": {"description": "Required. The default language of the agent as a language tag. See [Language Support](https://cloud.google.com/dialogflow/docs/reference/language) for a list of the currently supported language codes.", "type": "string"}, "location": {"description": "Agent location for Agent creation, supported values: global/us/eu. If not provided, us Engine will create Agent using us-central-1 by default; eu Engine will create Agent using eu-west-1 by default.", "type": "string"}, "timeZone": {"description": "Required. The time zone of the agent from the [time zone database](https://www.iana.org/time-zones), e.g., America/New_York, Europe/Paris.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngineChatEngineMetadata": {"description": "Additional information of a Chat Engine. Fields in this message are output only.", "id": "GoogleCloudDiscoveryengineV1alphaEngineChatEngineMetadata", "properties": {"dialogflowAgent": {"description": "The resource name of a Dialogflow agent, that this Chat Engine refers to. Format: `projects//locations//agents/`.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngineCommonConfig": {"description": "Common configurations for an Engine.", "id": "GoogleCloudDiscoveryengineV1alphaEngineCommonConfig", "properties": {"companyName": {"description": "The name of the company, business or entity that is associated with the engine. Setting this may help improve LLM related features.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngineMediaRecommendationEngineConfig": {"description": "Additional config specs for a Media Recommendation engine.", "id": "GoogleCloudDiscoveryengineV1alphaEngineMediaRecommendationEngineConfig", "properties": {"optimizationObjective": {"description": "The optimization objective e.g. `cvr`. This field together with optimization_objective describe engine metadata to use to control engine training and serving. Currently supported values: `ctr`, `cvr`. If not specified, we choose default based on engine type. Default depends on type of recommendation: `recommended-for-you` => `ctr` `others-you-may-like` => `ctr`", "type": "string"}, "optimizationObjectiveConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngineMediaRecommendationEngineConfigOptimizationObjectiveConfig", "description": "Name and value of the custom threshold for cvr optimization_objective. For target_field `watch-time`, target_field_value must be an integer value indicating the media progress time in seconds between (0, 86400] (excludes 0, includes 86400) (e.g., 90). For target_field `watch-percentage`, the target_field_value must be a valid float value between (0, 1.0] (excludes 0, includes 1.0) (e.g., 0.5)."}, "trainingState": {"description": "The training state that the engine is in (e.g. `TRAINING` or `PAUSED`). Since part of the cost of running the service is frequency of training - this can be used to determine when to train engine in order to control cost. If not specified: the default value for `CreateEngine` method is `TRAINING`. The default value for `UpdateEngine` method is to keep the state the same as before.", "enum": ["TRAINING_STATE_UNSPECIFIED", "PAUSED", "TRAINING"], "enumDescriptions": ["Unspecified training state.", "The engine training is paused.", "The engine is training."], "type": "string"}, "type": {"description": "Required. The type of engine e.g. `recommended-for-you`. This field together with optimization_objective describe engine metadata to use to control engine training and serving. Currently supported values: `recommended-for-you`, `others-you-may-like`, `more-like-this`, `most-popular-items`.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngineMediaRecommendationEngineConfigOptimizationObjectiveConfig": {"description": "Custom threshold for `cvr` optimization_objective.", "id": "GoogleCloudDiscoveryengineV1alphaEngineMediaRecommendationEngineConfigOptimizationObjectiveConfig", "properties": {"targetField": {"description": "Required. The name of the field to target. Currently supported values: `watch-percentage`, `watch-time`.", "type": "string"}, "targetFieldValueFloat": {"description": "Required. The threshold to be applied to the target (e.g., 0.5).", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngineRecommendationMetadata": {"description": "Additional information of a recommendation engine.", "id": "GoogleCloudDiscoveryengineV1alphaEngineRecommendationMetadata", "properties": {"dataState": {"description": "Output only. The state of data requirements for this engine: `DATA_OK` and `DATA_ERROR`. Engine cannot be trained if the data is in `DATA_ERROR` state. Engine can have `DATA_ERROR` state even if serving state is `ACTIVE`: engines were trained successfully before, but cannot be refreshed because the underlying engine no longer has sufficient data for training.", "enum": ["DATA_STATE_UNSPECIFIED", "DATA_OK", "DATA_ERROR"], "enumDescriptions": ["Unspecified default value, should never be explicitly set.", "The engine has sufficient training data.", "The engine does not have sufficient training data. Error messages can be queried via Stackdriver."], "readOnly": true, "type": "string"}, "lastTuneTime": {"description": "Output only. The timestamp when the latest successful tune finished. Only applicable on Media Recommendation engines.", "format": "google-datetime", "readOnly": true, "type": "string"}, "servingState": {"description": "Output only. The serving state of the engine: `ACTIVE`, `NOT_ACTIVE`.", "enum": ["SERVING_STATE_UNSPECIFIED", "INACTIVE", "ACTIVE", "TUNED"], "enumDescriptions": ["Unspecified serving state.", "The engine is not serving.", "The engine is serving and can be queried.", "The engine is trained on tuned hyperparameters and can be queried."], "readOnly": true, "type": "string"}, "tuningOperation": {"description": "Output only. The latest tune operation id associated with the engine. Only applicable on Media Recommendation engines. If present, this operation id can be used to determine if there is an ongoing tune for this engine. To check the operation status, send the GetOperation request with this operation id in the engine resource format. If no tuning has happened for this engine, the string is empty.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngineSearchEngineConfig": {"description": "Configurations for a Search Engine.", "id": "GoogleCloudDiscoveryengineV1alphaEngineSearchEngineConfig", "properties": {"searchAddOns": {"description": "The add-on that this search engine enables.", "items": {"enum": ["SEARCH_ADD_ON_UNSPECIFIED", "SEARCH_ADD_ON_LLM"], "enumDescriptions": ["Default value when the enum is unspecified. This is invalid to use.", "Large language model add-on."], "type": "string"}, "type": "array"}, "searchTier": {"description": "The search feature tier of this engine. Different tiers might have different pricing. To learn more, please check the pricing documentation. Defaults to SearchTier.SEARCH_TIER_STANDARD if not specified.", "enum": ["SEARCH_TIER_UNSPECIFIED", "SEARCH_TIER_STANDARD", "SEARCH_TIER_ENTERPRISE"], "enumDescriptions": ["Default value when the enum is unspecified. This is invalid to use.", "Standard tier.", "Enterprise tier."], "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEngineSimilarDocumentsEngineConfig": {"description": "Additional config specs for a `similar-items` engine.", "id": "GoogleCloudDiscoveryengineV1alphaEngineSimilarDocumentsEngineConfig", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeMetadata": {"description": "Metadata related to the progress of the EstimateDataSize operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequest": {"description": "Request message for EstimateBillingService.EstimateDataSize method", "id": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequest", "properties": {"fileDataSource": {"$ref": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequestFileDataSource", "description": "Structured or unstructured data."}, "websiteDataSource": {"$ref": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequestWebsiteDataSource", "description": "Website data."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequestFileDataSource": {"description": "Data source contains files either in Cloud Storage or BigQuery.", "id": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequestFileDataSource", "properties": {"bigquerySource": {"$ref": "GoogleCloudDiscoveryengineV1alphaBigQuerySource", "description": "BigQuery input source."}, "gcsSource": {"$ref": "GoogleCloudDiscoveryengineV1alphaGcsSource", "description": "Cloud Storage location for the input content."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequestWebsiteDataSource": {"description": "Data source is a set of website patterns that we crawl to get the total number of websites.", "id": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequestWebsiteDataSource", "properties": {"estimatorUriPatterns": {"description": "Required. The URI patterns to estimate the data sizes. At most 10 patterns are allowed, otherwise an INVALID_ARGUMENT error is thrown.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequestWebsiteDataSourceEstimatorUriPattern"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequestWebsiteDataSourceEstimatorUriPattern": {"description": "URI patterns that we use to crawl.", "id": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeRequestWebsiteDataSourceEstimatorUriPattern", "properties": {"exactMatch": {"description": "Whether we infer the generated URI or use the exact provided one.", "type": "boolean"}, "exclusive": {"description": "Whether the pattern is exclusive or not. If set to true, the pattern is considered exclusive. If unset or set to false, the pattern is considered inclusive by default.", "type": "boolean"}, "providedUriPattern": {"description": "User provided URI pattern. For example, `foo.com/bar/*`.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeResponse": {"description": "Response of the EstimateDataSize request. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudDiscoveryengineV1alphaEstimateDataSizeResponse", "properties": {"dataSizeBytes": {"description": "Data size in terms of bytes.", "format": "int64", "type": "string"}, "documentCount": {"description": "Total number of documents.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaFetchDomainVerificationStatusResponse": {"description": "Response message for SiteSearchEngineService.FetchDomainVerificationStatus method.", "id": "GoogleCloudDiscoveryengineV1alphaFetchDomainVerificationStatusResponse", "properties": {"nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "targetSites": {"description": "List of TargetSites containing the site verification status.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite"}, "type": "array"}, "totalSize": {"description": "The total number of items matching the request. This will always be populated in the response.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaFieldConfig": {"description": "Configurations for fields of a schema. For example, configuring a field is indexable, or searchable.", "id": "GoogleCloudDiscoveryengineV1alphaFieldConfig", "properties": {"completableOption": {"description": "If completable_option is COMPLETABLE_ENABLED, field values are directly used and returned as suggestions for Autocomplete in CompletionService.CompleteQuery. If completable_option is unset, the server behavior defaults to COMPLETABLE_DISABLED for fields that support setting completable options, which are just `string` fields. For those fields that do not support setting completable options, the server will skip completable option setting, and setting completable_option for those fields will throw `INVALID_ARGUMENT` error.", "enum": ["COMPLETABLE_OPTION_UNSPECIFIED", "COMPLETABLE_ENABLED", "COMPLETABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Completable option enabled for a schema field.", "Completable option disabled for a schema field."], "type": "string"}, "dynamicFacetableOption": {"description": "If dynamic_facetable_option is DY<PERSON><PERSON>C_FACETABLE_ENABLED, field values are available for dynamic facet. Could only be DYNAMIC_FACETABLE_DISABLED if FieldConfig.indexable_option is INDEXABLE_DISABLED. Otherwise, an `INVALID_ARGUMENT` error will be returned. If dynamic_facetable_option is unset, the server behavior defaults to DYNAMIC_FACETABLE_DISABLED for fields that support setting dynamic facetable options. For those fields that do not support setting dynamic facetable options, such as `object` and `boolean`, the server will skip dynamic facetable option setting, and setting dynamic_facetable_option for those fields will throw `INVALID_ARGUMENT` error.", "enum": ["DYNAMIC_FACETABLE_OPTION_UNSPECIFIED", "DYNAMIC_FACETABLE_ENABLED", "DYNAMIC_FACETABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Dynamic facetable option enabled for a schema field.", "Dynamic facetable option disabled for a schema field."], "type": "string"}, "fieldPath": {"description": "Required. Field path of the schema field. For example: `title`, `description`, `release_info.release_year`.", "type": "string"}, "fieldType": {"description": "Output only. Raw type of the field.", "enum": ["FIELD_TYPE_UNSPECIFIED", "OBJECT", "STRING", "NUMBER", "INTEGER", "BOOLEAN", "GEOLOCATION"], "enumDescriptions": ["Field type is unspecified.", "Field value type is Object.", "Field value type is String.", "Field value type is Number.", "Field value type is Integer.", "Field value type is Boolean.", "Field value type is Geolocation."], "readOnly": true, "type": "string"}, "indexableOption": {"description": "If indexable_option is INDEXABLE_ENABLED, field values are indexed so that it can be filtered or faceted in SearchService.Search. If indexable_option is unset, the server behavior defaults to INDEXABLE_DISABLED for fields that support setting indexable options. For those fields that do not support setting indexable options, such as `object` and `boolean` and key properties, the server will skip indexable_option setting, and setting indexable_option for those fields will throw `INVALID_ARGUMENT` error.", "enum": ["INDEXABLE_OPTION_UNSPECIFIED", "INDEXABLE_ENABLED", "INDEXABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Indexable option enabled for a schema field.", "Indexable option disabled for a schema field."], "type": "string"}, "keyPropertyType": {"description": "Output only. Type of the key property that this field is mapped to. Empty string if this is not annotated as mapped to a key property. Example types are `title`, `description`. Full list is defined by `keyPropertyMapping` in the schema field annotation. If the schema field has a `KeyPropertyMapping` annotation, `indexable_option` and `searchable_option` of this field cannot be modified.", "readOnly": true, "type": "string"}, "recsFilterableOption": {"description": "If recs_filterable_option is FILTERABLE_ENABLED, field values are filterable by filter expression in RecommendationService.Recommend. If FILTERABLE_ENABLED but the field type is numerical, field values are not filterable by text queries in RecommendationService.Recommend. Only textual fields are supported. If recs_filterable_option is unset, the default setting is FILTERABLE_DISABLED for fields that support setting filterable options. When a field set to [FILTERABLE_DISABLED] is filtered, a warning is generated and an empty result is returned.", "enum": ["FILTERABLE_OPTION_UNSPECIFIED", "FILTERABLE_ENABLED", "FILTERABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Filterable option enabled for a schema field.", "Filterable option disabled for a schema field."], "type": "string"}, "retrievableOption": {"description": "If retrievable_option is RETRIEVABLE_ENABLED, field values are included in the search results. If retrievable_option is unset, the server behavior defaults to RETRIEVABLE_DISABLED for fields that support setting retrievable options. For those fields that do not support setting retrievable options, such as `object` and `boolean`, the server will skip retrievable option setting, and setting retrievable_option for those fields will throw `INVALID_ARGUMENT` error.", "enum": ["RETRIEVABLE_OPTION_UNSPECIFIED", "RETRIEVABLE_ENABLED", "RETRIEVABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Retrievable option enabled for a schema field.", "Retrievable option disabled for a schema field."], "type": "string"}, "searchableOption": {"description": "If searchable_option is SEARCHABLE_ENABLED, field values are searchable by text queries in SearchService.Search. If SEARCHABLE_ENABLED but field type is numerical, field values will not be searchable by text queries in SearchService.Search, as there are no text values associated to numerical fields. If searchable_option is unset, the server behavior defaults to SEARCHABLE_DISABLED for fields that support setting searchable options. Only `string` fields that have no key property mapping support setting searchable_option. For those fields that do not support setting searchable options, the server will skip searchable option setting, and setting searchable_option for those fields will throw `INVALID_ARGUMENT` error.", "enum": ["SEARCHABLE_OPTION_UNSPECIFIED", "SEARCHABLE_ENABLED", "SEARCHABLE_DISABLED"], "enumDescriptions": ["Value used when unset.", "Searchable option enabled for a schema field.", "Searchable option disabled for a schema field."], "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaGcsSource": {"description": "Cloud Storage location for input content.", "id": "GoogleCloudDiscoveryengineV1alphaGcsSource", "properties": {"dataSchema": {"description": "The schema to use when parsing the data from the source. Supported values for document imports: * `document` (default): One JSON Document per line. Each document must have a valid Document.id. * `content`: Unstructured data (e.g. PDF, HTML). Each file matched by `input_uris` becomes a document, with the ID set to the first 128 bits of SHA256(URI) encoded as a hex string. * `custom`: One custom data JSON per row in arbitrary format that conforms to the defined Schema of the data store. This can only be used by Gen App Builder. * `csv`: A CSV file with header conforming to the defined Schema of the data store. Each entry after the header is imported as a Document. This can only be used by Gen App Builder. Supported values for user even imports: * `user_event` (default): One JSON UserEvent per line.", "type": "string"}, "inputUris": {"description": "Required. Cloud Storage URIs to input files. URI can be up to 2000 characters long. URIs can match the full object path (for example, `gs://bucket/directory/object.json`) or a pattern matching one or more files, such as `gs://bucket/directory/*.json`. A request can contain at most 100 files (or 100,000 files if `data_schema` is `content`). Each file can be up to 2 GB (or 100 MB if `data_schema` is `content`).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportDocumentsMetadata": {"description": "Metadata related to the progress of the ImportDocuments operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaImportDocumentsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportDocumentsRequest": {"description": "Request message for Import methods.", "id": "GoogleCloudDiscoveryengineV1alphaImportDocumentsRequest", "properties": {"autoGenerateIds": {"description": "Whether to automatically generate IDs for the documents if absent. If set to `true`, Document.ids are automatically generated based on the hash of the payload, where IDs may not be consistent during multiple imports. In which case ReconciliationMode.FULL is highly recommended to avoid duplicate contents. If unset or set to `false`, Document.ids have to be specified using id_field, otherwise, documents without IDs fail to be imported. Only set this field when using GcsSource or BigQuerySource, and when GcsSource.data_schema or BigQuerySource.data_schema is `custom` or `csv`. Otherwise, an INVALID_ARGUMENT error is thrown.", "type": "boolean"}, "bigquerySource": {"$ref": "GoogleCloudDiscoveryengineV1alphaBigQuerySource", "description": "BigQuery input source."}, "errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportErrorConfig", "description": "The desired location of errors incurred during the Import."}, "gcsSource": {"$ref": "GoogleCloudDiscoveryengineV1alphaGcsSource", "description": "Cloud Storage location for the input content."}, "idField": {"description": "The field in the Cloud Storage and BigQuery sources that indicates the unique IDs of the documents. For GcsSource it is the key of the JSON field. For instance, `my_id` for JSON `{\"my_id\": \"some_uuid\"}`. For BigQuerySource it is the column name of the BigQuery table where the unique ids are stored. The values of the JSON field or the BigQuery column are used as the Document.ids. The JSON field or the BigQuery column must be of string type, and the values must be set as valid strings conform to [RFC-1034](https://tools.ietf.org/html/rfc1034) with 1-63 characters. Otherwise, documents without valid IDs fail to be imported. Only set this field when using GcsSource or BigQuerySource, and when GcsSource.data_schema or BigQuerySource.data_schema is `custom`. And only set this field when auto_generate_ids is unset or set as `false`. Otherwise, an INVALID_ARGUMENT error is thrown. If it is unset, a default value `_id` is used when importing from the allowed data sources.", "type": "string"}, "inlineSource": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportDocumentsRequestInlineSource", "description": "The Inline source for the input content for documents."}, "reconciliationMode": {"description": "The mode of reconciliation between existing documents and the documents to be imported. Defaults to ReconciliationMode.INCREMENTAL.", "enum": ["RECONCILIATION_MODE_UNSPECIFIED", "INCREMENTAL", "FULL"], "enumDescriptions": ["Defaults to `INCREMENTAL`.", "Inserts new documents or updates existing documents.", "Calculates diff and replaces the entire document dataset. Existing documents may be deleted if they are not present in the source location."], "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportDocumentsRequestInlineSource": {"description": "The inline source for the input config for ImportDocuments method.", "id": "GoogleCloudDiscoveryengineV1alphaImportDocumentsRequestInlineSource", "properties": {"documents": {"description": "Required. A list of documents to update/create. Each document must have a valid Document.id. Recommended max of 100 items.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportDocumentsResponse": {"description": "Response of the ImportDocumentsRequest. If the long running operation is done, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudDiscoveryengineV1alphaImportDocumentsResponse", "properties": {"errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportErrorConfig", "description": "Echoes the destination for the complete errors in the request if set."}, "errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportErrorConfig": {"description": "Configuration of destination for Import related errors.", "id": "GoogleCloudDiscoveryengineV1alphaImportErrorConfig", "properties": {"gcsPrefix": {"description": "Cloud Storage prefix for import errors. This must be an empty, existing Cloud Storage directory. Import errors are written to sharded files in this directory, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesMetadata": {"description": "Metadata related to the progress of the ImportSuggestionDenyListEntries operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesRequest": {"description": "Request message for CompletionService.ImportSuggestionDenyListEntries method.", "id": "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesRequest", "properties": {"gcsSource": {"$ref": "GoogleCloudDiscoveryengineV1alphaGcsSource", "description": "Cloud Storage location for the input content. Only 1 file can be specified that contains all entries to import. Supported values `gcs_source.schema` for autocomplete suggestion deny list entry imports: * `suggestion_deny_list` (default): One JSON [SuggestionDenyListEntry] per line."}, "inlineSource": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesRequestInlineSource", "description": "The Inline source for the input content for suggestion deny list entries."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesRequestInlineSource": {"description": "The inline source for SuggestionDenyListEntry.", "id": "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesRequestInlineSource", "properties": {"entries": {"description": "Required. A list of all denylist entries to import. Max of 1000 items.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSuggestionDenyListEntry"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesResponse": {"description": "Response message for CompletionService.ImportSuggestionDenyListEntries method.", "id": "GoogleCloudDiscoveryengineV1alphaImportSuggestionDenyListEntriesResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "failedEntriesCount": {"description": "Count of deny list entries that failed to be imported.", "format": "int64", "type": "string"}, "importedEntriesCount": {"description": "Count of deny list entries successfully imported.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportUserEventsMetadata": {"description": "Metadata related to the progress of the Import operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaImportUserEventsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportUserEventsRequest": {"description": "Request message for the ImportUserEvents request.", "id": "GoogleCloudDiscoveryengineV1alphaImportUserEventsRequest", "properties": {"bigquerySource": {"$ref": "GoogleCloudDiscoveryengineV1alphaBigQuerySource", "description": "BigQuery input source."}, "errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportErrorConfig", "description": "The desired location of errors incurred during the Import. Cannot be set for inline user event imports."}, "gcsSource": {"$ref": "GoogleCloudDiscoveryengineV1alphaGcsSource", "description": "Cloud Storage location for the input content."}, "inlineSource": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportUserEventsRequestInlineSource", "description": "The Inline source for the input content for UserEvents."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportUserEventsRequestInlineSource": {"description": "The inline source for the input config for ImportUserEvents method.", "id": "GoogleCloudDiscoveryengineV1alphaImportUserEventsRequestInlineSource", "properties": {"userEvents": {"description": "Required. A list of user events to import. Recommended max of 10k items.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaUserEvent"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaImportUserEventsResponse": {"description": "Response of the ImportUserEventsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudDiscoveryengineV1alphaImportUserEventsResponse", "properties": {"errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportErrorConfig", "description": "Echoes the destination for the complete errors if this field was set in the request."}, "errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "joinedEventsCount": {"description": "Count of user events imported with complete existing Documents.", "format": "int64", "type": "string"}, "unjoinedEventsCount": {"description": "Count of user events imported, but with Document information not found in the existing Branch.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaInterval": {"description": "A floating point interval.", "id": "GoogleCloudDiscoveryengineV1alphaInterval", "properties": {"exclusiveMaximum": {"description": "Exclusive upper bound.", "format": "double", "type": "number"}, "exclusiveMinimum": {"description": "Exclusive lower bound.", "format": "double", "type": "number"}, "maximum": {"description": "Inclusive upper bound.", "format": "double", "type": "number"}, "minimum": {"description": "Inclusive lower bound.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaListConversationsResponse": {"description": "Response for ListConversations method.", "id": "GoogleCloudDiscoveryengineV1alphaListConversationsResponse", "properties": {"conversations": {"description": "All the Conversations for a given data store.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversation"}, "type": "array"}, "nextPageToken": {"description": "Pagination token, if not returned indicates the last page.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaListDataStoresResponse": {"description": "Response message for DataStoreService.ListDataStores method.", "id": "GoogleCloudDiscoveryengineV1alphaListDataStoresResponse", "properties": {"dataStores": {"description": "All the customer's DataStores.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaDataStore"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as ListDataStoresRequest.page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaListDocumentsResponse": {"description": "Response message for DocumentService.ListDocuments method.", "id": "GoogleCloudDiscoveryengineV1alphaListDocumentsResponse", "properties": {"documents": {"description": "The Documents.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as ListDocumentsRequest.page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaListEnginesResponse": {"description": "Response message for EngineService.ListEngines method.", "id": "GoogleCloudDiscoveryengineV1alphaListEnginesResponse", "properties": {"engines": {"description": "All the customer's Engines.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaEngine"}, "type": "array"}, "nextPageToken": {"description": "Not supported.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaListSchemasResponse": {"description": "Response message for SchemaService.ListSchemas method.", "id": "GoogleCloudDiscoveryengineV1alphaListSchemasResponse", "properties": {"nextPageToken": {"description": "A token that can be sent as ListSchemasRequest.page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "schemas": {"description": "The Schemas.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSchema"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaListTargetSitesResponse": {"description": "Response message for SiteSearchEngineService.ListTargetSites method.", "id": "GoogleCloudDiscoveryengineV1alphaListTargetSitesResponse", "properties": {"nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "targetSites": {"description": "List of TargetSites.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSite"}, "type": "array"}, "totalSize": {"description": "The total number of items matching the request. This will always be populated in the response.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaLookupWidgetConfigRequest": {"description": "Request message for WidgetService.LookupWidgetConfig method.", "id": "GoogleCloudDiscoveryengineV1alphaLookupWidgetConfigRequest", "properties": {"widgetConfigId": {"description": "Required. The UUID of the Widget Config.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaLookupWidgetConfigResponse": {"description": "Response message for WidgetService.LookupWidgetConfig method.", "id": "GoogleCloudDiscoveryengineV1alphaLookupWidgetConfigResponse", "properties": {"anonymousWidgetConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetConfig", "description": "The Anonymous Widget Config associated with the UUID."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaMediaInfo": {"description": "Media-specific user event information.", "id": "GoogleCloudDiscoveryengineV1alphaMediaInfo", "properties": {"mediaProgressDuration": {"description": "The media progress time in seconds, if applicable. For example, if the end user has finished 90 seconds of a playback video, then MediaInfo.media_progress_duration.seconds should be set to 90.", "format": "google-duration", "type": "string"}, "mediaProgressPercentage": {"description": "Media progress should be computed using only the media_progress_duration relative to the media total length. This value must be between `[0, 1.0]` inclusive. If this is not a playback or the progress cannot be computed (e.g. ongoing livestream), this field should be unset.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaOcrConfig": {"description": "The OCR options for parsing documents.", "id": "GoogleCloudDiscoveryengineV1alphaOcrConfig", "properties": {"enabled": {"description": "Required. If OCR is enabled or not. OCR must be enabled for other OcrConfig options to apply. We will only perform OCR on the first 80 pages of the PDF files.", "type": "boolean"}, "enhancedDocumentElements": {"description": "Apply additional enhanced OCR processing to a list of document elements. Supported values: * `table`: advanced table parsing model.", "items": {"type": "string"}, "type": "array"}, "useNativeText": {"description": "If true, will use native text instead of OCR text on pages containing native text.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPageInfo": {"description": "Detailed page information.", "id": "GoogleCloudDiscoveryengineV1alphaPageInfo", "properties": {"pageCategory": {"description": "The most specific category associated with a category page. To represent full path of category, use '>' sign to separate different hierarchies. If '>' is part of the category name, please replace it with other character(s). Category pages include special pages such as sales or promotions. For instance, a special sale page may have the category hierarchy: `\"pageCategory\" : \"Sales > 2017 Black Friday Deals\"`. Required for `view-category-page` events. Other event types should not set this field. Otherwise, an `INVALID_ARGUMENT` error is returned.", "type": "string"}, "pageviewId": {"description": "A unique ID of a web page view. This should be kept the same for all user events triggered from the same pageview. For example, an item detail page view could trigger multiple events as the user is browsing the page. The `pageview_id` property should be kept the same for all these events so that they can be grouped together properly. When using the client side event reporting with JavaScript pixel and Google Tag Manager, this value is filled in automatically.", "type": "string"}, "referrerUri": {"description": "The referrer URL of the current page. When using the client side event reporting with JavaScript pixel and Google Tag Manager, this value is filled in automatically. However, some browser privacy restrictions may cause this field to be empty.", "type": "string"}, "uri": {"description": "Complete URL (window.location.href) of the user's current page. When using the client side event reporting with JavaScript pixel and Google Tag Manager, this value is filled in automatically. Maximum length 5,000 characters.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPanelInfo": {"description": "Detailed panel information associated with a user event.", "id": "GoogleCloudDiscoveryengineV1alphaPanelInfo", "properties": {"displayName": {"description": "The display name of the panel.", "type": "string"}, "panelId": {"description": "Required. The panel ID.", "type": "string"}, "panelPosition": {"description": "The ordered position of the panel, if shown to the user with other panels. If set, then total_panels must also be set.", "format": "int32", "type": "integer"}, "totalPanels": {"description": "The total number of panels, including this one, shown to the user. Must be set if panel_position is set.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPauseEngineRequest": {"description": "Request for pausing training of an engine.", "id": "GoogleCloudDiscoveryengineV1alphaPauseEngineRequest", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPurgeDocumentsMetadata": {"description": "Metadata related to the progress of the PurgeDocuments operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaPurgeDocumentsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were deleted successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPurgeDocumentsRequest": {"description": "Request message for DocumentService.PurgeDocuments method.", "id": "GoogleCloudDiscoveryengineV1alphaPurgeDocumentsRequest", "properties": {"filter": {"description": "Required. Filter matching documents to purge. Only currently supported value is `*` (all items).", "type": "string"}, "force": {"description": "Actually performs the purge. If `force` is set to false, return the expected purge count without deleting any documents.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPurgeDocumentsResponse": {"description": "Response message for DocumentService.PurgeDocuments method. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudDiscoveryengineV1alphaPurgeDocumentsResponse", "properties": {"purgeCount": {"description": "The total count of documents purged as a result of the operation.", "format": "int64", "type": "string"}, "purgeSample": {"description": "A sample of document names that will be deleted. Only populated if `force` is set to false. A max of 100 names will be returned and the names are chosen at random.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPurgeSuggestionDenyListEntriesMetadata": {"description": "Metadata related to the progress of the PurgeSuggestionDenyListEntries operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaPurgeSuggestionDenyListEntriesMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPurgeSuggestionDenyListEntriesRequest": {"description": "Request message for CompletionService.PurgeSuggestionDenyListEntries method.", "id": "GoogleCloudDiscoveryengineV1alphaPurgeSuggestionDenyListEntriesRequest", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPurgeSuggestionDenyListEntriesResponse": {"description": "Response message for CompletionService.PurgeSuggestionDenyListEntries method.", "id": "GoogleCloudDiscoveryengineV1alphaPurgeSuggestionDenyListEntriesResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "purgeCount": {"description": "Number of suggestion deny list entries purged.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPurgeUserEventsMetadata": {"description": "Metadata related to the progress of the PurgeUserEvents operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaPurgeUserEventsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were deleted successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPurgeUserEventsRequest": {"description": "Request message for PurgeUserEvents method.", "id": "GoogleCloudDiscoveryengineV1alphaPurgeUserEventsRequest", "properties": {"filter": {"description": "Required. The filter string to specify the events to be deleted with a length limit of 5,000 characters. The eligible fields for filtering are: * `eventType`: Double quoted UserEvent.event_type string. * `eventTime`: in ISO 8601 \"zulu\" format. * `userPseudoId`: Double quoted string. Specifying this will delete all events associated with a visitor. * `userId`: Double quoted string. Specifying this will delete all events associated with a user. Examples: * Deleting all events in a time range: `eventTime > \"2012-04-23T18:25:43.511Z\" eventTime < \"2012-04-23T18:30:43.511Z\"` * Deleting specific eventType: `eventType = \"search\"` * Deleting all events for a specific visitor: `userPseudoId = \"visitor1024\"` * Deleting all events inside a DataStore: `*` The filtering fields are assumed to have an implicit AND.", "type": "string"}, "force": {"description": "The `force` field is currently not supported. Purge user event requests will permanently delete all purgeable events. Once the development is complete: If `force` is set to false, the method will return the expected purge count without deleting any user events. This field will default to false if not included in the request.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaPurgeUserEventsResponse": {"description": "Response of the PurgeUserEventsRequest. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudDiscoveryengineV1alphaPurgeUserEventsResponse", "properties": {"purgeCount": {"description": "The total count of events purged as a result of the operation.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaRecommendRequest": {"description": "Request message for Recommend method.", "id": "GoogleCloudDiscoveryengineV1alphaRecommendRequest", "properties": {"filter": {"description": "Filter for restricting recommendation results with a length limit of 5,000 characters. Currently, only filter expressions on the `filter_tags` attribute is supported. Examples: * `(filter_tags: ANY(\"Red\", \"Blue\") OR filter_tags: ANY(\"Hot\", \"Cold\"))` * `(filter_tags: ANY(\"Red\", \"Blue\")) AND NOT (filter_tags: ANY(\"Green\"))` If `attributeFilteringSyntax` is set to true under the `params` field, then attribute-based expressions are expected instead of the above described tag-based syntax. Examples: * (launguage: ANY(\"en\", \"es\")) AND NOT (categories: ANY(\"Movie\")) * (available: true) AND (launguage: ANY(\"en\", \"es\")) OR (categories: ANY(\"Movie\")) If your filter blocks all results, the API will return generic (unfiltered) popular Documents. If you only want results strictly matching the filters, set `strictFiltering` to True in RecommendRequest.params to receive empty results instead. Note that the API will never return Documents with `storageStatus` of `EXPIRED` or `DELETED` regardless of filter choices.", "type": "string"}, "pageSize": {"description": "Maximum number of results to return. Set this property to the number of recommendation results needed. If zero, the service will choose a reasonable default. The maximum allowed value is 100. Values above 100 will be coerced to 100.", "format": "int32", "type": "integer"}, "params": {"additionalProperties": {"type": "any"}, "description": "Additional domain specific parameters for the recommendations. Allowed values: * `returnDocument`: Boolean. If set to true, the associated Document object will be returned in RecommendResponse.RecommendationResult.document. * `returnScore`: Boolean. If set to true, the recommendation 'score' corresponding to each returned Document will be set in RecommendResponse.RecommendationResult.metadata. The given 'score' indicates the probability of a Document conversion given the user's context and history. * `strictFiltering`: Boolean. True by default. If set to false, the service will return generic (unfiltered) popular Documents instead of empty if your filter blocks all recommendation results. * `diversityLevel`: String. Default empty. If set to be non-empty, then it needs to be one of: * `no-diversity` * `low-diversity` * `medium-diversity` * `high-diversity` * `auto-diversity` This gives request-level control and adjusts recommendation results based on Document category. * `attributeFilteringSyntax`: Boolean. False by default. If set to true, the `filter` field is interpreted according to the new, attribute-based syntax.", "type": "object"}, "userEvent": {"$ref": "GoogleCloudDiscoveryengineV1alphaUserEvent", "description": "Required. Context about the user, what they are looking at and what action they took to trigger the Recommend request. Note that this user event detail won't be ingested to userEvent logs. Thus, a separate userEvent write request is required for event logging. Don't set UserEvent.user_pseudo_id or UserEvent.user_info.user_id to the same fixed ID for different users. If you are trying to receive non-personalized recommendations (not recommended; this can negatively impact model performance), instead set UserEvent.user_pseudo_id to a random unique ID and leave UserEvent.user_info.user_id unset."}, "userLabels": {"additionalProperties": {"type": "string"}, "description": "The user labels applied to a resource must meet the following requirements: * Each resource can have multiple labels, up to a maximum of 64. * Each label must be a key-value pair. * Keys have a minimum length of 1 character and a maximum length of 63 characters and cannot be empty. Values can be empty and have a maximum length of 63 characters. * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. All characters must use UTF-8 encoding, and international characters are allowed. * The key portion of a label must be unique. However, you can use the same key with multiple resources. * Keys must start with a lowercase letter or international character. See [Requirements for labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements) for more details.", "type": "object"}, "validateOnly": {"description": "Use validate only mode for this recommendation query. If set to true, a fake model will be used that returns arbitrary Document IDs. Note that the validate only mode should only be used for testing the API, or if the model is not ready.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaRecommendResponse": {"description": "Response message for Recommend method.", "id": "GoogleCloudDiscoveryengineV1alphaRecommendResponse", "properties": {"attributionToken": {"description": "A unique attribution token. This should be included in the UserEvent logs resulting from this recommendation, which enables accurate attribution of recommendation model performance.", "type": "string"}, "missingIds": {"description": "IDs of documents in the request that were missing from the default Branch associated with the requested ServingConfig.", "items": {"type": "string"}, "type": "array"}, "results": {"description": "A list of recommended Documents. The order represents the ranking (from the most relevant Document to the least).", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecommendResponseRecommendationResult"}, "type": "array"}, "validateOnly": {"description": "True if RecommendRequest.validate_only was set.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaRecommendResponseRecommendationResult": {"description": "RecommendationResult represents a generic recommendation result with associated metadata.", "id": "GoogleCloudDiscoveryengineV1alphaRecommendResponseRecommendationResult", "properties": {"document": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument", "description": "Set if `returnDocument` is set to true in RecommendRequest.params."}, "id": {"description": "Resource ID of the recommended Document.", "type": "string"}, "metadata": {"additionalProperties": {"type": "any"}, "description": "Additional Document metadata / annotations. Possible values: * `score`: Recommendation score in double value. Is set if `returnScore` is set to true in RecommendRequest.params.", "type": "object"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaRecrawlUrisMetadata": {"description": "Metadata related to the progress of the SiteSearchEngineService.RecrawlUris operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaRecrawlUrisMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "invalidUris": {"description": "Unique URIs in the request that don't match any TargetSite in the DataStore, only match TargetSites that haven't been fully indexed, or match a TargetSite with type EXCLUDE.", "items": {"type": "string"}, "type": "array"}, "pendingCount": {"description": "Total number of URIs that have yet to be crawled.", "format": "int32", "type": "integer"}, "quotaExceededCount": {"description": "Total number of URIs that were rejected due to insufficient indexing resources.", "format": "int32", "type": "integer"}, "successCount": {"description": "Total number of URIs that have been crawled so far.", "format": "int32", "type": "integer"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}, "validUrisCount": {"description": "Total number of unique URIs in the request that are not in invalid_uris.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaRecrawlUrisRequest": {"description": "Request message for SiteSearchEngineService.RecrawlUris method.", "id": "GoogleCloudDiscoveryengineV1alphaRecrawlUrisRequest", "properties": {"uris": {"description": "Required. List of URIs to crawl. At most 10K URIs are supported, otherwise an INVALID_ARGUMENT error is thrown. Each URI should match at least one TargetSite in `site_search_engine`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponse": {"description": "Response message for SiteSearchEngineService.RecrawlUris method.", "id": "GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponse", "properties": {"failedUris": {"description": "URIs that were not crawled before the LRO terminated.", "items": {"type": "string"}, "type": "array"}, "failureSamples": {"description": "Details for a sample of up to 10 `failed_uris`.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponseFailureInfo"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponseFailureInfo": {"description": "Details about why a particular URI failed to be crawled. Each FailureInfo contains one FailureReason per CorpusType.", "id": "GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponseFailureInfo", "properties": {"failureReasons": {"description": "List of failure reasons by corpus type (e.g. desktop, mobile).", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponseFailureInfoFailureReason"}, "type": "array"}, "uri": {"description": "URI that failed to be crawled.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponseFailureInfoFailureReason": {"description": "Details about why crawling failed for a particular CorpusType, e.g. DESKTOP and MOBILE crawling may fail for different reasons.", "id": "GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponseFailureInfoFailureReason", "properties": {"corpusType": {"description": "DESKTOP, MO<PERSON>LE, or CORPUS_TYPE_UNSPECIFIED.", "enum": ["CORPUS_TYPE_UNSPECIFIED", "DESKTOP", "MOBILE"], "enumDescriptions": ["Default value.", "Denotes a crawling attempt for the desktop version of a page.", "Denotes a crawling attempt for the mobile version of a page."], "type": "string"}, "errorMessage": {"description": "Reason why the URI was not crawled.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaReply": {"description": "Defines a reply message to user.", "id": "GoogleCloudDiscoveryengineV1alphaReply", "properties": {"references": {"deprecated": true, "description": "References in the reply.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaReplyReference"}, "type": "array"}, "reply": {"deprecated": true, "description": "DEPRECATED: use `summary` instead. Text reply.", "type": "string"}, "summary": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummary", "description": "Summary based on search results."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaReplyReference": {"deprecated": true, "description": "Defines reference in reply.", "id": "GoogleCloudDiscoveryengineV1alphaReplyReference", "properties": {"anchorText": {"description": "Anchor text.", "type": "string"}, "end": {"description": "Anchor text end index.", "format": "int32", "type": "integer"}, "start": {"description": "Anchor text start index.", "format": "int32", "type": "integer"}, "uri": {"description": "URI link reference.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaResumeEngineRequest": {"description": "Request for resuming training of an engine.", "id": "GoogleCloudDiscoveryengineV1alphaResumeEngineRequest", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSchema": {"description": "Defines the structure and layout of a type of document data.", "id": "GoogleCloudDiscoveryengineV1alphaSchema", "properties": {"fieldConfigs": {"description": "Output only. Configurations for fields of the schema.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaFieldConfig"}, "readOnly": true, "type": "array"}, "jsonSchema": {"description": "The JSON representation of the schema.", "type": "string"}, "name": {"description": "Immutable. The full resource name of the schema, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "type": "string"}, "structSchema": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The structured representation of the schema.", "type": "object"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchInfo": {"description": "Detailed search information.", "id": "GoogleCloudDiscoveryengineV1alphaSearchInfo", "properties": {"offset": {"description": "An integer that specifies the current offset for pagination (the 0-indexed starting location, amongst the products deemed by the API as relevant). See SearchRequest.offset for definition. If this field is negative, an `INVALID_ARGUMENT` is returned. This can only be set for `search` events. Other event types should not set this field. Otherwise, an `INVALID_ARGUMENT` error is returned.", "format": "int32", "type": "integer"}, "orderBy": {"description": "The order in which products are returned, if applicable. See SearchRequest.order_by for definition and syntax. The value must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an `INVALID_ARGUMENT` error is returned. This can only be set for `search` events. Other event types should not set this field. Otherwise, an `INVALID_ARGUMENT` error is returned.", "type": "string"}, "searchQuery": {"description": "The user's search query. See SearchRequest.query for definition. The value must be a UTF-8 encoded string with a length limit of 5,000 characters. Otherwise, an `INVALID_ARGUMENT` error is returned. At least one of search_query or PageInfo.page_category is required for `search` events. Other event types should not set this field. Otherwise, an `INVALID_ARGUMENT` error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequest": {"description": "Request message for SearchService.Search method.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequest", "properties": {"boostSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestBoostSpec", "description": "Boost specification to boost certain documents. For more information on boosting, see [Boosting](https://cloud.google.com/retail/docs/boosting#boost)"}, "branch": {"description": "The branch resource name, such as `projects/*/locations/global/collections/default_collection/dataStores/default_data_store/branches/0`. Use `default_branch` as the branch ID or leave this field empty, to search documents under the default branch.", "type": "string"}, "canonicalFilter": {"description": "The default filter that is applied when a user performs a search without checking any filters on the search page. The filter applied to every search request when quality improvement such as query expansion is needed. In the case a query does not have a sufficient amount of results this filter will be used to determine whether or not to enable the query expansion flow. The original filter will still be used for the query expanded search. This field is strongly recommended to achieve high search quality. For more information about filter syntax, see SearchRequest.filter.", "type": "string"}, "contentSearchSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpec", "description": "A specification for configuring the behavior of content search."}, "embeddingSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestEmbeddingSpec", "description": "Uses the provided embedding to do additional semantic document retrieval. The retrieval is based on the dot product of SearchRequest.EmbeddingSpec.EmbeddingVector.vector and the document embedding that is provided in SearchRequest.EmbeddingSpec.EmbeddingVector.field_path. If SearchRequest.EmbeddingSpec.EmbeddingVector.field_path is not provided, it will use ServingConfig.EmbeddingConfig.field_paths."}, "facetSpecs": {"description": "Facet specifications for faceted search. If empty, no facets are returned. A maximum of 100 values are allowed. Otherwise, an `INVALID_ARGUMENT` error is returned.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestFacetSpec"}, "type": "array"}, "filter": {"description": "The filter syntax consists of an expression language for constructing a predicate from one or more fields of the documents being filtered. Filter expression is case-sensitive. If this field is unrecognizable, an `INVALID_ARGUMENT` is returned. Filtering in Vertex AI Search is done by mapping the LHS filter key to a key property defined in the Vertex AI Search backend -- this mapping is defined by the customer in their schema. For example a media customer might have a field 'name' in their schema. In this case the filter would look like this: filter --> name:'<PERSON><PERSON>(\"king kong\")' For more information about filtering including syntax and filter operators, see [Filter](https://cloud.google.com/generative-ai-app-builder/docs/filter-search-metadata)", "type": "string"}, "imageQuery": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestImageQuery", "description": "Raw image query."}, "offset": {"description": "A 0-indexed integer that specifies the current offset (that is, starting result location, amongst the Documents deemed by the API as relevant) in search results. This field is only considered if page_token is unset. If this field is negative, an `INVALID_ARGUMENT` is returned.", "format": "int32", "type": "integer"}, "orderBy": {"description": "The order in which documents are returned. Documents can be ordered by a field in an Document object. Leave it unset if ordered by relevance. `order_by` expression is case-sensitive. For more information on ordering, see [Ordering](https://cloud.google.com/retail/docs/filter-and-order#order) If this field is unrecognizable, an `INVALID_ARGUMENT` is returned.", "type": "string"}, "pageSize": {"description": "Maximum number of Documents to return. If unspecified, defaults to a reasonable value. The maximum allowed value is 100. Values above 100 are coerced to 100. If this field is negative, an `INVALID_ARGUMENT` is returned.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token received from a previous SearchService.Search call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to SearchService.Search must match the call that provided the page token. Otherwise, an `INVALID_ARGUMENT` error is returned.", "type": "string"}, "params": {"additionalProperties": {"type": "any"}, "description": "Additional search parameters. For public website search only, supported values are: * `user_country_code`: string. Default empty. If set to non-empty, results are restricted or boosted based on the location provided. Example: user_country_code: \"au\" For available codes see [Country Codes](https://developers.google.com/custom-search/docs/json_api_reference#countryCodes) * `search_type`: double. Default empty. Enables non-webpage searching depending on the value. The only valid non-default value is 1, which enables image searching. Example: search_type: 1", "type": "object"}, "query": {"description": "Raw search query.", "type": "string"}, "queryExpansionSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestQueryExpansionSpec", "description": "The query expansion specification that specifies the conditions under which query expansion occurs."}, "rankingExpression": {"description": "The ranking expression controls the customized ranking on retrieval documents. This overrides ServingConfig.ranking_expression. The ranking expression is a single function or multiple functions that are joint by \"+\". * ranking_expression = function, { \" + \", function }; Supported functions: * double * relevance_score * double * dotProduct(embedding_field_path) Function variables: `relevance_score`: pre-defined keywords, used for measure relevance between query and document. `embedding_field_path`: the document embedding field used with query embedding vector. `dotProduct`: embedding function between embedding_field_path and query embedding vector. Example ranking expression: If document has an embedding field doc_embedding, the ranking expression could be `0.5 * relevance_score + 0.3 * dotProduct(doc_embedding)`.", "type": "string"}, "safeSearch": {"description": "Whether to turn on safe search. This is only supported for website search.", "type": "boolean"}, "servingConfig": {"description": "Required. The resource name of the Search serving config, such as `projects/*/locations/global/collections/default_collection/engines/*/servingConfigs/default_serving_config`, or `projects/*/locations/global/collections/default_collection/dataStores/default_data_store/servingConfigs/default_serving_config`. This field is used to identify the serving configuration name, set of models used to make the search.", "type": "string"}, "spellCorrectionSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestSpellCorrectionSpec", "description": "The spell correction specification that specifies the mode under which spell correction takes effect."}, "userInfo": {"$ref": "GoogleCloudDiscoveryengineV1alphaUserInfo", "description": "Information about the end user. Highly recommended for analytics. UserInfo.user_agent is used to deduce `device_type` for analytics."}, "userLabels": {"additionalProperties": {"type": "string"}, "description": "The user labels applied to a resource must meet the following requirements: * Each resource can have multiple labels, up to a maximum of 64. * Each label must be a key-value pair. * Keys have a minimum length of 1 character and a maximum length of 63 characters and cannot be empty. Values can be empty and have a maximum length of 63 characters. * Keys and values can contain only lowercase letters, numeric characters, underscores, and dashes. All characters must use UTF-8 encoding, and international characters are allowed. * The key portion of a label must be unique. However, you can use the same key with multiple resources. * Keys must start with a lowercase letter or international character. See [Google Cloud Document](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements) for more details.", "type": "object"}, "userPseudoId": {"description": "A unique identifier for tracking visitors. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor logs in or out of the website. This field should NOT have a fixed value such as `unknown_visitor`. This should be the same identifier as UserEvent.user_pseudo_id and CompleteQueryRequest.user_pseudo_id The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an `INVALID_ARGUMENT` error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestBoostSpec": {"description": "Boost specification to boost certain documents.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestBoostSpec", "properties": {"conditionBoostSpecs": {"description": "Condition boost specifications. If a document matches multiple conditions in the specifictions, boost scores from these specifications are all applied and combined in a non-linear way. Maximum number of specifications is 20.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestBoostSpecConditionBoostSpec"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestBoostSpecConditionBoostSpec": {"description": "Boost applies to documents which match a condition.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestBoostSpecConditionBoostSpec", "properties": {"boost": {"description": "Strength of the condition boost, which should be in [-1, 1]. Negative boost means demotion. Default is 0.0. Setting to 1.0 gives the document a big promotion. However, it does not necessarily mean that the boosted document will be the top result at all times, nor that other documents will be excluded. Results could still be shown even when none of them matches the condition. And results that are significantly more relevant to the search query can still trump your heavily favored but irrelevant documents. Setting to -1.0 gives the document a big demotion. However, results that are deeply relevant might still be shown. The document will have an upstream battle to get a fairly high ranking, but it is not blocked out completely. Setting to 0.0 means no boost applied. The boosting condition is ignored.", "format": "float", "type": "number"}, "condition": {"description": "An expression which specifies a boost condition. The syntax and supported fields are the same as a filter expression. See SearchRequest.filter for detail syntax and limitations. Examples: * To boost documents with document ID \"doc_1\" or \"doc_2\", and color \"Red\" or \"Blue\": * (id: ANY(\"doc_1\", \"doc_2\")) AND (color: ANY(\"Red\",\"Blue\"))", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpec": {"description": "A specification for configuring the behavior of content search.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpec", "properties": {"extractiveContentSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecExtractiveContentSpec", "description": "If there is no extractive_content_spec provided, there will be no extractive answer in the search response."}, "snippetSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSnippetSpec", "description": "If `snippetSpec` is not specified, snippets are not included in the search response."}, "summarySpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpec", "description": "If `summarySpec` is not specified, summaries are not included in the search response."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecExtractiveContentSpec": {"description": "A specification for configuring the extractive content in a search response.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecExtractiveContentSpec", "properties": {"maxExtractiveAnswerCount": {"description": "The maximum number of extractive answers returned in each search result. An extractive answer is a verbatim answer extracted from the original document, which provides a precise and contextually relevant answer to the search query. If the number of matching answers is less than the `max_extractive_answer_count`, return all of the answers. Otherwise, return the `max_extractive_answer_count`. At most five answers are returned for each SearchResult.", "format": "int32", "type": "integer"}, "maxExtractiveSegmentCount": {"description": "The max number of extractive segments returned in each search result. Only applied if the DataStore is set to DataStore.ContentConfig.CONTENT_REQUIRED or DataStore.solution_types is SOLUTION_TYPE_CHAT. An extractive segment is a text segment extracted from the original document that is relevant to the search query, and, in general, more verbose than an extractive answer. The segment could then be used as input for LLMs to generate summaries and answers. If the number of matching segments is less than `max_extractive_segment_count`, return all of the segments. Otherwise, return the `max_extractive_segment_count`.", "format": "int32", "type": "integer"}, "numNextSegments": {"description": "Return at most `num_next_segments` segments after each selected segments.", "format": "int32", "type": "integer"}, "numPreviousSegments": {"description": "Specifies whether to also include the adjacent from each selected segments. Return at most `num_previous_segments` segments before each selected segments.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSnippetSpec": {"description": "A specification for configuring snippets in a search response.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSnippetSpec", "properties": {"maxSnippetCount": {"deprecated": true, "description": "[DEPRECATED] This field is deprecated. To control snippet return, use `return_snippet` field. For backwards compatibility, we will return snippet if max_snippet_count > 0.", "format": "int32", "type": "integer"}, "referenceOnly": {"deprecated": true, "description": "[DEPRECATED] This field is deprecated and will have no affect on the snippet.", "type": "boolean"}, "returnSnippet": {"description": "If `true`, then return snippet. If no snippet can be generated, we return \"No snippet is available for this page.\" A `snippet_status` with `SUCCESS` or `NO_SNIPPET_AVAILABLE` will also be returned.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpec": {"description": "A specification for configuring a summary returned in a search response.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpec", "properties": {"ignoreAdversarialQuery": {"description": "Specifies whether to filter out adversarial queries. The default value is `false`. Google employs search-query classification to detect adversarial queries. No summary is returned if the search query is classified as an adversarial query. For example, a user might ask a question regarding negative comments about the company or submit a query designed to generate unsafe, policy-violating output. If this field is set to `true`, we skip generating summaries for adversarial queries and return fallback messages instead.", "type": "boolean"}, "ignoreNonSummarySeekingQuery": {"description": "Specifies whether to filter out queries that are not summary-seeking. The default value is `false`. Google employs search-query classification to detect summary-seeking queries. No summary is returned if the search query is classified as a non-summary seeking query. For example, `why is the sky blue` and `Who is the best soccer player in the world?` are summary-seeking queries, but `SFO airport` and `world cup 2026` are not. They are most likely navigational queries. If this field is set to `true`, we skip generating summaries for non-summary seeking queries and return fallback messages instead.", "type": "boolean"}, "includeCitations": {"description": "Specifies whether to include citations in the summary. The default value is `false`. When this field is set to `true`, summaries include in-line citation numbers. Example summary including citations: BigQuery is Google Cloud's fully managed and completely serverless enterprise data warehouse [1]. BigQuery supports all data types, works across clouds, and has built-in machine learning and business intelligence, all within a unified platform [2, 3]. The citation numbers refer to the returned search results and are 1-indexed. For example, [1] means that the sentence is attributed to the first search result. [2, 3] means that the sentence is attributed to both the second and third search results.", "type": "boolean"}, "languageCode": {"description": "Language code for Summary. Use language tags defined by [BCP47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt). Note: This is an experimental feature.", "type": "string"}, "modelPromptSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpecModelPromptSpec", "description": "If specified, the spec will be used to modify the prompt provided to the LLM."}, "modelSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpecModelSpec", "description": "If specified, the spec will be used to modify the model specification provided to the LLM."}, "summaryResultCount": {"description": "The number of top results to generate the summary from. If the number of results returned is less than `summaryResultCount`, the summary is generated from all of the results. At most five results can be used to generate a summary.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpecModelPromptSpec": {"description": "Specification of the prompt to use with the model.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpecModelPromptSpec", "properties": {"preamble": {"description": "Text at the beginning of the prompt that instructs the assistant. Examples are available in the user guide.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpecModelSpec": {"description": "Specification of the model.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpecSummarySpecModelSpec", "properties": {"version": {"description": "The model version used to generate the summary. Supported values are: * `stable`: string. Default value when no value is specified. Uses a generally available, fine-tuned version of the text-bison@001 model. * `preview`: string. (Public preview) Uses a fine-tuned version of the text-bison@002 model. This model works only for summaries in English.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestEmbeddingSpec": {"description": "The specification that uses customized query embedding vector to do semantic document retrieval.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestEmbeddingSpec", "properties": {"embeddingVectors": {"description": "The embedding vector used for retrieval. Limit to 1.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestEmbeddingSpecEmbeddingVector"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestEmbeddingSpecEmbeddingVector": {"description": "Embedding vector.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestEmbeddingSpecEmbeddingVector", "properties": {"fieldPath": {"description": "Embedding field path in schema.", "type": "string"}, "vector": {"description": "Query embedding vector.", "items": {"format": "float", "type": "number"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestFacetSpec": {"description": "A facet specification to perform faceted search.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestFacetSpec", "properties": {"enableDynamicPosition": {"description": "Enables dynamic position for this facet. If set to true, the position of this facet among all facets in the response is determined automatically. If dynamic facets are enabled, it is ordered together. If set to false, the position of this facet in the response is the same as in the request, and it is ranked before the facets with dynamic position enable and all dynamic facets. For example, you may always want to have rating facet returned in the response, but it's not necessarily to always display the rating facet at the top. In that case, you can set enable_dynamic_position to true so that the position of rating facet in response is determined automatically. Another example, assuming you have the following facets in the request: * \"rating\", enable_dynamic_position = true * \"price\", enable_dynamic_position = false * \"brands\", enable_dynamic_position = false And also you have a dynamic facets enabled, which generates a facet `gender`. Then the final order of the facets in the response can be (\"price\", \"brands\", \"rating\", \"gender\") or (\"price\", \"brands\", \"gender\", \"rating\") depends on how API orders \"gender\" and \"rating\" facets. However, notice that \"price\" and \"brands\" are always ranked at first and second position because their enable_dynamic_position is false.", "type": "boolean"}, "excludedFilterKeys": {"description": "List of keys to exclude when faceting. By default, FacetKey.key is not excluded from the filter unless it is listed in this field. Listing a facet key in this field allows its values to appear as facet results, even when they are filtered out of search results. Using this field does not affect what search results are returned. For example, suppose there are 100 documents with the color facet \"Red\" and 200 documents with the color facet \"Blue\". A query containing the filter \"color:ANY(\"Red\")\" and having \"color\" as FacetKey.key would by default return only \"Red\" documents in the search results, and also return \"Red\" with count 100 as the only color facet. Although there are also blue documents available, \"Blue\" would not be shown as an available facet value. If \"color\" is listed in \"excludedFilterKeys\", then the query returns the facet values \"Red\" with count 100 and \"Blue\" with count 200, because the \"color\" key is now excluded from the filter. Because this field doesn't affect search results, the search results are still correctly filtered to return only \"Red\" documents. A maximum of 100 values are allowed. Otherwise, an `INVALID_ARGUMENT` error is returned.", "items": {"type": "string"}, "type": "array"}, "facetKey": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestFacetSpecFacetKey", "description": "Required. The facet key specification."}, "limit": {"description": "Maximum of facet values that should be returned for this facet. If unspecified, defaults to 20. The maximum allowed value is 300. Values above 300 are coerced to 300. If this field is negative, an `INVALID_ARGUMENT` is returned.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestFacetSpecFacetKey": {"description": "Specifies how a facet is computed.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestFacetSpecFacetKey", "properties": {"caseInsensitive": {"description": "True to make facet keys case insensitive when getting faceting values with prefixes or contains; false otherwise.", "type": "boolean"}, "contains": {"description": "Only get facet values that contains the given strings. For example, suppose \"category\" has three values \"Action > 2022\", \"Action > 2021\" and \"Sci-Fi > 2022\". If set \"contains\" to \"2022\", the \"category\" facet only contains \"Action > 2022\" and \"Sci-Fi > 2022\". Only supported on textual fields. Maximum is 10.", "items": {"type": "string"}, "type": "array"}, "intervals": {"description": "Set only if values should be bucketed into intervals. Must be set for facets with numerical values. Must not be set for facet with text values. Maximum number of intervals is 30.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaInterval"}, "type": "array"}, "key": {"description": "Required. Supported textual and numerical facet keys in Document object, over which the facet values are computed. Facet key is case-sensitive.", "type": "string"}, "orderBy": {"description": "The order in which documents are returned. Allowed values are: * \"count desc\", which means order by SearchResponse.Facet.values.count descending. * \"value desc\", which means order by SearchResponse.Facet.values.value descending. Only applies to textual facets. If not set, textual values are sorted in [natural order](https://en.wikipedia.org/wiki/Natural_sort_order); numerical intervals are sorted in the order given by FacetSpec.FacetKey.intervals.", "type": "string"}, "prefixes": {"description": "Only get facet values that start with the given string prefix. For example, suppose \"category\" has three values \"Action > 2022\", \"Action > 2021\" and \"Sci-Fi > 2022\". If set \"prefixes\" to \"Action\", the \"category\" facet only contains \"Action > 2022\" and \"Action > 2021\". Only supported on textual fields. Maximum is 10.", "items": {"type": "string"}, "type": "array"}, "restrictedValues": {"description": "Only get facet for the given restricted values. Only supported on textual fields. For example, suppose \"category\" has three values \"Action > 2022\", \"Action > 2021\" and \"Sci-Fi > 2022\". If set \"restricted_values\" to \"Action > 2022\", the \"category\" facet only contains \"Action > 2022\". Only supported on textual fields. Maximum is 10.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestImageQuery": {"description": "Specifies the image query input.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestImageQuery", "properties": {"imageBytes": {"description": "Base64 encoded image bytes. Supported image formats: JPEG, PNG, and BMP.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestQueryExpansionSpec": {"description": "Specification to determine under which conditions query expansion should occur.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestQueryExpansionSpec", "properties": {"condition": {"description": "The condition under which query expansion should occur. Default to Condition.DISABLED.", "enum": ["CONDITION_UNSPECIFIED", "DISABLED", "AUTO"], "enumDescriptions": ["Unspecified query expansion condition. In this case, server behavior defaults to Condition.DISABLED.", "Disabled query expansion. Only the exact search query is used, even if SearchResponse.total_size is zero.", "Automatic query expansion built by the Search API."], "type": "string"}, "pinUnexpandedResults": {"description": "Whether to pin unexpanded results. If this field is set to true, unexpanded products are always at the top of the search results, followed by the expanded results.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchRequestSpellCorrectionSpec": {"description": "The specification for query spell correction.", "id": "GoogleCloudDiscoveryengineV1alphaSearchRequestSpellCorrectionSpec", "properties": {"mode": {"description": "The mode under which spell correction should take effect to replace the original search query. Default to Mode.AUTO.", "enum": ["MODE_UNSPECIFIED", "SUGGESTION_ONLY", "AUTO"], "enumDescriptions": ["Unspecified spell correction mode. In this case, server behavior defaults to Mode.AUTO.", "Search API will try to find a spell suggestion if there is any and put in the SearchResponse.corrected_query. The spell suggestion will not be used as the search query.", "Automatic spell correction built by the Search API. Search will be based on the corrected query if found."], "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponse": {"description": "Response message for SearchService.Search method.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponse", "properties": {"appliedControls": {"description": "Controls applied as part of the Control service.", "items": {"type": "string"}, "type": "array"}, "attributionToken": {"description": "A unique search token. This should be included in the UserEvent logs resulting from this search, which enables accurate attribution of search model performance.", "type": "string"}, "correctedQuery": {"description": "Contains the spell corrected query, if found. If the spell correction type is AUTOMATIC, then the search results are based on corrected_query. Otherwise the original query is used for search.", "type": "string"}, "facets": {"description": "Results of facets requested by user.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseFacet"}, "type": "array"}, "geoSearchDebugInfo": {"items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseGeoSearchDebugInfo"}, "type": "array"}, "guidedSearchResult": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseGuidedSearchResult", "description": "Guided search result."}, "nextPageToken": {"description": "A token that can be sent as SearchRequest.page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "queryExpansionInfo": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseQueryExpansionInfo", "description": "Query expansion information for the returned results."}, "redirectUri": {"description": "The URI of a customer-defined redirect page. If redirect action is triggered, no search is performed, and only redirect_uri and attribution_token are set in the response.", "type": "string"}, "results": {"description": "A list of matched documents. The order represents the ranking.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSearchResult"}, "type": "array"}, "summary": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummary", "description": "A summary as part of the search results. This field is only returned if SearchRequest.ContentSearchSpec.summary_spec is set."}, "totalSize": {"description": "The estimated total count of matched items irrespective of pagination. The count of results returned by pagination may be less than the total_size that matches.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseFacet": {"description": "A facet result.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseFacet", "properties": {"dynamicFacet": {"description": "Whether the facet is dynamically generated.", "type": "boolean"}, "key": {"description": "The key for this facet. E.g., \"colors\" or \"price\". It matches SearchRequest.FacetSpec.FacetKey.key.", "type": "string"}, "values": {"description": "The facet values for this field.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseFacetFacetValue"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseFacetFacetValue": {"description": "A facet value which contains value names and their count.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseFacetFacetValue", "properties": {"count": {"description": "Number of items that have this facet value.", "format": "int64", "type": "string"}, "interval": {"$ref": "GoogleCloudDiscoveryengineV1alphaInterval", "description": "Interval value for a facet, such as 10, 20) for facet \"price\". It matches [SearchRequest.FacetSpec.FacetKey.intervals."}, "value": {"description": "Text value of a facet, such as \"Black\" for facet \"colors\".", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseGeoSearchDebugInfo": {"description": "Debug information specifically related to forward geocoding issues arising from Geolocation Search.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseGeoSearchDebugInfo", "properties": {"errorMessage": {"description": "The error produced.", "type": "string"}, "originalAddressQuery": {"description": "The address from which forward geocoding ingestion produced issues.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseGuidedSearchResult": {"description": "Guided search result. The guided search helps user to refine the search results and narrow down to the real needs from a broaded search results.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseGuidedSearchResult", "properties": {"followUpQuestions": {"description": "Suggested follow-up questions.", "items": {"type": "string"}, "type": "array"}, "refinementAttributes": {"description": "A list of ranked refinement attributes.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseGuidedSearchResultRefinementAttribute"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseGuidedSearchResultRefinementAttribute": {"description": "Useful attribute for search result refinements.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseGuidedSearchResultRefinementAttribute", "properties": {"attributeKey": {"description": "Attribute key used to refine the results e.g. 'movie_type'.", "type": "string"}, "attributeValue": {"description": "Attribute value used to refine the results e.g. 'drama'.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseQueryExpansionInfo": {"description": "Information describing query expansion including whether expansion has occurred.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseQueryExpansionInfo", "properties": {"expandedQuery": {"description": "<PERSON><PERSON> describing whether query expansion has occurred.", "type": "boolean"}, "pinnedResultCount": {"description": "Number of pinned results. This field will only be set when expansion happens and SearchRequest.QueryExpansionSpec.pin_unexpanded_results is set to true.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseSearchResult": {"description": "Represents the search results.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseSearchResult", "properties": {"document": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocument", "description": "The document data snippet in the search response. Only fields that are marked as retrievable are populated."}, "id": {"description": "Document.id of the searched Document.", "type": "string"}, "modelScores": {"additionalProperties": {"$ref": "GoogleCloudDiscoveryengineV1alphaDoubleList"}, "description": "Google provided available scores.", "type": "object"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseSummary": {"description": "Summary of the top N search result specified by the summary spec.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummary", "properties": {"safetyAttributes": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummarySafetyAttributes", "description": "A collection of Safety Attribute categories and their associated confidence scores."}, "summarySkippedReasons": {"description": "Additional summary-skipped reasons. This provides the reason for ignored cases. If nothing is skipped, this field is not set.", "items": {"enum": ["SUMMARY_SKIPPED_REASON_UNSPECIFIED", "ADVERSARIAL_QUERY_IGNORED", "NON_SUMMARY_SEEKING_QUERY_IGNORED", "OUT_OF_DOMAIN_QUERY_IGNORED", "POTENTIAL_POLICY_VIOLATION", "LLM_ADDON_NOT_ENABLED"], "enumDescriptions": ["Default value. The summary skipped reason is not specified.", "The adversarial query ignored case. Only populated when SummarySpec.ignore_adversarial_query is set to `true`.", "The non-summary seeking query ignored case. Only populated when SummarySpec.ignore_non_summary_seeking_query is set to `true`.", "The out-of-domain query ignored case. Google skips the summary if there are no high-relevance search results. For example, the data store contains facts about company A but the user query is asking questions about company B.", "The potential policy violation case. Google skips the summary if there is a potential policy violation detected. This includes content that may be violent or toxic.", "The LLM addon not enabled case. Google skips the summary if the LLM addon is not enabled."], "type": "string"}, "type": "array"}, "summaryText": {"description": "The summary content.", "type": "string"}, "summaryWithMetadata": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummarySummaryWithMetadata"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryCitation": {"description": "Citation info for a segment.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryCitation", "properties": {"endIndex": {"description": "End of the attributed segment, exclusive.", "format": "int64", "type": "string"}, "sources": {"description": "Citation sources for the attributed segment.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryCitationSource"}, "type": "array"}, "startIndex": {"description": "Index indicates the start of the segment, measured in bytes/unicode.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryCitationMetadata": {"description": "Citation metadata.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryCitationMetadata", "properties": {"citations": {"description": "Citations for segments.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryCitation"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryCitationSource": {"description": "Citation source.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryCitationSource", "properties": {"referenceIndex": {"description": "Document reference index from SummaryWithMetadata.references. It is 0-indexed and the value will be zero if the reference_index is not set explicitly.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryReference": {"description": "Document reference.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryReference", "properties": {"document": {"description": "Required. Document.name of the document. Full resource name of the referenced document, in the format `projects/*/locations/*/collections/*/dataStores/*/branches/*/documents/*`.", "type": "string"}, "title": {"description": "Title of the document.", "type": "string"}, "uri": {"description": "Cloud Storage or HTTP uri for the document.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseSummarySafetyAttributes": {"description": "Safety Attribute categories and their associated confidence scores.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummarySafetyAttributes", "properties": {"categories": {"description": "The display names of Safety Attribute categories associated with the generated content. Order matches the Scores.", "items": {"type": "string"}, "type": "array"}, "scores": {"description": "The confidence scores of the each category, higher value means higher confidence. Order matches the Categories.", "items": {"format": "float", "type": "number"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSearchResponseSummarySummaryWithMetadata": {"description": "Summary with metadata information.", "id": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummarySummaryWithMetadata", "properties": {"citationMetadata": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryCitationMetadata", "description": "Citation metadata for given summary."}, "references": {"description": "Document References.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponseSummaryReference"}, "type": "array"}, "summary": {"description": "Summary text with no citation information.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSiteSearchEngine": {"description": "SiteSearchEngine captures DataStore level site search persisting configurations. It is a singleton value per data store.", "id": "GoogleCloudDiscoveryengineV1alphaSiteSearchEngine", "properties": {"name": {"description": "The fully qualified resource name of the site search engine. Format: `projects/*/locations/*/dataStores/*/siteSearchEngine`", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSiteVerificationInfo": {"description": "Verification information for target sites in advanced site search.", "id": "GoogleCloudDiscoveryengineV1alphaSiteVerificationInfo", "properties": {"siteVerificationState": {"description": "Site verification state indicating the ownership and validity.", "enum": ["SITE_VERIFICATION_STATE_UNSPECIFIED", "VERIFIED", "UNVERIFIED", "EXEMPTED"], "enumDescriptions": ["Defaults to VERIFIED.", "Site ownership verified.", "Site ownership pending verification or verification failed.", "Site exempt from verification, e.g. a public website that opens to all."], "type": "string"}, "verifyTime": {"description": "Latest site verification time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaSuggestionDenyListEntry": {"description": "Suggestion deny list entry identifying the phrase to block from suggestions and the applied operation for the phrase.", "id": "GoogleCloudDiscoveryengineV1alphaSuggestionDenyListEntry", "properties": {"blockPhrase": {"description": "Required. Phrase to block from suggestions served. Can be maximum 125 characters.", "type": "string"}, "matchOperator": {"description": "Required. The match operator to apply for this phrase. Whether to block the exact phrase, or block any suggestions containing this phrase.", "enum": ["MATCH_OPERATOR_UNSPECIFIED", "EXACT_MATCH", "CONTAINS"], "enumDescriptions": ["Default value. Should not be used", "If the suggestion is an exact match to the block_phrase, then block it.", "If the suggestion contains the block_phrase, then block it."], "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTargetSite": {"description": "A target site for the SiteSearchEngine.", "id": "GoogleCloudDiscoveryengineV1alphaTargetSite", "properties": {"exactMatch": {"description": "Input only. If set to false, a uri_pattern is generated to include all pages whose address contains the provided_uri_pattern. If set to true, an uri_pattern is generated to try to be an exact match of the provided_uri_pattern or just the specific page if the provided_uri_pattern is a specific one. provided_uri_pattern is always normalized to generate the URI pattern to be used by the search engine.", "type": "boolean"}, "failureReason": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSiteFailureReason", "description": "Output only. Failure reason.", "readOnly": true}, "generatedUriPattern": {"description": "Output only. This is system-generated based on the provided_uri_pattern.", "readOnly": true, "type": "string"}, "indexingStatus": {"description": "Output only. Indexing status.", "enum": ["INDEXING_STATUS_UNSPECIFIED", "PENDING", "FAILED", "SUCCEEDED", "DELETING"], "enumDescriptions": ["Defaults to SUCCEEDED.", "The target site is in the update queue and will be picked up by indexing pipeline.", "The target site fails to be indexed.", "The target site has been indexed.", "The previously indexed target site has been marked to be deleted. This is a transitioning state which will resulted in either: 1. target site deleted if unindexing is successful; 2. state reverts to SUCCEEDED if the unindexing fails."], "readOnly": true, "type": "string"}, "name": {"description": "Output only. The fully qualified resource name of the target site. `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}` The `target_site_id` is system-generated.", "readOnly": true, "type": "string"}, "providedUriPattern": {"description": "Required. Input only. The user provided URI pattern from which the `generated_uri_pattern` is generated.", "type": "string"}, "siteVerificationInfo": {"$ref": "GoogleCloudDiscoveryengineV1alphaSiteVerificationInfo", "description": "Output only. Site ownership and validity verification status.", "readOnly": true}, "type": {"description": "The type of the target site, e.g. whether the site is to be included or excluded.", "enum": ["TYPE_UNSPECIFIED", "INCLUDE", "EXCLUDE"], "enumDescriptions": ["This value is unused. In this case, server behavior defaults to Type.INCLUDE.", "Include the target site.", "Exclude the target site."], "type": "string"}, "updateTime": {"description": "Output only. The target site's last updated time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTargetSiteFailureReason": {"description": "Site search indexing failure reasons.", "id": "GoogleCloudDiscoveryengineV1alphaTargetSiteFailureReason", "properties": {"quotaFailure": {"$ref": "GoogleCloudDiscoveryengineV1alphaTargetSiteFailureReasonQuotaFailure", "description": "Failed due to insufficient quota."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTargetSiteFailureReasonQuotaFailure": {"id": "GoogleCloudDiscoveryengineV1alphaTargetSiteFailureReasonQuotaFailure", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTextInput": {"description": "Defines text input.", "id": "GoogleCloudDiscoveryengineV1alphaTextInput", "properties": {"context": {"$ref": "GoogleCloudDiscoveryengineV1alphaConversationContext", "description": "Conversation context of the input."}, "input": {"description": "Text input.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTrainCustomModelMetadata": {"description": "Metadata related to the progress of the TrainCustomModel operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaTrainCustomModelMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTrainCustomModelRequest": {"description": "Request message for SearchTuningService.TrainCustomModel method.", "id": "GoogleCloudDiscoveryengineV1alphaTrainCustomModelRequest", "properties": {"errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportErrorConfig", "description": "The desired location of errors incurred during the data ingestion and training."}, "gcsTrainingInput": {"$ref": "GoogleCloudDiscoveryengineV1alphaTrainCustomModelRequestGcsTrainingInput", "description": "Cloud Storage training input."}, "modelType": {"description": "Model to be trained. Supported values are: * **search-tuning**: Fine tuning the search system based on data provided.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTrainCustomModelRequestGcsTrainingInput": {"description": "Cloud Storage training data input.", "id": "GoogleCloudDiscoveryengineV1alphaTrainCustomModelRequestGcsTrainingInput", "properties": {"corpusDataPath": {"description": "The Cloud Storage corpus data which could be associated in train data. The data path format is gs:///. A newline delimited jsonl/ndjson file. For search-tuning model, each line should have the _id, title and text. Example: {\"_id\": \"doc1\", title: \"relevant doc\", \"text\": \"relevant text\"}", "type": "string"}, "queryDataPath": {"description": "The gcs query data which could be associated in train data. The data path format is gs:///. A newline delimited jsonl/ndjson file. For search-tuning model, each line should have the _id and text. Example: {\"_id\": \"query1\", \"text\": \"example query\"}", "type": "string"}, "testDataPath": {"description": "Cloud Storage test data. Same format as train_data_path. If not provided, a random 80/20 train/test split will be performed on train_data_path.", "type": "string"}, "trainDataPath": {"description": "Cloud Storage training data path whose format should be gs:///. The file should be in tsv format. Each line should have the doc_id and query_id and score (number). For search-tuning model, it should have the query-id corpus-id score as tsv file header. The score should be a number in [0, inf+). The larger the number is, the more relevant the pair is. Example: query-id\\tcorpus-id\\tscore query1\\tdoc1\\t1", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTrainCustomModelResponse": {"description": "Response of the TrainCustomModelRequest. This message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudDiscoveryengineV1alphaTrainCustomModelResponse", "properties": {"errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1alphaImportErrorConfig", "description": "Echoes the destination for the complete errors in the request if set."}, "errorSamples": {"description": "A sample of errors encountered while processing the data.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "modelStatus": {"description": "The trained model status. Possible values are: * **bad-data**: The training data quality is bad. * **no-improvement**: Tuning didn't improve performance. Won't deploy. * **in-progress**: Model training is in progress. * **ready**: The model is ready for serving.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTransactionInfo": {"description": "A transaction represents the entire purchase transaction.", "id": "GoogleCloudDiscoveryengineV1alphaTransactionInfo", "properties": {"cost": {"description": "All the costs associated with the products. These can be manufacturing costs, shipping expenses not borne by the end user, or any other costs, such that: * Profit = value - tax - cost", "format": "float", "type": "number"}, "currency": {"description": "Required. Currency code. Use three-character ISO-4217 code.", "type": "string"}, "discountValue": {"description": "The total discount(s) value applied to this transaction. This figure should be excluded from TransactionInfo.value For example, if a user paid TransactionInfo.value amount, then nominal (pre-discount) value of the transaction is the sum of TransactionInfo.value and TransactionInfo.discount_value This means that profit is calculated the same way, regardless of the discount value, and that TransactionInfo.discount_value can be larger than TransactionInfo.value: * Profit = value - tax - cost", "format": "float", "type": "number"}, "tax": {"description": "All the taxes associated with the transaction.", "format": "float", "type": "number"}, "transactionId": {"description": "The transaction ID with a length limit of 128 characters.", "type": "string"}, "value": {"description": "Required. Total non-zero value associated with the transaction. This value may include shipping, tax, or other adjustments to the total value that you want to include.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTuneEngineMetadata": {"description": "Metadata associated with a tune operation.", "id": "GoogleCloudDiscoveryengineV1alphaTuneEngineMetadata", "properties": {"engine": {"description": "Required. The resource name of the engine that this tune applies to. Format: `projects/{project_number}/locations/{location_id}/collections/{collection_id}/engines/{engine_id}`", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTuneEngineRequest": {"description": "Request to manually start a tuning process now (instead of waiting for the periodically scheduled tuning to happen).", "id": "GoogleCloudDiscoveryengineV1alphaTuneEngineRequest", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaTuneEngineResponse": {"description": "Response associated with a tune operation.", "id": "GoogleCloudDiscoveryengineV1alphaTuneEngineResponse", "properties": {}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaUpdateSchemaMetadata": {"description": "Metadata for UpdateSchema LRO.", "id": "GoogleCloudDiscoveryengineV1alphaUpdateSchemaMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaUpdateTargetSiteMetadata": {"description": "Metadata related to the progress of the SiteSearchEngineService.UpdateTargetSite operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1alphaUpdateTargetSiteMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaUserEvent": {"description": "UserEvent captures all metadata information Discovery Engine API needs to know about how end users interact with customers' website.", "id": "GoogleCloudDiscoveryengineV1alphaUserEvent", "properties": {"attributes": {"additionalProperties": {"$ref": "GoogleCloudDiscoveryengineV1alphaCustomAttribute"}, "description": "Extra user event features to include in the recommendation model. These attributes must NOT contain data that needs to be parsed or processed further, e.g. JSON or other encodings. If you provide custom attributes for ingested user events, also include them in the user events that you associate with prediction requests. Custom attribute formatting must be consistent between imported events and events provided with prediction requests. This lets the Discovery Engine API use those custom attributes when training models and serving predictions, which helps improve recommendation quality. This field needs to pass all below criteria, otherwise an `INVALID_ARGUMENT` error is returned: * The key must be a UTF-8 encoded string with a length limit of 5,000 characters. * For text attributes, at most 400 values are allowed. Empty values are not allowed. Each value must be a UTF-8 encoded string with a length limit of 256 characters. * For number attributes, at most 400 values are allowed. For product recommendations, an example of extra user information is `traffic_channel`, which is how a user arrives at the site. Users can arrive at the site by coming to the site directly, coming through Google search, or in other ways.", "type": "object"}, "attributionToken": {"description": "Token to attribute an API response to user action(s) to trigger the event. Highly recommended for user events that are the result of RecommendationService.Recommend. This field enables accurate attribution of recommendation model performance. The value must be one of: * RecommendResponse.attribution_token for events that are the result of RecommendationService.Recommend. * SearchResponse.attribution_token for events that are the result of SearchService.Search. This token enables us to accurately attribute page view or conversion completion back to the event and the particular predict response containing this clicked/purchased product. If user clicks on product K in the recommendation results, pass RecommendResponse.attribution_token as a URL parameter to product <PERSON>'s page. When recording events on product <PERSON>'s page, log the RecommendResponse.attribution_token to this field.", "type": "string"}, "completionInfo": {"$ref": "GoogleCloudDiscoveryengineV1alphaCompletionInfo", "description": "CompletionService.CompleteQuery details related to the event. This field should be set for `search` event when autocomplete function is enabled and the user clicks a suggestion for search."}, "directUserRequest": {"description": "Should set to true if the request is made directly from the end user, in which case the UserEvent.user_info.user_agent can be populated from the HTTP request. This flag should be set only if the API request is made directly from the end user such as a mobile app (and not if a gateway or a server is processing and pushing the user events). This should not be set when using the JavaScript tag in UserEventService.CollectUserEvent.", "type": "boolean"}, "documents": {"description": "List of Documents associated with this user event. This field is optional except for the following event types: * `view-item` * `add-to-cart` * `purchase` * `media-play` * `media-complete` In a `search` event, this field represents the documents returned to the end user on the current page (the end user may have not finished browsing the whole page yet). When a new page is returned to the end user, after pagination/filtering/ordering even for the same query, a new `search` event with different UserEvent.documents is desired.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaDocumentInfo"}, "type": "array"}, "eventTime": {"description": "Only required for UserEventService.ImportUserEvents method. Timestamp of when the user event happened.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "Required. User event type. Allowed values are: Generic values: * `search`: Search for Documents. * `view-item`: Detailed page view of a Document. * `view-item-list`: View of a panel or ordered list of Documents. * `view-home-page`: View of the home page. * `view-category-page`: View of a category page, e.g. Home > Men > Jeans Retail-related values: * `add-to-cart`: Add an item(s) to cart, e.g. in Retail online shopping * `purchase`: Purchase an item(s) Media-related values: * `media-play`: Start/resume watching a video, playing a song, etc. * `media-complete`: Finished or stopped midway through a video, song, etc.", "type": "string"}, "filter": {"description": "The filter syntax consists of an expression language for constructing a predicate from one or more fields of the documents being filtered. One example is for `search` events, the associated SearchRequest may contain a filter expression in SearchRequest.filter conforming to https://google.aip.dev/160#filtering. Similarly, for `view-item-list` events that are generated from a RecommendRequest, this field may be populated directly from RecommendRequest.filter conforming to https://google.aip.dev/160#filtering. The value must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an `INVALID_ARGUMENT` error is returned.", "type": "string"}, "mediaInfo": {"$ref": "GoogleCloudDiscoveryengineV1alphaMediaInfo", "description": "Media-specific info."}, "pageInfo": {"$ref": "GoogleCloudDiscoveryengineV1alphaPageInfo", "description": "Page metadata such as categories and other critical information for certain event types such as `view-category-page`."}, "panel": {"$ref": "GoogleCloudDiscoveryengineV1alphaPanelInfo", "description": "Panel metadata associated with this user event."}, "promotionIds": {"description": "The promotion IDs if this is an event associated with promotions. Currently, this field is restricted to at most one ID.", "items": {"type": "string"}, "type": "array"}, "searchInfo": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchInfo", "description": "SearchService.Search details related to the event. This field should be set for `search` event."}, "sessionId": {"description": "A unique identifier for tracking a visitor session with a length limit of 128 bytes. A session is an aggregation of an end user behavior in a time span. A general guideline to populate the session_id: 1. If user has no activity for 30 min, a new session_id should be assigned. 2. The session_id should be unique across users, suggest use uuid or add UserEvent.user_pseudo_id as prefix.", "type": "string"}, "tagIds": {"description": "A list of identifiers for the independent experiment groups this user event belongs to. This is used to distinguish between user events associated with different experiment setups on the customer end.", "items": {"type": "string"}, "type": "array"}, "transactionInfo": {"$ref": "GoogleCloudDiscoveryengineV1alphaTransactionInfo", "description": "The transaction metadata (if any) associated with this user event."}, "userInfo": {"$ref": "GoogleCloudDiscoveryengineV1alphaUserInfo", "description": "Information about the end user."}, "userPseudoId": {"description": "Required. A unique identifier for tracking visitors. For example, this could be implemented with an HTTP cookie, which should be able to uniquely identify a visitor on a single device. This unique identifier should not change if the visitor log in/out of the website. Do not set the field to the same fixed ID for different users. This mixes the event history of those users together, which results in degraded model quality. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an `INVALID_ARGUMENT` error is returned. The field should not contain PII or user-data. We recommend to use Google Analytics [Client ID](https://developers.google.com/analytics/devguides/collection/analyticsjs/field-reference#clientId) for this field.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaUserInfo": {"description": "Information of an end user.", "id": "GoogleCloudDiscoveryengineV1alphaUserInfo", "properties": {"userAgent": {"description": "User agent as included in the HTTP header. The field must be a UTF-8 encoded string with a length limit of 1,000 characters. Otherwise, an `INVALID_ARGUMENT` error is returned. This should not be set when using the client side event reporting with GTM or JavaScript tag in UserEventService.CollectUserEvent or if UserEvent.direct_user_request is set.", "type": "string"}, "userId": {"description": "Highly recommended for logged-in users. Unique identifier for logged-in user, such as a user name. Don't set for anonymous users. Always use a hashed value for this ID. Don't set the field to the same fixed ID for different users. This mixes the event history of those users together, which results in degraded model quality. The field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an `INVALID_ARGUMENT` error is returned.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetCompleteQueryRequest": {"description": "Request message for WidgetService.WidgetCompleteQuery method.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetCompleteQueryRequest", "properties": {"additionalParams": {"$ref": "GoogleCloudDiscoveryengineV1alphaAdditionalParams", "description": "Additional params for security and privacy enhancement."}, "completeQueryRequest": {"$ref": "GoogleCloudDiscoveryengineV1alphaCompleteQueryRequest", "description": "Required. The CompleteQuery request to perform auto-complete suggestion query."}, "configId": {"description": "Required. The UUID of the WidgetConfig. This field is used to identify the widget configuration, set of models used to make the auto complete query.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetCompleteQueryResponse": {"description": "Response message for WidgetService.WidgetCompleteQuery method.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetCompleteQueryResponse", "properties": {"uToken": {"description": "The token in response.", "type": "string"}, "widgetQuerySuggestions": {"description": "Results of the matched query suggestions in widget. The result list is ordered and the first result is a top suggestion.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetCompleteQueryResponseWidgetQuerySuggestion"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetCompleteQueryResponseWidgetQuerySuggestion": {"description": "Suggestions as search queries.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetCompleteQueryResponseWidgetQuerySuggestion", "properties": {"suggestion": {"description": "The suggestion for the query.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetConfig": {"description": "WidgetConfig captures configs at the Widget level.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetConfig", "properties": {"allowPublicAccess": {"description": "Whether allow no-auth integration with widget. If set true, public access to search or other solutions from widget is allowed without authenication token provided by customer hosted backend server.", "type": "boolean"}, "allowlistedDomains": {"description": "Allowlisted domains that can load this widget.", "items": {"type": "string"}, "type": "array"}, "configId": {"description": "Output only. Unique obfuscated identifier of a WidgetConfig.", "readOnly": true, "type": "string"}, "contentSearchSpec": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequestContentSearchSpec", "description": "The content search spec that configs the desired behavior of content search."}, "createTime": {"description": "Output only. Timestamp the WidgetConfig was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataStoreType": {"description": "Output only. The type of the parent data store.", "enum": ["DATA_STORE_TYPE_UNSPECIFIED", "SITE_SEARCH", "STRUCTURED", "UNSTRUCTURED", "BLENDED"], "enumDescriptions": ["Unspecified data store type.", "The parent data store contains a site search engine.", "The parent data store contains a search engine for structured data.", "The parent data store contains a search engine for unstructured data.", "The parent data store is served for blended search with multiple data stores."], "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The human readable widget config display name. Used in Discovery UI. This field must be a UTF-8 encoded string with a length limit of 128 characters. Otherwise, an INVALID_ARGUMENT error is returned.", "type": "string"}, "enableAutocomplete": {"description": "Whether or not to enable autocomplete.", "type": "boolean"}, "enableConversationalSearch": {"description": "Whether to allow conversational search (LLM, multi-turn) or not (non-LLM, single-turn).", "type": "boolean"}, "enableQualityFeedback": {"description": "Turn on or off collecting the search result quality feedback from end users.", "type": "boolean"}, "enableResultScore": {"description": "Whether to show the result score.", "type": "boolean"}, "enableSafeSearch": {"description": "Whether to enable safe search.", "type": "boolean"}, "enableSnippetResultSummary": {"description": "Turn on or off summary for each snippets result.", "type": "boolean"}, "enableSummarization": {"description": "Turn on or off summarization for the search response.", "type": "boolean"}, "facetField": {"description": "The configuration and appearance of facets in the end user view.", "items": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetConfigFacetField"}, "type": "array"}, "fieldsUiComponentsMap": {"additionalProperties": {"$ref": "GoogleCloudDiscoveryengineV1alphaWidgetConfigUIComponentField"}, "description": "The key is the UI component. Mock. Currently supported `title`, `thumbnail`, `url`, `custom1`, `custom2`, `custom3`. The value is the name of the field along with its device visibility. The 3 custom fields are optional and can be added or removed. `title`, `thumbnail`, `url` are required UI components that cannot be removed.", "type": "object"}, "llmEnabled": {"description": "Output only. Whether LLM is enabled in the corresponding data store.", "readOnly": true, "type": "boolean"}, "minimumDataTermAccepted": {"description": "Output only. Whether the customer accepted data use terms.", "readOnly": true, "type": "boolean"}, "name": {"description": "Immutable. The full resource name of the widget config. Format: `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}/widgetConfigs/{widget_config_id}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "type": "string"}, "resultDisplayType": {"description": "The type of snippet to display in UCS widget. - RESULT_DISPLAY_TYPE_UNSPECIFIED for existing users. - SNIPPET for new non-enterprise search users. - EXTRACTIVE_ANSWER for new enterprise search users.", "enum": ["RESULT_DISPLAY_TYPE_UNSPECIFIED", "SNIPPET", "EXTRACTIVE_ANSWER"], "enumDescriptions": ["Unspecified display type (default to showing snippet).", "Display results from the snippet field.", "Display results from extractive answers field."], "type": "string"}, "solutionType": {"description": "Required. Immutable. Specifies the solution type that this WidgetConfig can be used for.", "enum": ["SOLUTION_TYPE_UNSPECIFIED", "SOLUTION_TYPE_RECOMMENDATION", "SOLUTION_TYPE_SEARCH", "SOLUTION_TYPE_CHAT"], "enumDescriptions": ["Default value.", "Used for Recommendations AI.", "Used for Discovery Search.", "Used for use cases related to the Generative AI agent."], "type": "string"}, "updateTime": {"description": "Output only. Timestamp the WidgetConfig was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetConfigFacetField": {"description": "Facet fields that store the mapping of fields to end user widget appearance.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetConfigFacetField", "properties": {"displayName": {"description": "Optional. The field name that end users will see.", "type": "string"}, "field": {"description": "Required. Registered field name. The format is `field.abc`.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetConfigUIComponentField": {"description": "Facet field that maps to a UI Component.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetConfigUIComponentField", "properties": {"deviceVisibility": {"description": "The field visibility on different types of devices.", "items": {"enum": ["DEVICE_VISIBILITY_UNSPECIFIED", "MOBILE", "DESKTOP"], "enumDescriptions": ["Default value when not specified. Server returns INVALID_ARGUMENT if used in requests.", "The UI component is visible on Mobile devices.", "The UI component is visible on Browser-based client."], "type": "string"}, "type": "array"}, "displayTemplate": {"description": "The template to customize how the field is displayed. An example value would be a string that looks like: \"Price: {value}\".", "type": "string"}, "field": {"description": "Required. Registered field name. The format is `field.abc`.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetConverseConversationRequest": {"description": "Request message for WidgetService.WidgetConverseConversation method.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetConverseConversationRequest", "properties": {"additionalParams": {"$ref": "GoogleCloudDiscoveryengineV1alphaAdditionalParams", "description": "Additional params for security and privacy enhancement."}, "configId": {"description": "Required. The UUID of the WidgetConfig. This field is used to identify the widget configuration, set of models used to make the user event collection.", "type": "string"}, "conversationId": {"description": "The id of the Conversation to get. Use \"-\" to activate auto session mode, which automatically creates a new conversation inside a ConverseConversation session.", "type": "string"}, "converseConversationRequest": {"$ref": "GoogleCloudDiscoveryengineV1alphaConverseConversationRequest", "description": "Required. The ConverseConversationRequest request to perform converse a conversation. The ServingConfig id will be `default_search` by default."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetConverseConversationResponse": {"description": "Response message for WidgetService.WidgetConverseConversation method.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetConverseConversationResponse", "properties": {"conversationId": {"description": "The id of the Conversation returned.", "type": "string"}, "converseConversationResponse": {"$ref": "GoogleCloudDiscoveryengineV1alphaConverseConversationResponse", "description": "ConverseConversationResponse returned from ConversationalSearchService.ConverseConversation."}, "uToken": {"description": "The token in response.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetSearchRequest": {"description": "Request message for WidgetService.WidgetSearch method.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetSearchRequest", "properties": {"additionalParams": {"$ref": "GoogleCloudDiscoveryengineV1alphaAdditionalParams", "description": "Additional params for security and privacy enhancement."}, "configId": {"description": "Required. The UUID of the Search WidgetConfig. This field is used to identify the search widget configuration, set of models used to make the search.", "type": "string"}, "searchRequest": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchRequest", "description": "Required. The search request to perform search."}}, "type": "object"}, "GoogleCloudDiscoveryengineV1alphaWidgetSearchResponse": {"description": "Response message for WidgetService.WidgetSearch method.", "id": "GoogleCloudDiscoveryengineV1alphaWidgetSearchResponse", "properties": {"searchResponse": {"$ref": "GoogleCloudDiscoveryengineV1alphaSearchResponse", "description": "The search response after performing search."}, "uToken": {"description": "The token in response.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaCreateSchemaMetadata": {"description": "Metadata for Create Schema LRO.", "id": "GoogleCloudDiscoveryengineV1betaCreateSchemaMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaDeleteSchemaMetadata": {"description": "Metadata for DeleteSchema LRO.", "id": "GoogleCloudDiscoveryengineV1betaDeleteSchemaMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaImportDocumentsMetadata": {"description": "Metadata related to the progress of the ImportDocuments operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1betaImportDocumentsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaImportDocumentsResponse": {"description": "Response of the ImportDocumentsRequest. If the long running operation is done, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudDiscoveryengineV1betaImportDocumentsResponse", "properties": {"errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1betaImportErrorConfig", "description": "Echoes the destination for the complete errors in the request if set."}, "errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaImportErrorConfig": {"description": "Configuration of destination for Import related errors.", "id": "GoogleCloudDiscoveryengineV1betaImportErrorConfig", "properties": {"gcsPrefix": {"description": "Cloud Storage prefix for import errors. This must be an empty, existing Cloud Storage directory. Import errors are written to sharded files in this directory, one per line, as a JSON-encoded `google.rpc.Status` message.", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaImportSuggestionDenyListEntriesMetadata": {"description": "Metadata related to the progress of the ImportSuggestionDenyListEntries operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1betaImportSuggestionDenyListEntriesMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaImportSuggestionDenyListEntriesResponse": {"description": "Response message for CompletionService.ImportSuggestionDenyListEntries method.", "id": "GoogleCloudDiscoveryengineV1betaImportSuggestionDenyListEntriesResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "failedEntriesCount": {"description": "Count of deny list entries that failed to be imported.", "format": "int64", "type": "string"}, "importedEntriesCount": {"description": "Count of deny list entries successfully imported.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaImportUserEventsMetadata": {"description": "Metadata related to the progress of the Import operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1betaImportUserEventsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were processed successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaImportUserEventsResponse": {"description": "Response of the ImportUserEventsRequest. If the long running operation was successful, then this message is returned by the google.longrunning.Operations.response field if the operation was successful.", "id": "GoogleCloudDiscoveryengineV1betaImportUserEventsResponse", "properties": {"errorConfig": {"$ref": "GoogleCloudDiscoveryengineV1betaImportErrorConfig", "description": "Echoes the destination for the complete errors if this field was set in the request."}, "errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "joinedEventsCount": {"description": "Count of user events imported with complete existing Documents.", "format": "int64", "type": "string"}, "unjoinedEventsCount": {"description": "Count of user events imported, but with Document information not found in the existing Branch.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaPurgeDocumentsMetadata": {"description": "Metadata related to the progress of the PurgeDocuments operation. This will be returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1betaPurgeDocumentsMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "failureCount": {"description": "Count of entries that encountered errors while processing.", "format": "int64", "type": "string"}, "successCount": {"description": "Count of entries that were deleted successfully.", "format": "int64", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaPurgeDocumentsResponse": {"description": "Response message for DocumentService.PurgeDocuments method. If the long running operation is successfully done, then this message is returned by the google.longrunning.Operations.response field.", "id": "GoogleCloudDiscoveryengineV1betaPurgeDocumentsResponse", "properties": {"purgeCount": {"description": "The total count of documents purged as a result of the operation.", "format": "int64", "type": "string"}, "purgeSample": {"description": "A sample of document names that will be deleted. Only populated if `force` is set to false. A max of 100 names will be returned and the names are chosen at random.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaPurgeSuggestionDenyListEntriesMetadata": {"description": "Metadata related to the progress of the PurgeSuggestionDenyListEntries operation. This is returned by the google.longrunning.Operation.metadata field.", "id": "GoogleCloudDiscoveryengineV1betaPurgeSuggestionDenyListEntriesMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaPurgeSuggestionDenyListEntriesResponse": {"description": "Response message for CompletionService.PurgeSuggestionDenyListEntries method.", "id": "GoogleCloudDiscoveryengineV1betaPurgeSuggestionDenyListEntriesResponse", "properties": {"errorSamples": {"description": "A sample of errors encountered while processing the request.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "purgeCount": {"description": "Number of suggestion deny list entries purged.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaSchema": {"description": "Defines the structure and layout of a type of document data.", "id": "GoogleCloudDiscoveryengineV1betaSchema", "properties": {"jsonSchema": {"description": "The JSON representation of the schema.", "type": "string"}, "name": {"description": "Immutable. The full resource name of the schema, in the format of `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`. This field must be a UTF-8 encoded string with a length limit of 1024 characters.", "type": "string"}, "structSchema": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The structured representation of the schema.", "type": "object"}}, "type": "object"}, "GoogleCloudDiscoveryengineV1betaUpdateSchemaMetadata": {"description": "Metadata for UpdateSchema LRO.", "id": "GoogleCloudDiscoveryengineV1betaUpdateSchemaMetadata", "properties": {"createTime": {"description": "Operation create time.", "format": "google-datetime", "type": "string"}, "updateTime": {"description": "Operation last update time. If the operation is done, this is also the finish time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "GoogleTypeDate": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "GoogleTypeDate", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}}, "servicePath": "", "title": "Discovery Engine API", "version": "v1alpha", "version_module": true}