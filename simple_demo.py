"""
簡化版家樂福爬蟲展示
專門用來檢查網站訪問和基本功能
"""

import asyncio
from playwright.async_api import async_playwright

async def simple_demo():
    """簡化版展示"""
    print("🎬 === 簡化版家樂福爬蟲展示 ===")
    print("📱 即將打開瀏覽器，展示基本訪問功能")
    print("=" * 50)
    
    target_url = "https://online.carrefour.com.tw/zh/%E5%93%81%E7%89%8C%E6%97%97%E8%89%A6%E9%A4%A8/%E9%BB%91%E6%9D%BE%E5%93%81%E7%89%8C%E9%A4%A8"
    
    async with async_playwright() as p:
        print("🌐 正在啟動瀏覽器...")
        
        # 啟動瀏覽器 - 顯示視窗
        browser = await p.chromium.launch(
            headless=False,  # 顯示瀏覽器
            slow_mo=2000,    # 每個操作間隔 2 秒
            args=['--start-maximized']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        page = await context.new_page()
        
        try:
            print(f"🔗 正在訪問目標網站...")
            print(f"   網址: {target_url}")
            
            # 訪問網站
            response = await page.goto(target_url, timeout=60000)
            print(f"✅ 網站回應狀態: {response.status}")
            
            # 等待頁面載入
            print("⏳ 等待頁面載入...")
            await page.wait_for_timeout(10000)
            
            # 獲取頁面標題
            title = await page.title()
            print(f"📄 頁面標題: {title}")
            
            # 獲取頁面 URL
            current_url = page.url
            print(f"🔗 當前 URL: {current_url}")
            
            # 檢查頁面內容
            print("🔍 檢查頁面內容...")
            
            # 檢查是否有圖片
            images = await page.query_selector_all('img')
            print(f"🖼️  找到 {len(images)} 個圖片元素")
            
            # 檢查是否有連結
            links = await page.query_selector_all('a')
            print(f"🔗 找到 {len(links)} 個連結元素")
            
            # 檢查是否有包含價格的文字
            price_elements = await page.query_selector_all('*:has-text("NT$"), *:has-text("$")')
            print(f"💰 找到 {len(price_elements)} 個可能的價格元素")
            
            # 嘗試尋找產品相關元素
            print("🔍 尋找產品相關元素...")
            
            selectors_to_test = [
                'div[class*="product"]',
                'div[class*="item"]',
                'div[class*="card"]',
                '[data-testid*="product"]',
                'img[alt*="產品"]',
                'img[src*="product"]'
            ]
            
            for selector in selectors_to_test:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"   ✅ 選擇器 '{selector}': {len(elements)} 個元素")
                    else:
                        print(f"   ❌ 選擇器 '{selector}': 0 個元素")
                except Exception as e:
                    print(f"   ⚠️  選擇器 '{selector}': 錯誤 - {e}")
            
            # 滾動頁面
            print("📜 測試頁面滾動...")
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(3000)
            
            # 滾動回頂部
            await page.evaluate("window.scrollTo(0, 0)")
            await page.wait_for_timeout(2000)
            
            # 嘗試截圖
            print("📸 嘗試截圖...")
            try:
                await page.screenshot(path="carrefour_screenshot.png", full_page=True)
                print("✅ 截圖已儲存為 carrefour_screenshot.png")
            except Exception as e:
                print(f"⚠️  截圖失敗: {e}")
            
            # 檢查頁面是否有載入錯誤
            print("🔍 檢查頁面載入狀態...")
            
            # 檢查是否有錯誤訊息
            error_selectors = [
                '*:has-text("錯誤")',
                '*:has-text("Error")',
                '*:has-text("404")',
                '*:has-text("無法載入")',
                '.error',
                '.loading'
            ]
            
            for selector in error_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"   ⚠️  發現可能的錯誤元素 '{selector}': {len(elements)} 個")
                except:
                    continue
            
            # 等待用戶觀看
            print("👀 請觀看瀏覽器視窗，10 秒後將關閉...")
            await page.wait_for_timeout(10000)
            
            print("✅ 基本檢查完成")
            
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")
            print("⏳ 瀏覽器將在 10 秒後關閉...")
            await page.wait_for_timeout(10000)
        
        finally:
            await browser.close()
            print("🔚 瀏覽器已關閉")

async def test_alternative_urls():
    """測試其他可能的 URL"""
    print("\n🔍 === 測試其他可能的 URL ===")
    
    alternative_urls = [
        "https://online.carrefour.com.tw",
        "https://online.carrefour.com.tw/zh",
        "https://online.carrefour.com.tw/zh/categories",
        "https://www.carrefour.com.tw"
    ]
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        for url in alternative_urls:
            try:
                print(f"🔗 測試 URL: {url}")
                response = await page.goto(url, timeout=30000)
                title = await page.title()
                print(f"   ✅ 狀態: {response.status}, 標題: {title[:50]}...")
            except Exception as e:
                print(f"   ❌ 失敗: {e}")
        
        await browser.close()

async def main():
    """主函數"""
    # 執行簡化展示
    await simple_demo()
    
    # 測試其他 URL
    await test_alternative_urls()
    
    print("\n🎉 展示完成！")
    print("💡 如果看到瀏覽器成功打開並載入頁面，表示基本功能正常")
    print("💡 如果發現問題，可能需要調整選擇器或等待時間")

if __name__ == "__main__":
    asyncio.run(main())
