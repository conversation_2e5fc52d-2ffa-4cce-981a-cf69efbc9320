# STM32 Bootloader Android 移植測試說明

## 測試目的
驗證 Android Java 版本的 STM32 Bootloader 協議實現是否與 C# 版本一致。

## 測試工具
使用更新後的 `main41.py` Python 測試程式，新增了以下功能：

### 1. STM32 Bootloader 測試功能
- **自定義進入 Bootloader (0xB2)** - 發送自定義協議進入命令
- **STM32 同步字節 (0x7F)** - 發送標準 STM32 bootloader 同步字節
- **重置 MCU** - 發送重置命令
- **完整 Bootloader 序列** - 自動執行完整的進入流程
- **發送測試韌體包** - 模擬韌體寫入過程

### 2. HEX 數據發送
- 支援直接輸入和發送 HEX 格式數據
- 自動格式化和顯示發送的字節

### 3. 增強的接收顯示
- 同時顯示文本和 HEX 格式的接收數據
- 方便驗證二進制協議

## 測試步驟

### 1. 驗證自定義協議包格式
```
預期 C# 輸出: 01 0F B2 31 32 33 34 35 36 37 38 39 3A B3
Python 輸出: 應該完全相同
```

### 2. 檢查要點
- **STX (0x01)** - 起始字節
- **LEN (0x0F)** - 長度 = 15 (包含 CMD + DATA)
- **CMD (0xB2)** - 進入 Bootloader 命令
- **DATA** - "123456789:" 的 ASCII 碼
- **Checksum (0xB3)** - XOR 校驗和

### 3. 完整測試流程
1. 連接 USB 轉串口設備
2. 點擊「自定義進入 Bootloader」按鈕
3. 觀察發送的 HEX 數據是否正確
4. 檢查 Android 端是否正確接收和解析
5. 驗證 Android 端是否發送相同格式的數據

## 常見問題

### 問題：Android 端收到數據但顯示不正確
**解決方案**：
1. 確認 Android 端使用 HEX 接收模式
2. 檢查 `terminalService.sendData()` 是否正確處理 HEX 字符串
3. 驗證 `CustomBootloaderProtocol.bytesToHexString()` 輸出格式

### 問題：校驗和不匹配
**解決方案**：
1. 確認 XOR 計算範圍（不包括 STX）
2. 檢查字節順序是否正確
3. 驗證數據長度計算（LEN = CMD + DATA 的長度）

## 驗證清單
- [ ] 自定義協議包格式正確
- [ ] 校驗和計算正確
- [ ] Android 端正確解析接收的數據
- [ ] Android 端發送的數據格式一致
- [ ] 完整 Bootloader 序列可正常執行
- [ ] STM32 同步字節正確發送

## 使用提示
1. 運行 Python 測試工具：`python main41.py`
2. 選擇正確的串口（通常包含 "USB" 或 "Serial"）
3. 使用默認波特率 115200
4. 點擊「開始接收」以查看雙向通信
5. 使用各個測試按鈕驗證協議實現