import sys
import random
import string
from datetime import datetime, timedelta
import qrcode
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QPushButton, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit
from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import Qt
from io import BytesIO


class QRCodeGenerator(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        self.setWindowTitle('QR Code Generator')
        self.setGeometry(100, 100, 600, 400)

        layout = QVBoxLayout()

        # 輸入欄位
        input_layout = QHBoxLayout()
        self.machine_input = QLineEdit('B16')
        self.product_input = QLineEdit()
        self.product_name_input = QLineEdit('PET580')
        input_layout.addWidget(QLabel('Machine:'))
        input_layout.addWidget(self.machine_input)
        input_layout.addWidget(QLabel('Product:'))
        input_layout.addWidget(self.product_input)
        input_layout.addWidget(QLabel('Product Name:'))
        input_layout.addWidget(self.product_name_input)

        # 生成按鈕
        self.generate_btn = QPushButton('Generate QR Code')
        self.generate_btn.clicked.connect(self.generate_qr_code)

        # QR碼顯示區域
        self.qr_label = QLabel()
        self.qr_label.setAlignment(Qt.AlignCenter)

        # 數據顯示區域
        self.data_label = QLabel()
        self.data_label.setWordWrap(True)

        layout.addLayout(input_layout)
        layout.addWidget(self.generate_btn)
        layout.addWidget(self.qr_label)
        layout.addWidget(self.data_label)

        self.setLayout(layout)

    def generate_qr_code(self):
        # 生成QR碼數據
        qr_data = self.generate_qrcode_data()

        # 創建QR碼圖像
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(qr_data)
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")

        # 將圖像轉換為QPixmap並顯示
        buffer = BytesIO()
        img.save(buffer, format="PNG")
        qr_pixmap = QPixmap()
        qr_pixmap.loadFromData(buffer.getvalue())
        self.qr_label.setPixmap(qr_pixmap.scaled(200, 200, Qt.KeepAspectRatio))

        # 顯示QR碼數據
        self.data_label.setText(qr_data)

    def generate_qrcode_data(self):
        today = datetime.now()
        date_str = today.strftime("%Y%m%d")

        serial_number = f"{random.randint(0, 99):02d}"

        product_number = self.product_input.text() if self.product_input.text() else f"{random.randint(1000, 9999)}"

        random_string = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))

        roc_year = today.year - 1911

        next_year = today + timedelta(days=365)
        next_year_str = next_year.strftime("%Y%m%d")

        machine = self.machine_input.text()
        product_name = self.product_name_input.text()

        qr_data = f"{machine}{roc_year}{date_str[-4:]}{serial_number}-#52891;{product_number};{product_name}.;{date_str};{next_year_str};{machine}{roc_year}{date_str[-4:]}{serial_number};TWN;50;50;61;{date_str};{date_str};0120;P;10601;{machine};"

        return qr_data


if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = QRCodeGenerator()
    ex.show()
    sys.exit(app.exec_())