#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI條碼與QR Code產生器 - 修正版
倉庫貼標掃描系統專用
按照正確的資料庫格式
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
from datetime import datetime, timedelta
from PIL import Image, ImageTk
import io
import random

# 檢查並安裝必要套件
try:
    import barcode
    from barcode.writer import ImageWriter
    import qrcode
except ImportError as e:
    print(f"請安裝必要套件: pip install python-barcode qrcode[pil] Pillow")
    exit(1)


class BarcodeGeneratorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("倉庫貼標掃描系統 - 條碼產生器")
        self.root.geometry("900x1000")
        self.root.resizable(True, True)

        # 設置樣式
        style = ttk.Style()
        style.theme_use('clam')

        # 產品資料配置
        self.setup_product_data()

        # 創建GUI元素
        self.create_widgets()

        # 預設值
        self.set_default_values()

    def setup_product_data(self):
        """設置產品資料"""
        self.products = {
            "PET580 ｶﾂｪQFINｸﾉｵｹｶｼｮﾆ": "1287",
            "黑松沙士 330ml": "1214",
            "黑松汽水 330ml": "1215",
            "黑松茶花綠茶 500ml": "1216",
            "黑松茶花烏龍茶 500ml": "1217",
            "黑松FIN補給飲料 330ml": "1218",
            "黑松C&C檸檬 500ml": "1219",
            "黑松純水 600ml": "1220",
            "黑松蘋果西打 330ml": "1221",
            "黑松黑麥汁 330ml": "1222"
        }

        # 工廠代碼 (DELC)
        self.factories = {
            "斗六廠": "2",
            "中壢廠": "3"
        }

        # 通路別選項 (SHOP_KIND)
        self.shop_kinds = [
            "10601",  # 一般通路
            "10602",  # 便利商店
            "10603",  # 量販店
            "10604",  # 餐飲通路
            "10605"  # 自動販賣機
        ]

        # 產品註記 (PRODUCT_MARK)
        self.product_marks = ["P", "S", "N", ""]

        # 產地國別
        self.place_ids = {
            "台灣": "TWN",
            "中國": "CHN",
            "日本": "JPN"
        }

    def create_widgets(self):
        """創建GUI元件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        row = 0

        # 標題
        title_label = ttk.Label(main_frame, text="倉庫貼標掃描系統 - 條碼產生器",
                                font=('Arial', 16, 'bold'))
        title_label.grid(row=row, column=0, columnspan=4, pady=(0, 20))
        row += 1

        # === 基本資訊區域 ===
        basic_frame = ttk.LabelFrame(main_frame, text="基本資訊", padding="10")
        basic_frame.grid(row=row, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        basic_frame.columnconfigure(1, weight=1)
        basic_frame.columnconfigure(3, weight=1)
        row += 1

        # 發貨廠 (DELC)
        ttk.Label(basic_frame, text="發貨廠 (DELC):").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.delc_var = tk.StringVar()
        delc_combo = ttk.Combobox(basic_frame, textvariable=self.delc_var,
                                  values=list(self.factories.keys()), state="readonly", width=15)
        delc_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))

        # 發貨單號 (DELIVERY_NO) - 8位數字
        ttk.Label(basic_frame, text="發貨單號 (8碼):").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        self.delivery_no_var = tk.StringVar()
        delivery_entry = ttk.Entry(basic_frame, textvariable=self.delivery_no_var, width=15)
        delivery_entry.grid(row=0, column=3, sticky=(tk.W, tk.E))

        # 自動產生發貨單號按鈕
        auto_delivery_btn = ttk.Button(basic_frame, text="自動產生", command=self.auto_generate_delivery)
        auto_delivery_btn.grid(row=0, column=4, padx=(10, 0))

        # 成品收料/製造單號 (SLIP_NO)
        ttk.Label(basic_frame, text="製造單號 (SLIP_NO):").grid(row=1, column=0, sticky=tk.W, padx=(0, 10),
                                                                pady=(10, 0))
        self.slip_no_var = tk.StringVar()
        slip_no_entry = ttk.Entry(basic_frame, textvariable=self.slip_no_var, width=15)
        slip_no_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 20), pady=(10, 0))

        # 機別 (MACHINE)
        ttk.Label(basic_frame, text="機別 (MACHINE):").grid(row=1, column=2, sticky=tk.W, padx=(20, 10), pady=(10, 0))
        self.machine_var = tk.StringVar()
        machine_entry = ttk.Entry(basic_frame, textvariable=self.machine_var, width=15)
        machine_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=(10, 0))

        # === 產品資訊區域 ===
        product_frame = ttk.LabelFrame(main_frame, text="產品資訊", padding="10")
        product_frame.grid(row=row, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        product_frame.columnconfigure(1, weight=1)
        product_frame.columnconfigure(3, weight=1)
        row += 1

        # 產品代號 (PRODUCT_ID)
        ttk.Label(product_frame, text="產品代號:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.product_id_var = tk.StringVar()
        product_id_entry = ttk.Entry(product_frame, textvariable=self.product_id_var, width=15)
        product_id_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))

        # 產品名稱 (FULL_DESC)
        ttk.Label(product_frame, text="產品名稱:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        self.full_desc_var = tk.StringVar()
        product_combo = ttk.Combobox(product_frame, textvariable=self.full_desc_var,
                                     values=list(self.products.keys()), state="readonly", width=25)
        product_combo.grid(row=0, column=3, sticky=(tk.W, tk.E))
        product_combo.bind('<<ComboboxSelected>>', self.on_product_selected)

        # 製造日期 (PRODUCTDATE)
        ttk.Label(product_frame, text="製造日期:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.product_date_var = tk.StringVar()
        product_date_entry = ttk.Entry(product_frame, textvariable=self.product_date_var, width=15)
        product_date_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 20), pady=(10, 0))

        # 有效日期 (VALIDDATE)
        ttk.Label(product_frame, text="有效日期:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10), pady=(10, 0))
        self.valid_date_var = tk.StringVar()
        valid_date_entry = ttk.Entry(product_frame, textvariable=self.valid_date_var, width=15)
        valid_date_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=(10, 0))

        # 生產批號 (WO_NO)
        ttk.Label(product_frame, text="生產批號:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.wo_no_var = tk.StringVar()
        wo_no_entry = ttk.Entry(product_frame, textvariable=self.wo_no_var, width=15)
        wo_no_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 20), pady=(10, 0))

        # 重新產生批號按鈕
        regenerate_wo_btn = ttk.Button(product_frame, text="重新產生", command=self.regenerate_wo_no)
        regenerate_wo_btn.grid(row=2, column=2, padx=(10, 0), pady=(10, 0))

        # 產地國別 (PLACE_ID)
        ttk.Label(product_frame, text="產地國別:").grid(row=2, column=3, sticky=tk.W, padx=(20, 10), pady=(10, 0))
        self.place_id_var = tk.StringVar()
        place_combo = ttk.Combobox(product_frame, textvariable=self.place_id_var,
                                   values=list(self.place_ids.keys()), state="readonly", width=15)
        place_combo.grid(row=2, column=4, sticky=(tk.W, tk.E), pady=(10, 0))

        # === 數量資訊區域 ===
        quantity_frame = ttk.LabelFrame(main_frame, text="數量資訊", padding="10")
        quantity_frame.grid(row=row, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        quantity_frame.columnconfigure(1, weight=1)
        quantity_frame.columnconfigure(3, weight=1)
        row += 1

        # 每板裝載量 (UNIT_LOAD_QTY)
        ttk.Label(quantity_frame, text="每板裝載量:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.unit_load_qty_var = tk.StringVar()
        unit_load_entry = ttk.Entry(quantity_frame, textvariable=self.unit_load_qty_var, width=15)
        unit_load_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))

        # 產品數量 (QTY)
        ttk.Label(quantity_frame, text="產品數量:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        self.qty_var = tk.StringVar()
        qty_entry = ttk.Entry(quantity_frame, textvariable=self.qty_var, width=15)
        qty_entry.grid(row=0, column=3, sticky=(tk.W, tk.E))

        # 棧板代碼 (BOX_ID)
        ttk.Label(quantity_frame, text="棧板代碼:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.box_id_var = tk.StringVar()
        box_id_entry = ttk.Entry(quantity_frame, textvariable=self.box_id_var, width=15)
        box_id_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 20), pady=(10, 0))

        # 棧板號碼 (PLAT_NO_BEGIN)
        ttk.Label(quantity_frame, text="棧板號碼:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10), pady=(10, 0))
        self.plat_no_var = tk.StringVar()
        plat_no_entry = ttk.Entry(quantity_frame, textvariable=self.plat_no_var, width=15)
        plat_no_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=(10, 0))

        # === 其他資訊區域 ===
        other_frame = ttk.LabelFrame(main_frame, text="其他資訊", padding="10")
        other_frame.grid(row=row, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        other_frame.columnconfigure(1, weight=1)
        other_frame.columnconfigure(3, weight=1)
        row += 1

        # 產品註記 (PRODUCT_MARK)
        ttk.Label(other_frame, text="產品註記:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.product_mark_var = tk.StringVar()
        product_mark_combo = ttk.Combobox(other_frame, textvariable=self.product_mark_var,
                                          values=self.product_marks, state="normal", width=15)
        product_mark_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))

        # 通路別 (SHOP_KIND)
        ttk.Label(other_frame, text="通路別:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        self.shop_kind_var = tk.StringVar()
        shop_kind_combo = ttk.Combobox(other_frame, textvariable=self.shop_kind_var,
                                       values=self.shop_kinds, state="normal", width=15)
        shop_kind_combo.grid(row=0, column=3, sticky=(tk.W, tk.E))

        # 有效日期同日註記 (BOTTLE_MARK)
        ttk.Label(other_frame, text="有效日期同日註記:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.bottle_mark_var = tk.StringVar()
        bottle_mark_entry = ttk.Entry(other_frame, textvariable=self.bottle_mark_var, width=15)
        bottle_mark_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 20), pady=(10, 0))

        # 上傳註記 (UPLOAD_FLAG)
        ttk.Label(other_frame, text="上傳註記:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10), pady=(10, 0))
        self.upload_flag_var = tk.StringVar()
        upload_flag_combo = ttk.Combobox(other_frame, textvariable=self.upload_flag_var,
                                         values=["N", "Y"], state="readonly", width=15)
        upload_flag_combo.grid(row=1, column=3, sticky=(tk.W, tk.E), pady=(10, 0))

        # 按鈕區域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=4, pady=(10, 0))
        row += 1

        # 產生QR Code按鈕
        qr_btn = ttk.Button(button_frame, text="產生 QR Code", command=self.generate_qrcode, width=15)
        qr_btn.grid(row=0, column=0, padx=(0, 10))

        # 產生一維條碼按鈕
        barcode_btn = ttk.Button(button_frame, text="產生一維條碼", command=self.generate_barcode, width=15)
        barcode_btn.grid(row=0, column=1, padx=(0, 10))

        # 產生全部按鈕
        all_btn = ttk.Button(button_frame, text="產生全部", command=self.generate_all, width=15)
        all_btn.grid(row=0, column=2, padx=(0, 10))

        # 清除按鈕
        clear_btn = ttk.Button(button_frame, text="清除", command=self.clear_all, width=15)
        clear_btn.grid(row=0, column=3, padx=(0, 10))

        # 儲存按鈕
        save_btn = ttk.Button(button_frame, text="儲存圖片", command=self.save_images, width=15)
        save_btn.grid(row=0, column=4)

        # === 顯示區域 ===
        display_frame = ttk.LabelFrame(main_frame, text="產生結果", padding="10")
        display_frame.grid(row=row, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        display_frame.columnconfigure(0, weight=1)
        display_frame.columnconfigure(1, weight=1)
        display_frame.rowconfigure(1, weight=1)
        main_frame.rowconfigure(row, weight=1)

        # QR Code顯示
        qr_frame = ttk.Frame(display_frame)
        qr_frame.grid(row=0, column=0, padx=(0, 10), sticky=(tk.W, tk.E, tk.N, tk.S))
        qr_frame.columnconfigure(0, weight=1)
        qr_frame.rowconfigure(1, weight=1)

        ttk.Label(qr_frame, text="QR Code", font=('Arial', 12, 'bold')).grid(row=0, column=0, pady=(0, 10))
        self.qr_label = ttk.Label(qr_frame, text="請先產生QR Code", anchor='center')
        self.qr_label.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 一維條碼顯示
        barcode_frame = ttk.Frame(display_frame)
        barcode_frame.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E, tk.N, tk.S))
        barcode_frame.columnconfigure(0, weight=1)
        barcode_frame.rowconfigure(1, weight=1)

        ttk.Label(barcode_frame, text="一維條碼", font=('Arial', 12, 'bold')).grid(row=0, column=0, pady=(0, 10))
        self.barcode_label = ttk.Label(barcode_frame, text="請先產生一維條碼", anchor='center')
        self.barcode_label.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 資料顯示區域
        data_frame = ttk.Frame(display_frame)
        data_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        data_frame.columnconfigure(0, weight=1)
        data_frame.rowconfigure(0, weight=1)

        # 文字顯示區域
        self.data_text = tk.Text(data_frame, height=10, wrap=tk.WORD)
        data_scrollbar = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.data_text.yview)
        self.data_text.configure(yscrollcommand=data_scrollbar.set)

        self.data_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        data_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 存儲產生的圖片
        self.qr_image = None
        self.barcode_image = None
        self.current_data = None

    def set_default_values(self):
        """設置預設值"""
        self.delc_var.set("斗六廠")
        self.machine_var.set("B16")
        self.product_id_var.set("1287")
        self.full_desc_var.set("PET580 ｶﾂｪQFINｸﾉｵｹｶｼｮﾆ")
        self.unit_load_qty_var.set("72")
        self.qty_var.set("72")
        self.box_id_var.set("65")
        self.place_id_var.set("台灣")
        self.product_mark_var.set("P")
        self.shop_kind_var.set("10601")
        self.bottle_mark_var.set("")
        self.upload_flag_var.set("N")

        # 設置日期
        today = datetime.now()
        self.product_date_var.set(today.strftime("%Y%m%d"))
        expire_date = today + timedelta(days=300)
        self.valid_date_var.set(expire_date.strftime("%Y%m%d"))

        # 產生批號 - 修改為新格式
        # 格式：機別 + 民國年 + 月日 + 流水號(2碼)
        machine = self.machine_var.get()  # B16
        roc_year = today.year - 1911  # 轉換為民國年
        month_day = today.strftime("%m%d")  # 月日
        serial_no = random.randint(1, 99)  # 流水號 1-99

        wo_no = f"{machine}{roc_year:03d}{month_day}{serial_no:02d}"
        self.wo_no_var.set(wo_no)

        self.slip_no_var.set(f"{random.randint(50000, 59999)}")
        self.plat_no_var.set(f"{random.randint(100, 999):04d}")

        # 自動產生發貨單號
        self.auto_generate_delivery()

    def generate_wo_no(self):
        """單獨產生生產批號的方法"""
        today = datetime.now()
        machine = self.machine_var.get() if self.machine_var.get() else "B16"
        roc_year = today.year - 1911  # 轉換為民國年
        month_day = today.strftime("%m%d")  # 月日
        serial_no = random.randint(1, 99)  # 流水號 1-99

        wo_no = f"{machine}{roc_year:03d}{month_day}{serial_no:02d}"
        return wo_no

    def regenerate_wo_no(self):
        """重新產生生產批號"""
        new_wo_no = self.generate_wo_no()
        self.wo_no_var.set(new_wo_no)
        messagebox.showinfo("完成", f"已重新產生生產批號：{new_wo_no}")

    def on_product_selected(self, event):
        """產品選擇事件"""
        product_name = self.full_desc_var.get()
        if product_name in self.products:
            product_id = self.products[product_name]
            self.product_id_var.set(product_id)

    def auto_generate_delivery(self):
        """自動產生8位數字發貨單號"""
        # 產生8位數字發貨單號
        delivery_no = f"{random.randint(10000000, 99999999)}"
        self.delivery_no_var.set(delivery_no)

    def get_current_data(self):
        """獲取當前輸入資料"""
        # 獲取工廠代碼
        factory_name = self.delc_var.get()
        delc_code = self.factories.get(factory_name, "2")

        # 獲取產地代碼
        place_name = self.place_id_var.get()
        place_code = self.place_ids.get(place_name, "TWN")

        data = {
            "DELC": delc_code,
            "DELIVERY_NO": self.delivery_no_var.get(),
            "SLIP_NO": self.slip_no_var.get(),
            "PRODUCT_ID": self.product_id_var.get(),
            "FULL_DESC": self.full_desc_var.get(),
            "PRODUCTDATE": self.product_date_var.get(),
            "VALIDDATE": self.valid_date_var.get(),
            "WO_NO": self.wo_no_var.get(),
            "PLACE_ID": place_code,
            "UNIT_LOAD_QTY": int(self.unit_load_qty_var.get()) if self.unit_load_qty_var.get().isdigit() else 72,
            "QTY": int(self.qty_var.get()) if self.qty_var.get().isdigit() else 72,
            "BOX_ID": self.box_id_var.get(),
            "SLIP_DATE": self.product_date_var.get(),
            "PASTE_DATE": datetime.now().strftime("%Y%m%d"),
            "PLAT_NO_BEGIN": self.plat_no_var.get(),
            "PRODUCT_MARK": self.product_mark_var.get() if self.product_mark_var.get() else None,
            "SHOP_KIND": self.shop_kind_var.get() if self.shop_kind_var.get() else None,
            "MACHINE": self.machine_var.get(),
            "BOTTLE_MARK": self.bottle_mark_var.get() if self.bottle_mark_var.get() else None,
            "UPLOAD_FLAG": self.upload_flag_var.get()
        }

        return data

    def generate_qrcode(self):
        """產生QR Code - 按照正確的完整格式"""
        try:
            # 每次都重新獲取最新資料
            data = self.get_current_data()

            # 按照您提供的QR Code格式：
            # B16114052542-#52891;1214;PET580.;20250525;20260525;B16114052542;TWN;50;50;61;20250525;20250525;0120;P;10601;B16;

            # 分析格式結構：
            # WO_NO-#SLIP_NO;PRODUCT_ID;FULL_DESC;PRODUCTDATE;VALIDDATE;WO_NO;PLACE_ID;UNIT_LOAD_QTY;QTY;BOX_ID;SLIP_DATE;PASTE_DATE;PLAT_NO_BEGIN;PRODUCT_MARK;SHOP_KIND;MACHINE;BOTTLE_MARK;

            qr_content = (
                    f"{data['WO_NO']}-#{data['SLIP_NO']};"
                    f"{data['PRODUCT_ID']};"
                    f"{data['FULL_DESC']};"
                    f"{data['PRODUCTDATE']};"
                    f"{data['VALIDDATE']};"
                    f"{data['WO_NO']};"
                    f"{data['PLACE_ID']};"
                    f"{data['UNIT_LOAD_QTY']};"
                    f"{data['QTY']};"
                    f"{data['BOX_ID']};"
                    f"{data['SLIP_DATE']};"
                    f"{data['PASTE_DATE']};"
                    f"{data['PLAT_NO_BEGIN']};"
                    f"{data.get('PRODUCT_MARK', 'P')};"
                    f"{data.get('SHOP_KIND', data['DELIVERY_NO'][:5])};"
                    f"{data['MACHINE']};"
                    + (f"{data['BOTTLE_MARK']};" if data.get('BOTTLE_MARK', '') else "")
            )

            print(f"產生QR Code: {qr_content}")  # 除錯用

            # 創建新的QR Code實例
            qr = qrcode.QRCode(
                version=2,  # 增加版本以容納更多資料
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=8,  # 調整大小
                border=4,
            )
            qr.add_data(qr_content)
            qr.make(fit=True)

            # 創建新圖片
            qr_img = qr.make_image(fill_color="black", back_color="white")

            # 調整圖片大小以適合顯示
            qr_img = qr_img.resize((250, 250), Image.Resampling.LANCZOS)

            # 每次都創建新的PhotoImage
            self.qr_image = qr_img.copy()  # 建立副本
            qr_photo = ImageTk.PhotoImage(self.qr_image)

            # 更新顯示
            self.qr_label.configure(image=qr_photo, text="")
            self.qr_label.image = qr_photo  # 保持引用避免被垃圾回收

            # 更新資料顯示
            self.current_data = data
            self.display_qr_data(qr_content)

            print("QR Code 產生完成")  # 除錯用

        except Exception as e:
            print(f"QR Code 產生錯誤: {str(e)}")  # 除錯用
            messagebox.showerror("錯誤", f"QR Code 產生失敗：{str(e)}")

    def generate_barcode(self):
        """產生一維條碼 - 使用8位數字發貨單號"""
        try:
            # 每次都重新獲取最新資料
            data = self.get_current_data()

            # 一維條碼就是8位數字的發貨單號
            barcode_content = data['DELIVERY_NO']

            # 驗證是否為8位數字
            if not (barcode_content.isdigit() and len(barcode_content) == 8):
                raise ValueError("發貨單號必須是8位數字")

            print(f"產生一維條碼: {barcode_content}")  # 除錯用

            # 創建新的一維條碼實例
            code128 = barcode.get_barcode_class('code128')
            barcode_instance = code128(barcode_content, writer=ImageWriter())

            # 產生圖片到記憶體
            buffer = io.BytesIO()
            barcode_instance.write(buffer)
            buffer.seek(0)

            # 開啟圖片
            barcode_img = Image.open(buffer)

            # 調整圖片大小
            barcode_img = barcode_img.resize((350, 120), Image.Resampling.LANCZOS)

            # 每次都創建新的PhotoImage
            self.barcode_image = barcode_img.copy()  # 建立副本
            barcode_photo = ImageTk.PhotoImage(self.barcode_image)

            # 更新顯示
            self.barcode_label.configure(image=barcode_photo, text="")
            self.barcode_label.image = barcode_photo  # 保持引用避免被垃圾回收

            # 更新資料顯示
            self.current_data = data
            self.display_barcode_data(barcode_content)

            print("一維條碼產生完成")  # 除錯用

        except Exception as e:
            print(f"一維條碼產生錯誤: {str(e)}")  # 除錯用
            messagebox.showerror("錯誤", f"一維條碼產生失敗：{str(e)}")

    def generate_all(self):
        """產生全部條碼"""
        print("開始產生全部條碼...")
        self.generate_qrcode()
        self.generate_barcode()
        print("全部條碼產生完成")

    def display_qr_data(self, qr_content):
        """顯示QR Code資料內容"""
        self.data_text.delete(1.0, tk.END)

        if self.current_data:
            display_text = f"=== QR Code 資料庫欄位資訊 ===\n"
            display_text += f"發貨廠 (DELC): {self.current_data['DELC']}\n"
            display_text += f"發貨單號 (DELIVERY_NO): {self.current_data['DELIVERY_NO']}\n"
            display_text += f"製造單號 (SLIP_NO): {self.current_data['SLIP_NO']}\n"
            display_text += f"產品代號 (PRODUCT_ID): {self.current_data['PRODUCT_ID']}\n"
            display_text += f"產品名稱 (FULL_DESC): {self.current_data['FULL_DESC']}\n"
            display_text += f"製造日期 (PRODUCTDATE): {self.current_data['PRODUCTDATE']}\n"
            display_text += f"有效日期 (VALIDDATE): {self.current_data['VALIDDATE']}\n"
            display_text += f"生產批號 (WO_NO): {self.current_data['WO_NO']}\n"
            display_text += f"產地國別 (PLACE_ID): {self.current_data['PLACE_ID']}\n"
            display_text += f"每板裝載量 (UNIT_LOAD_QTY): {self.current_data['UNIT_LOAD_QTY']}\n"
            display_text += f"產品數量 (QTY): {self.current_data['QTY']}\n"
            display_text += f"棧板代碼 (BOX_ID): {self.current_data['BOX_ID']}\n"
            display_text += f"棧板號碼 (PLAT_NO_BEGIN): {self.current_data['PLAT_NO_BEGIN']}\n"
            display_text += f"產品註記 (PRODUCT_MARK): {self.current_data.get('PRODUCT_MARK', '')}\n"
            display_text += f"通路別 (SHOP_KIND): {self.current_data.get('SHOP_KIND', '')}\n"
            display_text += f"機別 (MACHINE): {self.current_data['MACHINE']}\n\n"
            if self.current_data.get('BOTTLE_MARK', ''):
                display_text += f"有效日期同日註記 (BOTTLE_MARK): {self.current_data['BOTTLE_MARK']}\n"
            display_text += "\n"

        display_text += f"=== QR Code 內容 ===\n{qr_content}\n\n"
        display_text += f"=== QR Code 格式說明 ===\n"
        display_text += f"WO_NO-#SLIP_NO;PRODUCT_ID;FULL_DESC;PRODUCTDATE;VALIDDATE;WO_NO;PLACE_ID;UNIT_LOAD_QTY;QTY;BOX_ID;SLIP_DATE;PASTE_DATE;PLAT_NO_BEGIN;PRODUCT_MARK;SHOP_KIND;MACHINE;"

        self.data_text.insert(1.0, display_text)

    def display_barcode_data(self, barcode_content):
        """顯示一維條碼資料內容"""
        self.data_text.delete(1.0, tk.END)

        if self.current_data:
            display_text = f"=== 一維條碼 資料庫欄位資訊 ===\n"
            display_text += f"發貨廠 (DELC): {self.current_data['DELC']}\n"
            display_text += f"發貨單號 (DELIVERY_NO): {self.current_data['DELIVERY_NO']}\n"
            display_text += f"製造單號 (SLIP_NO): {self.current_data['SLIP_NO']}\n"
            display_text += f"機別 (MACHINE): {self.current_data['MACHINE']}\n"
            display_text += f"生產批號 (WO_NO): {self.current_data['WO_NO']}\n\n"

        display_text += f"=== 一維條碼 內容 ===\n{barcode_content}\n\n"
        display_text += f"=== 格式說明 ===\n8位數字發貨單號，用於棧板識別\n\n"
        display_text += f"=== 生產批號格式說明 ===\n"
        display_text += f"格式：機別 + 民國年(3碼) + 月日(4碼) + 流水號(2碼)\n"
        display_text += f"範例：B16114011401 = B16 + 114 + 0114 + 01"

        self.data_text.insert(1.0, display_text)

    def clear_all(self):
        """清除所有內容"""
        self.qr_label.configure(image="", text="請先產生QR Code")
        self.barcode_label.configure(image="", text="請先產生一維條碼")
        self.data_text.delete(1.0, tk.END)
        self.qr_image = None
        self.barcode_image = None
        self.current_data = None

    def save_images(self):
        """儲存圖片"""
        if not self.qr_image and not self.barcode_image:
            messagebox.showwarning("警告", "沒有可儲存的圖片！")
            return

        # 選擇儲存目錄
        save_dir = filedialog.askdirectory(title="選擇儲存目錄")
        if not save_dir:
            return

        try:
            saved_files = []
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 儲存QR Code
            if self.qr_image:
                qr_filename = f"qrcode_{self.product_id_var.get()}_{timestamp}.png"
                qr_path = os.path.join(save_dir, qr_filename)
                self.qr_image.save(qr_path)
                saved_files.append(qr_filename)

            # 儲存一維條碼
            if self.barcode_image:
                barcode_filename = f"barcode_{self.delivery_no_var.get()}_{timestamp}.png"
                barcode_path = os.path.join(save_dir, barcode_filename)
                self.barcode_image.save(barcode_path)
                saved_files.append(barcode_filename)

            # 儲存資料JSON
            if self.current_data:
                json_filename = f"data_{timestamp}.json"
                json_path = os.path.join(save_dir, json_filename)
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(self.current_data, f, ensure_ascii=False, indent=2)
                saved_files.append(json_filename)

            messagebox.showinfo("成功", f"已儲存檔案：\n" + "\n".join(saved_files))

        except Exception as e:
            messagebox.showerror("錯誤", f"儲存失敗：{str(e)}")


def main():
    """主程式"""
    root = tk.Tk()
    app = BarcodeGeneratorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()