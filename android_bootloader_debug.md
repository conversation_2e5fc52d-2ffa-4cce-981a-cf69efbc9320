# Android STM32 Bootloader 調試指南

## 問題診斷

您提到「在實機上雖然有上傳但過程一看就是錯的」，可能的問題：

### 1. 數據發送格式問題
Android 端可能將二進制數據當作文本發送。

**診斷方法**：
1. 使用 Python 模擬器接收數據
2. 查看接收到的是 ASCII 文本還是二進制數據

```python
# 在 Python 模擬器中記錄
raw_data = self.serial_port.read(self.serial_port.in_waiting)
print(f"接收字節: {[hex(b) for b in raw_data]}")
print(f"作為文本: {raw_data.decode('ascii', errors='replace')}")
```

### 2. 修正方案

#### 方案 A：修改 SimpleUsbTerminalService
在 `SimpleUsbTerminalService.java` 中添加直接發送字節的方法：

```java
public boolean sendBinary(byte[] data) {
    try {
        if (serviceBound && service != null && connected) {
            service.write(data);
            
            // 記錄日誌
            String hexResult = bytesToHexString(data);
            Log.d(TAG, "Sent binary: " + hexResult);
            
            return true;
        }
        return false;
    } catch (IOException e) {
        Log.e(TAG, "Write error", e);
        return false;
    }
}
```

#### 方案 B：確保 HEX 字符串正確處理
檢查 `sendData()` 方法是否正確識別 HEX 格式：

```java
// 在 FirmwareUpdateHelper 中
byte[] enterCmd = CustomBootloaderProtocol.buildBootloaderFirstCommand();

// 調試日誌
Log.d(TAG, "原始字節: " + Arrays.toString(enterCmd));
// 應該輸出: [1, 15, -78, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, -77]

String hexCmd = CustomBootloaderProtocol.bytesToHexString(enterCmd);
Log.d(TAG, "HEX 字符串: " + hexCmd);
// 應該輸出: "01 0F B2 31 32 33 34 35 36 37 38 39 3A B3"

// 檢查 sendData 的處理
boolean isHex = isHexFormat(hexCmd);
Log.d(TAG, "識別為 HEX: " + isHex);
// 應該輸出: true
```

### 3. HEX 檔案處理

對於 .hex 檔案上傳：

1. **解析 Intel HEX 格式**
   - 每行以 `:` 開頭
   - 包含地址和數據信息
   - 需要轉換為二進制格式

2. **寫入流程**
   ```
   1. 解析 .hex -> 二進制數據
   2. 進入 Bootloader 模式
   3. 擦除 Flash
   4. 分頁寫入（每頁 256 字節）
   5. 驗證並重啟
   ```

### 4. 測試檢查清單

使用 Python 工具驗證每個步驟：

- [ ] 進入 Bootloader 命令正確發送
  - 預期: `01 0F B2 31 32 33 34 35 36 37 38 39 3A B3`
  
- [ ] STM32 同步字節正確發送
  - 預期: `7F`
  
- [ ] 擦除命令正確
  - 預期: `43 BC` 然後 `FF 00`
  
- [ ] 寫入命令序列正確
  - 命令: `31 CE`
  - 地址: 4字節 + 校驗和
  - 數據: N-1 + 數據 + 校驗和

### 5. 使用修正版本

我創建了 `FirmwareUpdateHelperFixed.java`，其中包含：

1. **協議測試功能** - 驗證包格式
2. **詳細日誌** - 每步驟都有調試信息
3. **HEX 檔案支援** - 可以處理 .hex 格式

在 `SimpleTerminalActivityV2.java` 中使用：

```java
// 替換原來的 FirmwareUpdateHelper
private FirmwareUpdateHelperFixed firmwareUpdateHelper;

// 在 initializeManagers() 中
firmwareUpdateHelper = new FirmwareUpdateHelperFixed(this, 
    terminalService, connectionManager, messageDisplayManager);
firmwareUpdateHelper.initializeFilePicker(this);
```

### 6. Python 驗證工具

運行各個 Python 工具：

1. **模擬 STM32 板子**
   ```bash
   python stm32_bootloader_simulator.py
   ```

2. **分析 HEX 檔案**
   ```bash
   python hex_file_analyzer.py your_firmware.hex
   ```

3. **測試協議**
   ```bash
   python bootloader_protocol_test.py
   ```

這樣可以準確找出問題所在！