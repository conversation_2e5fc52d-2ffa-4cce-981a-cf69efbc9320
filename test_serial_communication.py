#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的串口測試腳本
用於測試 Android BootloaderActivity 是否能正確接收訊息
"""

import serial
import time
import sys
import threading
from datetime import datetime

class SerialTester:
    def __init__(self, port, baudrate=115200):
        """
        初始化串口測試器
        :param port: 串口名稱 (例如: 'COM3', '/dev/ttyUSB0')
        :param baudrate: 波特率 (預設: 115200)
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.running = False
        
    def connect(self):
        """連接串口"""
        try:
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                parity=serial.PARITY_EVEN,  # 與 Android 應用匹配
                stopbits=serial.STOPBITS_ONE,
                bytesize=serial.EIGHTBITS,
                timeout=1
            )
            print(f"✅ 成功連接到 {self.port} (波特率: {self.baudrate})")
            return True
        except Exception as e:
            print(f"❌ 連接失敗: {e}")
            return False
    
    def disconnect(self):
        """斷開串口連接"""
        if self.ser and self.ser.is_open:
            self.running = False
            self.ser.close()
            print("🔌 串口已斷開")
    
    def send_message(self, message):
        """發送訊息"""
        if self.ser and self.ser.is_open:
            try:
                # 添加時間戳
                timestamp = datetime.now().strftime("%H:%M:%S")
                full_message = f"{message}"
                
                self.ser.write(full_message.encode('utf-8'))
                print(f"📤 發送: {full_message}")
                return True
            except Exception as e:
                print(f"❌ 發送失敗: {e}")
                return False
        else:
            print("❌ 串口未連接")
            return False
    
    def start_auto_send(self, interval=2):
        """開始自動發送測試訊息"""
        self.running = True
        
        def auto_send_worker():
            counter = 1
            while self.running:
                message = f"測試!+TEST_MESSAGE_{counter}_FROM_PYTHON"
                self.send_message(message)
                counter += 1
                time.sleep(interval)
        
        thread = threading.Thread(target=auto_send_worker, daemon=True)
        thread.start()
        print(f"🔄 開始自動發送訊息 (間隔: {interval}秒)")
    
    def send_bootloader_test(self):
        """發送 Bootloader 相關的測試訊息"""
        test_messages = [
            "STM32 Ready",
            "Bootloader Mode: ON",
            "Flash: 256KB Available",
            "MCU Temperature: 45°C",
            "System Clock: 72MHz",
            "Firmware Version: v1.2.3",
            "Status: Waiting for commands...",
        ]
        
        print("🧪 發送 Bootloader 測試訊息...")
        for i, msg in enumerate(test_messages, 1):
            self.send_message(f"[STM32] {msg}")
            time.sleep(1)
            print(f"   ({i}/{len(test_messages)}) 已發送")
        
        print("✅ Bootloader 測試訊息發送完成")
    
    def interactive_mode(self):
        """互動模式 - 手動輸入訊息"""
        print("\n📝 進入互動模式 (輸入 'quit' 退出)")
        print("💡 提示：輸入要發送的訊息，按 Enter 發送")
        
        while self.running:
            try:
                user_input = input("輸入訊息: ").strip()
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                if user_input:
                    self.send_message(user_input)
            except KeyboardInterrupt:
                break
        
        print("\n👋 退出互動模式")

def list_available_ports():
    """列出可用的串口"""
    import serial.tools.list_ports
    
    ports = serial.tools.list_ports.comports()
    if ports:
        print("📡 可用的串口:")
        for i, port in enumerate(ports, 1):
            print(f"  {i}. {port.device} - {port.description}")
        return [port.device for port in ports]
    else:
        print("❌ 沒有找到可用的串口")
        return []

def main():
    print("🔧 串口通訊測試工具")
    print("=" * 50)
    
    # 列出可用串口
    available_ports = list_available_ports()
    
    if not available_ports:
        print("請檢查設備連接或驅動程式")
        return
    
    # 選擇串口
    if len(sys.argv) > 1:
        port = sys.argv[1]
    else:
        try:
            choice = int(input(f"\n選擇串口 (1-{len(available_ports)}): ")) - 1
            port = available_ports[choice]
        except (ValueError, IndexError):
            print("❌ 無效選擇，使用第一個串口")
            port = available_ports[0]
    
    print(f"\n🎯 使用串口: {port}")
    
    # 創建測試器
    tester = SerialTester(port)
    
    if not tester.connect():
        return
    
    try:
        print("\n📋 測試選項:")
        print("1. 發送單次測試訊息")
        print("2. 自動發送測試訊息 (每2秒)")
        print("3. 發送 Bootloader 測試訊息")
        print("4. 互動模式 (手動輸入)")
        print("5. 退出")
        
        while True:
            try:
                choice = input("\n選擇測試模式 (1-5): ").strip()
                
                if choice == "1":
                    tester.send_message("Hello from Python! 這是一條測試訊息 📱")
                
                elif choice == "2":
                    tester.start_auto_send(2)
                    input("按 Enter 停止自動發送...")
                    tester.running = False
                
                elif choice == "3":
                    tester.send_bootloader_test()
                
                elif choice == "4":
                    tester.running = True
                    tester.interactive_mode()
                    tester.running = False
                
                elif choice == "5":
                    break
                
                else:
                    print("❌ 無效選擇，請選擇 1-5")
                    
            except KeyboardInterrupt:
                print("\n\n👋 收到中斷信號，正在退出...")
                break
    
    finally:
        tester.disconnect()
        print("🏁 測試完成")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程式被中斷")
    except Exception as e:
        print(f"\n❌ 發生錯誤: {e}")