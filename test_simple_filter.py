import ldap3
import sys

def test_simple_filter():
    """測試簡化的使用者篩選器"""
    server = ldap3.Server('HSWDC00.heysong.com.tw', 389, get_info=ldap3.ALL)
    
    print("=== 測試簡化的使用者篩選器 ===")
    
    username = input("輸入測試帳號 (或按 Enter 使用 16613): ").strip() or "16613"
    password = input("輸入密碼: ").strip()
    
    if not password:
        print("需要密碼才能測試")
        return
    
    # 1. 測試用戶 DN 認證
    user_dn = f"{username}@heysong.com.tw"
    print(f"\n1. 測試用戶 DN: {user_dn}")
    
    try:
        conn = ldap3.Connection(server, user=user_dn, password=password)
        if conn.bind():
            print("   ✅ 用戶 DN 認證成功")
            
            # 2. 測試簡化的篩選器
            print("2. 測試簡化的使用者篩選器...")
            
            # 建議的簡化篩選器
            simple_filter = f"(sAMAccountName={username})"
            print(f"   新篩選器: {simple_filter}")
            
            try:
                result = conn.search('DC=heysong,DC=com,DC=tw', simple_filter, ldap3.SUBTREE,
                                   attributes=['distinguishedName', 'sAMAccountName', 'userPrincipalName', 'givenName', 'sn'])
                
                if result and conn.entries:
                    print("   ✅ 簡化篩選器搜尋成功")
                    entry = conn.entries[0]
                    print(f"   找到使用者: {entry.distinguishedName}")
                    print(f"   sAMAccountName: {entry.sAMAccountName}")
                    print(f"   userPrincipalName: {entry.userPrincipalName}")
                    print(f"   givenName: {getattr(entry, 'givenName', '未設定')}")
                    print(f"   sn: {getattr(entry, 'sn', '未設定')}")
                else:
                    print("   ❌ 簡化篩選器搜尋失敗")
                    
            except Exception as e:
                print(f"   ❌ 簡化篩選器搜尋錯誤: {e}")
            
            # 3. 比較原始篩選器
            print("3. 比較原始篩選器...")
            original_filter = f"(&(objectCategory=Person)(objectClass=User)(sAMAccountName={username}))"
            print(f"   原始篩選器: {original_filter}")
            
            try:
                result = conn.search('DC=heysong,DC=com,DC=tw', original_filter, ldap3.SUBTREE,
                                   attributes=['distinguishedName', 'sAMAccountName'])
                
                if result and conn.entries:
                    print("   ✅ 原始篩選器也能搜尋成功")
                else:
                    print("   ❌ 原始篩選器搜尋失敗")
                    
            except Exception as e:
                print(f"   ❌ 原始篩選器搜尋錯誤: {e}")
            
            conn.unbind()
            
        else:
            print("   ❌ 用戶 DN 認證失敗")
            
    except Exception as e:
        print(f"   ❌ 連接錯誤: {e}")
    
    # 4. 建議的 Gitea 設定
    print(f"\n💡 建議的 Gitea 設定:")
    print("認證類型: LDAP (simple auth)")
    print(f"用戶 DN: %<EMAIL>")
    print("用戶搜尋基準: DC=heysong,DC=com,DC=tw")
    print("使用者篩選器: (sAMAccountName=%s)")
    print("電子郵件屬性: userPrincipalName")
    print("名字屬性: givenName")
    print("姓氏屬性: sn")

def test_alternative_approach():
    """測試替代方案 - 改回 BindDN 模式"""
    print(f"\n=== 替代方案：LDAP (via BindDN) ===")
    print("如果 simple auth 持續有問題，建議改用 BindDN 模式")
    print()
    print("優點:")
    print("✅ 更穩定可靠")
    print("✅ 功能更完整")
    print("✅ 更好的錯誤處理")
    print()
    print("缺點:")
    print("❌ 需要申請服務帳號")
    print()
    print("如果要改用 BindDN 模式，需要:")
    print("1. 聯繫 IT 申請服務帳號")
    print("2. 取得服務帳號的 DN 和密碼")
    print("3. 在 Gitea 中改為 LDAP (via BindDN)")
    print("4. 填入 Bind DN 和 Bind 密碼")

if __name__ == "__main__":
    test_simple_filter()
    test_alternative_approach()
