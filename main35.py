import cx_Oracle
import os
import re
import sys

# 資料欄位名稱（根據資料的結構推測）
columns = [
    "年月", "產品編號", "廠商代碼", "廠商名稱", "產品名稱", "包裝規格",
    "單價_不含稅", "稅額", "單價", "店鋪代碼", "店鋪名稱",
    "銷售量1", "銷售量2", "銷售量3", "銷售量4", "銷售金額", "銷售金額_不含稅",
    "庫存量", "其他數值", "條碼", "未知1", "未知2", "未知3", "未知4", "未知5",
    "有效期_開始", "有效期_結束"
]

# Oracle 連接資訊
oracle_username = "asuser"  # 請替換為您的Oracle用戶名
oracle_password = "asuser"  # 請替換為您的Oracle密碼
oracle_dsn = "*************/hy1"  # 請替換為您的Oracle連接字符串，例如: "localhost:1521/ORCL"


def try_direct_methods(file_path):
    """
    嘗試直接讀取文件的幾種方法，用於診斷
    """
    print("\n===== 文件診斷 =====")

    # 方法1: 直接以二進制方式讀取前100字節
    try:
        with open(file_path, 'rb') as f:
            header = f.read(100)
            print(f"文件頭100字節的十六進制表示: {header.hex()}")

            # 檢測文件是否有BOM標記
            if header.startswith(b'\xef\xbb\xbf'):
                print("檢測到UTF-8 BOM標記")
            elif header.startswith(b'\xff\xfe'):
                print("檢測到UTF-16 LE BOM標記")
            elif header.startswith(b'\xfe\xff'):
                print("檢測到UTF-16 BE BOM標記")
    except Exception as e:
        print(f"讀取文件頭時出錯: {e}")

    # 方法2: 嘗試使用直接的文件描述符讀取
    try:
        with open(file_path, 'rb') as f:
            # 檢查文件大小
            f.seek(0, os.SEEK_END)
            file_size = f.tell()
            print(f"文件大小: {file_size} 字節")

            # 檢查是否有特殊字符
            f.seek(0)
            content = f.read(min(file_size, 1000))  # 讀取前1000字節

            # 檢查是否含有分隔符 ^
            separator_count = content.count(b'^')
            print(f"前1000字節中包含 {separator_count} 個 '^' 符號")

            # 檢查是否包含特殊值
            newline_count = len(re.findall(b'\r\n|\r|\n', content))
            print(f"前1000字節中包含 {newline_count} 個換行符")
    except Exception as e:
        print(f"直接讀取時出錯: {e}")


def check_field_lengths(successful_lines):
    """
    檢查字段的最大長度和值範圍，用於確定合適的數據庫欄位大小
    """
    print("\n===== 字段分析 =====")
    max_lengths = [0] * 30  # 假設最多30個字段
    max_values = {}

    # 定義需要檢查最大值的數值字段索引
    numeric_fields = [6, 7, 8, 12, 13, 14, 15, 16, 17, 18]

    for i, field_idx in enumerate(numeric_fields):
        max_values[field_idx] = 0

    # 分析前1000行數據
    sample_size = min(1000, len(successful_lines))
    for i in range(sample_size):
        line = successful_lines[i].strip()
        if not line or '^' not in line:
            continue

        fields = line.split('^')
        for j, field in enumerate(fields):
            # 更新最大長度
            if len(field) > max_lengths[j]:
                max_lengths[j] = len(field)

            # 更新數值字段的最大值
            if j in numeric_fields:
                try:
                    value = float(field) if field else 0
                    if value > max_values[j]:
                        max_values[j] = value
                except (ValueError, TypeError):
                    pass

    # 輸出分析結果
    for i in range(len(max_lengths)):
        if max_lengths[i] > 0:
            field_name = columns[i] if i < len(columns) else f"欄位{i + 1}"
            print(f"{field_name}: 最大長度 = {max_lengths[i]}")

    print("\n數值字段最大值:")
    for idx, value in max_values.items():
        if idx < len(columns):
            print(f"{columns[idx]}: 最大值 = {value}")

    return max_lengths, max_values


def create_oracle_table(connection, drop_if_exists=False):
    """
    在Oracle數據庫中創建表格

    Args:
        connection: Oracle數據庫連接
        drop_if_exists: 如果表格已存在，是否刪除重建
    """
    try:
        cursor = connection.cursor()

        # 檢查表是否已存在
        table_exists = False
        try:
            cursor.execute("SELECT COUNT(*) FROM SALES")
            table_exists = True
            print("表格 SALES 已存在")

            if drop_if_exists:
                print("根據設置，將刪除並重建表格")
                cursor.execute("DROP TABLE SALES")
                table_exists = False
            else:
                return
        except:
            print("表格 SALES 不存在，將建立新表格")

        if not table_exists:
            # 創建表格 - 修正字段精度
            create_table_sql = """
                               CREATE TABLE SALES \
                               ( \
                                   ID              NUMBER PRIMARY KEY, \
                                   年月            VARCHAR2(10), \
                                   產品編號        VARCHAR2(30), \
                                   廠商代碼        VARCHAR2(30), \
                                   廠商名稱        VARCHAR2(100), \
                                   產品名稱        VARCHAR2(200), \
                                   包裝規格        VARCHAR2(100), \
                                   單價_不含稅     NUMBER(15, 4), \
                                   稅額            NUMBER(15, 4), \
                                   單價            NUMBER(15, 4), \
                                   店鋪代碼        VARCHAR2(30), \
                                   店鋪名稱        VARCHAR2(100), \
                                   銷售量1         NUMBER(15), \
                                   銷售量2         NUMBER(15), \
                                   銷售量3         NUMBER(15), \
                                   銷售量4         NUMBER(15), \
                                   銷售金額        NUMBER(20, 4), \
                                   銷售金額_不含稅 NUMBER(20, 4), \
                                   庫存量          NUMBER(15), \
                                   其他數值        NUMBER(15), \
                                   條碼            VARCHAR2(100), \
                                   未知1           VARCHAR2(100), \
                                   未知2           VARCHAR2(100), \
                                   未知3           VARCHAR2(100), \
                                   未知4           VARCHAR2(100), \
                                   未知5           VARCHAR2(100), \
                                   有效期_開始     VARCHAR2(30), \
                                   有效期_結束     VARCHAR2(30)
                               ) \
                               """
            cursor.execute(create_table_sql)

            # 創建序列用於自增ID
            try:
                cursor.execute("DROP SEQUENCE SALES_SEQ")
            except:
                pass  # 如果序列不存在，忽略錯誤

            cursor.execute("""
            CREATE SEQUENCE SALES_SEQ
            START WITH 1
            INCREMENT BY 1
            NOMAXVALUE
            """)

            # 創建觸發器來自動填充ID
            trigger_sql = """
                          CREATE OR REPLACE TRIGGER SALES_TRG
                              BEFORE INSERT \
                              ON SALES
                              FOR EACH ROW
                          BEGIN
                              SELECT SALES_SEQ.NEXTVAL INTO :NEW.ID FROM DUAL;
                          END; \
                          """
            cursor.execute(trigger_sql)

            connection.commit()
            print("成功創建表格、序列和觸發器")

    except Exception as e:
        print(f"創建表格時出錯: {str(e)}")
        connection.rollback()
        raise


def import_data_to_oracle(file_path, connection):
    """
    從文件匯入資料到Oracle數據庫

    Args:
        file_path (str): 文件路徑
        connection: Oracle數據庫連接
    """
    # 檢查文件是否存在
    if not os.path.exists(file_path):
        print(f"錯誤：找不到文件 '{file_path}'")
        return

    # 以二進制模式讀取文件
    try:
        with open(file_path, 'rb') as f:
            file_content = f.read()

            # 根據文件診斷結果，我們知道文件似乎是Big5編碼
            # 首先嘗試Big5編碼
            encodings_to_try = ['big5', 'gbk', 'gb2312', 'cp950', 'cp936', 'utf-8', 'utf-16', 'ascii']

            # 存儲成功解析的行
            successful_lines = []
            successful_encoding = None

            for encoding in encodings_to_try:
                try:
                    print(f"嘗試使用 {encoding} 解碼文件...")
                    decoded_content = file_content.decode(encoding, errors='replace')

                    # 按行分割
                    lines = decoded_content.splitlines()

                    # 檢查前幾行是否含有分隔符 ^
                    sample_lines = [line for line in lines[:10] if '^' in line]

                    if sample_lines:
                        successful_lines = lines
                        successful_encoding = encoding
                        print(f"使用 {encoding} 成功解碼文件")
                        print(f"共發現 {len(lines)} 行數據")
                        print(f"示例行: {sample_lines[0][:100]}")
                        break
                except Exception as e:
                    print(f"使用 {encoding} 解碼時出錯: {e}")
                    continue

            if not successful_lines:
                # 如果標準解碼都失敗，嘗試更激進的方法：尋找 ^ 分隔符並分割
                print("標準解碼方法失敗，嘗試直接搜索分隔符...")

                # 假設 ^ 是 ASCII 符號 (0x5E)，尋找它來分割數據
                # 先將二進制數據按照回車符或換行符分割成行
                binary_lines = re.split(b'\r\n|\r|\n', file_content)
                print(f"二進制模式下發現 {len(binary_lines)} 行")

                # 使用 b'^' 分割每一行
                for i, binary_line in enumerate(binary_lines):
                    try:
                        if b'^' in binary_line:
                            fields = binary_line.split(b'^')
                            field_texts = []

                            # 嘗試以不同編碼解碼每個字段
                            for field in fields:
                                field_decoded = None
                                for enc in encodings_to_try:
                                    try:
                                        field_decoded = field.decode(enc, errors='replace')
                                        if '�' not in field_decoded:  # 如果沒有替換字符，說明解碼可能是正確的
                                            break
                                    except:
                                        continue

                                if field_decoded is None:
                                    field_decoded = str(field)  # 最壞情況下使用原始表示

                                field_texts.append(field_decoded)

                            successful_lines.append('^'.join(field_texts))

                            if i < 3:  # 只顯示前3行
                                print(f"行 {i + 1} 解析結果: {'^'.join(field_texts)[:100]}")
                    except Exception as e:
                        if i < 5:  # 只顯示前5行的錯誤
                            print(f"處理行 {i + 1} 時出錯: {e}")

            # 分析字段長度和最大值
            if successful_lines:
                max_lengths, max_values = check_field_lengths(successful_lines)

                # 處理成功解析的行
                cursor = connection.cursor()

                # 準備批量插入的SQL語句 - 不包含ID欄位，因為它會由觸發器自動生成
                insert_sql = """
                             INSERT INTO SALES (年月, 產品編號, 廠商代碼, 廠商名稱, 產品名稱, 包裝規格, \
                                                單價_不含稅, 稅額, 單價, 店鋪代碼, 店鋪名稱, \
                                                銷售量1, 銷售量2, 銷售量3, 銷售量4, 銷售金額, 銷售金額_不含稅, \
                                                庫存量, 其他數值, 條碼, 未知1, 未知2, 未知3, 未知4, 未知5, \
                                                有效期_開始, 有效期_結束) \
                             VALUES (: 1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, \
                                     :15, :16, :17, :18, :19, :20, :21, :22, :23, :24, :25, :26, :27) \
                             """

                # 使用批量處理提高效率
                batch_size = 1000
                batch_data = []
                successful_inserts = 0
                error_count = 0

                for line_num, line in enumerate(successful_lines):
                    line = line.strip()

                    if not line or '^' not in line:
                        continue

                    # 使用 ^ 分割行
                    row = line.split('^')

                    try:
                        # 準備插入數據
                        row_data = []

                        # 確保有足夠的列數
                        while len(row) < 27:
                            row.append("")

                        # 格式化數據類型
                        for i, value in enumerate(row):
                            if i in [6, 7, 8, 15, 16]:  # 金額字段
                                try:
                                    row_data.append(float(value) if value else 0.0)
                                except (ValueError, TypeError):
                                    row_data.append(0.0)
                            elif i in [12, 13, 14, 15, 17, 18]:  # 整數字段
                                try:
                                    row_data.append(int(value) if value else 0)
                                except (ValueError, TypeError):
                                    row_data.append(0)
                            else:  # 文本字段
                                # 截斷過長的文本，避免超出VARCHAR2限制
                                max_text_len = 4000  # Oracle VARCHAR2最大限制
                                if value and len(value) > max_text_len:
                                    value = value[:max_text_len]
                                row_data.append(value)

                        # 添加到批處理數據
                        batch_data.append(tuple(row_data))

                        # 當批處理達到指定大小時執行插入
                        if len(batch_data) >= batch_size:
                            try:
                                cursor.executemany(insert_sql, batch_data)
                                connection.commit()
                                successful_inserts += len(batch_data)
                                print(f"已處理 {successful_inserts} 筆記錄...")
                                batch_data = []
                            except cx_Oracle.DatabaseError as e:
                                error, = e.args
                                print(f"批量插入時發生錯誤: {error.message}")
                                print("嘗試逐條插入記錄...")

                                # 如果批量插入失敗，嘗試逐條插入
                                for single_row in batch_data:
                                    try:
                                        cursor.execute(insert_sql, single_row)
                                        successful_inserts += 1
                                    except cx_Oracle.DatabaseError as e2:
                                        error_count += 1
                                        if error_count < 10:  # 只顯示前10個錯誤
                                            error_obj, = e2.args
                                            print(f"插入記錄時出錯: {error_obj.message}")

                                connection.commit()
                                batch_data = []

                    except Exception as e:
                        print(f"處理第 {line_num + 1} 行時出錯: {e}")
                        if line_num < 5:  # 只顯示前5行的錯誤詳情
                            print(f"行內容: {line}")
                        continue

                # 處理剩餘的數據
                if batch_data:
                    try:
                        cursor.executemany(insert_sql, batch_data)
                        connection.commit()
                        successful_inserts += len(batch_data)
                    except cx_Oracle.DatabaseError as e:
                        error, = e.args
                        print(f"批量插入時發生錯誤: {error.message}")
                        print("嘗試逐條插入記錄...")

                        # 如果批量插入失敗，嘗試逐條插入
                        for single_row in batch_data:
                            try:
                                cursor.execute(insert_sql, single_row)
                                successful_inserts += 1
                            except cx_Oracle.DatabaseError as e2:
                                error_count += 1
                                if error_count < 10:  # 只顯示前10個錯誤
                                    error_obj, = e2.args
                                    print(f"插入記錄時出錯: {error_obj.message}")

                        connection.commit()

                print(f"文件處理完成: 成功插入 {successful_inserts} 筆記錄，失敗 {error_count} 筆")

            else:
                print("無法解析文件，請檢查文件格式。")

    except Exception as e:
        print(f"處理文件時出錯: {e}")
        connection.rollback()
        raise


def verify_data(connection):
    """
    驗證導入的數據

    Args:
        connection: Oracle數據庫連接
    """
    try:
        cursor = connection.cursor()

        # 查詢記錄總數
        cursor.execute("SELECT COUNT(*) FROM SALES")
        count = cursor.fetchone()[0]
        print(f"\n數據庫中共有 {count} 筆記錄")

        # 顯示樣本數據
        if count > 0:
            print("\n樣本數據:")
            cursor.execute("SELECT * FROM SALES WHERE ROWNUM <= 3")
            rows = cursor.fetchall()

            for row in rows:
                print(row)

            # 顯示統計信息
            print("\n銷售數據統計:")
            cursor.execute("SELECT SUM(銷售金額) FROM SALES")
            total_sales = cursor.fetchone()[0]
            print(f"總銷售金額: {total_sales}")

            cursor.execute("SELECT 店鋪名稱, COUNT(*) FROM SALES GROUP BY 店鋪名稱")
            store_counts = cursor.fetchall()
            print("\n每個店鋪的記錄數:")
            for store, count in store_counts:
                print(f"{store}: {count}")

    except Exception as e:
        print(f"查詢數據時出錯: {e}")


def main():
    """
    主函數
    """
    # 請修改為您的實際文件路徑
    txt_file_path = "284303202504170D.txt"

    # 首先進行文件診斷
    try_direct_methods(txt_file_path)

    # 連接到Oracle數據庫
    try:
        print(f"\n嘗試連接到Oracle數據庫...")
        connection = cx_Oracle.connect(
            user=oracle_username,
            password=oracle_password,
            dsn=oracle_dsn
        )
        print("成功連接到Oracle數據庫")

        # 創建表格 - 設置為True表示如果表格已存在則刪除重建
        create_oracle_table(connection, drop_if_exists=True)

        # 導入數據
        print(f"\n開始從 {txt_file_path} 匯入數據...")
        import_data_to_oracle(txt_file_path, connection)

        # 驗證數據
        verify_data(connection)

        # 關閉連接
        connection.close()
        print("\n處理完成，數據庫連接已關閉")

    except cx_Oracle.Error as e:
        error, = e.args
        print(f"Oracle錯誤: {error.message}")

    except Exception as e:
        print(f"程序執行時出錯: {e}")
        print("請確保已經正確安裝cx_Oracle和Oracle客戶端")
        print("安裝指南: pip install cx_Oracle")
        print("Oracle客戶端下載: https://www.oracle.com/database/technologies/instant-client/downloads.html")


if __name__ == "__main__":
    main()

# 使用說明：
# 1. 安裝必要的庫: pip install cx_Oracle
# 2. 安裝Oracle客戶端 (Instant Client)
# 3. 修改oracle_username, oracle_password, oracle_dsn變數為您的Oracle連接資訊
# 4. 修改txt_file_path為您的數據文件路徑
# 5. 運行此腳本