#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Intel HEX 檔案分析器
用於分析和顯示 .hex 檔案內容
"""

import os
import sys

class IntelHexAnalyzer:
    def __init__(self, hex_file_path):
        self.hex_file_path = hex_file_path
        self.memory_map = {}
        self.base_address = 0
        self.entry_point = None
        
    def parse_hex_file(self):
        """解析 Intel HEX 檔案"""
        print(f"\n=== 分析 HEX 檔案: {self.hex_file_path} ===")
        
        if not os.path.exists(self.hex_file_path):
            print(f"❌ 檔案不存在: {self.hex_file_path}")
            return False
            
        line_count = 0
        data_bytes = 0
        min_addr = 0xFFFFFFFF
        max_addr = 0
        
        with open(self.hex_file_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                    
                if not line.startswith(':'):
                    print(f"❌ 第 {line_num} 行: 無效格式（缺少起始符號 ':'）")
                    continue
                    
                try:
                    # 解析 Intel HEX 格式
                    # :LLAAAATT[DD...]CC
                    # LL = 數據長度
                    # AAAA = 地址
                    # TT = 記錄類型
                    # DD = 數據
                    # CC = 校驗和
                    
                    byte_count = int(line[1:3], 16)
                    address = int(line[3:7], 16)
                    record_type = int(line[7:9], 16)
                    
                    # 提取數據部分
                    data_end = 9 + (byte_count * 2)
                    data = line[9:data_end]
                    checksum = int(line[data_end:data_end+2], 16)
                    
                    # 處理不同的記錄類型
                    if record_type == 0x00:  # 數據記錄
                        actual_address = self.base_address + address
                        
                        # 更新地址範圍
                        if actual_address < min_addr:
                            min_addr = actual_address
                        if actual_address + byte_count > max_addr:
                            max_addr = actual_address + byte_count
                            
                        # 存儲數據
                        for i in range(0, len(data), 2):
                            byte_value = int(data[i:i+2], 16)
                            self.memory_map[actual_address + i//2] = byte_value
                            
                        data_bytes += byte_count
                        
                    elif record_type == 0x01:  # EOF 記錄
                        print(f"✅ 找到 EOF 記錄（第 {line_num} 行）")
                        
                    elif record_type == 0x02:  # 擴展段地址
                        segment = int(data, 16)
                        self.base_address = segment * 16
                        print(f"📍 擴展段地址: 0x{self.base_address:04X}")
                        
                    elif record_type == 0x04:  # 擴展線性地址
                        upper_addr = int(data, 16)
                        self.base_address = upper_addr << 16
                        print(f"📍 擴展線性地址: 0x{self.base_address:08X}")
                        
                    elif record_type == 0x05:  # 起始線性地址（入口點）
                        self.entry_point = int(data, 16)
                        print(f"🎯 入口點: 0x{self.entry_point:08X}")
                        
                    line_count += 1
                    
                except Exception as e:
                    print(f"❌ 第 {line_num} 行解析錯誤: {e}")
                    print(f"   內容: {line}")
                    
        # 顯示統計信息
        print(f"\n📊 HEX 檔案統計:")
        print(f"   總行數: {line_count}")
        print(f"   數據字節: {data_bytes}")
        print(f"   地址範圍: 0x{min_addr:08X} - 0x{max_addr:08X}")
        print(f"   地址空間: {max_addr - min_addr} 字節")
        
        # 顯示記憶體映射
        self.display_memory_map()
        
        return True
        
    def display_memory_map(self):
        """顯示記憶體映射"""
        print(f"\n📦 記憶體映射（前 256 字節）:")
        
        if not self.memory_map:
            print("   （無數據）")
            return
            
        addresses = sorted(self.memory_map.keys())
        
        # 顯示前 256 字節
        for i in range(0, min(256, len(addresses)), 16):
            if i >= len(addresses):
                break
                
            addr = addresses[i]
            # 檢查是否連續
            line_data = []
            for j in range(16):
                if i + j < len(addresses) and addresses[i + j] == addr + j:
                    line_data.append(self.memory_map[addresses[i + j]])
                else:
                    line_data.append(None)
                    
            # 格式化顯示
            hex_str = ""
            ascii_str = ""
            for byte in line_data:
                if byte is not None:
                    hex_str += f"{byte:02X} "
                    ascii_str += chr(byte) if 32 <= byte < 127 else '.'
                else:
                    hex_str += "-- "
                    ascii_str += ' '
                    
            print(f"   0x{addr:08X}: {hex_str:<48} |{ascii_str}|")
            
    def generate_write_sequence(self):
        """生成 STM32 Bootloader 寫入序列"""
        print(f"\n=== 生成寫入序列 ===")
        
        if not self.memory_map:
            print("❌ 無數據可寫入")
            return
            
        # 按地址排序
        addresses = sorted(self.memory_map.keys())
        
        # 分組為連續的塊
        blocks = []
        current_block_start = addresses[0]
        current_block_data = [self.memory_map[addresses[0]]]
        
        for i in range(1, len(addresses)):
            if addresses[i] == addresses[i-1] + 1:
                # 連續地址
                current_block_data.append(self.memory_map[addresses[i]])
            else:
                # 不連續，保存當前塊並開始新塊
                blocks.append((current_block_start, current_block_data))
                current_block_start = addresses[i]
                current_block_data = [self.memory_map[addresses[i]]]
                
        # 保存最後一個塊
        blocks.append((current_block_start, current_block_data))
        
        print(f"📋 共 {len(blocks)} 個數據塊需要寫入")
        
        # 顯示前幾個塊的寫入命令
        for i, (start_addr, data) in enumerate(blocks[:5]):
            print(f"\n塊 {i+1}: 地址=0x{start_addr:08X}, 大小={len(data)} 字節")
            
            # 分割為 256 字節的頁
            page_size = 256
            for page_offset in range(0, len(data), page_size):
                page_data = data[page_offset:page_offset + page_size]
                page_addr = start_addr + page_offset
                
                print(f"\n  頁: 地址=0x{page_addr:08X}, 大小={len(page_data)} 字節")
                
                # 1. 寫入命令
                print(f"  1) 發送寫入命令: 31 CE")
                
                # 2. 地址
                addr_bytes = [
                    (page_addr >> 24) & 0xFF,
                    (page_addr >> 16) & 0xFF,
                    (page_addr >> 8) & 0xFF,
                    page_addr & 0xFF
                ]
                addr_checksum = 0
                for b in addr_bytes:
                    addr_checksum ^= b
                    
                print(f"  2) 發送地址: {' '.join([f'{b:02X}' for b in addr_bytes])} {addr_checksum:02X}")
                
                # 3. 數據
                data_packet = [len(page_data) - 1]  # N-1
                data_packet.extend(page_data)
                data_checksum = 0
                for b in data_packet:
                    data_checksum ^= b
                    
                print(f"  3) 發送數據: {data_packet[0]:02X} {' '.join([f'{b:02X}' for b in page_data[:8]])}... {data_checksum:02X}")
                
                if page_offset >= page_size:  # 只顯示第一頁
                    print(f"  ... (還有 {(len(data) - page_offset - 1) // page_size} 頁)")
                    break
                    
        if len(blocks) > 5:
            print(f"\n... 還有 {len(blocks) - 5} 個數據塊")
            
    def calculate_total_write_time(self):
        """計算總寫入時間估算"""
        if not self.memory_map:
            return
            
        total_bytes = len(self.memory_map)
        page_size = 256
        total_pages = (total_bytes + page_size - 1) // page_size
        
        # 估算時間（每頁約 50ms）
        estimated_time = total_pages * 0.05
        
        print(f"\n⏱️ 寫入時間估算:")
        print(f"   總字節數: {total_bytes}")
        print(f"   總頁數: {total_pages}")
        print(f"   預計時間: {estimated_time:.1f} 秒")

def main():
    if len(sys.argv) < 2:
        print("使用方法: python hex_file_analyzer.py <hex_file_path>")
        print("範例: python hex_file_analyzer.py firmware.hex")
        
        # 如果沒有參數，使用示例
        print("\n使用示例 HEX 內容進行演示...")
        
        # 創建示例 HEX 檔案
        example_hex = """:020000040800F2
:10000000000000000000000000000000000000009F
:1000100000000000000000000000000000000000AF
:10002000000000000000000000000000000000009F
:04003000DEADBEEF1E
:00000001FF"""
        
        with open("example.hex", "w") as f:
            f.write(example_hex)
            
        analyzer = IntelHexAnalyzer("example.hex")
    else:
        analyzer = IntelHexAnalyzer(sys.argv[1])
        
    if analyzer.parse_hex_file():
        analyzer.generate_write_sequence()
        analyzer.calculate_total_write_time()

if __name__ == "__main__":
    main()