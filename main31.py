import json
import re
import cx_Oracle


# 讀取 JSON 檔案
def load_json_data(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return json.load(file)


# 從地址中提取郵遞區號
def extract_zipcode(address):
    pattern = r'\((\d+)\)'
    match = re.search(pattern, address)
    if match:
        return match.group(1)
    return None


# 從地址中提取街道地址(不含城市和地區)
def extract_street_address(address, city, area):
    # 移除開頭的郵遞區號部分
    address_without_zipcode = re.sub(r'\(\d+\)', '', address).strip()

    # 移除城市和地區部分
    if city in address_without_zipcode and area in address_without_zipcode:
        street_part = address_without_zipcode.replace(city, '', 1).replace(area, '', 1).strip()
        return street_part

    return address_without_zipcode


# 檢查資料表是否存在並創建
def create_table_if_not_exists(cursor):
    # 檢查資料表是否存在
    try:
        cursor.execute("SELECT COUNT(*) FROM PX_STORES WHERE ROWNUM = 1")
        print("資料表 PX_STORES 已存在")
        return False
    except cx_Oracle.DatabaseError:
        print("資料表 PX_STORES 不存在，即將創建...")

        # 創建資料表
        sql = """
        CREATE TABLE PX_STORES (
            STORE_ID NUMBER PRIMARY KEY,           -- 主鍵，使用全聯提供的ID
            STORE_CODE VARCHAR2(6),                -- 門店代碼
            STORE_NAME VARCHAR2(100) NOT NULL,     -- 門店名稱
            ZIPCODE VARCHAR2(10),                  -- 郵遞區號 (調整為10位以應對可能的變化)
            CITY VARCHAR2(50),                     -- 城市
            AREA VARCHAR2(50),                     -- 地區
            FULL_ADDRESS VARCHAR2(200),            -- 完整地址
            STREET_ADDRESS VARCHAR2(150),          -- 街道地址(不含城市和地區)
            PHONE VARCHAR2(20),                    -- 電話號碼
            LONGITUDE NUMBER(9,6),                 -- 經度
            LATITUDE NUMBER(9,6),                  -- 緯度
            OPEN_TIME VARCHAR2(5),                 -- 開始營業時間
            CLOSE_TIME VARCHAR2(5),                -- 結束營業時間
            IS_RECOMMEND VARCHAR2(1) DEFAULT 'N',  -- 是否推薦
            IS_IMAGE VARCHAR2(1) DEFAULT 'N',      -- 是否有圖片
            CREATE_DATE DATE DEFAULT SYSDATE,      -- 資料建立日期
            UPDATE_DATE DATE                       -- 資料更新日期
        )
        """
        cursor.execute(sql)

        # 添加註釋
        cursor.execute("COMMENT ON TABLE PX_STORES IS '全聯門店基本資料表'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.STORE_ID IS '門店ID'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.STORE_CODE IS '門店代碼'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.STORE_NAME IS '門店名稱'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.ZIPCODE IS '郵遞區號'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.CITY IS '城市'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.AREA IS '地區'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.FULL_ADDRESS IS '完整地址'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.STREET_ADDRESS IS '街道地址'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.PHONE IS '電話號碼'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.LONGITUDE IS '經度'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.LATITUDE IS '緯度'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.OPEN_TIME IS '開始營業時間'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.CLOSE_TIME IS '結束營業時間'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.IS_RECOMMEND IS '是否推薦（Y/N）'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.IS_IMAGE IS '是否有圖片（Y/N）'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.CREATE_DATE IS '資料建立日期'")
        cursor.execute("COMMENT ON COLUMN PX_STORES.UPDATE_DATE IS '資料更新日期'")

        # 創建索引
        cursor.execute("CREATE INDEX IDX_PX_STORES_CODE ON PX_STORES(STORE_CODE)")
        cursor.execute("CREATE INDEX IDX_PX_STORES_CITY ON PX_STORES(CITY)")
        cursor.execute("CREATE INDEX IDX_PX_STORES_AREA ON PX_STORES(AREA)")
        cursor.execute("CREATE INDEX IDX_PX_STORES_ZIPCODE ON PX_STORES(ZIPCODE)")

        print("資料表 PX_STORES 創建完成")
        return True


# 處理門店資料並直接插入到資料庫
def insert_store_data(cursor, store_data):
    store_id = store_data["id"]
    attrs = store_data["attributes"]

    store_code = attrs.get("code", "")
    store_name = attrs.get("name", "")
    city = attrs.get("city", "")
    area = attrs.get("area", "")
    full_address = attrs.get("address", "")
    zipcode = extract_zipcode(full_address)
    street_address = extract_street_address(full_address, city, area)
    phone = attrs.get("phone", "")
    longitude = attrs.get("longitude", 0)
    latitude = attrs.get("latitude", 0)
    open_time = attrs.get("startDate", "")
    close_time = attrs.get("endDate", "")
    is_recommend = 'Y' if attrs.get("isRecommend", False) else 'N'
    is_image = 'Y' if attrs.get("isImage", False) else 'N'

    # 使用參數化查詢防止SQL注入
    sql = """
    INSERT INTO PX_STORES (
        STORE_ID, STORE_CODE, STORE_NAME, ZIPCODE, CITY, AREA, 
        FULL_ADDRESS, STREET_ADDRESS, PHONE, LONGITUDE, LATITUDE, 
        OPEN_TIME, CLOSE_TIME, IS_RECOMMEND, IS_IMAGE, CREATE_DATE
    ) VALUES (
        :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, SYSDATE
    )
    """

    cursor.execute(sql, (
        store_id, store_code, store_name, zipcode, city, area,
        full_address, street_address, phone, longitude, latitude,
        open_time, close_time, is_recommend, is_image
    ))


# 直接從JSON檔案讀取資料並插入到資料庫
def process_and_insert_to_database(json_file_path, db_connection_string):
    # 讀取JSON資料
    json_data = load_json_data(json_file_path)
    stores = json_data.get("data", [])

    print(f"從JSON檔案讀取了 {len(stores)} 筆門店資料")

    # 連接到Oracle資料庫
    try:
        connection = cx_Oracle.connect(db_connection_string)
        cursor = connection.cursor()

        # 檢查並創建資料表（如果不存在）
        create_table_if_not_exists(cursor)

        # 插入資料
        success_count = 0
        error_count = 0

        for i, store in enumerate(stores):
            try:
                # 檢查門店ID是否已存在
                store_id = store["id"]
                cursor.execute("SELECT COUNT(*) FROM PX_STORES WHERE STORE_ID = :1", (store_id,))
                if cursor.fetchone()[0] > 0:
                    # 如果已存在，則更新
                    print(f"門店ID {store_id} 已存在，將進行更新")
                    # 這裡可以添加更新現有記錄的邏輯
                else:
                    # 否則插入新記錄
                    insert_store_data(cursor, store)
                    success_count += 1

                # 每100筆提交一次以提高效能
                if (i + 1) % 100 == 0:
                    connection.commit()
                    print(f"已處理 {i + 1} 筆資料，提交至資料庫")

            except Exception as e:
                error_count += 1
                print(f"處理門店ID {store.get('id', '未知')} 時發生錯誤: {e}")

        # 最終提交
        connection.commit()

        print(f"\n資料處理完成:")
        print(f"成功插入: {success_count} 筆")
        print(f"處理失敗: {error_count} 筆")

    except cx_Oracle.DatabaseError as e:
        print(f"資料庫連接或操作錯誤: {e}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()
            print("資料庫連接已關閉")


# 主程式
if __name__ == "__main__":
    # 檔案路徑
    json_file_path = "pxmart_stores.json"  # 請替換為實際的 JSON 檔案路徑

    # 資料庫連接字串
    db_connection_string = "asuser/asuser@172.20.160.32/hy1"  # 請替換為實際的資料庫連接資訊

    # 處理資料並直接插入資料庫
    process_and_insert_to_database(json_file_path, db_connection_string)

    print("全部處理完成!")