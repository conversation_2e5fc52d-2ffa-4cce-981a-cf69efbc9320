#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的串口發送工具
用於測試STM32 Android應用程式的接收功能
"""

import serial
import serial.tools.list_ports
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime

class SerialSender:
    def __init__(self):
        self.serial_port = None
        self.is_connected = False
        self.is_receiving = False
        
        # 創建主窗口
        self.root = tk.Tk()
        self.root.title("串口發送工具 - STM32 Android測試")
        self.root.geometry("800x700")
        
        self.setup_ui()
        self.refresh_ports()
        
    def setup_ui(self):
        # 串口設置框架
        port_frame = ttk.LabelFrame(self.root, text="串口設置", padding=10)
        port_frame.pack(fill="x", padx=10, pady=5)
        
        # 串口選擇
        ttk.Label(port_frame, text="串口:").grid(row=0, column=0, sticky="w")
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(port_frame, textvariable=self.port_var, width=15)
        self.port_combo.grid(row=0, column=1, padx=5)
        
        ttk.Button(port_frame, text="刷新", command=self.refresh_ports).grid(row=0, column=2, padx=5)
        
        # 波特率
        ttk.Label(port_frame, text="波特率:").grid(row=0, column=3, sticky="w", padx=(20,0))
        self.baud_var = tk.StringVar(value="115200")
        baud_combo = ttk.Combobox(port_frame, textvariable=self.baud_var, width=10)
        baud_combo['values'] = ('9600', '19200', '38400', '57600', '115200')
        baud_combo.grid(row=0, column=4, padx=5)
        
        # 連接按鈕
        self.connect_btn = ttk.Button(port_frame, text="連接", command=self.toggle_connection)
        self.connect_btn.grid(row=0, column=5, padx=10)
        
        # 狀態標籤
        self.status_label = ttk.Label(port_frame, text="未連接", foreground="red")
        self.status_label.grid(row=1, column=0, columnspan=6, pady=5)
        
        # 發送框架
        send_frame = ttk.LabelFrame(self.root, text="發送數據", padding=10)
        send_frame.pack(fill="x", padx=10, pady=5)
        
        # 發送輸入框
        ttk.Label(send_frame, text="發送內容:").pack(anchor="w")
        self.send_entry = tk.Entry(send_frame, font=("Arial", 12))
        self.send_entry.pack(fill="x", pady=5)
        self.send_entry.bind("<Return>", lambda e: self.send_data())
        
        # 發送按鈕框架
        btn_frame = tk.Frame(send_frame)
        btn_frame.pack(fill="x", pady=5)
        
        self.send_btn = ttk.Button(btn_frame, text="發送", command=self.send_data)
        self.send_btn.pack(side="left", padx=5)
        
        ttk.Button(btn_frame, text="發送AT", command=lambda: self.send_text("AT")).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="測試Hello", command=lambda: self.send_text("Hello Android")).pack(side="left", padx=5)
        
        # 快速發送按鈕
        quick_frame = tk.Frame(send_frame)
        quick_frame.pack(fill="x", pady=5)
        
        ttk.Label(quick_frame, text="快速發送:").pack(side="left")
        ttk.Button(quick_frame, text="測試1", command=lambda: self.send_text("Test message 1")).pack(side="left", padx=2)
        ttk.Button(quick_frame, text="測試2", command=lambda: self.send_text("Test message 2")).pack(side="left", padx=2)
        ttk.Button(quick_frame, text="版本查詢", command=lambda: self.send_text("AT+VERSION?")).pack(side="left", padx=2)
        
        # STM32 Bootloader 測試按鈕
        bootloader_frame = ttk.LabelFrame(self.root, text="STM32 Bootloader 測試", padding=10)
        bootloader_frame.pack(fill="x", padx=10, pady=5)
        
        btn_row1 = tk.Frame(bootloader_frame)
        btn_row1.pack(fill="x", pady=2)
        
        ttk.Button(btn_row1, text="自定義進入Bootloader (0xB2)", 
                   command=self.send_custom_bootloader_cmd).pack(side="left", padx=2)
        ttk.Button(btn_row1, text="STM32同步字節 (0x7F)", 
                   command=self.send_stm32_sync).pack(side="left", padx=2)
        ttk.Button(btn_row1, text="重置MCU", 
                   command=self.send_reset_mcu).pack(side="left", padx=2)
        
        btn_row2 = tk.Frame(bootloader_frame)
        btn_row2.pack(fill="x", pady=2)
        
        ttk.Button(btn_row2, text="完整Bootloader序列", 
                   command=self.send_full_bootloader_sequence).pack(side="left", padx=2)
        ttk.Button(btn_row2, text="發送測試韌體包", 
                   command=self.send_test_firmware_packet).pack(side="left", padx=2)
        
        # HEX發送框架
        hex_frame = tk.Frame(bootloader_frame)
        hex_frame.pack(fill="x", pady=5)
        
        ttk.Label(hex_frame, text="HEX發送:").pack(side="left")
        self.hex_entry = tk.Entry(hex_frame, width=40, font=("Consolas", 10))
        self.hex_entry.pack(side="left", padx=5)
        self.hex_entry.value = "01 0F B2 31 32 33 34 35 36 37 38 39 3A B3"
        self.hex_entry.insert(0, self.hex_entry.value)
        
        ttk.Button(hex_frame, text="發送HEX", command=self.send_hex_data).pack(side="left", padx=2)
        
        # 接收框架
        recv_frame = ttk.LabelFrame(self.root, text="接收數據", padding=10)
        recv_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 接收控制
        recv_ctrl_frame = tk.Frame(recv_frame)
        recv_ctrl_frame.pack(fill="x", pady=(0,5))
        
        self.recv_btn = ttk.Button(recv_ctrl_frame, text="開始接收", command=self.toggle_receiving)
        self.recv_btn.pack(side="left", padx=5)
        
        ttk.Button(recv_ctrl_frame, text="清除", command=self.clear_received).pack(side="left", padx=5)
        
        # 接收顯示區
        self.recv_text = scrolledtext.ScrolledText(recv_frame, height=15, font=("Consolas", 10))
        self.recv_text.pack(fill="both", expand=True)
        
    def refresh_ports(self):
        """刷新可用串口列表"""
        ports = serial.tools.list_ports.comports()
        port_list = [f"{port.device} - {port.description}" for port in ports]
        self.port_combo['values'] = port_list
        
        if port_list:
            # 自動選擇包含USB或Serial的端口
            for port in port_list:
                if any(keyword in port.upper() for keyword in ['USB', 'SERIAL', 'COM8']):
                    self.port_var.set(port)
                    break
            else:
                self.port_var.set(port_list[0])
        
    def toggle_connection(self):
        """切換連接狀態"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()
    
    def connect(self):
        """連接串口"""
        try:
            port_info = self.port_var.get()
            if not port_info:
                messagebox.showerror("錯誤", "請選擇串口")
                return
            
            port_name = port_info.split(" - ")[0]
            baud_rate = int(self.baud_var.get())
            
            self.serial_port = serial.Serial(
                port=port_name,
                baudrate=baud_rate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1
            )
            
            self.is_connected = True
            self.connect_btn.config(text="斷開")
            self.status_label.config(text=f"已連接到 {port_name} ({baud_rate})", foreground="green")
            self.log_message(f"✅ 已連接到 {port_name}, 波特率: {baud_rate}")
            
        except Exception as e:
            messagebox.showerror("連接錯誤", f"無法連接串口: {str(e)}")
    
    def disconnect(self):
        """斷開串口連接"""
        try:
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
            
            self.is_connected = False
            self.is_receiving = False
            self.connect_btn.config(text="連接")
            self.recv_btn.config(text="開始接收")
            self.status_label.config(text="未連接", foreground="red")
            self.log_message("❌ 已斷開連接")
            
        except Exception as e:
            messagebox.showerror("斷開錯誤", f"斷開連接時出錯: {str(e)}")
    
    def send_data(self):
        """發送數據"""
        text = self.send_entry.get().strip()
        if text:
            self.send_text(text)
            self.send_entry.delete(0, tk.END)
    
    def send_text(self, text):
        """發送指定文本"""
        if not self.is_connected or not self.serial_port:
            messagebox.showwarning("警告", "請先連接串口")
            return
        
        try:
            # 發送文本，添加換行符
            data = text + "\r\n"
            self.serial_port.write(data.encode('utf-8'))
            self.log_message(f"📤 發送: {text}")
            
        except Exception as e:
            messagebox.showerror("發送錯誤", f"發送數據時出錯: {str(e)}")
    
    def send_hex_data(self):
        """發送HEX格式數據"""
        hex_string = self.hex_entry.get().strip()
        if not hex_string:
            return
            
        if not self.is_connected or not self.serial_port:
            messagebox.showwarning("警告", "請先連接串口")
            return
            
        try:
            # 移除空格、冒號等分隔符
            hex_string = hex_string.replace(" ", "").replace(":", "").replace("-", "")
            
            # 轉換為字節數組
            hex_bytes = bytes.fromhex(hex_string)
            
            # 發送數據
            self.serial_port.write(hex_bytes)
            
            # 顯示發送的數據
            hex_display = ' '.join([f"{b:02X}" for b in hex_bytes])
            self.log_message(f"📤 發送HEX: {hex_display}")
            self.log_message(f"   長度: {len(hex_bytes)} bytes")
            
        except ValueError as e:
            messagebox.showerror("HEX錯誤", f"無效的HEX格式: {str(e)}")
        except Exception as e:
            messagebox.showerror("發送錯誤", f"發送HEX數據時出錯: {str(e)}")
    
    def calculate_xor_checksum(self, data):
        """計算XOR校驗和"""
        checksum = 0
        for byte in data:
            checksum ^= byte
        return checksum
    
    def build_custom_packet(self, cmd, data):
        """建構自定義協議包
        格式: [STX][LEN][CMD][DATA][CHECKSUM]
        """
        STX = 0x01
        packet = bytearray()
        packet.append(STX)
        packet.append(len(data) + 1)  # LEN = data length + cmd
        packet.append(cmd)
        packet.extend(data)
        
        # 計算校驗和（不包括STX）
        checksum = self.calculate_xor_checksum(packet[1:])
        packet.append(checksum)
        
        return bytes(packet)
    
    def send_custom_bootloader_cmd(self):
        """發送自定義進入Bootloader命令 (0xB2)"""
        CMD_ENTER_BOOTLOADER = 0xB2
        BOOTLOADER_ENTRY_DATA = b'123456789:'  # ASCII "123456789:"
        
        packet = self.build_custom_packet(CMD_ENTER_BOOTLOADER, BOOTLOADER_ENTRY_DATA)
        
        if not self.is_connected or not self.serial_port:
            messagebox.showwarning("警告", "請先連接串口")
            return
            
        try:
            self.serial_port.write(packet)
            hex_display = ' '.join([f"{b:02X}" for b in packet])
            self.log_message(f"📤 發送進入Bootloader命令:")
            self.log_message(f"   HEX: {hex_display}")
            self.log_message(f"   預期C#輸出: 01 0F B2 31 32 33 34 35 36 37 38 39 3A B3")
            
        except Exception as e:
            messagebox.showerror("發送錯誤", f"發送命令時出錯: {str(e)}")
    
    def send_stm32_sync(self):
        """發送STM32同步字節 (0x7F)"""
        if not self.is_connected or not self.serial_port:
            messagebox.showwarning("警告", "請先連接串口")
            return
            
        try:
            sync_byte = bytes([0x7F])
            self.serial_port.write(sync_byte)
            self.log_message(f"📤 發送STM32同步字節: 7F")
            self.log_message(f"   等待ACK回應 (0x79)...")
            
        except Exception as e:
            messagebox.showerror("發送錯誤", f"發送同步字節時出錯: {str(e)}")
    
    def send_reset_mcu(self):
        """發送重置MCU命令"""
        CMD_RESET_MCU = 0xB1
        RESET_DATA = b'RESET_MCU'
        
        packet = self.build_custom_packet(CMD_RESET_MCU, RESET_DATA)
        
        if not self.is_connected or not self.serial_port:
            messagebox.showwarning("警告", "請先連接串口")
            return
            
        try:
            self.serial_port.write(packet)
            hex_display = ' '.join([f"{b:02X}" for b in packet])
            self.log_message(f"📤 發送重置MCU命令:")
            self.log_message(f"   HEX: {hex_display}")
            
        except Exception as e:
            messagebox.showerror("發送錯誤", f"發送重置命令時出錯: {str(e)}")
    
    def send_full_bootloader_sequence(self):
        """發送完整的Bootloader進入序列"""
        if not self.is_connected or not self.serial_port:
            messagebox.showwarning("警告", "請先連接串口")
            return
            
        self.log_message("\n===== 開始完整Bootloader序列 =====")
        
        # 步驟1: 發送進入Bootloader命令
        self.log_message("步驟 1/3: 發送進入Bootloader命令...")
        self.send_custom_bootloader_cmd()
        
        # 等待2秒
        self.log_message("步驟 2/3: 等待設備重啟 (2秒)...")
        self.root.after(2000, self._continue_bootloader_sequence)
    
    def _continue_bootloader_sequence(self):
        """繼續Bootloader序列"""
        # 步驟3: 發送STM32同步字節
        self.log_message("步驟 3/3: 發送STM32同步字節...")
        self.send_stm32_sync()
        self.log_message("===== Bootloader序列完成 =====\n")
    
    def send_test_firmware_packet(self):
        """發送測試韌體數據包"""
        if not self.is_connected or not self.serial_port:
            messagebox.showwarning("警告", "請先連接串口")
            return
            
        try:
            # 模擬STM32寫入命令
            CMD_WRITE = 0x31
            write_cmd = bytes([CMD_WRITE, ~CMD_WRITE & 0xFF])
            
            self.log_message(f"📤 發送STM32寫入命令: 31 CE")
            self.serial_port.write(write_cmd)
            
            # 模擬發送地址（0x08000000）
            address = 0x08000000
            addr_bytes = bytearray([
                (address >> 24) & 0xFF,
                (address >> 16) & 0xFF,
                (address >> 8) & 0xFF,
                address & 0xFF
            ])
            checksum = self.calculate_xor_checksum(addr_bytes)
            addr_bytes.append(checksum)
            
            time.sleep(0.05)
            hex_display = ' '.join([f"{b:02X}" for b in addr_bytes])
            self.log_message(f"📤 發送地址: {hex_display}")
            self.serial_port.write(addr_bytes)
            
            # 模擬發送測試數據
            test_data = bytes([0xFF] * 16)  # 16字節的0xFF
            data_packet = bytearray([len(test_data) - 1])  # N-1
            data_packet.extend(test_data)
            checksum = self.calculate_xor_checksum(data_packet)
            data_packet.append(checksum)
            
            time.sleep(0.05)
            self.log_message(f"📤 發送測試數據 ({len(test_data)} bytes)")
            self.serial_port.write(data_packet)
            
        except Exception as e:
            messagebox.showerror("發送錯誤", f"發送測試韌體包時出錯: {str(e)}")
    
    def toggle_receiving(self):
        """切換接收狀態"""
        if not self.is_connected:
            messagebox.showwarning("警告", "請先連接串口")
            return
        
        if self.is_receiving:
            self.is_receiving = False
            self.recv_btn.config(text="開始接收")
            self.log_message("⏹️ 停止接收")
        else:
            self.is_receiving = True
            self.recv_btn.config(text="停止接收")
            self.log_message("▶️ 開始接收...")
            # 啟動接收線程
            threading.Thread(target=self.receive_data, daemon=True).start()
    
    def receive_data(self):
        """接收數據線程"""
        while self.is_receiving and self.is_connected and self.serial_port:
            try:
                if self.serial_port.in_waiting > 0:
                    # 讀取所有可用數據
                    raw_data = self.serial_port.read(self.serial_port.in_waiting)
                    
                    # 嘗試解碼為文本
                    try:
                        text_data = raw_data.decode('utf-8', errors='ignore').strip()
                        if text_data:
                            self.log_message(f"📥 接收(文本): {text_data}")
                    except:
                        pass
                    
                    # 同時顯示HEX格式
                    hex_display = ' '.join([f"{b:02X}" for b in raw_data])
                    if hex_display:
                        self.log_message(f"📥 接收(HEX): {hex_display}")
                time.sleep(0.1)
            except Exception as e:
                self.log_message(f"❌ 接收錯誤: {str(e)}")
                break
    
    def log_message(self, message):
        """記錄消息到接收區"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.recv_text.insert(tk.END, full_message)
        self.recv_text.see(tk.END)
    
    def clear_received(self):
        """清除接收區內容"""
        self.recv_text.delete(1.0, tk.END)
    
    def run(self):
        """運行應用程式"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """關閉應用程式時的清理"""
        if self.is_connected:
            self.disconnect()
        self.root.destroy()

if __name__ == "__main__":
    # 檢查是否安裝了pyserial
    try:
        import serial
    except ImportError:
        print("請先安裝pyserial庫:")
        print("pip install pyserial")
        exit(1)
    
    app = SerialSender()
    app.run()
