#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootloader 協議測試工具
用於驗證和對比 C# 與 Android Java 實現的差異
"""

import time

class BootloaderProtocolTest:
    """測試 Bootloader 協議實現"""
    
    @staticmethod
    def test_custom_protocol():
        """測試自定義協議包格式"""
        print("\n=== 測試自定義協議包 ===")
        
        # 常數定義
        STX = 0x01
        CMD_ENTER_BOOTLOADER = 0xB2
        BOOTLOADER_DATA = b'123456789:'
        
        # 建構包
        packet = bytearray()
        packet.append(STX)
        packet.append(len(BOOTLOADER_DATA) + 1)  # LEN = data length + cmd
        packet.append(CMD_ENTER_BOOTLOADER)
        packet.extend(BOOTLOADER_DATA)
        
        # 計算校驗和（不包括STX）
        checksum = 0
        for byte in packet[1:]:  # 從 LEN 開始
            checksum ^= byte
        packet.append(checksum)
        
        # 顯示結果
        hex_display = ' '.join([f"{b:02X}" for b in packet])
        print(f"Python 生成: {hex_display}")
        print(f"C# 預期值 : 01 0F B2 31 32 33 34 35 36 37 38 39 3A B3")
        print(f"匹配結果  : {'✅ 相同' if hex_display == '01 0F B2 31 32 33 34 35 36 37 38 39 3A B3' else '❌ 不同'}")
        
        # 詳細分析
        print("\n詳細分析:")
        print(f"  STX      : 0x{packet[0]:02X}")
        print(f"  LEN      : 0x{packet[1]:02X} ({packet[1]} = {len(BOOTLOADER_DATA)} + 1)")
        print(f"  CMD      : 0x{packet[2]:02X}")
        print(f"  DATA     : {' '.join([f'{b:02X}' for b in packet[3:-1]])}")
        print(f"  CHECKSUM : 0x{packet[-1]:02X}")
        
        # 校驗和計算過程
        print("\n校驗和計算:")
        checksum = 0
        for i, byte in enumerate(packet[1:-1]):
            old_checksum = checksum
            checksum ^= byte
            print(f"  Step {i+1}: 0x{old_checksum:02X} XOR 0x{byte:02X} = 0x{checksum:02X}")
        
        return packet
    
    @staticmethod
    def test_stm32_write_sequence():
        """測試 STM32 寫入序列"""
        print("\n=== 測試 STM32 寫入序列 ===")
        
        # 1. 寫入命令
        CMD_WRITE = 0x31
        write_cmd = bytes([CMD_WRITE, ~CMD_WRITE & 0xFF])
        print(f"1. 寫入命令: {' '.join([f'{b:02X}' for b in write_cmd])}")
        print(f"   CMD=0x{CMD_WRITE:02X}, ~CMD=0x{~CMD_WRITE & 0xFF:02X}")
        
        # 2. 地址包
        address = 0x08000000
        addr_bytes = bytearray([
            (address >> 24) & 0xFF,
            (address >> 16) & 0xFF,
            (address >> 8) & 0xFF,
            address & 0xFF
        ])
        
        # 計算地址校驗和
        addr_checksum = 0
        for byte in addr_bytes:
            addr_checksum ^= byte
        addr_bytes.append(addr_checksum)
        
        print(f"\n2. 地址包: {' '.join([f'{b:02X}' for b in addr_bytes])}")
        print(f"   地址=0x{address:08X}, 校驗和=0x{addr_checksum:02X}")
        
        # 3. 數據包
        test_data = bytes([0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                          0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F])
        
        data_packet = bytearray()
        data_packet.append(len(test_data) - 1)  # N-1
        data_packet.extend(test_data)
        
        # 計算數據校驗和
        data_checksum = 0
        for byte in data_packet:
            data_checksum ^= byte
        data_packet.append(data_checksum)
        
        print(f"\n3. 數據包:")
        print(f"   長度字節: 0x{data_packet[0]:02X} (N-1 = {len(test_data)}-1 = {len(test_data)-1})")
        print(f"   數據: {' '.join([f'{b:02X}' for b in data_packet[1:-1]])}")
        print(f"   校驗和: 0x{data_packet[-1]:02X}")
        
        return write_cmd, addr_bytes, data_packet
    
    @staticmethod
    def compare_implementations():
        """比較不同實現的差異"""
        print("\n=== Android 實現檢查清單 ===")
        
        print("\n1. CustomBootloaderProtocol.java 檢查:")
        print("   □ buildCustomPacket() 中 LEN 計算是否為 data.length + 1")
        print("   □ calculateXorChecksum() 是否從 packet[1] 開始（不包括 STX）")
        print("   □ BOOTLOADER_ENTRY_DATA 是否為 \"123456789:\" 的 byte[]")
        
        print("\n2. FirmwareUpdateHelper.java 檢查:")
        print("   □ hexStringToBytes() 是否正確處理 HEX 字符串")
        print("   □ writePage() 中地址是否使用大端序（Big-Endian）")
        print("   □ 數據包長度是否為 N-1 格式")
        print("   □ 校驗和計算是否包含長度字節")
        
        print("\n3. SimpleUsbTerminalService.java 檢查:")
        print("   □ sendData() 是否能正確發送二進制數據")
        print("   □ isHexFormat() 判斷是否正確")
        print("   □ 是否需要直接使用 write(byte[]) 而不是 sendData(String)")
        
        print("\n4. 常見問題:")
        print("   ❗ Android 端可能將 HEX 字符串當作文本發送")
        print("   ❗ 字節順序（大小端）可能不一致")
        print("   ❗ 校驗和計算範圍可能有誤")
        print("   ❗ 數據長度格式（N vs N-1）可能混淆")
    
    @staticmethod
    def generate_test_cases():
        """生成測試用例"""
        print("\n=== 測試用例 ===")
        
        test_cases = [
            {
                "name": "進入 Bootloader",
                "python": "01 0F B2 31 32 33 34 35 36 37 38 39 3A B3",
                "description": "自定義協議進入 bootloader"
            },
            {
                "name": "STM32 同步",
                "python": "7F",
                "description": "標準 STM32 bootloader 同步字節"
            },
            {
                "name": "寫入命令",
                "python": "31 CE",
                "description": "STM32 寫入命令（0x31 和補碼 0xCE）"
            },
            {
                "name": "擦除命令",
                "python": "43 BC",
                "description": "STM32 擦除命令（0x43 和補碼 0xBC）"
            }
        ]
        
        for test in test_cases:
            print(f"\n{test['name']}:")
            print(f"  HEX: {test['python']}")
            print(f"  說明: {test['description']}")
    
    @staticmethod
    def debug_android_issue():
        """診斷 Android 實現問題"""
        print("\n=== Android 調試建議 ===")
        
        print("\n1. 在 FirmwareUpdateHelper.java 中添加調試日誌:")
        print("""
   // 在 enterBootloaderMode() 中
   byte[] enterCmd = CustomBootloaderProtocol.buildBootloaderFirstCommand();
   Log.d(TAG, "Raw bytes: " + Arrays.toString(enterCmd));
   String hexCmd = CustomBootloaderProtocol.bytesToHexString(enterCmd);
   Log.d(TAG, "Hex string: " + hexCmd);
   
   // 檢查 sendData 的處理
   Log.d(TAG, "Before sendData: " + hexCmd);
   boolean sent = terminalService.sendData(hexCmd);
   Log.d(TAG, "Send result: " + sent);
        """)
        
        print("\n2. 修改發送方式（如果需要）:")
        print("""
   // 方案 A: 直接發送字節數組
   terminalService.write(enterCmd);  // 需要在 Service 中實現
   
   // 方案 B: 修改 sendData 以識別二進制模式
   terminalService.sendBinary(enterCmd);  // 新增方法
        """)
        
        print("\n3. 驗證接收端:")
        print("""
   // 在 Python 模擬器中記錄原始字節
   raw_bytes = self.serial_port.read(self.serial_port.in_waiting)
   print(f"Raw: {[f'0x{b:02X}' for b in raw_bytes]}")
   print(f"ASCII: {raw_bytes.decode('ascii', errors='replace')}")
        """)

if __name__ == "__main__":
    test = BootloaderProtocolTest()
    
    # 執行所有測試
    test.test_custom_protocol()
    test.test_stm32_write_sequence()
    test.compare_implementations()
    test.generate_test_cases()
    test.debug_android_issue()
    
    print("\n" + "="*50)
    print("測試完成！請根據上述檢查清單驗證 Android 實現。")