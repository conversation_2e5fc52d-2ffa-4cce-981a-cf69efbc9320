import asyncio
from discord_bot import start_bots, send_discord_message, handle_error_message

# Bot Token 與頻道對應的 Map
bot_tokens = {
    'TEST_BOT': 'MTMxMzY3NjI0ODAzNTIyOTgxOA.G1Jqsg.BrM3nbjEk7mJYkQdiFELFwxEKnjVP_RxaUHPZ0'
}

channel_map = {
    'TEST_BOT': {
        'TEST_BOT_CHANNEL': 1313683892024250368  # 替換為您的頻道 ID
    },
}

# 測試多次發送消息與錯誤處理的函數
async def test_multiple_messages():
    # 發送普通訊息
    await send_discord_message("TEST_BOT", "TEST_BOT_CHANNEL", "Testing: Regular message", color_style="blue")

    # 發送多條錯誤訊息
    await handle_error_message("TEST_BOT", "TEST_BOT_CHANNEL", "Warning: Disk space low")
    await handle_error_message("TEST_BOT", "TEST_BOT_CHANNEL", "Critical: Database connection failed")
    await handle_error_message("TEST_BOT", "TEST_BOT_CHANNEL", "Info: Scheduled maintenance at 2 AM")

    # 發送多條普通訊息，確認順序與樣式
    await send_discord_message("TEST_BOT", "TEST_BOT_CHANNEL", "Testing: Second regular message", color_style="green")
    await send_discord_message("TEST_BOT", "TEST_BOT_CHANNEL", "Testing: Third regular message", color_style="red")

    # 發送普通訊息
    success_message1 = "Info: 新增成功：mtno : V060048 date : 20241112 time : 161954 channelNo : 015 coinAll : 015 coinChg : 000 pnid2 : 0000000000000000 tradeType : 0 nc : 12002 index : S0005 chksum : 2824 sentEOT : E"
    await send_discord_message("TEST_BOT", "TEST_BOT_CHANNEL", success_message1, color_style="blue")

    error_message1 = "Critical: 接收到的資料長度無效，拒絕處理 : T030063 AT^SISR=4,"
    await handle_error_message("TEST_BOT", "TEST_BOT_CHANNEL", error_message1)

async def main():
    # 啟動所有 Bot 作為一個獨立的任務
    bot_task = asyncio.create_task(start_bots(bot_tokens, channel_map))

    # 等待 Bot 完全啟動
    await asyncio.sleep(5)  # 等待 5 秒確保 Bot 啟動

    # 執行測試發送消息
    await test_multiple_messages()

    # 確保 Bot 持續運行
    await bot_task

if __name__ == "__main__":
    asyncio.run(main())
