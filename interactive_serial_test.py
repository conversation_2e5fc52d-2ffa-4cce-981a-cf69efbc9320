#!/usr/bin/env python3
"""
互動式串口測試工具 - 測試 Android APP 換行符處理
"""

import serial
import serial.tools.list_ports
import time
import sys

def select_port():
    """讓使用者選擇串口"""
    ports = list(serial.tools.list_ports.comports())
    
    if not ports:
        print("❌ 未找到任何串口！")
        print("\n請確認:")
        print("1. USB 轉串口線是否已連接")
        print("2. 驅動程式是否已安裝")
        return None
    
    print("\n📋 可用的串口:")
    for i, port in enumerate(ports):
        print(f"{i+1}. {port.device} - {port.description}")
    
    while True:
        try:
            choice = input(f"\n請選擇串口 (1-{len(ports)}): ")
            index = int(choice) - 1
            if 0 <= index < len(ports):
                return ports[index].device
            else:
                print("❌ 無效的選擇，請重試")
        except ValueError:
            print("❌ 請輸入數字")
        except KeyboardInterrupt:
            print("\n\n程式已取消")
            return None

def quick_test(ser):
    """快速測試換行符"""
    print("\n🚀 開始快速測試...\n")
    
    tests = [
        ("Hello World\\r\\n", b"Hello World\r\n", "測試基本訊息 + CRLF"),
        ("Line1\\r\\nLine2\\r\\n", b"Line1\r\nLine2\r\n", "測試多行訊息"),
        ("No newline", b"No newline", "測試無換行訊息"),
        ("\\r\\n", b"\r\n", "測試單獨換行符"),
        ("Test中文\\r\\n", "Test中文\r\n".encode('utf-8'), "測試中文 + CRLF"),
    ]
    
    for desc, data, comment in tests:
        print(f"📤 {comment}")
        print(f"   發送: {desc}")
        print(f"   16進位: {data.hex()}")
        ser.write(data)
        time.sleep(0.5)
        print()

def interactive_mode(ser):
    """互動模式"""
    print("\n💬 進入互動模式")
    print("="*50)
    print("指令說明:")
    print("  直接輸入文字 - 發送文字（自動加 \\r\\n）")
    print("  文字!        - 發送文字（不加換行）")
    print("  hex:48656C6C6F - 發送16進位資料")
    print("  \\n          - 只發送換行符 (0D0A)")
    print("  clear       - 清除畫面")
    print("  test        - 執行快速測試")
    print("  quit        - 退出程式")
    print("="*50)
    
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'clear':
                print("\033[2J\033[H")  # 清除畫面
                continue
            elif user_input.lower() == 'test':
                quick_test(ser)
                continue
            elif user_input == '\\n':
                # 只發送換行
                ser.write(b'\r\n')
                print("✅ 已發送: CRLF (0D0A)")
            elif user_input.startswith('hex:'):
                # 發送16進位
                hex_str = user_input[4:].replace(' ', '')
                try:
                    data = bytes.fromhex(hex_str)
                    ser.write(data)
                    print(f"✅ 已發送 16進位: {data.hex()}")
                    # 嘗試解碼顯示
                    try:
                        text = data.decode('utf-8')
                        print(f"   對應文字: {repr(text)}")
                    except:
                        print("   (無法解碼為文字)")
                except ValueError as e:
                    print(f"❌ 無效的16進位: {e}")
            else:
                # 發送文字
                if user_input.endswith('!'):
                    # 不加換行
                    text = user_input[:-1]
                    data = text.encode('utf-8')
                    ser.write(data)
                    print(f"✅ 已發送: '{text}' (無換行)")
                else:
                    # 自動加換行
                    text = user_input + '\r\n'
                    data = text.encode('utf-8')
                    ser.write(data)
                    print(f"✅ 已發送: '{user_input}' + CRLF")
                
                print(f"   16進位: {data.hex()}")
                
        except KeyboardInterrupt:
            print("\n\n是否要退出？(y/n): ", end='')
            if input().lower() == 'y':
                break
        except Exception as e:
            print(f"❌ 錯誤: {e}")

def main():
    """主程式"""
    print("🔧 Android APP 串口測試工具")
    print("用於測試換行符 (0D0A) 處理")
    print("-" * 40)
    
    # 選擇串口
    port = select_port()
    if not port:
        return
    
    # 設定波特率
    baudrate_input = input("\n請輸入波特率 (預設 115200): ").strip()
    baudrate = int(baudrate_input) if baudrate_input else 115200
    
    try:
        # 開啟串口
        print(f"\n正在開啟 {port} @ {baudrate} bps...")
        ser = serial.Serial(port, baudrate, timeout=1)
        print(f"✅ 串口已開啟！")
        time.sleep(2)  # 等待穩定
        
        # 詢問測試模式
        print("\n請選擇模式:")
        print("1. 快速測試 (自動發送測試資料)")
        print("2. 互動模式 (手動輸入)")
        print("3. 兩者都要")
        
        choice = input("\n選擇 (1/2/3): ").strip()
        
        if choice == '1':
            quick_test(ser)
        elif choice == '2':
            interactive_mode(ser)
        else:
            quick_test(ser)
            interactive_mode(ser)
        
        ser.close()
        print("\n✅ 串口已關閉")
        
    except serial.SerialException as e:
        print(f"\n❌ 串口錯誤: {e}")
        print("\n可能的原因:")
        print("1. 串口被其他程式佔用")
        print("2. 串口權限不足")
        print("3. 設備已斷開")
    except Exception as e:
        print(f"\n❌ 錯誤: {e}")
    finally:
        if 'ser' in locals() and ser.is_open:
            ser.close()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程式已停止")