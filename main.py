import sys
import os
from tkinter import ttk
import tkinter as tk
from PIL import Image, ImageTk, ImageFont, ImageDraw
import barcode
from barcode.writer import ImageWriter
import random
import string


class CustomImageWriter(ImageWriter):
    def __init__(self):
        super().__init__()
        self.font = "DejaVuSansMono.ttf"  # 這只是一個佔位符，實際上不會使用

    def _paint_text(self, text, pos):
        draw = ImageDraw.Draw(self._image)
        font = ImageFont.load_default()

        # 確保文本是字符串
        if not isinstance(text, str):
            text = str(text)

        # 確保位置是元組
        if not isinstance(pos, tuple):
            pos = (0, 0)  # 默認位置

        draw.text(pos, text, font=font, fill=self.foreground)


def generate_random_barcode():
    random_letters = ''.join(random.choices(string.ascii_uppercase, k=2))
    random_numbers = ''.join(random.choices(string.digits, k=14))
    barcode_number = random_letters + random_numbers

    CODE128 = barcode.get_barcode_class('code128')
    writer = CustomImageWriter()

    code128 = CODE128(barcode_number, writer=writer)
    filename = code128.save('barcode')
    return filename, barcode_number


def update_barcode():
    filename, number = generate_random_barcode()
    img = Image.open(filename)
    barcode_img = ImageTk.PhotoImage(img)
    barcode_label.config(image=barcode_img)
    barcode_label.image = barcode_img
    print(f"條碼號碼: {number}")
    barcode_number_label.config(text=f"條碼號碼: {number}")


app = tk.Tk()
app.title("台灣自然人條碼產生器")

barcode_label = ttk.Label(app)
barcode_label.pack(pady=10)

barcode_number_label = ttk.Label(app, text="條碼號碼: ")
barcode_number_label.pack(pady=5)

generate_button = ttk.Button(app, text="生成新條碼", command=update_barcode)
generate_button.pack(pady=10)

update_barcode()  # 啟動時顯示一個條碼

app.mainloop()