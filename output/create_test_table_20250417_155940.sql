
-- 創建全聯門店測試表
CREATE TABLE ASUSER.ASAA_TEST (
  "AAVENN" VARCHAR2(5 BYTE) NOT NULL,
  "AACSTN" VARCHAR2(10 BYTE) NOT NULL,
  "AANAME" VARCHAR2(22 BYTE) NOT NULL,
  "AAROUT" VARCHAR2(5 BYTE),
  "AAKIND" VARCHAR2(1 BYTE),
  "AABOSS" VARCHAR2(12 BYTE),
  "AAADDR" VARCHAR2(54 BYTE),
  "AAEMPO" VARCHAR2(6 BYTE),
  "AATELN" VARCHAR2(20 BYTE),
  "AADATE" VARCHAR2(7 BYTE),
  "AASTOP" VARCHAR2(7 BYTE),
  "AARACO" VARCHAR2(4 BYTE),
  "AADISP" VARCHAR2(1 BYTE) DEFAULT ' ',
  "AAHYCS" VARCHAR2(10 BYTE),
  "AAUPDD" VARCHAR2(7 BYTE),
  "AAITEM" VARCHAR2(2 BYTE),
  "AACSTN1" VARCHAR2(10 BYTE),
  "AAYYMM" VARCHAR2(5 BYTE),
  "AAINST" VARCHAR2(2 BYTE) DEFAULT 'AS',
  "AADIRECT" VARCHAR2(1 BYTE) DEFAULT 'N',
  "AATOKEN" VARCHAR2(255 BYTE),
  "AAADDRW" NVARCHAR2(54),
  "MATCHING_METHOD" VARCHAR2(20 BYTE),
  "CONFIDENCE" NUMBER(3)
);

-- 添加註解
COMMENT ON TABLE ASUSER.ASAA_TEST IS '全聯門店區域代碼測試表';
COMMENT ON COLUMN ASUSER.ASAA_TEST."AARACO" IS '鄉鎮區域別';
COMMENT ON COLUMN ASUSER.ASAA_TEST."MATCHING_METHOD" IS '匹配方法';
COMMENT ON COLUMN ASUSER.ASAA_TEST."CONFIDENCE" IS '匹配信心度(百分比)';

-- 創建索引
CREATE INDEX ASAA_TEST_IDX1 ON ASUSER.ASAA_TEST ("AACSTN");
CREATE INDEX ASAA_TEST_IDX2 ON ASUSER.ASAA_TEST ("MATCHING_METHOD");
CREATE INDEX ASAA_TEST_IDX3 ON ASUSER.ASAA_TEST ("CONFIDENCE");
