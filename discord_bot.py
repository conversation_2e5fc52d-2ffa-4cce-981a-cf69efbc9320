import discord
from discord.ext import commands, tasks
from datetime import datetime, timedelta
import asyncio

# 初始化 client_map 來儲存 Bot 名稱對應的 client 實例
client_map = {}

# 初始化錯誤紀錄和待處理清單
error_log = {}
pending_tasks = {}

# 定義全局變量 channel_map
channel_map = {}


# 取得特定 Bot 的頻道
def get_channel_by_name(bot_name, channel_name):
    client = client_map.get(bot_name)
    channel_id = channel_map.get(bot_name, {}).get(channel_name)
    if client:
        print(f"[DEBUG] Bot: {bot_name}, Channel Name: {channel_name}, Channel ID: {channel_id}")
    else:
        print(f"[ERROR] 無法找到 Bot: {bot_name}")
    return client.get_channel(channel_id) if client else None


# 發送帶顏色的簡單文字訊息的函式
async def send_discord_message(bot_name, channel_name, message, color_style="normal"):
    channel = get_channel_by_name(bot_name, channel_name)
    if channel and message:
        formatted_message = format_message_by_color(message, color_style)
        await channel.send(formatted_message)
        print(f"訊息發送成功至 {channel_name}: {message}")
    else:
        print(f"[ERROR] 無法發送訊息至 {channel_name}: 找不到頻道或訊息為空")


# 格式化訊息根據顏色樣式
def format_message_by_color(message, color_style):
    if color_style == 'red':
        return f"```diff\n- {message}\n```"  # 紅色
    elif color_style == 'green':
        return f"```diff\n+ {message}\n```"  # 綠色
    elif color_style == 'blue':
        return f"```css\n[{message}]\n```"  # 藍色
    return message  # 正常顯示


# 自動分級處理錯誤訊息的函式
async def handle_error_message(bot_name, channel_name, message):
    channel = get_channel_by_name(bot_name, channel_name)
    if "critical" in message.lower():
        await send_discord_message(bot_name, channel_name, f"🚨 **嚴重錯誤**: {message}", color_style="red")
        log_error(bot_name, "Critical", message)
    elif "warning" in message.lower():
        await send_discord_message(bot_name, channel_name, f"⚠️ **警告**: {message}", color_style="orange")
        log_error(bot_name, "Warning", message)
    else:
        await send_discord_message(bot_name, channel_name, f"ℹ️ **資訊**: {message}")
        log_error(bot_name, "Info", message)


# 錯誤紀錄存入錯誤清單
def log_error(bot_name, error_type, message):
    if bot_name not in error_log:
        error_log[bot_name] = []
    error_log[bot_name].append({
        "type": error_type,
        "message": message,
        "time": datetime.now()
    })


# 初始化並啟動所有 Bot
async def start_bots(bot_tokens, channels):
    global channel_map
    channel_map = channels  # 更新全局的 channel_map

    clients = []
    for bot_name, token in bot_tokens.items():
        intents = discord.Intents.all()
        client = commands.Bot(command_prefix="!", intents=intents)
        client_map[bot_name] = client

        # 註冊 Bot 上線事件
        @client.event
        async def on_ready():
            print(f"Bot {bot_name} 已登入，名稱為 {client.user.name}，伺服器: {client.guilds}")

        # 啟動 Bot
        clients.append(client.start(token))

    # 等待所有 Bot 同時啟動
    await asyncio.gather(*clients)
