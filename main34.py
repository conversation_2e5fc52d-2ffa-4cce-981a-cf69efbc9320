#!/usr/bin/env python
# -*- coding: utf-8 -*-
import traceback

import cx_Oracle
import re
import logging
import os
from datetime import datetime

# 設定日誌記錄
logging.basicConfig(
    filename=f'promotion_process_{datetime.now().strftime("%Y%m%d")}.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Oracle 連線配置
DB_CONFIG = {
    'user': 'asuser',
    'password': 'asuser',
    'dsn': '172.20.160.32/hy1',
}

# 資料表名稱設定
TABLE_NAME = 'SBAK_PRICE'  # 使用新的資料表名稱

# 促銷類型正則表達式與對應公式的配置
PROMOTION_PATTERNS = [
    # 第二件X折類型
    {
        'regex': r'第(二|2)件(\d+)折',
        'formula': lambda match, price: f'(原價 + 原價*{int(match.group(2)) / 10})/2',
        'calculate': lambda match, price: (price + price * int(match.group(2)) / 10) / 2
    },
    # 第二件X元類型
    {
        'regex': r'第(二|2)件(\d+)元',
        'formula': lambda match, price: f'(原價 + {match.group(2)})/2',
        'calculate': lambda match, price: (price + int(match.group(2))) / 2
    },
    # 同商品/同價位第二件X折類型
    {
        'regex': r'(同商品|同價位|同品牌|同品項|同價格)(第(二|2)件|第二件)(\d+)折',
        'formula': lambda match, price: f'(原價 + 原價*{int(match.group(4)) / 10})/2',
        'calculate': lambda match, price: (price + price * int(match.group(4)) / 10) / 2
    },
    # 任X件Y折類型
    {
        'regex': r'任(\d+)件(\d+)折',
        'formula': lambda match, price: f'原價*{match.group(1)}*{int(match.group(2)) / 10}/{match.group(1)}',
        'calculate': lambda match, price: price * int(match.group(1)) * (int(match.group(2)) / 10) / int(match.group(1))
    },
    # 任選X件Y折類型
    {
        'regex': r'任選(\d+)件(\d+)折',
        'formula': lambda match, price: f'原價*{match.group(1)}*{int(match.group(2)) / 10}/{match.group(1)}',
        'calculate': lambda match, price: price * int(match.group(1)) * (int(match.group(2)) / 10) / int(match.group(1))
    },
    # 任二件X折類型
    {
        'regex': r'任(選)?(二|2)件(\d+)折',
        'formula': lambda match, price: f'原價*2*{int(match.group(3)) / 10}/2',
        'calculate': lambda match, price: price * 2 * (int(match.group(3)) / 10) / 2
    },
    # 二件X折類型
    {
        'regex': r'(二|2)件(\d+)折',
        'formula': lambda match, price: f'原價*2*{int(match.group(2)) / 10}/2',
        'calculate': lambda match, price: price * 2 * (int(match.group(2)) / 10) / 2
    },
    # X件Y折類型
    {
        'regex': r'(\d+)件(\d+)折',
        'formula': lambda match, price: f'原價*{match.group(1)}*{int(match.group(2)) / 10}/{match.group(1)}',
        'calculate': lambda match, price: price * int(match.group(1)) * (int(match.group(2)) / 10) / int(match.group(1))
    },
    # 買一送一類型
    {
        'regex': r'買一送一',
        'formula': lambda match, price: '原價/2',
        'calculate': lambda match, price: price / 2
    },
    # 買X送Y類型
    {
        'regex': r'買(\d+)送(\d+)',
        'formula': lambda match, price: f'原價*{match.group(1)}/({int(match.group(1))}+{int(match.group(2))})',
        'calculate': lambda match, price: price * int(match.group(1)) / (int(match.group(1)) + int(match.group(2)))
    },
    # +X元多Y件類型
    {
        'regex': r'\+(\d+)元多(\d+)件',
        'formula': lambda match, price: f'(原價 + {match.group(1)})/(1+{match.group(2)})',
        'calculate': lambda match, price: (price + int(match.group(1))) / (1 + int(match.group(2)))
    },
    # X元/Y瓶(件/罐/包)類型
    {
        'regex': r'(\d+)元\/(\d+)(瓶|件|罐|包)',
        'formula': lambda match, price: f'{match.group(1)}/{match.group(2)}',
        'calculate': lambda match, price: int(match.group(1)) / int(match.group(2))
    },
    # 特價X元類型
    {
        'regex': r'特價(\d+)元',
        'formula': lambda match, price: f'{match.group(1)}',
        'calculate': lambda match, price: int(match.group(1))
    },
    # 單瓶/件/罐/杯X元類型
    {
        'regex': r'單(瓶|件|罐|杯)(\d+)元',
        'formula': lambda match, price: f'{match.group(2)}',
        'calculate': lambda match, price: int(match.group(2))
    },
    # 買三送一特例
    {
        'regex': r'買三送一',
        'formula': lambda match, price: '原價*3/4',
        'calculate': lambda match, price: price * 3 / 4
    },
    # 買二送一特例
    {
        'regex': r'買二送一',
        'formula': lambda match, price: '原價*2/3',
        'calculate': lambda match, price: price * 2 / 3
    },
    # 買二送二特例
    {
        'regex': r'買二送二',
        'formula': lambda match, price: '原價*2/4',
        'calculate': lambda match, price: price * 2 / 4
    },
    # 買5送5特例
    {
        'regex': r'買5送5',
        'formula': lambda match, price: '原價*5/10',
        'calculate': lambda match, price: price * 5 / 10
    },
    # 特殊促銷類型
    {
        'regex': r'(嚐鮮價|降價|嚐新價|清涼價)',
        'formula': lambda match, price: '特價',
        'calculate': lambda match, price: None  # 無法計算具體價格，需要另外處理
    }
]


def identify_promotion(promotion_text, original_price):
    """識別促銷文字並返回對應的計算公式和計算結果"""
    if not promotion_text or promotion_text.strip() == '無':
        return None, None

    promotion_text = promotion_text.strip()

    # 嘗試匹配每一種促銷模式
    for pattern in PROMOTION_PATTERNS:
        match = re.search(pattern['regex'], promotion_text)
        if match:
            try:
                formula = pattern['formula'](match, original_price)
                calculated_price = pattern['calculate'](match, original_price)
                # 如果計算結果為 None（如特價類型），則使用原價
                if calculated_price is None:
                    calculated_price = original_price
                # 四捨五入到整數
                calculated_price = round(calculated_price)
                return formula, calculated_price
            except Exception as e:
                logging.error(f"計算價格時發生錯誤: {promotion_text}, 原價: {original_price}, 錯誤: {str(e)}")
                logging.error(traceback.format_exc())  # 記錄詳細的錯誤堆疊
                return None, None

    # 如果沒有匹配到任何模式，記錄該促銷文字
    logging.warning(f"未能識別的促銷條件: {promotion_text}, 原價: {original_price}")
    return None, None


def split_promotion_text(text):
    """分割促銷文字，處理多種分隔符號"""
    if not text:
        return []
    # 分割多個促銷條件（使用';'、'；'或'、'作為分隔符）
    return [item.strip() for item in re.split(r'[;；、]', text) if item.strip()]


def check_table_structure():
    """檢查資料表是否存在並包含必要欄位"""
    connection = None
    cursor = None
    try:
        connection = cx_Oracle.connect(
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            dsn=DB_CONFIG['dsn'],
        )
        cursor = connection.cursor()

        # 檢查表是否存在
        cursor.execute(f"""
        SELECT COUNT(*)
        FROM ALL_TABLES
        WHERE TABLE_NAME = '{TABLE_NAME}'
        """)

        if cursor.fetchone()[0] == 0:
            logging.error(f"{TABLE_NAME} 表不存在！")
            return False

        # 檢查表結構
        cursor.execute(f"""
        SELECT COLUMN_NAME
        FROM ALL_TAB_COLUMNS
        WHERE TABLE_NAME = '{TABLE_NAME}'
        """)

        columns = [row[0] for row in cursor.fetchall()]
        logging.info(f"{TABLE_NAME} 表的列: {columns}")

        required_columns = [
            'AJPMNO_KEY',
            'AKITNO_KEY',
            'PROMOTION_CONDITION',
            'PROMOTION_FORMULA',
            'CALCULATED_PRICE',
            'AJROUT'  # 新增 AJROUT 欄位檢查
        ]

        missing_columns = [col for col in required_columns if col not in columns]
        if missing_columns:
            logging.error(f"{TABLE_NAME} 表缺少列: {missing_columns}")
            return False

        return True
    except cx_Oracle.Error as e:
        logging.error(f"檢查表結構時發生錯誤: {e}")
        return False
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def process_promotions():
    """處理資料庫中的促銷資訊並更新到目標表"""
    connection = None
    cursor = None
    try:
        # 檢查表結構
        if not check_table_structure():
            return False, f"{TABLE_NAME} 表結構不正確或不存在，請檢查資料庫"

        # 連接到 Oracle 資料庫
        try:
            connection = cx_Oracle.connect(
                user=DB_CONFIG['user'],
                password=DB_CONFIG['password'],
                dsn=DB_CONFIG['dsn'],
            )
            logging.info("成功連接到 Oracle 資料庫")
        except cx_Oracle.Error as e:
            logging.error(f"資料庫連接失敗: {e}")
            return False, f"資料庫連接失敗: {e}"

        cursor = connection.cursor()

        # 查詢需要處理的促銷資訊，加入 AKSPRIC（原價）和 AJROUT（客戶店型態）
        query = """
                SELECT AJPMNO, AKITNO, AKSPRIC, AJMEMO, AJROUT
                FROM SBAJ,
                     SBAK
                WHERE AJPMNO = AKPMNO
                  AND AJCRDT >= :start_date
                """

        cursor.execute(query, start_date='1140101')
        records = cursor.fetchall()

        logging.info(f"查詢到 {len(records)} 筆待處理的促銷資訊")

        # 使用集合來檢查重複項
        processed_records = set()
        insert_data = []

        for ajpmno, akitno, akspric, ajmemo, ajrout in records:
            if not ajmemo:
                continue

            # 確保 akspric 是數字
            try:
                akspric = float(akspric) if akspric is not None else 0
            except (ValueError, TypeError):
                logging.warning(f"原價轉換失敗: {ajpmno}, {akitno}, {akspric}")
                continue

            # 分割多個促銷條件
            promotion_conditions = split_promotion_text(ajmemo)

            # 處理每個促銷條件
            for condition in promotion_conditions:
                formula, calculated_price = identify_promotion(condition, akspric)
                # print('formula:',formula, 'calculated_price:',calculated_price)
                if formula:
                    # 創建唯一鍵來檢查重複
                    unique_key = (ajpmno, akitno, ajrout, condition)
                    print('unique_key:',unique_key)
                    if unique_key not in processed_records:
                        processed_records.add(unique_key)
                        # 添加 AJROUT 欄位和計算後的價格到插入數據中
                        insert_data.append((ajpmno, akitno, condition, formula, calculated_price, ajrout))

        logging.info(f"成功識別 {len(insert_data)} 個不重複的促銷條件和計算公式")

        # 刪除目標表中可能存在的舊資料
        try:
            cursor.execute(f"""
                          DELETE
                          FROM {TABLE_NAME}
                          WHERE AJPMNO_KEY IN (SELECT AJPMNO
                                               FROM SBAJ
                                               WHERE AJCRDT >= :start_date)
                          """, start_date='1140101')

            deleted_count = cursor.rowcount
            logging.info(f"刪除了 {deleted_count} 筆舊的促銷公式資料")
            connection.commit()  # 先提交刪除操作
            logging.info("刪除操作已提交")
        except cx_Oracle.Error as e:
            logging.error(f"刪除舊資料時發生錯誤: {e}")
            logging.error(traceback.format_exc())
            if connection:
                connection.rollback()
            return False, f"刪除舊資料失敗: {str(e)}"

        # 使用單一插入而非批量插入，捕捉任何錯誤
        success_count = 0
        for i, record in enumerate(insert_data):
            try:
                # 記錄每10筆資料的處理進度
                if i % 10 == 0:
                    logging.info(f"正在處理第 {i}/{len(insert_data)} 筆資料")

                # 使用新的表名插入資料，包括 AJROUT 欄位
                cursor.execute(f"""
                               INSERT INTO {TABLE_NAME}
                                   (AJPMNO_KEY, AKITNO_KEY, PROMOTION_CONDITION, PROMOTION_FORMULA, CALCULATED_PRICE, AJROUT)
                               VALUES (:1, :2, :3, :4, :5, :6)
                               """, record)

                success_count += 1

                # 每10筆記錄提交一次，避免交易過大
                if success_count % 10 == 0:
                    connection.commit()
                    logging.info(f"已提交 {success_count} 筆記錄")

            except cx_Oracle.DatabaseError as e:
                error, = e.args
                logging.error(f"插入記錄失敗 (第{i}筆): {record}, 錯誤: {error.message}")
                # 繼續處理下一條記錄，而不是中斷整個過程

        # 提交最後的更改
        try:
            if success_count % 10 != 0:  # 確保最後未提交的記錄也能被提交
                connection.commit()
            logging.info(f"最後提交完成，成功插入 {success_count} 筆新的促銷公式資料")
        except cx_Oracle.Error as e:
            logging.error(f"提交時發生錯誤: {e}")
            logging.error(traceback.format_exc())
            if connection:
                connection.rollback()
            return False, f"提交失敗: {str(e)}"

        # 關閉資料庫連接
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logging.info("資料處理完成，資料庫連接已關閉")

        return True, f"成功處理 {len(records)} 筆促銷資訊，識別了 {len(insert_data)} 個促銷條件，插入了 {success_count} 筆資料"

    except cx_Oracle.Error as error:
        error_msg = f"資料庫錯誤: {error}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return False, error_msg
    except Exception as e:
        error_msg = f"發生錯誤: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        return False, error_msg
    finally:
        # 確保資源正確釋放
        if cursor:
            try:
                cursor.close()
            except:
                pass
        if connection:
            try:
                connection.close()
            except:
                pass


if __name__ == "__main__":
    logging.info("開始處理促銷資訊")
    success, message = process_promotions()
    if success:
        logging.info(message)
        print(message)
    else:
        logging.error(f"處理失敗: {message}")
        print(f"處理失敗: {message}")