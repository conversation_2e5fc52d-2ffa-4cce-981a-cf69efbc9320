"""
家樂福爬蟲使用範例
"""

import asyncio
from carrefour_scraper import CarrefourScraper

async def example_usage():
    """使用範例"""
    
    # 創建爬蟲實例
    scraper = CarrefourScraper()
    
    print("=== 家樂福產品爬蟲範例 ===")
    print("目標網站: 家樂福黑松品牌館")
    print("開始爬取...")
    
    # 爬取產品 (最多 2 頁，可以看到瀏覽器操作)
    products = await scraper.scrape_products(
        headless=False,  # 設為 True 可隱藏瀏覽器視窗
        max_pages=2      # 爬取頁數
    )
    
    # 顯示結果
    scraper.print_summary()
    
    # 儲存結果
    if products:
        scraper.save_to_csv("example_products.csv")
        scraper.save_to_json("example_products.json")
        
        print(f"\n找到的前 3 個產品:")
        for i, product in enumerate(products[:3], 1):
            print(f"{i}. 產品名稱: {product['name']}")
            print(f"   價格: NT$ {product['price']:.0f}")
            print(f"   連結: {product['product_url']}")
            print()

def custom_scraper_example():
    """自訂爬蟲範例"""
    
    async def custom_scrape():
        scraper = CarrefourScraper()
        
        # 可以修改目標 URL
        scraper.target_url = "https://online.carrefour.com.tw/zh/其他品牌館網址"
        
        # 爬取產品
        products = await scraper.scrape_products(headless=True, max_pages=1)
        
        # 篩選特定價格範圍的產品
        filtered_products = [p for p in products if 100 <= p['price'] <= 500]
        
        print(f"價格在 NT$ 100-500 之間的產品: {len(filtered_products)} 個")
        
        return filtered_products
    
    return asyncio.run(custom_scrape())

if __name__ == "__main__":
    # 執行基本範例
    asyncio.run(example_usage())
